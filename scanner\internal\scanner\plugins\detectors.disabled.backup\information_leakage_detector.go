package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// InformationLeakageDetector 信息泄露检测器
// 支持敏感文件检测、敏感数据泄露、调试信息泄露、版本信息泄露等多种信息泄露检测
type InformationLeakageDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	sensitiveFiles    []string                  // 敏感文件路径
	debugEndpoints    []string                  // 调试端点
	backupFiles       []string                  // 备份文件
	configFiles       []string                  // 配置文件
	sensitivePatterns map[string]*regexp.Regexp // 敏感数据模式
	versionPatterns   []*regexp.Regexp          // 版本信息模式
	errorPatterns     []*regexp.Regexp          // 错误信息模式
	debugPatterns     []*regexp.Regexp          // 调试信息模式
	httpClient        *http.Client
}

// NewInformationLeakageDetector 创建信息泄露检测器
func NewInformationLeakageDetector() *InformationLeakageDetector {
	detector := &InformationLeakageDetector{
		id:          "information-leakage-comprehensive",
		name:        "信息泄露漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // 信息泄露是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-200", "CWE-209", "CWE-532"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测信息泄露漏洞，包括敏感文件泄露、敏感数据泄露、调试信息泄露、版本信息泄露等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         12 * time.Second, // 信息泄露检测需要适中时间
		MaxRetries:      2,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，文件内容可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeFiles()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *InformationLeakageDetector) GetID() string                     { return d.id }
func (d *InformationLeakageDetector) GetName() string                   { return d.name }
func (d *InformationLeakageDetector) GetCategory() string               { return d.category }
func (d *InformationLeakageDetector) GetSeverity() string               { return d.severity }
func (d *InformationLeakageDetector) GetCVE() []string                  { return d.cve }
func (d *InformationLeakageDetector) GetCWE() []string                  { return d.cwe }
func (d *InformationLeakageDetector) GetVersion() string                { return d.version }
func (d *InformationLeakageDetector) GetAuthor() string                 { return d.author }
func (d *InformationLeakageDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *InformationLeakageDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *InformationLeakageDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *InformationLeakageDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *InformationLeakageDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *InformationLeakageDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *InformationLeakageDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *InformationLeakageDetector) GetDependencies() []string         { return []string{} }
func (d *InformationLeakageDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *InformationLeakageDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *InformationLeakageDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *InformationLeakageDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *InformationLeakageDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 12 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *InformationLeakageDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *InformationLeakageDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.sensitiveFiles) == 0 {
		return fmt.Errorf("敏感文件列表不能为空")
	}
	if len(d.sensitivePatterns) == 0 {
		return fmt.Errorf("敏感数据模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *InformationLeakageDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 信息泄露检测适用于所有Web目标，因为任何Web应用都可能存在信息泄露
	return true
}

// Detect 执行检测
func (d *InformationLeakageDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种信息泄露检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 敏感文件检测
	fileEvidence, fileConfidence, filePayload, fileRequest, fileResponse := d.detectSensitiveFiles(ctx, target)
	if fileConfidence > maxConfidence {
		maxConfidence = fileConfidence
		vulnerablePayload = filePayload
		vulnerableRequest = fileRequest
		vulnerableResponse = fileResponse
	}
	evidence = append(evidence, fileEvidence...)

	// 2. 敏感数据泄露检测
	dataEvidence, dataConfidence, dataPayload, dataRequest, dataResponse := d.detectSensitiveData(ctx, target)
	if dataConfidence > maxConfidence {
		maxConfidence = dataConfidence
		vulnerablePayload = dataPayload
		vulnerableRequest = dataRequest
		vulnerableResponse = dataResponse
	}
	evidence = append(evidence, dataEvidence...)

	// 3. 调试信息检测
	debugEvidence, debugConfidence, debugPayload, debugRequest, debugResponse := d.detectDebugInformation(ctx, target)
	if debugConfidence > maxConfidence {
		maxConfidence = debugConfidence
		vulnerablePayload = debugPayload
		vulnerableRequest = debugRequest
		vulnerableResponse = debugResponse
	}
	evidence = append(evidence, debugEvidence...)

	// 4. 版本信息泄露检测
	versionEvidence, versionConfidence, versionPayload, versionRequest, versionResponse := d.detectVersionInformation(ctx, target)
	if versionConfidence > maxConfidence {
		maxConfidence = versionConfidence
		vulnerablePayload = versionPayload
		vulnerableRequest = versionRequest
		vulnerableResponse = versionResponse
	}
	evidence = append(evidence, versionEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "信息泄露漏洞",
		Description:       "检测到信息泄露漏洞，可能泄露敏感信息或系统配置信息",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "移除或保护敏感文件，配置适当的访问控制，避免在响应中泄露敏感信息",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Information_exposure", "https://cwe.mitre.org/data/definitions/200.html"},
		Tags:              []string{"information-leakage", "sensitive-data", "web", "medium"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *InformationLeakageDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的文件进行验证
	verificationFiles := []string{
		"/robots.txt",
		"/.env",
		"/config.php",
		"/web.config",
		"/phpinfo.php",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, file := range verificationFiles {
		// 发送验证请求
		resp, err := d.sendFileRequest(ctx, target.URL, file)
		if err != nil {
			continue
		}

		// 检查信息泄露响应特征
		responseConfidence := d.checkInformationLeakage(resp, file)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证文件触发了信息泄露响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "information-response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用信息响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *InformationLeakageDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("information_leakage_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *InformationLeakageDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (信息泄露通常是中等风险)
	baseScore := 6.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeFiles 初始化文件列表
func (d *InformationLeakageDetector) initializeFiles() {
	// 敏感文件列表
	d.sensitiveFiles = []string{
		// 配置文件
		"/.env",
		"/.env.local",
		"/.env.production",
		"/.env.development",
		"/config.php",
		"/config.inc.php",
		"/configuration.php",
		"/wp-config.php",
		"/wp-config-sample.php",
		"/web.config",
		"/app.config",
		"/database.yml",
		"/database.xml",
		"/settings.py",
		"/local_settings.py",
		"/application.properties",
		"/hibernate.cfg.xml",

		// 信息文件
		"/robots.txt",
		"/sitemap.xml",
		"/crossdomain.xml",
		"/clientaccesspolicy.xml",
		"/humans.txt",
		"/security.txt",
		"/.well-known/security.txt",

		// 调试和测试文件
		"/phpinfo.php",
		"/info.php",
		"/test.php",
		"/debug.php",
		"/trace.php",
		"/status.php",
		"/health.php",
		"/metrics.php",
		"/ping.php",
		"/version.php",

		// 版本控制文件
		"/.git/config",
		"/.git/HEAD",
		"/.git/index",
		"/.git/logs/HEAD",
		"/.svn/entries",
		"/.svn/wc.db",
		"/.hg/hgrc",
		"/.bzr/branch/branch.conf",

		// 备份文件
		"/backup.sql",
		"/database.sql",
		"/dump.sql",
		"/backup.zip",
		"/backup.tar.gz",
		"/site.zip",
		"/www.zip",
		"/web.zip",

		// 日志文件
		"/error.log",
		"/access.log",
		"/debug.log",
		"/application.log",
		"/system.log",
		"/php_errors.log",
		"/error_log",
		"/logs/error.log",
		"/logs/access.log",

		// 管理文件
		"/admin.php",
		"/administrator.php",
		"/login.php",
		"/auth.php",
		"/signin.php",
		"/dashboard.php",
		"/panel.php",
		"/manage.php",
		"/console.php",

		// 其他敏感文件
		"/.htaccess",
		"/.htpasswd",
		"/readme.txt",
		"/README.md",
		"/CHANGELOG.md",
		"/LICENSE",
		"/composer.json",
		"/package.json",
		"/yarn.lock",
		"/Gemfile",
		"/requirements.txt",
		"/Pipfile",
		"/pom.xml",
		"/build.gradle",
	}

	// 调试端点
	d.debugEndpoints = []string{
		"/debug",
		"/debug/",
		"/debug/vars",
		"/debug/pprof",
		"/debug/pprof/",
		"/debug/requests",
		"/debug/sql",
		"/debug/routes",
		"/debug/config",
		"/debug/env",
		"/debug/info",
		"/debug/status",
		"/debug/health",
		"/debug/metrics",
		"/debug/trace",
		"/debug/dump",
		"/debug/log",
		"/debug/error",
		"/debug/session",
		"/debug/cache",
		"/debug/db",
		"/debug/api",
		"/debug/test",
		"/debug/console",
		"/debug/profiler",
		"/debug/monitor",
	}

	// 备份文件扩展名
	d.backupFiles = []string{
		".bak",
		".backup",
		".old",
		".orig",
		".save",
		".tmp",
		".temp",
		".swp",
		".swo",
		".copy",
		".1",
		".2",
		".3",
		"~",
		".zip",
		".tar",
		".tar.gz",
		".rar",
		".7z",
	}

	// 配置文件扩展名
	d.configFiles = []string{
		".conf",
		".config",
		".cfg",
		".ini",
		".properties",
		".yml",
		".yaml",
		".json",
		".xml",
		".env",
		".local",
		".dev",
		".prod",
		".test",
		".staging",
	}
}

// initializePatterns 初始化检测模式
func (d *InformationLeakageDetector) initializePatterns() {
	// 敏感数据模式
	d.sensitivePatterns = make(map[string]*regexp.Regexp)

	sensitivePatternStrings := map[string]string{
		// 身份信息
		"信用卡号":  `\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b`,
		"社会保障号": `\b\d{3}-\d{2}-\d{4}\b`,
		"身份证号":  `\b\d{17}[\dXx]\b`,
		"手机号":   `\b1[3-9]\d{9}\b`,
		"邮箱地址":  `\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b`,

		// API和密钥
		"API密钥":    `(?i)(api[_-]?key|apikey|access[_-]?token)[\s]*[:=][\s]*['\"]?([a-zA-Z0-9_-]{20,})['\"]?`,
		"JWT令牌":    `eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*`,
		"AWS密钥":    `AKIA[0-9A-Z]{16}`,
		"Google密钥": `AIza[0-9A-Za-z_-]{35}`,
		"GitHub令牌": `ghp_[A-Za-z0-9]{36}`,
		"Slack令牌":  `xox[baprs]-[0-9a-zA-Z-]{10,48}`,

		// 密码和凭据
		"密码":    `(?i)(password|passwd|pwd)[\s]*[:=][\s]*['\"]?([^\s'\"]{6,})['\"]?`,
		"数据库连接": `(?i)(mysql|postgresql|mongodb|redis)://[^\s]+`,
		"连接字符串": `(?i)(server|host|hostname)[\s]*[:=][\s]*['\"]?([^\s'\"]+)['\"]?`,

		// 私钥和证书
		"私钥":    `-----BEGIN [A-Z ]+PRIVATE KEY-----`,
		"证书":    `-----BEGIN CERTIFICATE-----`,
		"SSH密钥": `ssh-rsa [A-Za-z0-9+/]+`,

		// 内部IP和路径
		"内部IP":   `\b(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)\d{1,3}\.\d{1,3}\b`,
		"文件路径":   `[A-Za-z]:\\[^<>:"|?*\r\n]+`,
		"Unix路径": `/[a-zA-Z0-9_.-]+(/[a-zA-Z0-9_.-]+)+`,

		// 错误信息
		"SQL错误":    `(?i)(sql|mysql|postgresql|oracle|sqlite).*(error|exception|warning)`,
		"PHP错误":    `(?i)(fatal error|parse error|warning|notice).*in\s+/`,
		"Java错误":   `(?i)(exception|error).*at\s+[a-zA-Z0-9.]+\(`,
		"Python错误": `(?i)traceback.*file\s+"[^"]+",\s+line\s+\d+`,

		// 调试信息
		"调试信息": `(?i)(debug|trace|dump|var_dump|print_r)`,
		"堆栈跟踪": `(?i)(stack\s+trace|call\s+stack|backtrace)`,
		"变量转储": `(?i)(var_dump|print_r|var_export)\s*\(`,
	}

	for name, pattern := range sensitivePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.sensitivePatterns[name] = compiled
		}
	}

	// 版本信息模式
	versionPatternStrings := []string{
		`(?i)(apache|nginx|iis|tomcat|jetty|weblogic|websphere)/[\d.]+`,
		`(?i)(php|python|java|ruby|perl|node\.js)/[\d.]+`,
		`(?i)(mysql|postgresql|oracle|mongodb|redis)/[\d.]+`,
		`(?i)(windows|linux|ubuntu|centos|debian)\s+[\d.]+`,
		`(?i)(server|version):\s*[^/\s]+/[\d.]+`,
		`(?i)x-powered-by:\s*[^/\s]+/[\d.]+`,
		`(?i)x-aspnet-version:\s*[\d.]+`,
		`(?i)x-generator:\s*[^/\s]+\s+[\d.]+`,
	}

	d.versionPatterns = make([]*regexp.Regexp, 0, len(versionPatternStrings))
	for _, pattern := range versionPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.versionPatterns = append(d.versionPatterns, compiled)
		}
	}

	// 错误信息模式
	errorPatternStrings := []string{
		`(?i)(fatal\s+error|parse\s+error|warning|notice).*in\s+/`,
		`(?i)(exception|error).*at\s+[a-zA-Z0-9.]+\(`,
		`(?i)traceback.*file\s+"[^"]+",\s+line\s+\d+`,
		`(?i)(sql|mysql|postgresql|oracle|sqlite).*(error|exception)`,
		`(?i)(access\s+denied|permission\s+denied|unauthorized)`,
		`(?i)(file\s+not\s+found|directory\s+not\s+found|path\s+not\s+found)`,
		`(?i)(connection\s+failed|timeout|refused)`,
		`(?i)(invalid\s+syntax|syntax\s+error|parse\s+error)`,
		`(?i)(null\s+pointer|segmentation\s+fault|memory\s+error)`,
		`(?i)(stack\s+overflow|buffer\s+overflow|heap\s+overflow)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 调试信息模式
	debugPatternStrings := []string{
		`(?i)(debug|trace|dump|var_dump|print_r)`,
		`(?i)(stack\s+trace|call\s+stack|backtrace)`,
		`(?i)(var_dump|print_r|var_export)\s*\(`,
		`(?i)(console\.log|console\.debug|console\.trace)`,
		`(?i)(system\s+info|environment|config|settings)`,
		`(?i)(memory\s+usage|execution\s+time|performance)`,
		`(?i)(database\s+queries|sql\s+log|query\s+log)`,
		`(?i)(session\s+data|cookie\s+data|header\s+data)`,
		`(?i)(request\s+data|response\s+data|post\s+data)`,
		`(?i)(error\s+log|access\s+log|debug\s+log)`,
	}

	d.debugPatterns = make([]*regexp.Regexp, 0, len(debugPatternStrings))
	for _, pattern := range debugPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.debugPatterns = append(d.debugPatterns, compiled)
		}
	}
}
