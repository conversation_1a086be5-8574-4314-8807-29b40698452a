package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestWeakPasswordDetectorBasicFunctionality 测试弱密码检测器基础功能
func TestWeakPasswordDetectorBasicFunctionality(t *testing.T) {
	detector := NewWeakPasswordDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "weak-password-comprehensive", detector.GetID())
	assert.Equal(t, "弱密码漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-521")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestWeakPasswordDetectorApplicability 测试弱密码检测器适用性
func TestWeakPasswordDetectorApplicability(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 测试有密码表单的目标
	passwordFormTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/login",
				Method: "POST",
				Fields: map[string]string{
					"username": "text",
					"password": "password",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(passwordFormTarget))

	// 测试有认证字段的表单
	authFormTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/signin",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/signin",
				Method: "POST",
				Fields: map[string]string{
					"user":   "text",
					"passwd": "password",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(authFormTarget))

	// 测试认证相关的URL
	authURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin/login",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(authURLTarget))

	// 测试管理页面URL
	adminTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/wp-admin",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(adminTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（弱密码检测适用于所有Web目标）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(simpleTarget))
}

// TestWeakPasswordDetectorConfiguration 测试弱密码检测器配置
func TestWeakPasswordDetectorConfiguration(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 10*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 2, config.Concurrency) // 较低并发，避免账户锁定
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 3, updatedConfig.Concurrency)
}

// TestWeakPasswordDetectorCredentials 测试弱密码检测器凭据
func TestWeakPasswordDetectorCredentials(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 检查登录路径
	assert.NotEmpty(t, detector.loginPaths)
	assert.Greater(t, len(detector.loginPaths), 30)

	// 检查默认凭据
	assert.NotEmpty(t, detector.defaultCreds)
	assert.Greater(t, len(detector.defaultCreds), 20)

	// 检查常见凭据
	assert.NotEmpty(t, detector.commonCreds)
	assert.Greater(t, len(detector.commonCreds), 15)

	// 检查弱密码列表
	assert.NotEmpty(t, detector.weakPasswords)
	assert.Greater(t, len(detector.weakPasswords), 40)

	// 检查常见用户名
	assert.NotEmpty(t, detector.commonUsernames)
	assert.Greater(t, len(detector.commonUsernames), 15)

	// 检查登录指示器
	assert.NotEmpty(t, detector.loginIndicators)
	assert.Greater(t, len(detector.loginIndicators), 20)

	// 检查认证模式
	assert.NotEmpty(t, detector.authPatterns)
	assert.Greater(t, len(detector.authPatterns), 10)

	// 检查成功模式
	assert.NotEmpty(t, detector.successPatterns)
	assert.Greater(t, len(detector.successPatterns), 20)

	// 检查特定的凭据
	foundAdminAdmin := false
	for _, cred := range detector.defaultCreds {
		if cred.Username == "admin" && cred.Password == "admin" {
			foundAdminAdmin = true
			break
		}
	}
	assert.True(t, foundAdminAdmin)

	// 检查特定的路径
	assert.Contains(t, detector.loginPaths, "/login")
	assert.Contains(t, detector.loginPaths, "/admin")
	assert.Contains(t, detector.loginPaths, "/wp-admin")

	// 检查特定的弱密码
	assert.Contains(t, detector.weakPasswords, "123456")
	assert.Contains(t, detector.weakPasswords, "password")
	assert.Contains(t, detector.weakPasswords, "admin")
}

// TestWeakPasswordDetectorLoginPageResponse 测试登录页面响应检查
func TestWeakPasswordDetectorLoginPageResponse(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 测试包含登录表单的页面
	loginPageResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
	<form action="/login" method="post">
		<input type="text" name="username" placeholder="Username">
		<input type="password" name="password" placeholder="Password">
		<input type="submit" value="Login">
	</form>
</body>
</html>`
	loginPath := "/login"
	confidence := detector.checkLoginPageResponse(loginPageResponse, loginPath)
	assert.Greater(t, confidence, 0.7)

	// 测试管理员登录页面
	adminPageResponse := `Status: 200 OK
Content-Type: text/html

<html>
<head><title>Admin Login</title></head>
<body>
	<h1>Administrator Login</h1>
	<form action="/admin/auth" method="post">
		<label>Username:</label>
		<input type="text" name="user" required>
		<label>Password:</label>
		<input type="password" name="pwd" required>
		<button type="submit">Sign In</button>
	</form>
</body>
</html>`
	adminPath := "/admin"
	confidence2 := detector.checkLoginPageResponse(adminPageResponse, adminPath)
	assert.Greater(t, confidence2, 0.6)

	// 测试无登录功能的页面
	normalPageResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
	<h1>Welcome</h1>
	<p>This is a normal page without login functionality.</p>
</body>
</html>`
	normalPath := "/about"
	confidence3 := detector.checkLoginPageResponse(normalPageResponse, normalPath)
	assert.LessOrEqual(t, confidence3, 0.5) // 可能有状态码分数和其他指示器
}

// TestWeakPasswordDetectorAuthResponse 测试认证响应检查
func TestWeakPasswordDetectorAuthResponse(t *testing.T) {
	detector := NewWeakPasswordDetector()
	testCred := Credential{Username: "admin", Password: "admin", Source: "test"}

	// 测试成功登录响应（重定向到dashboard）
	successResponse := `Status: 302 Found
Location: /dashboard
Set-Cookie: session=abc123; Path=/

<html>
<body>
<p>Redirecting to dashboard...</p>
</body>
</html>`
	confidence := detector.checkAuthResponse(successResponse, testCred)
	assert.Greater(t, confidence, 0.8)

	// 测试成功登录响应（欢迎页面）
	welcomeResponse := `Status: 200 OK
Set-Cookie: auth_token=xyz789; Path=/

<html>
<body>
<h1>Welcome back, admin!</h1>
<p>Login successful. Welcome to the dashboard.</p>
<a href="/logout">Sign out</a>
</body>
</html>`
	confidence2 := detector.checkAuthResponse(welcomeResponse, testCred)
	assert.Greater(t, confidence2, 0.7)

	// 测试失败登录响应
	failureResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Login Failed</h1>
<p>Invalid username or password. Please try again.</p>
<form action="/login" method="post">
	<input type="text" name="username">
	<input type="password" name="password">
	<input type="submit" value="Login">
</form>
</body>
</html>`
	confidence3 := detector.checkAuthResponse(failureResponse, testCred)
	assert.Less(t, confidence3, 0.3)

	// 测试中文成功响应
	chineseSuccessResponse := `Status: 200 OK
Set-Cookie: sessionid=def456; Path=/

<html>
<body>
<h1>欢迎回来！</h1>
<p>登录成功，欢迎来到管理面板。</p>
<a href="/logout">注销</a>
</body>
</html>`
	confidence4 := detector.checkAuthResponse(chineseSuccessResponse, testCred)
	assert.Greater(t, confidence4, 0.7)
}

// TestWeakPasswordDetectorBuildLoginURL 测试构造登录URL
func TestWeakPasswordDetectorBuildLoginURL(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 测试基础URL构造
	baseURL := "http://example.com"
	path := "/login"
	loginURL := detector.buildLoginURL(baseURL, path)
	assert.Equal(t, "http://example.com/login", loginURL)

	// 测试带端口的URL构造
	baseURLWithPort := "http://example.com:8080"
	loginURLWithPort := detector.buildLoginURL(baseURLWithPort, path)
	assert.Equal(t, "http://example.com:8080/login", loginURLWithPort)

	// 测试HTTPS URL构造
	httpsURL := "https://example.com"
	httpsLoginURL := detector.buildLoginURL(httpsURL, path)
	assert.Equal(t, "https://example.com/login", httpsLoginURL)

	// 测试带路径的基础URL
	baseURLWithPath := "http://example.com/app"
	loginURLFromPath := detector.buildLoginURL(baseURLWithPath, "/admin")
	assert.Equal(t, "http://example.com/admin", loginURLFromPath)
}

// TestWeakPasswordDetectorRiskScore 测试风险评分计算
func TestWeakPasswordDetectorRiskScore(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestWeakPasswordDetectorLifecycle 测试检测器生命周期
func TestWeakPasswordDetectorLifecycle(t *testing.T) {
	detector := NewWeakPasswordDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkWeakPasswordDetectorLoginPageCheck 基准测试登录页面检查性能
func BenchmarkWeakPasswordDetectorLoginPageCheck(b *testing.B) {
	detector := NewWeakPasswordDetector()
	response := `<html><body><form action="/login" method="post"><input type="text" name="username"><input type="password" name="password"><input type="submit" value="Login"></form></body></html>`
	path := "/login"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkLoginPageResponse(response, path)
	}
}

// BenchmarkWeakPasswordDetectorAuthCheck 基准测试认证检查性能
func BenchmarkWeakPasswordDetectorAuthCheck(b *testing.B) {
	detector := NewWeakPasswordDetector()
	response := `Status: 302 Found\nLocation: /dashboard\nSet-Cookie: session=abc123\n\nRedirecting...`
	cred := Credential{Username: "admin", Password: "admin", Source: "test"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkAuthResponse(response, cred)
	}
}
