package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestDataValidationDetectorBasicFunctionality 测试数据验证检测器基本功能
func TestDataValidationDetectorBasicFunctionality(t *testing.T) {
	detector := NewDataValidationDetector()

	// 测试基本信息
	if detector.GetID() != "data-validation-comprehensive" {
		t.<PERSON><PERSON>("Expected ID 'data-validation-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "数据验证漏洞综合检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '数据验证漏洞综合检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>rf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "medium" {
		t.<PERSON><PERSON><PERSON>("Expected severity 'medium', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestDataValidationDetectorApplicability 测试数据验证检测器适用性
func TestDataValidationDetectorApplicability(t *testing.T) {
	detector := NewDataValidationDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有表单的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/submit",
						Method: "POST",
						Fields: map[string]string{"name": "text", "email": "email"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有参数的URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com?search=test",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有数据验证技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring Validation", Version: "2.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有JSON内容类型的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestDataValidationDetectorConfiguration 测试数据验证检测器配置
func TestDataValidationDetectorConfiguration(t *testing.T) {
	detector := NewDataValidationDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 20*time.Second {
		t.Errorf("Expected timeout 20s, got %v", config.Timeout)
	}

	if config.MaxRetries != 2 {
		t.Errorf("Expected max retries 2, got %d", config.MaxRetries)
	}

	if config.Concurrency != 6 {
		t.Errorf("Expected concurrency 6, got %d", config.Concurrency)
	}

	if config.RateLimit != 6 {
		t.Errorf("Expected rate limit 6, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 2*1024*1024 {
		t.Errorf("Expected max response size 2MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     8,
		RateLimit:       8,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 30*time.Second {
		t.Errorf("Expected updated timeout 30s, got %v", updatedConfig.Timeout)
	}
}

// TestDataValidationDetectorBoundaryValuePayloads 测试边界值载荷
func TestDataValidationDetectorBoundaryValuePayloads(t *testing.T) {
	detector := NewDataValidationDetector()

	if len(detector.boundaryValuePayloads) == 0 {
		t.Error("Expected some boundary value payloads")
	}

	// 检查是否包含关键的边界值载荷
	expectedPayloads := []string{
		"0",
		"-1",
		"2147483647",
		"-2147483648",
		"",
		"NaN",
		"Infinity",
		"true",
		"false",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.boundaryValuePayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find boundary value payload '%s'", expected)
		}
	}
}

// TestDataValidationDetectorTypeConfusionPayloads 测试类型混淆载荷
func TestDataValidationDetectorTypeConfusionPayloads(t *testing.T) {
	detector := NewDataValidationDetector()

	if len(detector.typeConfusionPayloads) == 0 {
		t.Error("Expected some type confusion payloads")
	}

	// 检查是否包含关键的类型混淆载荷
	expectedPayloads := []string{
		"123",
		"true",
		"false",
		"null",
		"undefined",
		"[]",
		"{}",
		"function",
		"Date",
		"RegExp",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.typeConfusionPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find type confusion payload '%s'", expected)
		}
	}
}

// TestDataValidationDetectorFormatBypassPayloads 测试格式验证绕过载荷
func TestDataValidationDetectorFormatBypassPayloads(t *testing.T) {
	detector := NewDataValidationDetector()

	if len(detector.formatBypassPayloads) == 0 {
		t.Error("Expected some format bypass payloads")
	}

	// 检查是否包含关键的格式验证绕过载荷
	expectedPayloads := []string{
		"test@",
		"@example.com",
		"http://",
		"256.256.256.256",
		"2023-13-01",
		"1234",
		"12345678901234567890",
		"",
		"password",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.formatBypassPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find format bypass payload '%s'", expected)
		}
	}
}

// TestDataValidationDetectorLengthBypassPayloads 测试长度验证绕过载荷
func TestDataValidationDetectorLengthBypassPayloads(t *testing.T) {
	detector := NewDataValidationDetector()

	if len(detector.lengthBypassPayloads) == 0 {
		t.Error("Expected some length bypass payloads")
	}

	// 检查是否包含不同长度的载荷
	var hasShort, hasMedium, hasLong bool
	for _, payload := range detector.lengthBypassPayloads {
		length := len(payload)
		if length < 10 {
			hasShort = true
		} else if length >= 100 && length <= 1000 {
			hasMedium = true
		} else if length > 1000 {
			hasLong = true
		}
	}

	if !hasShort {
		t.Error("Expected to find short length payloads")
	}
	if !hasMedium {
		t.Error("Expected to find medium length payloads")
	}
	if !hasLong {
		t.Error("Expected to find long length payloads")
	}
}

// TestDataValidationDetectorTestParameters 测试测试参数
func TestDataValidationDetectorTestParameters(t *testing.T) {
	detector := NewDataValidationDetector()

	if len(detector.testParameters) == 0 {
		t.Error("Expected some test parameters")
	}

	// 检查是否包含关键的测试参数
	expectedParams := []string{
		"data", "input", "value", "content", "text",
		"name", "email", "phone", "password", "age",
		"validate", "check", "verify", "filter",
		"数据", "输入", "验证", "检查",
	}

	for _, expected := range expectedParams {
		found := false
		for _, param := range detector.testParameters {
			if param == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find test parameter '%s'", expected)
		}
	}
}

// TestDataValidationDetectorPatterns 测试检测模式
func TestDataValidationDetectorPatterns(t *testing.T) {
	detector := NewDataValidationDetector()

	// 测试数据验证模式
	if len(detector.validationPatterns) == 0 {
		t.Error("Expected some validation patterns")
	}

	// 测试错误模式
	if len(detector.errorPatterns) == 0 {
		t.Error("Expected some error patterns")
	}

	// 测试成功模式
	if len(detector.successPatterns) == 0 {
		t.Error("Expected some success patterns")
	}

	// 测试绕过模式
	if len(detector.bypassPatterns) == 0 {
		t.Error("Expected some bypass patterns")
	}
}

// TestDataValidationDetectorDataValidationFeatures 测试数据验证功能检查
func TestDataValidationDetectorDataValidationFeatures(t *testing.T) {
	detector := NewDataValidationDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有JSON内容类型的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
			},
			expected: true,
		},
		{
			name: "有验证技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring Validation", Version: "2.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有表单链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/form", Text: "Contact Form"},
				},
			},
			expected: true,
		},
		{
			name: "有验证字段的表单目标",
			target: &plugins.ScanTarget{
				Forms: []plugins.FormInfo{
					{
						Fields: map[string]string{"email": "email", "phone": "tel"},
					},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasDataValidationFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestDataValidationDetectorRiskScore 测试风险评分
func TestDataValidationDetectorRiskScore(t *testing.T) {
	detector := NewDataValidationDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 3.25},
		{0.8, 5.2},
		{1.0, 6.5},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.2f for confidence %.1f, got %.2f", tc.expected, tc.confidence, score)
		}
	}
}

// TestDataValidationDetectorLifecycle 测试检测器生命周期
func TestDataValidationDetectorLifecycle(t *testing.T) {
	detector := NewDataValidationDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
