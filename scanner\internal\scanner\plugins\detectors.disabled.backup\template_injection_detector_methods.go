package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectJinja2Injection 检测Jinja2模板注入
func (d *TemplateInjectionDetector) detectJinja2Injection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Jinja2载荷
	for _, payload := range d.jinja2Payloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Jinja2注入请求
		resp, err := d.sendTemplateRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Jinja2注入响应
		confidence := d.checkJinja2Response(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Jinja2注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "jinja2-injection",
				Description: fmt.Sprintf("发现Jinja2模板注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractTemplateEvidence(resp, "jinja2-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectTwigInjection 检测Twig模板注入
func (d *TemplateInjectionDetector) detectTwigInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Twig载荷
	for _, payload := range d.twigPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Twig注入请求
		resp, err := d.sendTemplateRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Twig注入响应
		confidence := d.checkTwigResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Twig注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "twig-injection",
				Description: fmt.Sprintf("发现Twig模板注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractTemplateEvidence(resp, "twig-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFreemarkerInjection 检测Freemarker模板注入
func (d *TemplateInjectionDetector) detectFreemarkerInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Freemarker载荷
	for _, payload := range d.freemarkerPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Freemarker注入请求
		resp, err := d.sendTemplateRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Freemarker注入响应
		confidence := d.checkFreemarkerResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Freemarker注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "freemarker-injection",
				Description: fmt.Sprintf("发现Freemarker模板注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractTemplateEvidence(resp, "freemarker-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectVelocityInjection 检测Velocity模板注入
func (d *TemplateInjectionDetector) detectVelocityInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Velocity载荷
	for _, payload := range d.velocityPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Velocity注入请求
		resp, err := d.sendTemplateRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Velocity注入响应
		confidence := d.checkVelocityResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Velocity注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "velocity-injection",
				Description: fmt.Sprintf("发现Velocity模板注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractTemplateEvidence(resp, "velocity-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendTemplateRequest 发送模板注入请求
func (d *TemplateInjectionDetector) sendTemplateRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendTemplateGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendTemplatePOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试PUT请求注入
	putResp, err := d.sendTemplatePUTRequest(ctx, targetURL, payload)
	if err == nil && putResp != "" {
		return putResp, nil
	}

	// 返回POST响应（即使有错误）
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendTemplateGETRequest 发送模板注入GET请求
func (d *TemplateInjectionDetector) sendTemplateGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendTemplatePOSTRequest 发送模板注入POST请求
func (d *TemplateInjectionDetector) sendTemplatePOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendTemplatePUTRequest 发送模板注入PUT请求
func (d *TemplateInjectionDetector) sendTemplatePUTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "PUT", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkJinja2Response 检查Jinja2模板注入响应
func (d *TemplateInjectionDetector) checkJinja2Response(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示模板错误
	}

	// 检查Jinja2模式匹配
	for _, pattern := range d.jinja2Patterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Jinja2特定指示器
	jinja2Indicators := []string{
		"49", "7777777", "aaaaaaa", "config", "secret_key", "debug",
		"<class 'str'>", "<class 'object'>", "jinja2.exceptions",
		"templateruntimeerror", "undefinederror", "templatesyntaxerror",
		"配置", "未定义", "模板", "错误",
	}

	for _, indicator := range jinja2Indicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // Jinja2特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkTwigResponse 检查Twig模板注入响应
func (d *TemplateInjectionDetector) checkTwigResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示模板错误
	}

	// 检查Twig模式匹配
	for _, pattern := range d.twigPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Twig特定指示器
	twigIndicators := []string{
		"49", "7777777", "aaaaaaa", "twig\\environment", "twig_environment",
		"gettemplatename", "getenvironment", "root:", "/bin/bash", "/etc/passwd",
		"file_get_contents", "twig_error", "twig\\error", "runtimeerror", "syntaxerror",
		"环境", "文件", "运行时", "语法", "错误",
	}

	for _, indicator := range twigIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // Twig特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkFreemarkerResponse 检查Freemarker模板注入响应
func (d *TemplateInjectionDetector) checkFreemarkerResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示模板错误
	}

	// 检查Freemarker模式匹配
	for _, pattern := range d.freemarkerPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Freemarker特定指示器
	freemarkerIndicators := []string{
		"49", "7777777", "aaaaaaa", "freemarker", "version", "class java.lang.string",
		"class java.lang.runtime", "class java.lang.processbuilder", "java.lang.class",
		"freemarker.template", "templateexception", "invalidreferenceexception",
		"templatemodelexception", "版本", "类", "模板", "异常", "引用", "错误",
	}

	for _, indicator := range freemarkerIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // Freemarker特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkVelocityResponse 检查Velocity模板注入响应
func (d *TemplateInjectionDetector) checkVelocityResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示模板错误
	}

	// 检查Velocity模式匹配
	for _, pattern := range d.velocityPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Velocity特定指示器
	velocityIndicators := []string{
		"49", "7777777", "aaaaaaa", "class java.lang.string", "class java.lang.runtime",
		"class java.lang.processbuilder", "java.lang.class", "user.name", "java.version",
		"os.name", "file.separator", "org.apache.velocity", "velocityexception",
		"parseexception", "methodinvocationexception", "系统", "属性", "类", "信息",
		"解析", "异常", "方法", "调用",
	}

	for _, indicator := range velocityIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // Velocity特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractTemplateEvidence 提取模板注入证据
func (d *TemplateInjectionDetector) extractTemplateEvidence(response, injectionType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据注入类型提取相关证据
	switch injectionType {
	case "jinja2-injection":
		return d.extractJinja2Evidence(response)
	case "twig-injection":
		return d.extractTwigEvidence(response)
	case "freemarker-injection":
		return d.extractFreemarkerEvidence(response)
	case "velocity-injection":
		return d.extractVelocityEvidence(response)
	default:
		return response
	}
}

// extractJinja2Evidence 提取Jinja2证据
func (d *TemplateInjectionDetector) extractJinja2Evidence(response string) string {
	evidence := "Jinja2模板注入证据:\n"

	// 查找Jinja2特定内容
	jinja2Indicators := []string{
		"49", "7777777", "aaaaaaa", "config", "secret_key", "debug",
		"<class 'str'>", "<class 'object'>", "jinja2.exceptions",
		"templateruntimeerror", "undefinederror", "templatesyntaxerror",
	}

	for _, indicator := range jinja2Indicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现Jinja2指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractTwigEvidence 提取Twig证据
func (d *TemplateInjectionDetector) extractTwigEvidence(response string) string {
	evidence := "Twig模板注入证据:\n"

	// 查找Twig特定内容
	twigIndicators := []string{
		"49", "7777777", "aaaaaaa", "twig\\environment", "twig_environment",
		"gettemplatename", "getenvironment", "root:", "/bin/bash", "/etc/passwd",
		"file_get_contents", "twig_error", "twig\\error", "runtimeerror", "syntaxerror",
	}

	for _, indicator := range twigIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现Twig指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractFreemarkerEvidence 提取Freemarker证据
func (d *TemplateInjectionDetector) extractFreemarkerEvidence(response string) string {
	evidence := "Freemarker模板注入证据:\n"

	// 查找Freemarker特定内容
	freemarkerIndicators := []string{
		"49", "7777777", "aaaaaaa", "freemarker", "version", "class java.lang.string",
		"class java.lang.runtime", "class java.lang.processbuilder", "java.lang.class",
		"freemarker.template", "templateexception", "invalidreferenceexception",
		"templatemodelexception",
	}

	for _, indicator := range freemarkerIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现Freemarker指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractVelocityEvidence 提取Velocity证据
func (d *TemplateInjectionDetector) extractVelocityEvidence(response string) string {
	evidence := "Velocity模板注入证据:\n"

	// 查找Velocity特定内容
	velocityIndicators := []string{
		"49", "7777777", "aaaaaaa", "class java.lang.string", "class java.lang.runtime",
		"class java.lang.processbuilder", "java.lang.class", "user.name", "java.version",
		"os.name", "file.separator", "org.apache.velocity", "velocityexception",
		"parseexception", "methodinvocationexception",
	}

	for _, indicator := range velocityIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现Velocity指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
