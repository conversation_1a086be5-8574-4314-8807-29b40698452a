package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// FileUploadDetector 文件上传漏洞检测器
// 支持文件类型检查、上传路径扫描、恶意文件上传检测等多种文件上传安全检测
type FileUploadDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	uploadPaths         []string         // 上传路径
	dangerousExtensions []string         // 危险文件扩展名
	securityIssues      []string         // 安全问题模式
	uploadPatterns      []*regexp.Regexp // 上传页面模式
	fileTypePatterns    []*regexp.Regexp // 文件类型模式
	httpClient          *http.Client
}

// NewFileUploadDetector 创建文件上传检测器
func NewFileUploadDetector() *FileUploadDetector {
	detector := &FileUploadDetector{
		id:          "file-upload-comprehensive",
		name:        "文件上传漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 文件上传是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-434", "CWE-79", "CWE-94"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测文件上传漏洞，包括文件类型绕过、路径遍历、恶意文件上传等攻击",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 文件上传检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，可能需要处理上传页面
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeData()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *FileUploadDetector) GetID() string                     { return d.id }
func (d *FileUploadDetector) GetName() string                   { return d.name }
func (d *FileUploadDetector) GetCategory() string               { return d.category }
func (d *FileUploadDetector) GetSeverity() string               { return d.severity }
func (d *FileUploadDetector) GetCVE() []string                  { return d.cve }
func (d *FileUploadDetector) GetCWE() []string                  { return d.cwe }
func (d *FileUploadDetector) GetVersion() string                { return d.version }
func (d *FileUploadDetector) GetAuthor() string                 { return d.author }
func (d *FileUploadDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *FileUploadDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *FileUploadDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *FileUploadDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *FileUploadDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *FileUploadDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *FileUploadDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *FileUploadDetector) GetDependencies() []string         { return []string{} }
func (d *FileUploadDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *FileUploadDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *FileUploadDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *FileUploadDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *FileUploadDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *FileUploadDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *FileUploadDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.uploadPaths) == 0 {
		return fmt.Errorf("上传路径列表不能为空")
	}
	if len(d.dangerousExtensions) == 0 {
		return fmt.Errorf("危险扩展名列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *FileUploadDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 检查是否有文件上传相关的表单
	for _, form := range target.Forms {
		for fieldName, fieldType := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)
			fieldTypeLower := strings.ToLower(fieldType)

			// 检查是否有文件上传字段
			if fieldTypeLower == "file" ||
				strings.Contains(fieldNameLower, "file") ||
				strings.Contains(fieldNameLower, "upload") ||
				strings.Contains(fieldNameLower, "attachment") ||
				strings.Contains(fieldNameLower, "image") ||
				strings.Contains(fieldNameLower, "document") {
				return true
			}
		}
	}

	// 检查URL路径是否暗示文件上传功能
	urlLower := strings.ToLower(target.URL)
	uploadIndicators := []string{
		"/upload", "/uploads", "/file", "/files",
		"/attachment", "/attachments", "/media",
		"/images", "/documents", "/admin/upload",
		"/user/upload", "/fileupload", "/file_upload",
	}

	for _, indicator := range uploadIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	// 检查是否有上传相关的链接
	for _, link := range target.Links {
		linkLower := strings.ToLower(link.URL)
		for _, indicator := range uploadIndicators {
			if strings.Contains(linkLower, indicator) {
				return true
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *FileUploadDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种文件上传检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 上传页面发现
	pageEvidence, pageConfidence, pageRequest, pageResponse := d.detectUploadPages(ctx, target)
	if pageConfidence > maxConfidence {
		maxConfidence = pageConfidence
		vulnerableRequest = pageRequest
		vulnerableResponse = pageResponse
	}
	evidence = append(evidence, pageEvidence...)

	// 2. 文件类型限制检查
	typeEvidence, typeConfidence, typeRequest, typeResponse := d.detectFileTypeRestrictions(ctx, target)
	if typeConfidence > maxConfidence {
		maxConfidence = typeConfidence
		vulnerableRequest = typeRequest
		vulnerableResponse = typeResponse
	}
	evidence = append(evidence, typeEvidence...)

	// 3. 恶意文件上传测试
	maliciousEvidence, maliciousConfidence, maliciousRequest, maliciousResponse := d.detectMaliciousUpload(ctx, target)
	if maliciousConfidence > maxConfidence {
		maxConfidence = maliciousConfidence
		vulnerableRequest = maliciousRequest
		vulnerableResponse = maliciousResponse
	}
	evidence = append(evidence, maliciousEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "文件上传漏洞",
		Description:       "检测到文件上传漏洞，攻击者可能能够上传恶意文件来获取服务器控制权或执行任意代码",
		Evidence:          evidence,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对上传文件进行严格的类型检查、大小限制、文件名过滤，将上传文件存储在安全目录中，禁止执行上传的文件",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload", "https://cwe.mitre.org/data/definitions/434.html"},
		Tags:              []string{"file-upload", "web", "rce", "webshell"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *FileUploadDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 进行更保守的验证
	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 验证上传页面是否真的存在
	if len(target.Forms) > 0 {
		for _, form := range target.Forms {
			hasFileField := d.checkFormFileField(form)
			if hasFileField {
				verificationConfidence += 0.4
				evidence = append(evidence, plugins.Evidence{
					Type:        "form-analysis",
					Description: fmt.Sprintf("表单 %s 包含文件上传字段", form.Action),
					Content:     fmt.Sprintf("Method: %s, Fields: %v", form.Method, form.Fields),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "form-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用表单分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *FileUploadDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("file_upload_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *FileUploadDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (文件上传通常是高风险)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// checkFormFileField 检查表单是否有文件字段
func (d *FileUploadDetector) checkFormFileField(form plugins.FormInfo) bool {
	for fieldName, fieldType := range form.Fields {
		fieldNameLower := strings.ToLower(fieldName)
		fieldTypeLower := strings.ToLower(fieldType)

		// 检查是否有文件上传字段
		if fieldTypeLower == "file" ||
			strings.Contains(fieldNameLower, "file") ||
			strings.Contains(fieldNameLower, "upload") ||
			strings.Contains(fieldNameLower, "attachment") {
			return true
		}
	}
	return false
}

// initializeData 初始化检测数据
func (d *FileUploadDetector) initializeData() {
	// 常见的文件上传路径
	d.uploadPaths = []string{
		// 基础上传路径
		"/upload",
		"/uploads",
		"/file",
		"/files",
		"/attachment",
		"/attachments",
		"/media",
		"/images",
		"/documents",

		// 管理后台上传路径
		"/admin/upload",
		"/admin/uploads",
		"/admin/file",
		"/admin/files",
		"/admin/media",

		// 用户上传路径
		"/user/upload",
		"/user/uploads",
		"/user/file",
		"/user/files",
		"/profile/upload",

		// 特定功能上传路径
		"/fileupload",
		"/file_upload",
		"/upload_file",
		"/uploadfile",
		"/file-upload",
		"/upload-file",

		// 扩展名特定路径
		"/upload.php",
		"/upload.asp",
		"/upload.aspx",
		"/upload.jsp",
		"/upload.py",
		"/fileupload.php",
		"/file_upload.php",

		// 其他常见路径
		"/uploader",
		"/upfiles",
		"/uploadfiles",
		"/file_manager",
		"/filemanager",
		"/editor/upload",
		"/ckeditor/upload",
		"/fckeditor/upload",
		"/kindeditor/upload",
	}

	// 危险文件扩展名
	d.dangerousExtensions = []string{
		// 脚本文件
		"php", "php3", "php4", "php5", "phtml", "pht",
		"asp", "aspx", "asa", "asax", "ascx", "ashx", "asmx",
		"jsp", "jspx", "jsw", "jsv", "jspf",
		"py", "pyc", "pyo", "pyw",
		"pl", "pm", "cgi",
		"rb", "rhtml", "erb",
		"sh", "bash", "zsh", "csh", "ksh",
		"ps1", "psm1", "psd1",

		// 可执行文件
		"exe", "com", "bat", "cmd", "scr", "pif",
		"msi", "msp", "mst",
		"bin", "run", "app", "deb", "rpm",

		// 配置文件
		"htaccess", "htpasswd", "ini", "conf", "config",
		"xml", "yml", "yaml", "json",

		// 其他危险文件
		"jar", "war", "ear",
		"swf", "fla",
		"vbs", "vbe", "js", "jse",
		"hta", "htr", "idc", "shtm", "shtml",
		"cer", "cdx", "ade", "adp", "bas", "chm",
		"class", "inf", "ins", "isp", "job", "lnk",
		"mdb", "mde", "mdt", "mdw", "msc", "ops",
		"pcd", "prf", "reg", "scf", "sct", "shb",
		"shs", "url", "vb", "wsc", "wsf", "wsh",
	}

	// 安全问题模式
	d.securityIssues = []string{
		// 文件类型限制问题
		"accept=\"*/*\"",
		"accept='*/*'",
		"accept=*/*",
		"no file type restriction",
		"all file types allowed",
		"任意文件类型",
		"所有文件类型",
		"不限制文件类型",
		"无文件类型限制",
		"支持所有格式",
		"accept all files",
		"any file type",
		"unrestricted upload",
		"unlimited file types",

		// 大小限制问题
		"no size limit",
		"unlimited size",
		"无大小限制",
		"不限制大小",
		"任意大小",
		"unlimited file size",
		"no file size restriction",

		// 路径问题
		"../",
		"..\\",
		"directory traversal",
		"path traversal",
		"路径遍历",
		"目录遍历",
	}

	// 上传页面模式
	uploadPatterns := []string{
		`<input[^>]*type\s*=\s*["']?file["']?[^>]*>`,
		`<form[^>]*enctype\s*=\s*["']?multipart/form-data["']?[^>]*>`,
		`file\s*upload`,
		`upload\s*file`,
		`choose\s*file`,
		`select\s*file`,
		`browse\s*file`,
		`文件上传`,
		`选择文件`,
		`浏览文件`,
		`上传文件`,
		`附件上传`,
		`图片上传`,
		`文档上传`,
	}

	d.uploadPatterns = make([]*regexp.Regexp, 0, len(uploadPatterns))
	for _, pattern := range uploadPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.uploadPatterns = append(d.uploadPatterns, compiled)
		}
	}

	// 文件类型模式
	fileTypePatterns := []string{
		`accept\s*=\s*["'][^"']*["']`,
		`file\s*type`,
		`allowed\s*types?`,
		`supported\s*formats?`,
		`valid\s*extensions?`,
		`permitted\s*files?`,
		`文件类型`,
		`支持格式`,
		`允许类型`,
		`有效扩展名`,
	}

	d.fileTypePatterns = make([]*regexp.Regexp, 0, len(fileTypePatterns))
	for _, pattern := range fileTypePatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.fileTypePatterns = append(d.fileTypePatterns, compiled)
		}
	}
}
