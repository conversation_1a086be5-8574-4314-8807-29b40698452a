package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 查询最新的几个扫描任务的信息收集数据
	query := `
		SELECT id, name, type, status, 
		       CASE 
		           WHEN info_gathering_data IS NULL THEN 'NULL'
		           WHEN info_gathering_data = '' THEN 'EMPTY'
		           ELSE 'HAS_DATA'
		       END as data_status,
		       LENGTH(info_gathering_data) as data_length
		FROM scan_tasks 
		WHERE deleted_at IS NULL
		ORDER BY id DESC 
		LIMIT 5
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Fatal("查询失败:", err)
	}
	defer rows.Close()

	fmt.Println("=== 信息收集数据状态检查 ===")
	for rows.Next() {
		var id int
		var name, taskType, status, dataStatus string
		var dataLength sql.NullInt64

		err := rows.Scan(&id, &name, &taskType, &status, &dataStatus, &dataLength)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}

		length := int64(0)
		if dataLength.Valid {
			length = dataLength.Int64
		}

		fmt.Printf("任务ID: %d\n", id)
		fmt.Printf("  名称: %s\n", name)
		fmt.Printf("  类型: %s\n", taskType)
		fmt.Printf("  状态: %s\n", status)
		fmt.Printf("  数据状态: %s\n", dataStatus)
		fmt.Printf("  数据长度: %d\n", length)
		fmt.Println("---")
	}

	// 检查是否有任何任务包含信息收集数据
	var countWithData int
	err = db.QueryRow("SELECT COUNT(*) FROM scan_tasks WHERE info_gathering_data IS NOT NULL AND info_gathering_data != ''").Scan(&countWithData)
	if err != nil {
		log.Printf("统计失败: %v", err)
	} else {
		fmt.Printf("\n包含信息收集数据的任务总数: %d\n", countWithData)
	}
}
