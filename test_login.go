package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// 登录请求结构
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// 登录响应结构
type LoginResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Token string `json:"token"`
		User  struct {
			ID       int    `json:"id"`
			Username string `json:"username"`
		} `json:"user"`
	} `json:"data"`
}

func main() {
	fmt.Println("🔐 测试登录获取Token")
	fmt.Println("==================")

	// 尝试登录
	token := login("admin", "admin123")
	if token == "" {
		fmt.Println("❌ 登录失败")
		return
	}

	fmt.Printf("✅ 登录成功，Token: %s\n", token)
}

func login(username, password string) string {
	// 构造登录请求
	loginReq := LoginRequest{
		Username: username,
		Password: password,
	}

	// 发送请求
	jsonData, _ := json.Marshal(loginReq)
	resp, err := http.Post("http://localhost:8080/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ 发送请求失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	// 解析响应
	body, _ := io.ReadAll(resp.Body)
	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		fmt.Printf("响应内容: %s\n", string(body))
		return ""
	}

	if loginResp.Code != 200 {
		fmt.Printf("❌ 登录失败: %s\n", loginResp.Message)
		return ""
	}

	return loginResp.Data.Token
}
