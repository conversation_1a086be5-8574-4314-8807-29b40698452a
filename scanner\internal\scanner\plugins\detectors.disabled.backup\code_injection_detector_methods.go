package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectPHPCodeInjection 检测PHP代码注入
func (d *CodeInjectionDetector) detectPHPCodeInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试PHP代码注入载荷
	for _, payload := range d.phpPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送PHP代码注入请求
		resp, err := d.sendCodeRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查PHP代码注入响应
		confidence := d.checkPHPCodeInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("PHP代码注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "php-code-injection",
				Description: fmt.Sprintf("发现PHP代码注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractCodeEvidence(resp, "php-code-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPythonCodeInjection 检测Python代码注入
func (d *CodeInjectionDetector) detectPythonCodeInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Python代码注入载荷
	for _, payload := range d.pythonPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Python代码注入请求
		resp, err := d.sendCodeRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Python代码注入响应
		confidence := d.checkPythonCodeInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Python代码注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "python-code-injection",
				Description: fmt.Sprintf("发现Python代码注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractCodeEvidence(resp, "python-code-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJavaScriptCodeInjection 检测JavaScript代码注入
func (d *CodeInjectionDetector) detectJavaScriptCodeInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试JavaScript代码注入载荷
	for _, payload := range d.javascriptPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送JavaScript代码注入请求
		resp, err := d.sendCodeRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查JavaScript代码注入响应
		confidence := d.checkJavaScriptCodeInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("JavaScript代码注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "javascript-code-injection",
				Description: fmt.Sprintf("发现JavaScript代码注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractCodeEvidence(resp, "javascript-code-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJavaCodeInjection 检测Java代码注入
func (d *CodeInjectionDetector) detectJavaCodeInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Java代码注入载荷
	for _, payload := range d.javaPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Java代码注入请求
		resp, err := d.sendCodeRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Java代码注入响应
		confidence := d.checkJavaCodeInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Java代码注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "java-code-injection",
				Description: fmt.Sprintf("发现Java代码注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractCodeEvidence(resp, "java-code-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendCodeRequest 发送代码注入请求
func (d *CodeInjectionDetector) sendCodeRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendCodeGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendCodePOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试代码载荷注入
	codeResp, err := d.sendCodeBodyRequest(ctx, targetURL, payload)
	if err == nil && codeResp != "" {
		return codeResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendCodeGETRequest 发送代码注入GET请求
func (d *CodeInjectionDetector) sendCodeGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendCodePOSTRequest 发送代码注入POST请求
func (d *CodeInjectionDetector) sendCodePOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendCodeBodyRequest 发送代码载荷请求
func (d *CodeInjectionDetector) sendCodeBodyRequest(ctx context.Context, targetURL, payload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(payload))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "text/plain")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkPHPCodeInjectionResponse 检查PHP代码注入响应
func (d *CodeInjectionDetector) checkPHPCodeInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查PHP模式匹配
	for _, pattern := range d.phpPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查PHP特定错误
	phpErrors := []string{
		"php error", "fatal error", "parse error", "syntax error",
		"call to undefined function", "class not found", "include failed",
		"unexpected token", "php错误", "语法错误", "函数未定义", "类未找到",
	}

	for _, error := range phpErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // PHP特定错误的强指示器
			break
		}
	}

	// 检查PHP执行结果
	phpResults := []string{
		"uid=", "gid=", "root:", "daemon:", "www-data", "apache", "nginx",
		"phpinfo()", "php version", "$_server", "$_env", "$globals",
	}

	for _, result := range phpResults {
		if strings.Contains(response, result) {
			confidence += 0.8 // PHP执行结果的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkPythonCodeInjectionResponse 检查Python代码注入响应
func (d *CodeInjectionDetector) checkPythonCodeInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查Python模式匹配
	for _, pattern := range d.pythonPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Python特定错误
	pythonErrors := []string{
		"python error", "traceback", "syntaxerror", "nameerror",
		"importerror", "modulenotfounderror", "python错误", "语法错误",
		"名称错误", "导入错误",
	}

	for _, error := range pythonErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // Python特定错误的强指示器
			break
		}
	}

	// 检查Python执行结果
	pythonResults := []string{
		"uid=", "gid=", "root:", "daemon:", "__import__", "__builtins__",
		"subprocess", "os.system", "exec(", "eval(", "python",
	}

	for _, result := range pythonResults {
		if strings.Contains(response, result) {
			confidence += 0.8 // Python执行结果的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkJavaScriptCodeInjectionResponse 检查JavaScript代码注入响应
func (d *CodeInjectionDetector) checkJavaScriptCodeInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查JavaScript模式匹配
	for _, pattern := range d.javascriptPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查JavaScript特定错误
	jsErrors := []string{
		"javascript error", "referenceerror", "syntaxerror", "typeerror",
		"rangeerror", "evalerror", "require is not defined", "module not found",
		"javascript错误", "语法错误", "类型错误", "引用错误",
	}

	for _, error := range jsErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // JavaScript特定错误的强指示器
			break
		}
	}

	// 检查JavaScript执行结果
	jsResults := []string{
		"uid=", "gid=", "root:", "daemon:", "node.js", "child_process",
		"process.mainmodule", "require(", "eval(", "function(",
	}

	for _, result := range jsResults {
		if strings.Contains(response, result) {
			confidence += 0.8 // JavaScript执行结果的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkJavaCodeInjectionResponse 检查Java代码注入响应
func (d *CodeInjectionDetector) checkJavaCodeInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查Java模式匹配
	for _, pattern := range d.javaPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Java特定错误
	javaErrors := []string{
		"java exception", "java error", "classnotfoundexception", "nosuchmethodexception",
		"illegalargumentexception", "nullpointerexception", "java异常", "java错误",
		"类未找到", "方法未找到",
	}

	for _, error := range javaErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // Java特定错误的强指示器
			break
		}
	}

	// 检查Java执行结果
	javaResults := []string{
		"uid=", "gid=", "root:", "daemon:", "java.lang.runtime", "processbuilder",
		"class.forname", "reflection", "scriptengine", "java version",
	}

	for _, result := range javaResults {
		if strings.Contains(response, result) {
			confidence += 0.8 // Java执行结果的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractCodeEvidence 提取代码注入证据
func (d *CodeInjectionDetector) extractCodeEvidence(response, injectionType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据注入类型提取相关证据
	switch injectionType {
	case "php-code-injection":
		return d.extractPHPEvidence(response)
	case "python-code-injection":
		return d.extractPythonEvidence(response)
	case "javascript-code-injection":
		return d.extractJavaScriptEvidence(response)
	case "java-code-injection":
		return d.extractJavaEvidence(response)
	default:
		return response
	}
}

// extractPHPEvidence 提取PHP证据
func (d *CodeInjectionDetector) extractPHPEvidence(response string) string {
	evidence := "PHP代码注入证据:\n"

	// 查找PHP特定内容
	phpIndicators := []string{
		"php error", "fatal error", "parse error", "syntax error",
		"call to undefined function", "class not found", "phpinfo()",
		"php version", "$_server", "$_env", "$globals", "uid=", "gid=",
	}

	for _, indicator := range phpIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现PHP指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractPythonEvidence 提取Python证据
func (d *CodeInjectionDetector) extractPythonEvidence(response string) string {
	evidence := "Python代码注入证据:\n"

	// 查找Python特定内容
	pythonIndicators := []string{
		"python error", "traceback", "syntaxerror", "nameerror",
		"importerror", "__import__", "__builtins__", "subprocess",
		"os.system", "exec(", "eval(", "uid=", "gid=",
	}

	for _, indicator := range pythonIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现Python指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractJavaScriptEvidence 提取JavaScript证据
func (d *CodeInjectionDetector) extractJavaScriptEvidence(response string) string {
	evidence := "JavaScript代码注入证据:\n"

	// 查找JavaScript特定内容
	jsIndicators := []string{
		"javascript error", "referenceerror", "syntaxerror", "typeerror",
		"require is not defined", "child_process", "process.mainmodule",
		"require(", "eval(", "function(", "uid=", "gid=",
	}

	for _, indicator := range jsIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现JavaScript指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractJavaEvidence 提取Java证据
func (d *CodeInjectionDetector) extractJavaEvidence(response string) string {
	evidence := "Java代码注入证据:\n"

	// 查找Java特定内容
	javaIndicators := []string{
		"java exception", "java error", "classnotfoundexception",
		"java.lang.runtime", "processbuilder", "class.forname",
		"reflection", "scriptengine", "uid=", "gid=",
	}

	for _, indicator := range javaIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现Java指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
