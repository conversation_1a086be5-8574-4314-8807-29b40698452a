package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestPasswordPolicyDetectorBasicFunctionality 测试密码策略检测器基本功能
func TestPasswordPolicyDetectorBasicFunctionality(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	// 测试基本信息
	if detector.GetID() != "password-policy-comprehensive" {
		t.<PERSON>("Expected ID 'password-policy-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "密码策略安全检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '密码策略安全检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "medium" {
		t.<PERSON><PERSON><PERSON>("Expected severity 'medium', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.<PERSON>r("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestPasswordPolicyDetectorApplicability 测试密码策略检测器适用性
func TestPasswordPolicyDetectorApplicability(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有密码字段的表单目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/login",
						Method: "POST",
						Fields: map[string]string{"username": "text", "password": "password"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有密码策略技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring Security", Version: "5.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有认证头部的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Headers: map[string]string{
					"X-Auth-Type": "password",
				},
			},
			expected: true,
		},
		{
			name: "有密码策略链接的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/password/policy", Text: "Password Policy"},
				},
			},
			expected: true,
		},
		{
			name: "有认证Cookie的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Cookies: []plugins.CookieInfo{
					{Name: "auth_token", Value: "password_auth"},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestPasswordPolicyDetectorConfiguration 测试密码策略检测器配置
func TestPasswordPolicyDetectorConfiguration(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 25*time.Second {
		t.Errorf("Expected timeout 25s, got %v", config.Timeout)
	}

	if config.MaxRetries != 3 {
		t.Errorf("Expected max retries 3, got %d", config.MaxRetries)
	}

	if config.Concurrency != 4 {
		t.Errorf("Expected concurrency 4, got %d", config.Concurrency)
	}

	if config.RateLimit != 4 {
		t.Errorf("Expected rate limit 4, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 3*1024*1024 {
		t.Errorf("Expected max response size 3MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      5,
		Concurrency:     6,
		RateLimit:       6,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 30*time.Second {
		t.Errorf("Expected updated timeout 30s, got %v", updatedConfig.Timeout)
	}
}

// TestPasswordPolicyDetectorPasswordPolicyPaths 测试密码策略路径
func TestPasswordPolicyDetectorPasswordPolicyPaths(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	if len(detector.passwordPolicyPaths) == 0 {
		t.Error("Expected some password policy paths")
	}

	// 检查是否包含关键的密码策略路径
	expectedPaths := []string{
		"/password/policy",
		"/password-policy",
		"/security/password",
		"/auth/password",
		"/password/change",
		"/password/reset",
		"/forgot-password",
	}

	for _, expected := range expectedPaths {
		found := false
		for _, path := range detector.passwordPolicyPaths {
			if path == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find password policy path '%s'", expected)
		}
	}
}

// TestPasswordPolicyDetectorPasswordTestPayloads 测试密码测试载荷
func TestPasswordPolicyDetectorPasswordTestPayloads(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	if len(detector.passwordTestPayloads) == 0 {
		t.Error("Expected some password test payloads")
	}

	// 检查是否包含关键的密码测试载荷
	expectedPayloads := []string{
		"",
		"password",
		"Password",
		"password123",
		"admin",
		"admin123",
		"123456",
		"qwerty",
		"密码",
		"密码123",
	}

	for _, expected := range expectedPayloads {
		found := false
		for _, payload := range detector.passwordTestPayloads {
			if payload == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find password test payload '%s'", expected)
		}
	}
}

// TestPasswordPolicyDetectorWeakPasswordPatterns 测试弱密码模式
func TestPasswordPolicyDetectorWeakPasswordPatterns(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	if len(detector.weakPasswordPatterns) == 0 {
		t.Error("Expected some weak password patterns")
	}

	// 检查是否包含关键的弱密码模式
	expectedPatterns := []string{
		"^.{1,3}$",   // 1-3字符
		"^\\d+$",     // 纯数字
		"^[a-z]+$",   // 纯小写字母
		"^[A-Z]+$",   // 纯大写字母
		"^(.)\\1+$",  // 重复字符
	}

	for _, expected := range expectedPatterns {
		found := false
		for _, pattern := range detector.weakPasswordPatterns {
			if pattern == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find weak password pattern '%s'", expected)
		}
	}
}

// TestPasswordPolicyDetectorPolicyViolationTests 测试策略违规测试
func TestPasswordPolicyDetectorPolicyViolationTests(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	if len(detector.policyViolationTests) == 0 {
		t.Error("Expected some policy violation tests")
	}

	// 检查是否包含不同类别的测试
	var hasLength, hasComplexity, hasHistory, hasExpiry bool
	for _, test := range detector.policyViolationTests {
		switch test.Category {
		case "length":
			hasLength = true
		case "complexity":
			hasComplexity = true
		case "history":
			hasHistory = true
		case "expiry":
			hasExpiry = true
		}
	}

	if !hasLength {
		t.Error("Expected to find length policy tests")
	}
	if !hasComplexity {
		t.Error("Expected to find complexity policy tests")
	}
	if !hasHistory {
		t.Error("Expected to find history policy tests")
	}
	if !hasExpiry {
		t.Error("Expected to find expiry policy tests")
	}
}

// TestPasswordPolicyDetectorPasswordResetPaths 测试密码重置路径
func TestPasswordPolicyDetectorPasswordResetPaths(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	if len(detector.passwordResetPaths) == 0 {
		t.Error("Expected some password reset paths")
	}

	// 检查是否包含关键的密码重置路径
	expectedPaths := []string{
		"/password/reset",
		"/forgot-password",
		"/password/recover",
		"/reset-password",
		"/forgot",
		"/recover",
		"/密码重置",
		"/忘记密码",
	}

	for _, expected := range expectedPaths {
		found := false
		for _, path := range detector.passwordResetPaths {
			if path == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find password reset path '%s'", expected)
		}
	}
}

// TestPasswordPolicyDetectorPasswordChangePaths 测试密码修改路径
func TestPasswordPolicyDetectorPasswordChangePaths(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	if len(detector.passwordChangePaths) == 0 {
		t.Error("Expected some password change paths")
	}

	// 检查是否包含关键的密码修改路径
	expectedPaths := []string{
		"/password/change",
		"/change-password",
		"/password/update",
		"/update-password",
		"/password/edit",
		"/edit-password",
		"/密码修改",
		"/修改密码",
	}

	for _, expected := range expectedPaths {
		found := false
		for _, path := range detector.passwordChangePaths {
			if path == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find password change path '%s'", expected)
		}
	}
}

// TestPasswordPolicyDetectorPatterns 测试检测模式
func TestPasswordPolicyDetectorPatterns(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	// 测试密码策略模式
	if len(detector.policyPatterns) == 0 {
		t.Error("Expected some policy patterns")
	}

	// 测试弱点模式
	if len(detector.weaknessPatterns) == 0 {
		t.Error("Expected some weakness patterns")
	}

	// 测试安全模式
	if len(detector.securityPatterns) == 0 {
		t.Error("Expected some security patterns")
	}
}

// TestPasswordPolicyDetectorPasswordPolicyFeatures 测试密码策略功能检查
func TestPasswordPolicyDetectorPasswordPolicyFeatures(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有密码头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"X-Password-Policy": "enabled",
				},
			},
			expected: true,
		},
		{
			name: "有安全技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Spring Security", Version: "5.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有密码策略链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/password/policy", Text: "Password Policy"},
				},
			},
			expected: true,
		},
		{
			name: "有认证Cookie的目标",
			target: &plugins.ScanTarget{
				Cookies: []plugins.CookieInfo{
					{Name: "auth_session", Value: "password_auth"},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasPasswordPolicyFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestPasswordPolicyDetectorRiskScore 测试风险评分
func TestPasswordPolicyDetectorRiskScore(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 3.0},
		{0.8, 4.8},
		{1.0, 6.0},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.2f for confidence %.1f, got %.2f", tc.expected, tc.confidence, score)
		}
	}
}

// TestPasswordPolicyDetectorLifecycle 测试检测器生命周期
func TestPasswordPolicyDetectorLifecycle(t *testing.T) {
	detector := NewPasswordPolicyDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
