package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestEncryptionDetectorBasicFunctionality 测试加密检测器基础功能
func TestEncryptionDetectorBasicFunctionality(t *testing.T) {
	detector := NewEncryptionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "encryption-comprehensive", detector.GetID())
	assert.Equal(t, "加密漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-326")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestEncryptionDetectorApplicability 测试加密检测器适用性
func TestEncryptionDetectorApplicability(t *testing.T) {
	detector := NewEncryptionDetector()

	// 测试HTTPS目标
	httpsTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "https://example.com",
		Protocol: "https",
		Port:     443,
	}
	assert.True(t, detector.IsApplicable(httpsTarget))

	// 测试有加密头的目标
	encryptionHeaderTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Authorization": "Bearer jwt_token",
		},
	}
	assert.True(t, detector.IsApplicable(encryptionHeaderTarget))

	// 测试有加密Cookie的目标
	encryptionCookieTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Cookies: []plugins.CookieInfo{
			{
				Name:  "jwt_token",
				Value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
			},
		},
	}
	assert.True(t, detector.IsApplicable(encryptionCookieTarget))

	// 测试加密相关URL
	cryptoTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/crypto/hash",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(cryptoTarget))

	// 测试SSL相关URL
	sslTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/ssl/certificate",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(sslTarget))

	// 测试HTTP目标
	httpTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(httpTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestEncryptionDetectorConfiguration 测试加密检测器配置
func TestEncryptionDetectorConfiguration(t *testing.T) {
	detector := NewEncryptionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestEncryptionDetectorWeakTLSVersions 测试加密检测器弱TLS版本
func TestEncryptionDetectorWeakTLSVersions(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查弱TLS版本列表
	assert.NotEmpty(t, detector.weakTLSVersions)
	assert.Greater(t, len(detector.weakTLSVersions), 15)

	// 检查SSL版本
	assert.Contains(t, detector.weakTLSVersions, "SSL 2.0")
	assert.Contains(t, detector.weakTLSVersions, "SSL 3.0")
	assert.Contains(t, detector.weakTLSVersions, "SSLv2")
	assert.Contains(t, detector.weakTLSVersions, "SSLv3")

	// 检查弱TLS版本
	assert.Contains(t, detector.weakTLSVersions, "TLS 1.0")
	assert.Contains(t, detector.weakTLSVersions, "TLS 1.1")
	assert.Contains(t, detector.weakTLSVersions, "TLSv1.0")
	assert.Contains(t, detector.weakTLSVersions, "TLSv1.1")

	// 检查协议版本号
	assert.Contains(t, detector.weakTLSVersions, "0x0200")
	assert.Contains(t, detector.weakTLSVersions, "0x0300")
	assert.Contains(t, detector.weakTLSVersions, "0x0301")
	assert.Contains(t, detector.weakTLSVersions, "0x0302")
}

// TestEncryptionDetectorWeakCipherSuites 测试加密检测器弱加密套件
func TestEncryptionDetectorWeakCipherSuites(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查弱加密套件列表
	assert.NotEmpty(t, detector.weakCipherSuites)
	assert.Greater(t, len(detector.weakCipherSuites), 25)

	// 检查NULL加密
	assert.Contains(t, detector.weakCipherSuites, "TLS_NULL_WITH_NULL_NULL")
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_WITH_NULL_MD5")
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_WITH_NULL_SHA")

	// 检查匿名密钥交换
	assert.Contains(t, detector.weakCipherSuites, "TLS_DH_anon_WITH_RC4_128_MD5")
	assert.Contains(t, detector.weakCipherSuites, "TLS_DH_anon_WITH_3DES_EDE_CBC_SHA")

	// 检查RC4加密
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_WITH_RC4_128_MD5")
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_WITH_RC4_128_SHA")

	// 检查DES和3DES加密
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_WITH_DES_CBC_SHA")
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_WITH_3DES_EDE_CBC_SHA")

	// 检查导出级加密
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_EXPORT_WITH_RC4_40_MD5")
	assert.Contains(t, detector.weakCipherSuites, "TLS_RSA_EXPORT_WITH_DES40_CBC_SHA")
}

// TestEncryptionDetectorWeakHashAlgorithms 测试加密检测器弱哈希算法
func TestEncryptionDetectorWeakHashAlgorithms(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查弱哈希算法列表
	assert.NotEmpty(t, detector.weakHashAlgorithms)
	assert.Greater(t, len(detector.weakHashAlgorithms), 15)

	// 检查弱哈希算法
	assert.Contains(t, detector.weakHashAlgorithms, "MD2")
	assert.Contains(t, detector.weakHashAlgorithms, "MD4")
	assert.Contains(t, detector.weakHashAlgorithms, "MD5")
	assert.Contains(t, detector.weakHashAlgorithms, "SHA-0")
	assert.Contains(t, detector.weakHashAlgorithms, "SHA1")
	assert.Contains(t, detector.weakHashAlgorithms, "SHA-1")

	// 检查算法标识
	assert.Contains(t, detector.weakHashAlgorithms, "md5")
	assert.Contains(t, detector.weakHashAlgorithms, "sha1")

	// 检查算法OID
	assert.Contains(t, detector.weakHashAlgorithms, "1.2.840.113549.2.5") // MD5
	assert.Contains(t, detector.weakHashAlgorithms, "1.3.14.3.2.26")      // SHA-1
}

// TestEncryptionDetectorWeakKeyExchanges 测试加密检测器弱密钥交换
func TestEncryptionDetectorWeakKeyExchanges(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查弱密钥交换列表
	assert.NotEmpty(t, detector.weakKeyExchanges)
	assert.Greater(t, len(detector.weakKeyExchanges), 20)

	// 检查弱密钥交换
	assert.Contains(t, detector.weakKeyExchanges, "RSA_512")
	assert.Contains(t, detector.weakKeyExchanges, "RSA_1024")
	assert.Contains(t, detector.weakKeyExchanges, "DH_512")
	assert.Contains(t, detector.weakKeyExchanges, "DH_1024")

	// 检查密钥长度
	assert.Contains(t, detector.weakKeyExchanges, "512-bit")
	assert.Contains(t, detector.weakKeyExchanges, "1024-bit")

	// 检查弱曲线
	assert.Contains(t, detector.weakKeyExchanges, "secp160r1")
	assert.Contains(t, detector.weakKeyExchanges, "secp192r1")
	assert.Contains(t, detector.weakKeyExchanges, "prime192v1")
}

// TestEncryptionDetectorJWTWeakSecrets 测试加密检测器JWT弱密钥
func TestEncryptionDetectorJWTWeakSecrets(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查JWT弱密钥列表
	assert.NotEmpty(t, detector.jwtWeakSecrets)
	assert.Greater(t, len(detector.jwtWeakSecrets), 30)

	// 检查常见弱密钥
	assert.Contains(t, detector.jwtWeakSecrets, "secret")
	assert.Contains(t, detector.jwtWeakSecrets, "password")
	assert.Contains(t, detector.jwtWeakSecrets, "123456")
	assert.Contains(t, detector.jwtWeakSecrets, "admin")
	assert.Contains(t, detector.jwtWeakSecrets, "test")

	// 检查简单密钥
	assert.Contains(t, detector.jwtWeakSecrets, "a")
	assert.Contains(t, detector.jwtWeakSecrets, "abc")
	assert.Contains(t, detector.jwtWeakSecrets, "123")

	// 检查空密钥
	assert.Contains(t, detector.jwtWeakSecrets, "")
	assert.Contains(t, detector.jwtWeakSecrets, " ")
	assert.Contains(t, detector.jwtWeakSecrets, "null")

	// 检查常见应用密钥
	assert.Contains(t, detector.jwtWeakSecrets, "laravel_session")
	assert.Contains(t, detector.jwtWeakSecrets, "django_secret")
}

// TestEncryptionDetectorJWTAlgorithms 测试加密检测器JWT算法
func TestEncryptionDetectorJWTAlgorithms(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查JWT算法列表
	assert.NotEmpty(t, detector.jwtAlgorithms)
	assert.Greater(t, len(detector.jwtAlgorithms), 20)

	// 检查对称算法
	assert.Contains(t, detector.jwtAlgorithms, "HS256")
	assert.Contains(t, detector.jwtAlgorithms, "HS384")
	assert.Contains(t, detector.jwtAlgorithms, "HS512")

	// 检查非对称算法
	assert.Contains(t, detector.jwtAlgorithms, "RS256")
	assert.Contains(t, detector.jwtAlgorithms, "ES256")
	assert.Contains(t, detector.jwtAlgorithms, "PS256")

	// 检查危险算法
	assert.Contains(t, detector.jwtAlgorithms, "none")
	assert.Contains(t, detector.jwtAlgorithms, "NONE")
	assert.Contains(t, detector.jwtAlgorithms, "None")
	assert.Contains(t, detector.jwtAlgorithms, "null")

	// 检查弱算法
	assert.Contains(t, detector.jwtAlgorithms, "HS1")
	assert.Contains(t, detector.jwtAlgorithms, "MD5")
	assert.Contains(t, detector.jwtAlgorithms, "SHA1")
}

// TestEncryptionDetectorPatterns 测试加密检测器模式
func TestEncryptionDetectorPatterns(t *testing.T) {
	detector := NewEncryptionDetector()

	// 检查加密模式
	assert.NotEmpty(t, detector.encryptionPatterns)
	assert.GreaterOrEqual(t, len(detector.encryptionPatterns), 20)

	// 检查证书模式
	assert.NotEmpty(t, detector.certificatePatterns)
	assert.GreaterOrEqual(t, len(detector.certificatePatterns), 10)

	// 检查密钥模式
	assert.NotEmpty(t, detector.keyPatterns)
	assert.GreaterOrEqual(t, len(detector.keyPatterns), 13)
}

// TestEncryptionDetectorEncryptionFeatures 测试加密功能检查
func TestEncryptionDetectorEncryptionFeatures(t *testing.T) {
	detector := NewEncryptionDetector()

	// 测试有加密头的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Authorization": "Bearer jwt_token",
		},
	}
	assert.True(t, detector.hasEncryptionFeatures(headerTarget))

	// 测试有加密Cookie的目标
	cookieTarget := &plugins.ScanTarget{
		Cookies: []plugins.CookieInfo{
			{
				Name:  "jwt_token",
				Value: "abc123",
			},
		},
	}
	assert.True(t, detector.hasEncryptionFeatures(cookieTarget))

	// 测试加密URL的目标
	urlTarget := &plugins.ScanTarget{
		URL: "https://example.com/ssl/certificate",
	}
	assert.True(t, detector.hasEncryptionFeatures(urlTarget))

	// 测试无加密功能的目标
	simpleTarget := &plugins.ScanTarget{
		URL:     "http://example.com/about",
		Headers: map[string]string{},
		Cookies: []plugins.CookieInfo{},
	}
	assert.False(t, detector.hasEncryptionFeatures(simpleTarget))
}

// TestEncryptionDetectorRiskScore 测试风险评分计算
func TestEncryptionDetectorRiskScore(t *testing.T) {
	detector := NewEncryptionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestEncryptionDetectorLifecycle 测试检测器生命周期
func TestEncryptionDetectorLifecycle(t *testing.T) {
	detector := NewEncryptionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
