package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// FileOperationDetector 文件操作检测器
// 支持文件上传漏洞、文件下载漏洞、文件包含漏洞、文件遍历漏洞等多种文件操作安全检测
type FileOperationDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	fileUploadPaths       []string         // 文件上传路径
	fileDownloadPaths     []string         // 文件下载路径
	fileIncludePaths      []string         // 文件包含路径
	fileTraversalPaths    []string         // 文件遍历路径
	fileUploadPayloads    []string         // 文件上传载荷
	fileDownloadPayloads  []string         // 文件下载载荷
	fileIncludePayloads   []string         // 文件包含载荷
	fileTraversalPayloads []string         // 文件遍历载荷
	fileExtensions        []string         // 文件扩展名
	dangerousFiles        []string         // 危险文件
	fileOperationPatterns []*regexp.Regexp // 文件操作模式
	vulnerabilityPatterns []*regexp.Regexp // 漏洞模式
	securityPatterns      []*regexp.Regexp // 安全模式
	httpClient            *http.Client
}

// NewFileOperationDetector 创建文件操作检测器
func NewFileOperationDetector() *FileOperationDetector {
	detector := &FileOperationDetector{
		id:          "file-operation-comprehensive",
		name:        "文件操作安全检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-19781", "CVE-2018-13379"},
		cwe:         []string{"CWE-22", "CWE-434", "CWE-98", "CWE-200", "CWE-73", "CWE-94"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测文件操作安全问题，包括文件上传、文件下载、文件包含、文件遍历等多种文件操作安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second, // 文件操作检测需要较长时间
		MaxRetries:      2,                // 文件操作检测可以重试
		Concurrency:     5,                // 中等并发数
		RateLimit:       5,                // 中等速率限制
		FollowRedirects: true,             // 跟随重定向检查文件操作
		VerifySSL:       false,
		MaxResponseSize: 5 * 1024 * 1024, // 5MB，文件操作响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeFileUploadPaths()
	detector.initializeFileDownloadPaths()
	detector.initializeFileIncludePaths()
	detector.initializeFileTraversalPaths()
	detector.initializeFileUploadPayloads()
	detector.initializeFileDownloadPayloads()
	detector.initializeFileIncludePayloads()
	detector.initializeFileTraversalPayloads()
	detector.initializeFileExtensions()
	detector.initializeDangerousFiles()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *FileOperationDetector) GetID() string            { return d.id }
func (d *FileOperationDetector) GetName() string          { return d.name }
func (d *FileOperationDetector) GetCategory() string      { return d.category }
func (d *FileOperationDetector) GetSeverity() string      { return d.severity }
func (d *FileOperationDetector) GetCVE() []string         { return d.cve }
func (d *FileOperationDetector) GetCWE() []string         { return d.cwe }
func (d *FileOperationDetector) GetVersion() string       { return d.version }
func (d *FileOperationDetector) GetAuthor() string        { return d.author }
func (d *FileOperationDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *FileOperationDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *FileOperationDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *FileOperationDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *FileOperationDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *FileOperationDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *FileOperationDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *FileOperationDetector) GetDependencies() []string         { return []string{} }
func (d *FileOperationDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *FileOperationDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *FileOperationDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *FileOperationDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *FileOperationDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 30 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 5
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *FileOperationDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *FileOperationDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.fileUploadPaths) == 0 {
		return fmt.Errorf("文件上传路径不能为空")
	}
	if len(d.fileUploadPayloads) == 0 {
		return fmt.Errorf("文件上传载荷不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *FileOperationDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 文件操作检测适用于有文件功能的Web应用
	// 检查是否有文件操作相关的特征
	if d.hasFileOperationFeatures(target) {
		return true
	}

	// 对于有表单或文件上传的Web应用，也适用
	if len(target.Forms) > 0 {
		for _, form := range target.Forms {
			for fieldName, fieldType := range form.Fields {
				fieldNameLower := strings.ToLower(fieldName)
				fieldTypeLower := strings.ToLower(fieldType)
				if strings.Contains(fieldNameLower, "file") ||
					strings.Contains(fieldNameLower, "upload") ||
					strings.Contains(fieldNameLower, "attachment") ||
					strings.Contains(fieldTypeLower, "file") ||
					strings.Contains(fieldNameLower, "文件") ||
					strings.Contains(fieldNameLower, "上传") ||
					strings.Contains(fieldNameLower, "附件") {
					return true
				}
			}
		}
	}

	// 对于文件操作相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	fileKeywords := []string{
		"file", "upload", "download", "attachment", "document", "image", "photo",
		"video", "audio", "media", "resource", "asset", "binary", "data",
		"include", "require", "import", "load", "read", "write", "save",
		"文件", "上传", "下载", "附件", "文档", "图片", "照片", "视频",
		"音频", "媒体", "资源", "资产", "二进制", "数据", "包含", "导入",
	}

	for _, keyword := range fileKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 文件操作是通用Web安全问题，默认适用于所有Web目标
}

// hasFileOperationFeatures 检查是否有文件操作功能
func (d *FileOperationDetector) hasFileOperationFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有文件操作相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "file") ||
			strings.Contains(keyLower, "upload") ||
			strings.Contains(keyLower, "download") ||
			strings.Contains(keyLower, "attachment") ||
			strings.Contains(valueLower, "file") ||
			strings.Contains(valueLower, "upload") ||
			strings.Contains(valueLower, "download") ||
			strings.Contains(valueLower, "attachment") ||
			strings.Contains(valueLower, "multipart") ||
			strings.Contains(valueLower, "form-data") {
			return true
		}
	}

	// 检查技术栈中是否有文件操作相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		fileTechnologies := []string{
			"apache", "nginx", "iis", "tomcat", "jetty", "undertow", "netty",
			"spring", "struts", "django", "flask", "express", "koa", "fastapi",
			"multer", "busboy", "formidable", "multiparty", "fileupload",
			"commons-fileupload", "apache-fileupload", "spring-web",
			"文件", "上传", "下载", "附件", "存储", "媒体", "资源",
		}

		for _, fileTech := range fileTechnologies {
			if strings.Contains(techNameLower, fileTech) {
				return true
			}
		}
	}

	// 检查链接中是否有文件操作相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "file") ||
			strings.Contains(linkURLLower, "upload") ||
			strings.Contains(linkURLLower, "download") ||
			strings.Contains(linkURLLower, "attachment") ||
			strings.Contains(linkTextLower, "file") ||
			strings.Contains(linkTextLower, "upload") ||
			strings.Contains(linkTextLower, "download") ||
			strings.Contains(linkTextLower, "attachment") ||
			strings.Contains(linkTextLower, "document") ||
			strings.Contains(linkTextLower, "文件") ||
			strings.Contains(linkTextLower, "上传") ||
			strings.Contains(linkTextLower, "下载") ||
			strings.Contains(linkTextLower, "附件") ||
			strings.Contains(linkTextLower, "文档") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *FileOperationDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种文件操作检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 文件上传漏洞检测
	uploadEvidence, uploadConfidence, uploadPayload, uploadRequest, uploadResponse := d.detectFileUploadVulnerabilities(ctx, target)
	if uploadConfidence > maxConfidence {
		maxConfidence = uploadConfidence
		vulnerablePayload = uploadPayload
		vulnerableRequest = uploadRequest
		vulnerableResponse = uploadResponse
	}
	evidence = append(evidence, uploadEvidence...)

	// 2. 文件下载漏洞检测
	downloadEvidence, downloadConfidence, downloadPayload, downloadRequest, downloadResponse := d.detectFileDownloadVulnerabilities(ctx, target)
	if downloadConfidence > maxConfidence {
		maxConfidence = downloadConfidence
		vulnerablePayload = downloadPayload
		vulnerableRequest = downloadRequest
		vulnerableResponse = downloadResponse
	}
	evidence = append(evidence, downloadEvidence...)

	// 3. 文件包含漏洞检测
	includeEvidence, includeConfidence, includePayload, includeRequest, includeResponse := d.detectFileIncludeVulnerabilities(ctx, target)
	if includeConfidence > maxConfidence {
		maxConfidence = includeConfidence
		vulnerablePayload = includePayload
		vulnerableRequest = includeRequest
		vulnerableResponse = includeResponse
	}
	evidence = append(evidence, includeEvidence...)

	// 4. 文件遍历漏洞检测
	traversalEvidence, traversalConfidence, traversalPayload, traversalRequest, traversalResponse := d.detectFileTraversalVulnerabilities(ctx, target)
	if traversalConfidence > maxConfidence {
		maxConfidence = traversalConfidence
		vulnerablePayload = traversalPayload
		vulnerableRequest = traversalRequest
		vulnerableResponse = traversalResponse
	}
	evidence = append(evidence, traversalEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "文件操作安全漏洞",
		Description:       "检测到文件操作安全漏洞，应用程序的文件操作功能存在安全缺陷，可能导致文件上传、下载、包含、遍历等攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强文件上传验证、限制文件下载权限、防止文件包含攻击、阻止文件遍历漏洞",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload", "https://cwe.mitre.org/data/definitions/22.html", "https://cwe.mitre.org/data/definitions/434.html"},
		Tags:              []string{"file", "upload", "download", "include", "traversal", "path"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *FileOperationDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"file-upload-verification",
		"file-download-verification",
		"file-include-verification",
		"file-traversal-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyFileOperationMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了文件操作漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "file-operation-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用文件操作验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *FileOperationDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("file_operation_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *FileOperationDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (文件操作通常是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyFileOperationMethod 验证文件操作方法
func (d *FileOperationDetector) verifyFileOperationMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "file-upload-verification":
		return d.verifyFileUpload(ctx, target)
	case "file-download-verification":
		return d.verifyFileDownload(ctx, target)
	case "file-include-verification":
		return d.verifyFileInclude(ctx, target)
	case "file-traversal-verification":
		return d.verifyFileTraversal(ctx, target)
	default:
		return 0.0
	}
}

// verifyFileUpload 验证文件上传
func (d *FileOperationDetector) verifyFileUpload(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的文件上传验证
	if d.hasFileOperationFeatures(target) {
		return 0.7 // 有文件操作特征的目标可能有上传问题
	}
	return 0.3
}

// verifyFileDownload 验证文件下载
func (d *FileOperationDetector) verifyFileDownload(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的文件下载验证
	if d.hasFileOperationFeatures(target) {
		return 0.6 // 有文件操作特征的目标可能有下载问题
	}
	return 0.2
}

// verifyFileInclude 验证文件包含
func (d *FileOperationDetector) verifyFileInclude(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的文件包含验证
	if d.hasFileOperationFeatures(target) {
		return 0.6 // 有文件操作特征的目标可能有包含问题
	}
	return 0.2
}

// verifyFileTraversal 验证文件遍历
func (d *FileOperationDetector) verifyFileTraversal(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的文件遍历验证
	if d.hasFileOperationFeatures(target) {
		return 0.6 // 有文件操作特征的目标可能有遍历问题
	}
	return 0.2
}
