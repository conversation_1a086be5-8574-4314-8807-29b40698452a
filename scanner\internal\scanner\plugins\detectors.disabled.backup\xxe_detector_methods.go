package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// initializePatterns 初始化检测模式
func (d *XXEDetector) initializePatterns() {
	// 错误模式 - 检测XML解析错误
	errorPatterns := []string{
		`xml parsing error`,
		`external entity`,
		`entity.*not.*found`,
		`entity.*reference`,
		`dtd.*error`,
		`xml.*error`,
		`parsing.*failed`,
		`malformed.*xml`,
		`invalid.*xml`,
		`xml.*syntax.*error`,
		`entity.*declaration`,
		`entity.*expansion`,
		`recursive.*entity`,
		`entity.*loop`,
		`billion.*laughs`,
		`quadratic.*blowup`,
		`entity.*limit`,
		`entity.*depth`,
		`xml.*bomb`,
		`entity.*processing.*error`,
		`external.*subset`,
		`parameter.*entity`,
		`general.*entity`,
		`entity.*resolver`,
		`entity.*handler`,
		`sax.*exception`,
		`dom.*exception`,
		`xpath.*exception`,
		`xslt.*exception`,
		`schema.*validation.*error`,
		`dtd.*validation.*error`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatterns))
	for _, pattern := range errorPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测XXE成功的响应特征
	responsePatterns := []string{
		// 文件内容模式
		`root:x:\d+:\d+:`,
		`daemon:x:\d+:\d+:`,
		`bin:x:\d+:\d+:`,
		`sys:x:\d+:\d+:`,
		`localhost\s+127\.0\.0\.1`,
		`::1\s+ip6-localhost`,
		`\[boot\s+loader\]`,
		`\[operating\s+systems\]`,
		`multi\(\d+\)disk\(\d+\)rdisk\(\d+\)partition\(\d+\)`,

		// 网络响应模式
		`http/1\.[01]`,
		`server:\s*nginx`,
		`server:\s*apache`,
		`server:\s*microsoft-iis`,
		`ssh-\d+\.\d+`,
		`220.*ftp`,

		// 云服务元数据模式
		`ami-[a-f0-9]+`,
		`i-[a-f0-9]+`,
		`instance-id`,
		`security-groups`,
		`latest/meta-data`,
		`computeMetadata`,

		// XML处理模式
		`entity.*expanded`,
		`entity.*resolved`,
		`external.*entity.*loaded`,
		`dtd.*processed`,
		`xml.*parsed`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatterns))
	for _, pattern := range responsePatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}

// detectFileRead 检测文件读取XXE
func (d *XXEDetector) detectFileRead(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试文件读取载荷
	for i, payload := range d.fileReadPayloads {
		if i >= 5 { // 限制载荷数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送XML请求
		resp, err := d.sendXMLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查文件读取响应
		confidence := d.checkFileReadResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "file-read",
				Description: fmt.Sprintf("文件读取载荷触发了XXE响应 (置信度: %.2f)", confidence),
				Content:     d.extractXXEEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSSRF 检测SSRF XXE
func (d *XXEDetector) detectSSRF(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试SSRF载荷
	for i, payload := range d.ssrfPayloads {
		if i >= 4 { // 限制载荷数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送XML请求
		resp, err := d.sendXMLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查SSRF响应
		confidence := d.checkSSRFResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "ssrf",
				Description: fmt.Sprintf("SSRF载荷触发了XXE响应 (置信度: %.2f)", confidence),
				Content:     d.extractSSRFEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDoS 检测拒绝服务XXE
func (d *XXEDetector) detectDoS(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试DoS载荷
	for i, payload := range d.dosPayloads {
		if i >= 2 { // 限制DoS载荷数量，避免真正造成拒绝服务
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送XML请求（使用较短超时）
		shortCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
		resp, err := d.sendXMLRequest(shortCtx, target.URL, payload)
		cancel()

		// 检查DoS响应（包括超时）
		confidence := d.checkDoSResponse(resp, err, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.4 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "dos",
				Description: fmt.Sprintf("DoS载荷触发了XXE响应 (置信度: %.2f)", confidence),
				Content:     d.extractDoSEvidence(resp, err, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendXMLRequest 发送XML请求
func (d *XXEDetector) sendXMLRequest(ctx context.Context, targetURL, xmlPayload string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(xmlPayload))
	if err != nil {
		return "", err
	}

	// 设置XML相关的头
	req.Header.Set("Content-Type", "application/xml")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/xml,text/xml,*/*")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkFileReadResponse 检查文件读取响应
func (d *XXEDetector) checkFileReadResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查文件内容指示器
	for _, indicator := range d.fileIndicators {
		if strings.Contains(response, strings.ToLower(indicator)) {
			confidence += 0.4
			// 特定文件内容的额外加分
			if strings.Contains(indicator, "root:") || strings.Contains(indicator, "daemon:") {
				confidence += 0.3
			}
			break
		}
	}

	// 检查响应模式
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查XML错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查载荷特定的响应
	if strings.Contains(payload, "file:///etc/passwd") &&
		(strings.Contains(response, "root:") || strings.Contains(response, "daemon:")) {
		confidence += 0.4
	}

	if strings.Contains(payload, "file:///etc/hosts") &&
		(strings.Contains(response, "localhost") || strings.Contains(response, "127.0.0.1")) {
		confidence += 0.4
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkSSRFResponse 检查SSRF响应
func (d *XXEDetector) checkSSRFResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查网络服务响应
	networkIndicators := []string{
		"http/1.",
		"server:",
		"ssh-",
		"220",
		"connection refused",
		"connection timeout",
		"network unreachable",
		"host unreachable",
	}

	for _, indicator := range networkIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查云服务元数据
	cloudIndicators := []string{
		"ami-",
		"instance-id",
		"security-groups",
		"meta-data",
		"computemetadata",
	}

	for _, indicator := range cloudIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5
			break
		}
	}

	// 检查内网访问特征
	if strings.Contains(payload, "127.0.0.1") || strings.Contains(payload, "localhost") {
		if strings.Contains(response, "http/1.") || strings.Contains(response, "server:") {
			confidence += 0.3
		}
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkDoSResponse 检查拒绝服务响应
func (d *XXEDetector) checkDoSResponse(response string, err error, payload string) float64 {
	confidence := 0.0

	// 检查超时错误
	if err != nil {
		errorStr := strings.ToLower(err.Error())
		if strings.Contains(errorStr, "timeout") ||
			strings.Contains(errorStr, "deadline exceeded") ||
			strings.Contains(errorStr, "context canceled") {
			confidence += 0.6
		}
	}

	if response != "" {
		response = strings.ToLower(response)

		// 检查DoS相关错误
		dosIndicators := []string{
			"out of memory",
			"memory limit",
			"stack overflow",
			"recursion limit",
			"entity expansion",
			"billion laughs",
			"quadratic blowup",
			"entity limit",
			"processing limit",
			"resource exhausted",
			"too many entities",
			"entity depth",
			"xml bomb",
		}

		for _, indicator := range dosIndicators {
			if strings.Contains(response, indicator) {
				confidence += 0.5
				break
			}
		}

		// 检查错误模式
		for _, pattern := range d.errorPatterns {
			if pattern.MatchString(response) {
				confidence += 0.3
				break
			}
		}
	}

	// 检查载荷类型
	if strings.Contains(payload, "billion laughs") || strings.Contains(payload, "&lol") {
		confidence += 0.2
	}

	if strings.Contains(payload, "recursive") || strings.Contains(payload, "&a;") {
		confidence += 0.2
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkXXEResponse 检查通用XXE响应
func (d *XXEDetector) checkXXEResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查文件内容
	fileConfidence := d.checkFileReadResponse(response, payload)
	if fileConfidence > confidence {
		confidence = fileConfidence
	}

	// 检查SSRF响应
	ssrfConfidence := d.checkSSRFResponse(response, payload)
	if ssrfConfidence > confidence {
		confidence = ssrfConfidence
	}

	// 检查XML处理特征
	xmlIndicators := []string{
		"entity",
		"dtd",
		"external",
		"xml",
		"parsing",
	}

	for _, indicator := range xmlIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.1
		}
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractXXEEvidence 提取XXE证据
func (d *XXEDetector) extractXXEEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 5 {
			lineLower := strings.ToLower(line)
			if strings.Contains(lineLower, "root:") ||
				strings.Contains(lineLower, "daemon:") ||
				strings.Contains(lineLower, "localhost") ||
				strings.Contains(lineLower, "entity") ||
				strings.Contains(lineLower, "xml") ||
				strings.Contains(lineLower, "dtd") {
				// 返回相关行及其上下文
				start := i
				if start > 0 {
					start--
				}
				end := i + 2
				if end >= len(lines) {
					end = len(lines)
				}
				return strings.Join(lines[start:end], "\n")
			}
		}
	}

	// 如果没有找到特定证据，返回响应的前500个字符
	if len(response) > 500 {
		return response[:500] + "..."
	}
	return response
}

// extractSSRFEvidence 提取SSRF证据
func (d *XXEDetector) extractSSRFEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 0 {
			lineLower := strings.ToLower(line)
			if strings.Contains(lineLower, "http/1.") ||
				strings.Contains(lineLower, "server:") ||
				strings.Contains(lineLower, "ssh-") ||
				strings.Contains(lineLower, "connection") ||
				strings.Contains(lineLower, "ami-") ||
				strings.Contains(lineLower, "instance-id") {
				return line
			}
		}
	}
	return "检测到XXE SSRF响应特征"
}

// extractDoSEvidence 提取DoS证据
func (d *XXEDetector) extractDoSEvidence(response string, err error, payload string) string {
	if err != nil {
		return fmt.Sprintf("请求错误: %s", err.Error())
	}

	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 0 {
			lineLower := strings.ToLower(line)
			if strings.Contains(lineLower, "memory") ||
				strings.Contains(lineLower, "limit") ||
				strings.Contains(lineLower, "entity") ||
				strings.Contains(lineLower, "recursion") ||
				strings.Contains(lineLower, "overflow") {
				return line
			}
		}
	}
	return "检测到XXE DoS响应特征"
}
