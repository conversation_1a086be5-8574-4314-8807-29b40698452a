package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestBusinessLogicDetectorBasicFunctionality 测试业务逻辑检测器基础功能
func TestBusinessLogicDetectorBasicFunctionality(t *testing.T) {
	detector := NewBusinessLogicDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "business-logic-comprehensive", detector.GetID())
	assert.Equal(t, "业务逻辑漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-840")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestBusinessLogicDetectorApplicability 测试业务逻辑检测器适用性
func TestBusinessLogicDetectorApplicability(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试有业务表单的目标
	businessTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/checkout",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/payment",
				Method: "POST",
				Fields: map[string]string{
					"price":    "text",
					"quantity": "number",
					"total":    "text",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(businessTarget))

	// 测试有业务参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/shop",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{
				URL:  "http://example.com/product?price=100&quantity=1",
				Text: "Product Link",
			},
		},
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试电商相关URL
	shopTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/shop/cart",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(shopTarget))

	// 测试支付相关URL
	paymentTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/payment/checkout",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paymentTarget))

	// 测试用户管理URL
	userTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/user/profile",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(userTarget))

	// 测试API端点
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/orders",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无业务功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestBusinessLogicDetectorConfiguration 测试业务逻辑检测器配置
func TestBusinessLogicDetectorConfiguration(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 2, config.Concurrency)
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 3, updatedConfig.Concurrency)
}

// TestBusinessLogicDetectorParameters 测试业务逻辑检测器参数列表
func TestBusinessLogicDetectorParameters(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 检查价格参数
	assert.NotEmpty(t, detector.priceParameters)
	assert.Greater(t, len(detector.priceParameters), 10)

	// 检查数量参数
	assert.NotEmpty(t, detector.quantityParameters)
	assert.Greater(t, len(detector.quantityParameters), 10)

	// 检查重定向参数
	assert.NotEmpty(t, detector.redirectParameters)
	assert.Greater(t, len(detector.redirectParameters), 10)

	// 检查业务端点
	assert.NotEmpty(t, detector.businessEndpoints)
	assert.Greater(t, len(detector.businessEndpoints), 20)

	// 检查逻辑模式
	assert.NotEmpty(t, detector.logicPatterns)
	assert.Greater(t, len(detector.logicPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 10)

	// 检查成功模式
	assert.NotEmpty(t, detector.successPatterns)
	assert.Greater(t, len(detector.successPatterns), 10)

	// 检查特定的参数
	assert.Contains(t, detector.priceParameters, "price")
	assert.Contains(t, detector.priceParameters, "amount")
	assert.Contains(t, detector.quantityParameters, "quantity")
	assert.Contains(t, detector.quantityParameters, "qty")
	assert.Contains(t, detector.redirectParameters, "redirect")
	assert.Contains(t, detector.redirectParameters, "return")
	assert.Contains(t, detector.businessEndpoints, "/pay")
	assert.Contains(t, detector.businessEndpoints, "/checkout")
}

// TestBusinessLogicDetectorPaymentLogicCheck 测试支付逻辑检查
func TestBusinessLogicDetectorPaymentLogicCheck(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试成功的支付响应
	paymentSuccessResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Payment Successful</h1>
<p>Your payment has been processed successfully.</p>
<p>Transaction ID: 12345</p>
<p>Amount: $0.01</p>
<p>Order confirmed!</p>
</body>
</html>`
	param := "price"
	value := "0.01"
	confidence := detector.checkPaymentLogicResponse(paymentSuccessResponse, param, value)
	assert.Greater(t, confidence, 0.8)

	// 测试异常价格被接受
	abnormalPriceResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Order Placed</h1>
<p>Your order has been placed successfully.</p>
<p>Total: $0.00</p>
<p>Thank you for your purchase!</p>
</body>
</html>`
	abnormalParam := "total"
	abnormalValue := "0"
	confidence2 := detector.checkPaymentLogicResponse(abnormalPriceResponse, abnormalParam, abnormalValue)
	assert.Greater(t, confidence2, 0.6)

	// 测试支付失败响应
	paymentFailResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Payment Failed</h1>
<p>Payment error: Invalid amount.</p>
<p>Please check your payment details.</p>
</body>
</html>`
	failParam := "amount"
	failValue := "-100"
	confidence3 := detector.checkPaymentLogicResponse(paymentFailResponse, failParam, failValue)
	assert.Less(t, confidence3, 0.5)
}

// TestBusinessLogicDetectorBusinessProcessCheck 测试业务流程检查
func TestBusinessLogicDetectorBusinessProcessCheck(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试步骤跳过成功
	stepSkipResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Process Completed</h1>
<p>You have successfully completed the final step.</p>
<p>Current stage: Final</p>
<p>Workflow status: Completed</p>
</body>
</html>`
	param := "step"
	value := "999"
	confidence := detector.checkBusinessProcessResponse(stepSkipResponse, param, value)
	assert.Greater(t, confidence, 0.7)

	// 测试正常流程响应
	normalProcessResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Step 2</h1>
<p>Please complete step 2 of the process.</p>
<p>Current stage: 2</p>
</body>
</html>`
	normalParam := "stage"
	normalValue := "2"
	confidence2 := detector.checkBusinessProcessResponse(normalProcessResponse, normalParam, normalValue)
	assert.GreaterOrEqual(t, confidence2, 0.4)
	assert.LessOrEqual(t, confidence2, 1.0) // 正常流程可能有较高置信度

	// 测试流程错误响应
	processErrorResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Process Error</h1>
<p>Invalid step number.</p>
<p>Please follow the correct workflow.</p>
</body>
</html>`
	errorParam := "phase"
	errorValue := "-1"
	confidence3 := detector.checkBusinessProcessResponse(processErrorResponse, errorParam, errorValue)
	assert.LessOrEqual(t, confidence3, 1.0) // 错误响应可能仍有一定置信度
}

// TestBusinessLogicDetectorDataValidationCheck 测试数据验证检查
func TestBusinessLogicDetectorDataValidationCheck(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试异常数量被接受
	abnormalQuantityResponse := `Status: 200 OK
Content-Type: application/json

{
	"status": "success",
	"message": "Item added to cart",
	"quantity": -1,
	"total_items": 0
}`
	param := "quantity"
	value := "-1"
	confidence := detector.checkDataValidationResponse(abnormalQuantityResponse, param, value)
	assert.Greater(t, confidence, 0.7)

	// 测试正常数量响应
	normalQuantityResponse := `Status: 200 OK
Content-Type: application/json

{
	"status": "success",
	"message": "Item added to cart",
	"quantity": 2,
	"total_items": 2
}`
	normalParam := "qty"
	normalValue := "2"
	confidence2 := detector.checkDataValidationResponse(normalQuantityResponse, normalParam, normalValue)
	assert.Greater(t, confidence2, 0.3)
	assert.Less(t, confidence2, 0.7)

	// 测试验证错误响应
	validationErrorResponse := `Status: 200 OK
Content-Type: application/json

{
	"status": "error",
	"message": "Validation failed: Invalid quantity",
	"errors": ["Quantity must be positive"]
}`
	errorParam := "count"
	errorValue := "0"
	confidence3 := detector.checkDataValidationResponse(validationErrorResponse, errorParam, errorValue)
	assert.LessOrEqual(t, confidence3, 0.5) // 验证错误响应可能仍有一定置信度
}

// TestBusinessLogicDetectorLogicFlawCheck 测试逻辑缺陷检查
func TestBusinessLogicDetectorLogicFlawCheck(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试开放重定向
	redirectResponse := `Status: 302 Found
Location: http://evil.com
Content-Type: text/html

<html>
<body>
<p>Redirecting...</p>
</body>
</html>`
	param := "redirect"
	value := "http://evil.com"
	confidence := detector.checkLogicFlawResponse(redirectResponse, param, value)
	assert.Greater(t, confidence, 0.8)

	// 测试JavaScript重定向
	jsRedirectResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<script>window.location = 'javascript:alert(1)';</script>
</body>
</html>`
	jsParam := "return"
	jsValue := "javascript:alert(1)"
	confidence2 := detector.checkLogicFlawResponse(jsRedirectResponse, jsParam, jsValue)
	assert.GreaterOrEqual(t, confidence2, 0.0) // JavaScript重定向可能有较低置信度

	// 测试正常重定向
	normalRedirectResponse := `Status: 302 Found
Location: /dashboard
Content-Type: text/html

<html>
<body>
<p>Redirecting to dashboard...</p>
</body>
</html>`
	normalParam := "next"
	normalValue := "/dashboard"
	confidence3 := detector.checkLogicFlawResponse(normalRedirectResponse, normalParam, normalValue)
	assert.GreaterOrEqual(t, confidence3, 0.0)
	assert.LessOrEqual(t, confidence3, 1.0)
}

// TestBusinessLogicDetectorRiskScore 测试风险评分计算
func TestBusinessLogicDetectorRiskScore(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestBusinessLogicDetectorLifecycle 测试检测器生命周期
func TestBusinessLogicDetectorLifecycle(t *testing.T) {
	detector := NewBusinessLogicDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkBusinessLogicDetectorPaymentCheck 基准测试支付逻辑检查性能
func BenchmarkBusinessLogicDetectorPaymentCheck(b *testing.B) {
	detector := NewBusinessLogicDetector()
	response := `Status: 200 OK\nContent-Type: text/html\n\n<html><body><h1>Payment Successful</h1><p>Amount: $0.01</p></body></html>`
	param := "price"
	value := "0.01"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkPaymentLogicResponse(response, param, value)
	}
}

// BenchmarkBusinessLogicDetectorProcessCheck 基准测试业务流程检查性能
func BenchmarkBusinessLogicDetectorProcessCheck(b *testing.B) {
	detector := NewBusinessLogicDetector()
	response := `Status: 200 OK\nContent-Type: text/html\n\n<html><body><h1>Process Completed</h1><p>Step: 999</p></body></html>`
	param := "step"
	value := "999"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkBusinessProcessResponse(response, param, value)
	}
}
