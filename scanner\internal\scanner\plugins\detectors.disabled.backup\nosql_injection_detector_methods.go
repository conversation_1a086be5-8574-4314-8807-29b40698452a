package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectMongoInjection 检测MongoDB注入
func (d *NoSQLInjectionDetector) detectMongoInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试MongoDB注入载荷
	for _, payload := range d.mongoPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送MongoDB注入请求
		resp, err := d.sendNoSQLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查MongoDB注入响应
		confidence := d.checkMongoInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("MongoDB注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "mongo-injection",
				Description: fmt.Sprintf("发现MongoDB注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractNoSQLEvidence(resp, "mongo-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCouchDBInjection 检测CouchDB注入
func (d *NoSQLInjectionDetector) detectCouchDBInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试CouchDB注入载荷
	for _, payload := range d.couchdbPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送CouchDB注入请求
		resp, err := d.sendNoSQLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查CouchDB注入响应
		confidence := d.checkCouchDBInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("CouchDB注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "couchdb-injection",
				Description: fmt.Sprintf("发现CouchDB注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractNoSQLEvidence(resp, "couchdb-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectRedisInjection 检测Redis注入
func (d *NoSQLInjectionDetector) detectRedisInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Redis注入载荷
	for _, payload := range d.redisPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Redis注入请求
		resp, err := d.sendNoSQLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Redis注入响应
		confidence := d.checkRedisInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Redis注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "redis-injection",
				Description: fmt.Sprintf("发现Redis注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractNoSQLEvidence(resp, "redis-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCassandraInjection 检测Cassandra注入
func (d *NoSQLInjectionDetector) detectCassandraInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Cassandra注入载荷
	for _, payload := range d.cassandraPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送Cassandra注入请求
		resp, err := d.sendNoSQLRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查Cassandra注入响应
		confidence := d.checkCassandraInjectionResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("Cassandra注入: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "cassandra-injection",
				Description: fmt.Sprintf("发现Cassandra注入: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractNoSQLEvidence(resp, "cassandra-injection"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendNoSQLRequest 发送NoSQL注入请求
func (d *NoSQLInjectionDetector) sendNoSQLRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendNoSQLGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST参数注入
	postResp, err := d.sendNoSQLPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 尝试JSON载荷注入
	jsonResp, err := d.sendNoSQLJSONRequest(ctx, targetURL, payload)
	if err == nil && jsonResp != "" {
		return jsonResp, nil
	}

	// 返回GET响应（即使有错误）
	if getResp != "" {
		return getResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendNoSQLGETRequest 发送NoSQL注入GET请求
func (d *NoSQLInjectionDetector) sendNoSQLGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendNoSQLPOSTRequest 发送NoSQL注入POST请求
func (d *NoSQLInjectionDetector) sendNoSQLPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造POST数据
	postData := url.Values{}

	for _, param := range d.testParameters {
		postData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(postData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendNoSQLJSONRequest 发送NoSQL注入JSON请求
func (d *NoSQLInjectionDetector) sendNoSQLJSONRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构造JSON数据
	jsonData := fmt.Sprintf(`{"query":%s,"search":%s,"filter":%s}`, payload, payload, payload)

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(jsonData))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkMongoInjectionResponse 检查MongoDB注入响应
func (d *NoSQLInjectionDetector) checkMongoInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查MongoDB模式匹配
	for _, pattern := range d.mongoPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查MongoDB特定错误
	mongoErrors := []string{
		"mongodb error", "mongo error", "bson error", "objectid error",
		"invalid objectid", "duplicate key error", "collection not found",
		"database not found", "namespace not found", "cursor not found",
		"mongodb错误", "mongo错误", "对象标识错误", "集合未找到",
	}

	for _, error := range mongoErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // MongoDB特定错误的强指示器
			break
		}
	}

	// 检查MongoDB操作符
	mongoOperators := []string{
		"$ne", "$gt", "$gte", "$lt", "$lte", "$in", "$nin",
		"$exists", "$type", "$regex", "$where", "$or", "$and",
		"$nor", "$not", "$all", "$size", "$elemmatch",
	}

	for _, operator := range mongoOperators {
		if strings.Contains(response, operator) {
			confidence += 0.4 // MongoDB操作符的指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkCouchDBInjectionResponse 检查CouchDB注入响应
func (d *NoSQLInjectionDetector) checkCouchDBInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 400") ||
		strings.Contains(response, "status: 404") ||
		strings.Contains(response, "status: 500") {
		confidence += 0.4 // 错误响应可能表示注入错误
	}

	// 检查CouchDB模式匹配
	for _, pattern := range d.couchdbPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查CouchDB特定错误
	couchdbErrors := []string{
		"couchdb error", "bad request", "not found", "conflict",
		"unauthorized", "forbidden", "_rev missing", "document not found",
		"database does not exist", "design document not found", "view not found",
		"couchdb错误", "文档未找到", "数据库不存在",
	}

	for _, error := range couchdbErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // CouchDB特定错误的强指示器
			break
		}
	}

	// 检查CouchDB JSON响应
	couchdbJSON := []string{
		`"ok":true`, `"error":"not_found"`, `"reason":"missing"`,
		`"rows":[`, `"total_rows":`, `"offset":`,
	}

	for _, jsonPattern := range couchdbJSON {
		if strings.Contains(response, jsonPattern) {
			confidence += 0.4 // CouchDB JSON响应的指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkRedisInjectionResponse 检查Redis注入响应
func (d *NoSQLInjectionDetector) checkRedisInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 内部服务器错误可能表示注入错误
	}

	// 检查Redis模式匹配
	for _, pattern := range d.redisPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Redis特定错误
	redisErrors := []string{
		"redis error", "wrong number of arguments", "unknown command",
		"syntax error", "invalid argument", "redis错误", "命令错误", "参数错误",
	}

	for _, error := range redisErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // Redis特定错误的强指示器
			break
		}
	}

	// 检查Redis响应格式
	redisResponses := []string{
		"+ok", "-err", ":", "$", "*", "flushall", "flushdb", "keys",
	}

	for _, redisResp := range redisResponses {
		if strings.Contains(response, redisResp) {
			confidence += 0.4 // Redis响应格式的指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// checkCassandraInjectionResponse 检查Cassandra注入响应
func (d *NoSQLInjectionDetector) checkCassandraInjectionResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能表示注入成功
	} else if strings.Contains(response, "status: 400") ||
		strings.Contains(response, "status: 500") {
		confidence += 0.4 // 错误响应可能表示注入错误
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查响应模式匹配
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查Cassandra特定错误
	cassandraErrors := []string{
		"cassandra error", "cql error", "syntax error", "invalid query",
		"keyspace not found", "table not found", "column not found",
		"cassandra错误", "cql错误", "语法错误", "无效查询",
	}

	for _, error := range cassandraErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // Cassandra特定错误的强指示器
			break
		}
	}

	// 检查Cassandra CQL关键字
	cqlKeywords := []string{
		"select", "insert", "update", "delete", "create", "drop",
		"keyspace", "table", "column", "index", "token", "writetime", "ttl",
	}

	for _, keyword := range cqlKeywords {
		if strings.Contains(response, keyword) {
			confidence += 0.3 // CQL关键字的指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.3
	}

	return confidence
}

// extractNoSQLEvidence 提取NoSQL注入证据
func (d *NoSQLInjectionDetector) extractNoSQLEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var nosqlLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "mongo-injection":
			if strings.Contains(lineLower, "mongodb") ||
				strings.Contains(lineLower, "mongo") ||
				strings.Contains(lineLower, "bson") ||
				strings.Contains(lineLower, "objectid") ||
				strings.Contains(lineLower, "$ne") ||
				strings.Contains(lineLower, "$gt") ||
				strings.Contains(lineLower, "$where") ||
				strings.Contains(lineLower, "collection") ||
				strings.Contains(lineLower, "database") {
				nosqlLines = append(nosqlLines, line)
			}
		case "couchdb-injection":
			if strings.Contains(lineLower, "couchdb") ||
				strings.Contains(lineLower, "couch") ||
				strings.Contains(lineLower, "_rev") ||
				strings.Contains(lineLower, "document") ||
				strings.Contains(lineLower, "selector") ||
				strings.Contains(lineLower, "view") ||
				strings.Contains(lineLower, "design") {
				nosqlLines = append(nosqlLines, line)
			}
		case "redis-injection":
			if strings.Contains(lineLower, "redis") ||
				strings.Contains(lineLower, "+ok") ||
				strings.Contains(lineLower, "-err") ||
				strings.Contains(lineLower, "flushall") ||
				strings.Contains(lineLower, "flushdb") ||
				strings.Contains(lineLower, "keys") ||
				strings.Contains(lineLower, "eval") {
				nosqlLines = append(nosqlLines, line)
			}
		case "cassandra-injection":
			if strings.Contains(lineLower, "cassandra") ||
				strings.Contains(lineLower, "cql") ||
				strings.Contains(lineLower, "keyspace") ||
				strings.Contains(lineLower, "table") ||
				strings.Contains(lineLower, "column") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "writetime") {
				nosqlLines = append(nosqlLines, line)
			}
		default:
			if strings.Contains(lineLower, "nosql") ||
				strings.Contains(lineLower, "mongo") ||
				strings.Contains(lineLower, "couch") ||
				strings.Contains(lineLower, "redis") ||
				strings.Contains(lineLower, "cassandra") ||
				strings.Contains(lineLower, "database") ||
				strings.Contains(lineLower, "query") ||
				strings.Contains(lineLower, "injection") {
				nosqlLines = append(nosqlLines, line)
			}
		}

		if len(nosqlLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(nosqlLines) > 0 {
		return strings.Join(nosqlLines, "\n")
	}

	// 如果没有找到特定的NoSQL信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
