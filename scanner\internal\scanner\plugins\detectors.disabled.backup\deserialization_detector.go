package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// DeserializationDetector 反序列化检测器
// 支持反序列化检测，包括Java反序列化、.NET反序列化、Python反序列化、PHP反序列化等多种反序列化攻击技术
type DeserializationDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	javaPayloads     []string         // Java反序列化载荷
	dotnetPayloads   []string         // .NET反序列化载荷
	pythonPayloads   []string         // Python反序列化载荷
	phpPayloads      []string         // PHP反序列化载荷
	nodeJSPayloads   []string         // Node.js反序列化载荷
	rubyPayloads     []string         // Ruby反序列化载荷
	testParameters   []string         // 测试参数
	javaPatterns     []*regexp.Regexp // Java特征模式
	dotnetPatterns   []*regexp.Regexp // .NET特征模式
	pythonPatterns   []*regexp.Regexp // Python特征模式
	phpPatterns      []*regexp.Regexp // PHP特征模式
	errorPatterns    []*regexp.Regexp // 错误模式
	responsePatterns []*regexp.Regexp // 响应模式
	httpClient       *http.Client
}

// NewDeserializationDetector 创建反序列化检测器
func NewDeserializationDetector() *DeserializationDetector {
	detector := &DeserializationDetector{
		id:          "deserialization-comprehensive",
		name:        "反序列化漏洞综合检测器",
		category:    "web",
		severity:    "critical",
		cve:         []string{"CVE-2015-8103", "CVE-2017-5638", "CVE-2019-2725", "CVE-2020-2555"},
		cwe:         []string{"CWE-502", "CWE-20", "CWE-94", "CWE-78"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测反序列化漏洞，包括Java反序列化、.NET反序列化、Python反序列化、PHP反序列化等多种反序列化攻击技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 反序列化检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，反序列化响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeJavaPayloads()
	detector.initializeDotnetPayloads()
	detector.initializePythonPayloads()
	detector.initializePHPPayloads()
	detector.initializeNodeJSPayloads()
	detector.initializeRubyPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *DeserializationDetector) GetID() string            { return d.id }
func (d *DeserializationDetector) GetName() string          { return d.name }
func (d *DeserializationDetector) GetCategory() string      { return d.category }
func (d *DeserializationDetector) GetSeverity() string      { return d.severity }
func (d *DeserializationDetector) GetCVE() []string         { return d.cve }
func (d *DeserializationDetector) GetCWE() []string         { return d.cwe }
func (d *DeserializationDetector) GetVersion() string       { return d.version }
func (d *DeserializationDetector) GetAuthor() string        { return d.author }
func (d *DeserializationDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *DeserializationDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *DeserializationDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *DeserializationDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 9000, 9090}
}
func (d *DeserializationDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *DeserializationDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *DeserializationDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *DeserializationDetector) GetDependencies() []string         { return []string{} }
func (d *DeserializationDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *DeserializationDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *DeserializationDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *DeserializationDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *DeserializationDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *DeserializationDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *DeserializationDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.javaPayloads) == 0 {
		return fmt.Errorf("Java反序列化载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *DeserializationDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 反序列化检测适用于有序列化功能的Web应用
	// 检查是否有反序列化相关的特征
	if d.hasSerializationFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "json", "xml", "rpc", "soap", "service",
		"data", "object", "serialize", "deserialize", "marshal",
		"接口", "数据", "对象", "序列化", "反序列化",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 反序列化是通用Web漏洞，默认适用于所有Web目标
}

// hasSerializationFeatures 检查是否有序列化功能
func (d *DeserializationDetector) hasSerializationFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有序列化相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "content-type" &&
			(strings.Contains(valueLower, "application/java-archive") ||
				strings.Contains(valueLower, "application/x-java-serialized-object") ||
				strings.Contains(valueLower, "application/x-java-object") ||
				strings.Contains(valueLower, "application/octet-stream")) {
			return true
		}

		if keyLower == "server" &&
			(strings.Contains(valueLower, "tomcat") ||
				strings.Contains(valueLower, "jetty") ||
				strings.Contains(valueLower, "weblogic") ||
				strings.Contains(valueLower, "websphere") ||
				strings.Contains(valueLower, "jboss")) {
			return true
		}
	}

	// 检查技术栈中是否有序列化相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		serializationTechnologies := []string{
			"java", "spring", "struts", "hibernate", "jackson", "gson",
			"dotnet", "asp.net", "wcf", "newtonsoft", "binaryformatter",
			"python", "pickle", "django", "flask", "celery",
			"php", "symfony", "laravel", "zend", "magento",
			"nodejs", "node.js", "express", "koa", "fastify",
			"ruby", "rails", "sinatra", "marshal",
			"序列化", "反序列化", "对象", "数据",
		}

		for _, serTech := range serializationTechnologies {
			if strings.Contains(techNameLower, serTech) {
				return true
			}
		}
	}

	// 检查链接中是否有序列化相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkURLLower, "service") ||
			strings.Contains(linkURLLower, "data") ||
			strings.Contains(linkTextLower, "api") ||
			strings.Contains(linkTextLower, "service") ||
			strings.Contains(linkTextLower, "数据") ||
			strings.Contains(linkTextLower, "接口") {
			return true
		}
	}

	// 检查表单中是否有序列化相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			serializationFields := []string{
				"data", "object", "payload", "content", "body", "json", "xml",
				"serialize", "deserialize", "marshal", "unmarshal",
				"数据", "对象", "内容", "载荷", "序列化", "反序列化",
			}

			for _, serField := range serializationFields {
				if strings.Contains(fieldNameLower, serField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *DeserializationDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种反序列化检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. Java反序列化检测
	javaEvidence, javaConfidence, javaPayload, javaRequest, javaResponse := d.detectJavaDeserialization(ctx, target)
	if javaConfidence > maxConfidence {
		maxConfidence = javaConfidence
		vulnerablePayload = javaPayload
		vulnerableRequest = javaRequest
		vulnerableResponse = javaResponse
	}
	evidence = append(evidence, javaEvidence...)

	// 2. .NET反序列化检测
	dotnetEvidence, dotnetConfidence, dotnetPayload, dotnetRequest, dotnetResponse := d.detectDotnetDeserialization(ctx, target)
	if dotnetConfidence > maxConfidence {
		maxConfidence = dotnetConfidence
		vulnerablePayload = dotnetPayload
		vulnerableRequest = dotnetRequest
		vulnerableResponse = dotnetResponse
	}
	evidence = append(evidence, dotnetEvidence...)

	// 3. Python反序列化检测
	pythonEvidence, pythonConfidence, pythonPayload, pythonRequest, pythonResponse := d.detectPythonDeserialization(ctx, target)
	if pythonConfidence > maxConfidence {
		maxConfidence = pythonConfidence
		vulnerablePayload = pythonPayload
		vulnerableRequest = pythonRequest
		vulnerableResponse = pythonResponse
	}
	evidence = append(evidence, pythonEvidence...)

	// 4. PHP反序列化检测
	phpEvidence, phpConfidence, phpPayload, phpRequest, phpResponse := d.detectPHPDeserialization(ctx, target)
	if phpConfidence > maxConfidence {
		maxConfidence = phpConfidence
		vulnerablePayload = phpPayload
		vulnerableRequest = phpRequest
		vulnerableResponse = phpResponse
	}
	evidence = append(evidence, phpEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "反序列化漏洞",
		Description:       "检测到反序列化漏洞，攻击者可能通过恶意序列化数据实现远程代码执行或其他攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "避免反序列化不可信数据，使用安全的序列化格式，实施输入验证和过滤，使用白名单机制",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Deserialization_of_untrusted_data", "https://cwe.mitre.org/data/definitions/502.html"},
		Tags:              []string{"deserialization", "rce", "web", "critical"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *DeserializationDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"java-deserialization",
		"dotnet-deserialization",
		"python-deserialization",
		"php-deserialization",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyDeserializationMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了反序列化漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "deserialization-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用反序列化验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *DeserializationDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("deserialization_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *DeserializationDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (反序列化通常是严重风险漏洞)
	baseScore := 9.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyDeserializationMethod 验证反序列化方法
func (d *DeserializationDetector) verifyDeserializationMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "java-deserialization":
		return d.verifyJavaDeserialization(ctx, target)
	case "dotnet-deserialization":
		return d.verifyDotnetDeserialization(ctx, target)
	case "python-deserialization":
		return d.verifyPythonDeserialization(ctx, target)
	case "php-deserialization":
		return d.verifyPHPDeserialization(ctx, target)
	default:
		return 0.0
	}
}

// verifyJavaDeserialization 验证Java反序列化
func (d *DeserializationDetector) verifyJavaDeserialization(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Java反序列化验证
	if d.hasSerializationFeatures(target) {
		return 0.8 // 有序列化特征的目标可能有Java反序列化
	}
	return 0.4
}

// verifyDotnetDeserialization 验证.NET反序列化
func (d *DeserializationDetector) verifyDotnetDeserialization(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的.NET反序列化验证
	if d.hasSerializationFeatures(target) {
		return 0.7 // 有序列化特征的目标可能有.NET反序列化
	}
	return 0.3
}

// verifyPythonDeserialization 验证Python反序列化
func (d *DeserializationDetector) verifyPythonDeserialization(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Python反序列化验证
	if d.hasSerializationFeatures(target) {
		return 0.6 // 有序列化特征的目标可能有Python反序列化
	}
	return 0.2
}

// verifyPHPDeserialization 验证PHP反序列化
func (d *DeserializationDetector) verifyPHPDeserialization(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的PHP反序列化验证
	if d.hasSerializationFeatures(target) {
		return 0.6 // 有序列化特征的目标可能有PHP反序列化
	}
	return 0.2
}

// initializeJavaPayloads 初始化Java反序列化载荷列表
func (d *DeserializationDetector) initializeJavaPayloads() {
	d.javaPayloads = []string{
		// Java序列化魔术字节
		"rO0AB",    // Base64编码的Java序列化头 (0xaced0005)
		"aced0005", // 十六进制Java序列化头
		"rO0ABQ==", // 完整的Base64编码Java序列化头
		"rO0ABXNy", // Base64编码的Java序列化对象开始

		// Apache Commons Collections载荷标识
		"org.apache.commons.collections",
		"org.apache.commons.collections.Transformer",
		"org.apache.commons.collections.functors.InvokerTransformer",
		"org.apache.commons.collections.functors.ChainedTransformer",
		"org.apache.commons.collections.functors.ConstantTransformer",
		"org.apache.commons.collections.map.LazyMap",
		"org.apache.commons.collections4.functors.InvokerTransformer",
		"org.apache.commons.collections4.functors.ChainedTransformer",

		// Spring Framework载荷
		"org.springframework.beans.factory.ObjectFactory",
		"org.springframework.context.support.ClassPathXmlApplicationContext",
		"org.springframework.beans.factory.config.PropertyPathFactoryBean",
		"org.springframework.jndi.JndiObjectFactoryBean",

		// JNDI注入载荷
		"ldap://evil.com/",
		"rmi://evil.com/",
		"dns://evil.com/",
		"java:comp/env/",
		"javax.naming.InitialContext",
		"javax.naming.directory.InitialDirContext",

		// Fastjson载荷
		"com.sun.rowset.JdbcRowSetImpl",
		"com.sun.org.apache.xalan.internal.xsltc.trax.TemplatesImpl",
		"org.apache.ibatis.datasource.jndi.JndiDataSourceFactory",
		"com.alibaba.fastjson.JSONObject",

		// Jackson载荷
		"com.fasterxml.jackson.databind.node.POJONode",
		"org.springframework.beans.factory.config.PropertyPathFactoryBean",
		"com.mchange.v2.c3p0.JndiRefForwardingDataSource",
		"com.mchange.v2.c3p0.WrapperConnectionPoolDataSource",

		// Hibernate载荷
		"org.hibernate.engine.spi.TypedValue",
		"org.hibernate.tuple.component.AbstractComponentTuplizer",
		"org.hibernate.tuple.component.PojoComponentTuplizer",

		// Groovy载荷
		"org.codehaus.groovy.runtime.ConvertedClosure",
		"org.codehaus.groovy.runtime.MethodClosure",
		"groovy.util.Expando",

		// ROME载荷
		"com.sun.syndication.feed.impl.EqualsBean",
		"com.sun.syndication.feed.impl.ToStringBean",
		"com.rometools.rome.feed.impl.EqualsBean",
		"com.rometools.rome.feed.impl.ToStringBean",

		// 中文Java载荷
		"java序列化", "反序列化", "对象注入", "远程代码执行",
	}
}

// initializeDotnetPayloads 初始化.NET反序列化载荷列表
func (d *DeserializationDetector) initializeDotnetPayloads() {
	d.dotnetPayloads = []string{
		// .NET序列化魔术字节
		"AAEAAAD/////AQAAAAAAAAAMAgAAAF", // BinaryFormatter序列化头
		"AAEAAAD/////",                   // 简化的BinaryFormatter头
		"AAEAAAD",                        // 更简化的BinaryFormatter头

		// .NET序列化类型
		"System.Runtime.Serialization",
		"System.Runtime.Serialization.Formatters.Binary.BinaryFormatter",
		"System.Runtime.Serialization.Formatters.Soap.SoapFormatter",
		"System.Web.UI.LosFormatter",
		"System.Web.UI.ObjectStateFormatter",

		// .NET反序列化载荷
		"System.Configuration.Install.AssemblyInstaller",
		"System.Activities.Presentation.WorkflowDesigner",
		"System.Windows.Data.ObjectDataProvider",
		"Microsoft.Exchange.Management.SystemManager.WinForms.ExchangeSettingsProvider",

		// ViewState载荷
		"/wEPDwUKLTI2NjY5", // ViewState开始标识
		"__VIEWSTATE",
		"__EVENTVALIDATION",
		"__VIEWSTATEGENERATOR",

		// JSON.NET载荷
		"Newtonsoft.Json.JsonConvert",
		"$type",
		"System.Windows.Forms.BindingSource",
		"System.Data.Services.Internal.ProjectedWrapper",

		// WCF载荷
		"System.ServiceModel",
		"NetDataContractSerializer",
		"DataContractSerializer",
		"XmlSerializer",

		// PowerShell载荷
		"System.Management.Automation.PSObject",
		"System.Management.Automation.Runspaces.Runspace",
		"Microsoft.PowerShell.Commands.GetProcessCommand",

		// 中文.NET载荷
		"dotnet序列化", "二进制格式化", "视图状态", "对象状态",
	}
}

// initializePythonPayloads 初始化Python反序列化载荷列表
func (d *DeserializationDetector) initializePythonPayloads() {
	d.pythonPayloads = []string{
		// Python pickle魔术字节
		"cposix\nsystem\n",
		"c__builtin__\neval\n",
		"csubprocess\nPopen\n",
		"cos\nsystem\n",
		"ccommands\ngetoutput\n",

		// Pickle协议标识
		"\\x80\\x03", // Pickle协议3
		"\\x80\\x04", // Pickle协议4
		"\\x80\\x05", // Pickle协议5
		"gANjb3M=",   // Base64编码的pickle载荷开始

		// Python对象载荷
		"__reduce__",
		"__reduce_ex__",
		"__setstate__",
		"__getstate__",

		// 危险模块载荷
		"subprocess.call",
		"subprocess.Popen",
		"os.system",
		"os.popen",
		"eval",
		"exec",
		"compile",
		"__import__",

		// Django载荷
		"django.core.management.commands.shell.Command",
		"django.contrib.sessions.serializers.PickleSerializer",

		// Flask载荷
		"flask.sessions.SecureCookieSessionInterface",
		"itsdangerous.Serializer",

		// Celery载荷
		"celery.canvas.Signature",
		"celery.result.AsyncResult",

		// PyYAML载荷
		"!!python/object/apply:os.system",
		"!!python/object/apply:subprocess.call",
		"!!python/object/apply:eval",

		// 中文Python载荷
		"python序列化", "pickle载荷", "对象还原", "代码执行",
	}
}

// initializePHPPayloads 初始化PHP反序列化载荷列表
func (d *DeserializationDetector) initializePHPPayloads() {
	d.phpPayloads = []string{
		// PHP序列化基础载荷
		`O:8:"stdClass":1:{s:4:"test";s:4:"test";}`,
		`a:1:{s:4:"test";s:4:"test";}`,
		`s:4:"test";`,
		`i:123;`,
		`b:1;`,
		`d:1.23;`,
		`N;`,

		// PHP对象注入载荷
		`O:4:"Test":1:{s:4:"code";s:10:"phpinfo();";}`,
		`O:9:"Exception":1:{s:7:"message";s:10:"phpinfo();";}`,
		`O:8:"stdClass":1:{s:4:"exec";s:6:"whoami";}`,

		// PHP魔术方法载荷
		`O:4:"Test":1:{s:11:"__destruct";s:10:"phpinfo();";}`,
		`O:4:"Test":1:{s:8:"__wakeup";s:10:"phpinfo();";}`,
		`O:4:"Test":1:{s:10:"__toString";s:10:"phpinfo();";}`,
		`O:4:"Test":1:{s:7:"__call";s:10:"phpinfo();";}`,

		// Symfony载荷
		"Symfony\\Component\\Process\\Process",
		"Symfony\\Component\\HttpFoundation\\File\\UploadedFile",
		"Symfony\\Component\\Routing\\Route",

		// Laravel载荷
		"Illuminate\\Foundation\\Testing\\PendingCommand",
		"Illuminate\\Broadcasting\\PendingBroadcast",
		"Illuminate\\Validation\\Rules\\RequiredIf",

		// Zend载荷
		"Zend\\Mail\\Transport\\File",
		"Zend\\Log\\Writer\\Mail",
		"Zend\\View\\Resolver\\TemplateMapResolver",

		// Magento载荷
		"Mage_Core_Model_Layout",
		"Varien_Data_Collection",
		"Mage_Catalog_Model_Product_Option",

		// WordPress载荷
		"WP_Object_Cache",
		"WP_Hook",
		"WP_User",
		"WP_Post",

		// Drupal载荷
		"Drupal\\Core\\Database\\Query\\Select",
		"Drupal\\Core\\Form\\FormState",
		"Drupal\\Core\\Render\\Markup",

		// CodeIgniter载荷
		"CI_DB_mysql_driver",
		"CI_Session",
		"CI_Upload",

		// CakePHP载荷
		"Cake\\ORM\\Query",
		"Cake\\Http\\Response",
		"Cake\\Validation\\Validator",

		// 中文PHP载荷
		"php序列化", "对象注入", "魔术方法", "代码执行",
	}
}

// initializeNodeJSPayloads 初始化Node.js反序列化载荷列表
func (d *DeserializationDetector) initializeNodeJSPayloads() {
	d.nodeJSPayloads = []string{
		// Node.js序列化载荷
		`{"rce":"_$$ND_FUNC$$_function(){require('child_process').exec('whoami');}()"}`,
		`{"__proto__":{"isAdmin":true}}`,
		`{"constructor":{"prototype":{"isAdmin":true}}}`,

		// Node-serialize载荷
		`_$$ND_FUNC$$_function(){require('child_process').exec('whoami');}()`,
		`_$$ND_FUNC$$_function(){global.process.mainModule.require('child_process').exec('whoami');}()`,

		// Funcster载荷
		`{"__js_function":"function(){require('child_process').exec('whoami');}"}`,
		`{"__js_function":"function(){global.process.exit();}"}`,

		// V8序列化载荷
		`ff0d6f2203666f6f`, // V8序列化魔术字节
		`ff0f6f`,           // 简化的V8序列化头

		// Express载荷
		"express.static",
		"express.Router",
		"express.application",

		// Socket.io载荷
		"socket.io.parser",
		"socket.io.client",

		// 中文Node.js载荷
		"nodejs序列化", "原型污染", "代码注入", "进程执行",
	}
}

// initializeRubyPayloads 初始化Ruby反序列化载荷列表
func (d *DeserializationDetector) initializeRubyPayloads() {
	d.rubyPayloads = []string{
		// Ruby Marshal载荷
		"\\x04\\x08", // Ruby Marshal魔术字节
		"BAh7",       // Base64编码的Ruby Marshal开始

		// Ruby对象载荷
		"Marshal.load",
		"Marshal.restore",
		"YAML.load",
		"YAML.load_file",

		// Ruby危险类载荷
		"Kernel.system",
		"Kernel.exec",
		"Kernel.`",
		"IO.popen",
		"File.open",
		"Dir.glob",

		// Rails载荷
		"ActiveRecord::Base",
		"ActionController::Base",
		"ActiveSupport::Cache",
		"ActionView::Template",

		// Sinatra载荷
		"Sinatra::Application",
		"Sinatra::Base",

		// 中文Ruby载荷
		"ruby序列化", "marshal载荷", "yaml载荷", "代码执行",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *DeserializationDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 序列化相关参数
		"data", "object", "payload", "content", "body", "input", "value",
		"serialize", "serialized", "deserialize", "deserialized",
		"marshal", "marshaled", "unmarshal", "unmarshaled",
		"pickle", "pickled", "unpickle", "unpickled",

		// 格式相关参数
		"json", "xml", "binary", "base64", "encoded", "decoded",
		"format", "type", "mime", "encoding", "charset",

		// 对象相关参数
		"obj", "entity", "model", "bean", "pojo", "dto", "vo",
		"class", "instance", "state", "session", "cache",

		// API相关参数
		"api", "service", "method", "function", "call", "invoke",
		"request", "response", "message", "packet", "frame",

		// 文件相关参数
		"file", "upload", "download", "attachment", "document",
		"stream", "buffer", "blob", "chunk", "segment",

		// 配置相关参数
		"config", "settings", "options", "params", "args",
		"properties", "attributes", "metadata", "info",

		// 会话相关参数
		"session", "token", "cookie", "auth", "credential",
		"user", "profile", "account", "identity", "principal",

		// 缓存相关参数
		"cache", "store", "storage", "repository", "database",
		"memory", "temp", "temporary", "volatile", "persistent",

		// 通用参数
		"d", "o", "p", "c", "b", "i", "v", "s", "t", "f",
		"param", "parameter", "arg", "argument", "var", "variable",

		// 中文参数
		"数据", "对象", "载荷", "内容", "序列化", "反序列化",
		"格式", "类型", "编码", "解码", "文件", "配置",
		"会话", "缓存", "存储", "参数", "变量", "值",
	}
}

// initializePatterns 初始化检测模式
func (d *DeserializationDetector) initializePatterns() {
	// Java模式 - 检测Java反序列化相关的响应内容
	javaPatternStrings := []string{
		// Java序列化特征
		`(?i)(java\.io\.serializable|java\.io\.objectinputstream|java\.io\.objectoutputstream)`,
		`(?i)(java\.lang\.classnotfoundexception|java\.io\.streamcorruptedexception)`,
		`(?i)(java\.io\.invalidclassexception|java\.io\.optionaldataexception)`,

		// Java反序列化库特征
		`(?i)(apache\.commons\.collections|org\.springframework|com\.fasterxml\.jackson)`,
		`(?i)(hibernate\.engine|groovy\.util|com\.sun\.rowset)`,
		`(?i)(fastjson|gson|kryo|hessian|protobuf)`,

		// Java异常特征
		`(?i)(classcastexception|nosuchmethodexception|illegalaccessexception)`,
		`(?i)(instantiationexception|invocationtargetexception|securityexception)`,

		// JNDI注入特征
		`(?i)(javax\.naming|jndi|ldap://|rmi://|dns://)`,
		`(?i)(initialcontext|dircontext|reference|referenceable)`,

		// 中文Java特征
		`(?i)(java序列化|反序列化|对象注入|远程代码执行)`,
		`(?i)(类加载|方法调用|异常堆栈|序列化流)`,
	}

	d.javaPatterns = make([]*regexp.Regexp, 0, len(javaPatternStrings))
	for _, pattern := range javaPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.javaPatterns = append(d.javaPatterns, compiled)
		}
	}

	// .NET模式 - 检测.NET反序列化相关的响应内容
	dotnetPatternStrings := []string{
		// .NET序列化特征
		`(?i)(system\.runtime\.serialization|binaryformatter|soapformatter)`,
		`(?i)(system\.web\.ui\.losformatter|objectstateformatter|viewstate)`,
		`(?i)(newtonsoft\.json|json\.net|datacontractserializer)`,

		// .NET异常特征
		`(?i)(serializationexception|targetinvocationexception|typeloadexception)`,
		`(?i)(methodaccessexception|fieldaccessexception|memberaccessexception)`,
		`(?i)(argumentexception|invalidoperationexception|notimplementedexception)`,

		// .NET反序列化库特征
		`(?i)(system\.activities|system\.windows\.data|microsoft\.exchange)`,
		`(?i)(system\.configuration|system\.management\.automation)`,

		// ViewState特征
		`(?i)(__viewstate|__eventvalidation|__viewstategenerator)`,
		`(?i)(viewstate.*validation|mac.*validation|invalid.*viewstate)`,

		// 中文.NET特征
		`(?i)(dotnet序列化|二进制格式化|视图状态|对象状态)`,
		`(?i)(程序集加载|类型加载|方法调用|异常信息)`,
	}

	d.dotnetPatterns = make([]*regexp.Regexp, 0, len(dotnetPatternStrings))
	for _, pattern := range dotnetPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.dotnetPatterns = append(d.dotnetPatterns, compiled)
		}
	}

	// Python模式 - 检测Python反序列化相关的响应内容
	pythonPatternStrings := []string{
		// Python pickle特征
		`(?i)(pickle\.load|pickle\.loads|pickle\.dump|pickle\.dumps)`,
		`(?i)(cpickle\.load|cpickle\.loads|_pickle\.load|_pickle\.loads)`,
		`(?i)(pickle\.picklingerror|pickle\.unpicklingerror)`,

		// Python对象特征
		`(?i)(__reduce__|__reduce_ex__|__setstate__|__getstate__)`,
		`(?i)(__new__|__init__|__call__|__getattr__)`,

		// Python危险模块特征
		`(?i)(subprocess\.call|subprocess\.popen|os\.system|os\.popen)`,
		`(?i)(eval|exec|compile|__import__|importlib)`,

		// Python框架特征
		`(?i)(django\.core|flask\.sessions|celery\.canvas)`,
		`(?i)(itsdangerous|pyyaml|yaml\.load)`,

		// Python异常特征
		`(?i)(attributeerror|typeerror|valueerror|importerror)`,
		`(?i)(nameerror|syntaxerror|runtimeerror|systemexit)`,

		// 中文Python特征
		`(?i)(python序列化|pickle载荷|对象还原|代码执行)`,
		`(?i)(模块导入|函数调用|异常信息|堆栈跟踪)`,
	}

	d.pythonPatterns = make([]*regexp.Regexp, 0, len(pythonPatternStrings))
	for _, pattern := range pythonPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.pythonPatterns = append(d.pythonPatterns, compiled)
		}
	}

	// PHP模式 - 检测PHP反序列化相关的响应内容
	phpPatternStrings := []string{
		// PHP序列化特征
		`(?i)(serialize|unserialize|serialize_precision|unserialize_callback_func)`,
		`(?i)(o:\d+:|a:\d+:|s:\d+:|i:\d+:|b:[01];|d:[\d\.]+;|n;)`,

		// PHP魔术方法特征
		`(?i)(__construct|__destruct|__wakeup|__sleep|__toString)`,
		`(?i)(__call|__callstatic|__get|__set|__isset|__unset)`,

		// PHP对象注入特征
		`(?i)(stdclass|exception|error|throwable)`,
		`(?i)(reflection|closure|generator|weakref)`,

		// PHP框架特征
		`(?i)(symfony|laravel|zend|magento|wordpress|drupal)`,
		`(?i)(codeigniter|cakephp|yii|phalcon|slim)`,

		// PHP异常特征
		`(?i)(fatal.*error|parse.*error|notice|warning|deprecated)`,
		`(?i)(call.*undefined|class.*not.*found|function.*not.*found)`,

		// 中文PHP特征
		`(?i)(php序列化|对象注入|魔术方法|代码执行)`,
		`(?i)(类加载|方法调用|异常信息|错误信息)`,
	}

	d.phpPatterns = make([]*regexp.Regexp, 0, len(phpPatternStrings))
	for _, pattern := range phpPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.phpPatterns = append(d.phpPatterns, compiled)
		}
	}

	// 错误模式 - 检测反序列化错误相关的响应内容
	errorPatternStrings := []string{
		// 通用序列化错误
		`(?i)(serialization.*error|deserialization.*error|unmarshaling.*error)`,
		`(?i)(invalid.*format|corrupt.*data|malformed.*data)`,
		`(?i)(class.*not.*found|method.*not.*found|field.*not.*found)`,

		// 安全相关错误
		`(?i)(access.*denied|permission.*denied|security.*exception)`,
		`(?i)(unauthorized|forbidden|not.*allowed|restricted)`,

		// 类型相关错误
		`(?i)(type.*mismatch|cast.*exception|conversion.*error)`,
		`(?i)(invalid.*type|unsupported.*type|unknown.*type)`,

		// 中文错误
		`(?i)(序列化错误|反序列化错误|格式错误|数据损坏)`,
		`(?i)(类型错误|转换错误|访问拒绝|权限不足)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测成功反序列化相关的响应内容
	responsePatternStrings := []string{
		// 成功反序列化指示器
		`(?i)(deserialization.*success|unmarshaling.*success|object.*loaded)`,
		`(?i)(data.*restored|state.*restored|object.*created)`,

		// 执行成功指示器
		`(?i)(command.*executed|code.*executed|function.*called)`,
		`(?i)(method.*invoked|process.*started|task.*completed)`,

		// 对象状态指示器
		`(?i)(object.*initialized|instance.*created|class.*loaded)`,
		`(?i)(property.*set|field.*assigned|value.*updated)`,

		// 系统信息泄露
		`(?i)(system.*info|environment.*info|configuration.*info)`,
		`(?i)(version.*info|build.*info|debug.*info)`,

		// 中文响应
		`(?i)(反序列化成功|对象加载|数据恢复|状态还原)`,
		`(?i)(代码执行|方法调用|进程启动|任务完成)`,
		`(?i)(对象初始化|实例创建|类加载|属性设置)`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
