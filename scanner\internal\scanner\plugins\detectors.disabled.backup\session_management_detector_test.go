package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestSessionManagementDetectorBasicFunctionality 测试会话管理检测器基础功能
func TestSessionManagementDetectorBasicFunctionality(t *testing.T) {
	detector := NewSessionManagementDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "session-management-comprehensive", detector.GetID())
	assert.Equal(t, "会话管理漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-384")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestSessionManagementDetectorApplicability 测试会话管理检测器适用性
func TestSessionManagementDetectorApplicability(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试有会话Cookie的目标
	sessionCookieTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com",
		Protocol: "http",
		Port:     80,
		Cookies: []plugins.CookieInfo{
			{
				Name:  "PHPSESSID",
				Value: "abc123def456",
			},
		},
	}
	assert.True(t, detector.IsApplicable(sessionCookieTarget))

	// 测试有会话表单的目标
	sessionFormTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/login",
				Method: "POST",
				Fields: map[string]string{
					"username":    "text",
					"password":    "password",
					"csrf_token":  "hidden",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(sessionFormTarget))

	// 测试有会话头的目标
	sessionHeaderTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Set-Cookie": "sessionid=abc123; HttpOnly; Secure",
		},
	}
	assert.True(t, detector.IsApplicable(sessionHeaderTarget))

	// 测试登录相关URL
	loginTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(loginTarget))

	// 测试用户相关URL
	userTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/user/profile",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(userTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无会话功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestSessionManagementDetectorConfiguration 测试会话管理检测器配置
func TestSessionManagementDetectorConfiguration(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 12*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestSessionManagementDetectorCookies 测试会话管理检测器Cookie
func TestSessionManagementDetectorCookies(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 检查会话Cookie列表
	assert.NotEmpty(t, detector.sessionCookies)
	assert.Greater(t, len(detector.sessionCookies), 20)

	// 检查通用会话Cookie
	assert.Contains(t, detector.sessionCookies, "sessionid")
	assert.Contains(t, detector.sessionCookies, "session_id")
	assert.Contains(t, detector.sessionCookies, "PHPSESSID")
	assert.Contains(t, detector.sessionCookies, "JSESSIONID")
	assert.Contains(t, detector.sessionCookies, "ASP.NET_SessionId")
}

// TestSessionManagementDetectorAttributes 测试会话管理检测器安全属性
func TestSessionManagementDetectorAttributes(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 检查安全属性列表
	assert.NotEmpty(t, detector.securityAttributes)
	assert.Greater(t, len(detector.securityAttributes), 10)

	// 检查基础安全属性
	assert.Contains(t, detector.securityAttributes, "Secure")
	assert.Contains(t, detector.securityAttributes, "HttpOnly")
	assert.Contains(t, detector.securityAttributes, "SameSite")
	assert.Contains(t, detector.securityAttributes, "Expires")
	assert.Contains(t, detector.securityAttributes, "Max-Age")
}

// TestSessionManagementDetectorTokens 测试会话管理检测器CSRF令牌
func TestSessionManagementDetectorTokens(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 检查CSRF令牌列表
	assert.NotEmpty(t, detector.csrfTokenNames)
	assert.Greater(t, len(detector.csrfTokenNames), 15)

	// 检查通用CSRF令牌
	assert.Contains(t, detector.csrfTokenNames, "csrf_token")
	assert.Contains(t, detector.csrfTokenNames, "csrftoken")
	assert.Contains(t, detector.csrfTokenNames, "_token")
	assert.Contains(t, detector.csrfTokenNames, "authenticity_token")
	assert.Contains(t, detector.csrfTokenNames, "xsrf_token")
}

// TestSessionManagementDetectorPatterns 测试会话管理检测器模式
func TestSessionManagementDetectorPatterns(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 检查会话模式
	assert.NotEmpty(t, detector.sessionPatterns)
	assert.Greater(t, len(detector.sessionPatterns), 10)

	// 检查安全模式
	assert.NotEmpty(t, detector.securityPatterns)
	assert.Greater(t, len(detector.securityPatterns), 8)

	// 检查漏洞模式
	assert.NotEmpty(t, detector.vulnerabilityPatterns)
	assert.Greater(t, len(detector.vulnerabilityPatterns), 10)
}

// TestSessionManagementDetectorSessionFeatures 测试会话功能检查
func TestSessionManagementDetectorSessionFeatures(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试有会话Cookie的目标
	cookieTarget := &plugins.ScanTarget{
		Cookies: []plugins.CookieInfo{
			{
				Name:  "PHPSESSID",
				Value: "abc123",
			},
		},
	}
	assert.True(t, detector.hasSessionFeatures(cookieTarget))

	// 测试有会话表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{
				Fields: map[string]string{
					"csrf_token": "hidden",
				},
			},
		},
	}
	assert.True(t, detector.hasSessionFeatures(formTarget))

	// 测试有会话头的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Set-Cookie": "sessionid=abc123",
		},
	}
	assert.True(t, detector.hasSessionFeatures(headerTarget))

	// 测试无会话功能的目标
	simpleTarget := &plugins.ScanTarget{
		Cookies: []plugins.CookieInfo{},
		Forms:   []plugins.FormInfo{},
		Headers: map[string]string{},
	}
	assert.False(t, detector.hasSessionFeatures(simpleTarget))
}

// TestSessionManagementDetectorSessionID 测试会话ID提取
func TestSessionManagementDetectorSessionID(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试提取会话ID成功
	responseWithSession := `Status: 200 OK
Set-Cookie: PHPSESSID=abc123def456; HttpOnly; Secure
Content-Type: text/html

<html>
<body>
<h1>Welcome</h1>
</body>
</html>`

	sessionID := detector.extractSessionID(responseWithSession)
	assert.Equal(t, "abc123def456", sessionID)

	// 测试提取会话ID失败
	responseWithoutSession := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Welcome</h1>
</body>
</html>`

	noSessionID := detector.extractSessionID(responseWithoutSession)
	assert.Equal(t, "", noSessionID)
}

// TestSessionManagementDetectorSessionFixation 测试会话固定检查
func TestSessionManagementDetectorSessionFixation(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试会话固定（会话ID未改变）
	initialSessionID := "abc123"
	afterLoginSessionID := "abc123"
	response := "Login successful"

	confidence := detector.checkSessionFixation(initialSessionID, afterLoginSessionID, response)
	assert.Greater(t, confidence, 0.7)

	// 测试正常会话管理（会话ID已改变）
	initialSessionID2 := "abc123"
	afterLoginSessionID2 := "def456"
	response2 := "Login successful"

	confidence2 := detector.checkSessionFixation(initialSessionID2, afterLoginSessionID2, response2)
	assert.Less(t, confidence2, 0.3)
}

// TestSessionManagementDetectorCookieSecurity 测试Cookie安全属性检查
func TestSessionManagementDetectorCookieSecurity(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试缺少安全属性的Cookie
	insecureResponse := `Status: 200 OK
Set-Cookie: PHPSESSID=abc123def456
Content-Type: text/html`

	missingAttributes := detector.checkCookieSecurityAttributes(insecureResponse)
	assert.Greater(t, len(missingAttributes), 0)
	assert.Contains(t, missingAttributes, "Secure")
	assert.Contains(t, missingAttributes, "HttpOnly")
	assert.Contains(t, missingAttributes, "SameSite")

	// 测试安全的Cookie
	secureResponse := `Status: 200 OK
Set-Cookie: PHPSESSID=abc123def456; HttpOnly; Secure; SameSite=Strict
Content-Type: text/html`

	noMissingAttributes := detector.checkCookieSecurityAttributes(secureResponse)
	assert.Len(t, noMissingAttributes, 0)
}

// TestSessionManagementDetectorSessionTimeout 测试会话超时检查
func TestSessionManagementDetectorSessionTimeout(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试无超时设置的会话
	noTimeoutResponse := `Status: 200 OK
Set-Cookie: PHPSESSID=abc123def456; HttpOnly; Secure
Content-Type: text/html`

	timeoutIssues := detector.checkSessionTimeout(noTimeoutResponse)
	assert.Greater(t, len(timeoutIssues), 0)
	assert.Contains(t, timeoutIssues, "无过期时间设置")

	// 测试有超时设置的会话
	timeoutResponse := `Status: 200 OK
Set-Cookie: PHPSESSID=abc123def456; HttpOnly; Secure; Max-Age=3600
Content-Type: text/html`

	noTimeoutIssues := detector.checkSessionTimeout(timeoutResponse)
	assert.Len(t, noTimeoutIssues, 0)
}

// TestSessionManagementDetectorSessionPredictability 测试会话ID可预测性检查
func TestSessionManagementDetectorSessionPredictability(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试可预测的会话ID
	predictableSessionID := "123456"
	predictability := detector.checkSessionPredictability(predictableSessionID)
	assert.Greater(t, predictability, 0.7)

	// 测试短会话ID
	shortSessionID := "abc"
	shortPredictability := detector.checkSessionPredictability(shortSessionID)
	assert.Greater(t, shortPredictability, 0.3)

	// 测试安全的会话ID
	secureSessionID := "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
	securePredictability := detector.checkSessionPredictability(secureSessionID)
	assert.Less(t, securePredictability, 0.3)
}

// TestSessionManagementDetectorCSRFToken 测试CSRF令牌检查
func TestSessionManagementDetectorCSRFToken(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试有CSRF令牌的表单
	formWithCSRF := plugins.FormInfo{
		Fields: map[string]string{
			"username":   "text",
			"password":   "password",
			"csrf_token": "hidden",
		},
	}
	assert.True(t, detector.checkFormCSRFToken(formWithCSRF))

	// 测试无CSRF令牌的表单
	formWithoutCSRF := plugins.FormInfo{
		Fields: map[string]string{
			"username": "text",
			"password": "password",
		},
	}
	assert.False(t, detector.checkFormCSRFToken(formWithoutCSRF))
}

// TestSessionManagementDetectorRiskScore 测试风险评分计算
func TestSessionManagementDetectorRiskScore(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.5)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestSessionManagementDetectorLifecycle 测试检测器生命周期
func TestSessionManagementDetectorLifecycle(t *testing.T) {
	detector := NewSessionManagementDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkSessionManagementDetectorCookieCheck 基准测试Cookie检查性能
func BenchmarkSessionManagementDetectorCookieCheck(b *testing.B) {
	detector := NewSessionManagementDetector()
	response := `Status: 200 OK\nSet-Cookie: PHPSESSID=abc123def456; HttpOnly; Secure\nContent-Type: text/html`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkCookieSecurityAttributes(response)
	}
}

// BenchmarkSessionManagementDetectorSessionCheck 基准测试会话检查性能
func BenchmarkSessionManagementDetectorSessionCheck(b *testing.B) {
	detector := NewSessionManagementDetector()
	sessionID := "abc123def456ghi789"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkSessionPredictability(sessionID)
	}
}
