package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// BufferOverflowDetector 缓冲区溢出检测器
// 支持缓冲区溢出检测，包括栈溢出、堆溢出、格式化字符串溢出等多种缓冲区溢出检测技术
type BufferOverflowDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	stackOverflowPayloads    []string         // 栈溢出载荷
	heapOverflowPayloads     []string         // 堆溢出载荷
	formatStringPayloads     []string         // 格式化字符串载荷
	integerOverflowPayloads  []string         // 整数溢出载荷
	bufferUnderflowPayloads  []string         // 缓冲区下溢载荷
	memoryCorruptionPayloads []string         // 内存损坏载荷
	testParameters           []string         // 测试参数
	stackPatterns            []*regexp.Regexp // 栈溢出模式
	heapPatterns             []*regexp.Regexp // 堆溢出模式
	formatPatterns           []*regexp.Regexp // 格式化字符串模式
	integerPatterns          []*regexp.Regexp // 整数溢出模式
	errorPatterns            []*regexp.Regexp // 错误模式
	responsePatterns         []*regexp.Regexp // 响应模式
	httpClient               *http.Client
}

// NewBufferOverflowDetector 创建缓冲区溢出检测器
func NewBufferOverflowDetector() *BufferOverflowDetector {
	detector := &BufferOverflowDetector{
		id:          "buffer-overflow-comprehensive",
		name:        "缓冲区溢出漏洞综合检测器",
		category:    "web",
		severity:    "critical",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-120", "CWE-121", "CWE-122", "CWE-787", "CWE-788", "CWE-134"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测缓冲区溢出漏洞，包括栈溢出、堆溢出、格式化字符串溢出等多种缓冲区溢出检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 缓冲区溢出检测需要较长时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       2,
		FollowRedirects: true, // 跟随重定向检查溢出处理
		VerifySSL:       false,
		MaxResponseSize: 5 * 1024 * 1024, // 5MB，溢出响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeStackOverflowPayloads()
	detector.initializeHeapOverflowPayloads()
	detector.initializeFormatStringPayloads()
	detector.initializeIntegerOverflowPayloads()
	detector.initializeBufferUnderflowPayloads()
	detector.initializeMemoryCorruptionPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *BufferOverflowDetector) GetID() string            { return d.id }
func (d *BufferOverflowDetector) GetName() string          { return d.name }
func (d *BufferOverflowDetector) GetCategory() string      { return d.category }
func (d *BufferOverflowDetector) GetSeverity() string      { return d.severity }
func (d *BufferOverflowDetector) GetCVE() []string         { return d.cve }
func (d *BufferOverflowDetector) GetCWE() []string         { return d.cwe }
func (d *BufferOverflowDetector) GetVersion() string       { return d.version }
func (d *BufferOverflowDetector) GetAuthor() string        { return d.author }
func (d *BufferOverflowDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *BufferOverflowDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *BufferOverflowDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *BufferOverflowDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *BufferOverflowDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *BufferOverflowDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *BufferOverflowDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *BufferOverflowDetector) GetDependencies() []string         { return []string{} }
func (d *BufferOverflowDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *BufferOverflowDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *BufferOverflowDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *BufferOverflowDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *BufferOverflowDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *BufferOverflowDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *BufferOverflowDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.stackOverflowPayloads) == 0 {
		return fmt.Errorf("栈溢出载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *BufferOverflowDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 缓冲区溢出检测适用于有缓冲区处理功能的Web应用
	// 检查是否有缓冲区相关的特征
	if d.hasBufferFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于缓冲区相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	bufferKeywords := []string{
		"buffer", "memory", "stack", "heap", "overflow", "underflow",
		"format", "string", "input", "data", "upload", "file",
		"缓冲", "内存", "栈", "堆", "溢出", "下溢", "格式", "字符串",
	}

	for _, keyword := range bufferKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 缓冲区溢出是通用Web漏洞，默认适用于所有Web目标
}

// hasBufferFeatures 检查是否有缓冲区功能
func (d *BufferOverflowDetector) hasBufferFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有缓冲区相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "server" &&
			(strings.Contains(valueLower, "apache") ||
				strings.Contains(valueLower, "nginx") ||
				strings.Contains(valueLower, "iis") ||
				strings.Contains(valueLower, "tomcat")) {
			return true
		}

		if keyLower == "x-powered-by" &&
			(strings.Contains(valueLower, "php") ||
				strings.Contains(valueLower, "asp") ||
				strings.Contains(valueLower, "jsp") ||
				strings.Contains(valueLower, "python")) {
			return true
		}
	}

	// 检查技术栈中是否有缓冲区相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		bufferTechnologies := []string{
			"c", "c++", "php", "python", "java", "javascript", "node.js",
			"apache", "nginx", "iis", "tomcat", "mysql", "postgresql",
			"缓冲", "内存", "栈", "堆", "处理",
		}

		for _, bufferTech := range bufferTechnologies {
			if strings.Contains(techNameLower, bufferTech) {
				return true
			}
		}
	}

	// 检查链接中是否有缓冲区相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "buffer") ||
			strings.Contains(linkURLLower, "memory") ||
			strings.Contains(linkURLLower, "upload") ||
			strings.Contains(linkTextLower, "buffer") ||
			strings.Contains(linkTextLower, "memory") ||
			strings.Contains(linkTextLower, "缓冲") ||
			strings.Contains(linkTextLower, "内存") {
			return true
		}
	}

	// 检查表单中是否有缓冲区相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			bufferFields := []string{
				"buffer", "memory", "data", "input", "content", "message",
				"text", "comment", "description", "body", "file", "upload",
				"缓冲", "内存", "数据", "输入", "内容", "消息", "文本", "文件",
			}

			for _, bufferField := range bufferFields {
				if strings.Contains(fieldNameLower, bufferField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *BufferOverflowDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种缓冲区溢出检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 栈溢出检测
	stackEvidence, stackConfidence, stackPayload, stackRequest, stackResponse := d.detectStackOverflow(ctx, target)
	if stackConfidence > maxConfidence {
		maxConfidence = stackConfidence
		vulnerablePayload = stackPayload
		vulnerableRequest = stackRequest
		vulnerableResponse = stackResponse
	}
	evidence = append(evidence, stackEvidence...)

	// 2. 堆溢出检测
	heapEvidence, heapConfidence, heapPayload, heapRequest, heapResponse := d.detectHeapOverflow(ctx, target)
	if heapConfidence > maxConfidence {
		maxConfidence = heapConfidence
		vulnerablePayload = heapPayload
		vulnerableRequest = heapRequest
		vulnerableResponse = heapResponse
	}
	evidence = append(evidence, heapEvidence...)

	// 3. 格式化字符串溢出检测
	formatEvidence, formatConfidence, formatPayload, formatRequest, formatResponse := d.detectFormatStringOverflow(ctx, target)
	if formatConfidence > maxConfidence {
		maxConfidence = formatConfidence
		vulnerablePayload = formatPayload
		vulnerableRequest = formatRequest
		vulnerableResponse = formatResponse
	}
	evidence = append(evidence, formatEvidence...)

	// 4. 整数溢出检测
	integerEvidence, integerConfidence, integerPayload, integerRequest, integerResponse := d.detectIntegerOverflow(ctx, target)
	if integerConfidence > maxConfidence {
		maxConfidence = integerConfidence
		vulnerablePayload = integerPayload
		vulnerableRequest = integerRequest
		vulnerableResponse = integerResponse
	}
	evidence = append(evidence, integerEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "缓冲区溢出漏洞",
		Description:       "检测到缓冲区溢出漏洞，攻击者可能通过溢出缓冲区执行任意代码、获取系统权限或导致拒绝服务",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和限制输入长度，使用安全的字符串处理函数，实施栈保护和地址空间随机化",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Buffer_Overflow", "https://cwe.mitre.org/data/definitions/120.html", "https://cwe.mitre.org/data/definitions/121.html"},
		Tags:              []string{"buffer", "overflow", "stack", "heap", "format", "integer", "memory", "corruption", "web", "code-execution"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *BufferOverflowDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"stack-overflow",
		"heap-overflow",
		"format-string",
		"integer-overflow",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyBufferMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了缓冲区溢出漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "buffer-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用缓冲区验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *BufferOverflowDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("buffer_overflow_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *BufferOverflowDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (缓冲区溢出通常是严重漏洞)
	baseScore := 9.8

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyBufferMethod 验证缓冲区方法
func (d *BufferOverflowDetector) verifyBufferMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "stack-overflow":
		return d.verifyStackOverflow(ctx, target)
	case "heap-overflow":
		return d.verifyHeapOverflow(ctx, target)
	case "format-string":
		return d.verifyFormatString(ctx, target)
	case "integer-overflow":
		return d.verifyIntegerOverflow(ctx, target)
	default:
		return 0.0
	}
}

// verifyStackOverflow 验证栈溢出
func (d *BufferOverflowDetector) verifyStackOverflow(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的栈溢出验证
	if d.hasBufferFeatures(target) {
		return 0.8 // 有缓冲区特征的目标可能有栈溢出
	}
	return 0.4
}

// verifyHeapOverflow 验证堆溢出
func (d *BufferOverflowDetector) verifyHeapOverflow(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的堆溢出验证
	if d.hasBufferFeatures(target) {
		return 0.7 // 有缓冲区特征的目标可能有堆溢出
	}
	return 0.3
}

// verifyFormatString 验证格式化字符串
func (d *BufferOverflowDetector) verifyFormatString(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的格式化字符串验证
	if d.hasBufferFeatures(target) {
		return 0.6 // 有缓冲区特征的目标可能有格式化字符串漏洞
	}
	return 0.2
}

// verifyIntegerOverflow 验证整数溢出
func (d *BufferOverflowDetector) verifyIntegerOverflow(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的整数溢出验证
	if d.hasBufferFeatures(target) {
		return 0.6 // 有缓冲区特征的目标可能有整数溢出
	}
	return 0.2
}

// initializeStackOverflowPayloads 初始化栈溢出载荷列表
func (d *BufferOverflowDetector) initializeStackOverflowPayloads() {
	d.stackOverflowPayloads = []string{
		// 基础栈溢出载荷
		strings.Repeat("A", 100),
		strings.Repeat("A", 256),
		strings.Repeat("A", 512),
		strings.Repeat("A", 1024),
		strings.Repeat("A", 2048),
		strings.Repeat("A", 4096),
		strings.Repeat("A", 8192),
		strings.Repeat("A", 16384),

		// 模式化栈溢出载荷
		strings.Repeat("AAAA", 64),  // 256字节
		strings.Repeat("BBBB", 128), // 512字节
		strings.Repeat("CCCC", 256), // 1024字节
		strings.Repeat("DDDD", 512), // 2048字节

		// 特殊字符栈溢出载荷
		strings.Repeat("\x41", 1024),
		strings.Repeat("\x42", 2048),
		strings.Repeat("\x90", 1024), // NOP滑板
		strings.Repeat("\xCC", 512),  // 断点指令

		// 格式化字符串栈溢出
		strings.Repeat("%s", 100),
		strings.Repeat("%x", 100),
		strings.Repeat("%n", 50),
		strings.Repeat("%p", 100),

		// 混合栈溢出载荷
		strings.Repeat("A", 500) + strings.Repeat("B", 500),
		strings.Repeat("X", 256) + strings.Repeat("Y", 256) + strings.Repeat("Z", 256),
		strings.Repeat("\x41\x42\x43\x44", 256),

		// 中文栈溢出载荷
		strings.Repeat("测试", 512),
		strings.Repeat("缓冲区", 256),
		strings.Repeat("溢出", 1024),
		strings.Repeat("栈", 2048),
	}
}

// initializeHeapOverflowPayloads 初始化堆溢出载荷列表
func (d *BufferOverflowDetector) initializeHeapOverflowPayloads() {
	d.heapOverflowPayloads = []string{
		// 基础堆溢出载荷
		strings.Repeat("H", 1000),
		strings.Repeat("H", 2000),
		strings.Repeat("H", 4000),
		strings.Repeat("H", 8000),
		strings.Repeat("H", 16000),
		strings.Repeat("H", 32000),
		strings.Repeat("H", 65536),
		strings.Repeat("H", 131072),

		// 模式化堆溢出载荷
		strings.Repeat("HEAP", 250),  // 1000字节
		strings.Repeat("OVER", 500),  // 2000字节
		strings.Repeat("FLOW", 1000), // 4000字节
		strings.Repeat("BUFF", 2000), // 8000字节

		// 特殊字符堆溢出载荷
		strings.Repeat("\x48", 4000),  // H
		strings.Repeat("\x45", 8000),  // E
		strings.Repeat("\x41", 16000), // A
		strings.Repeat("\x50", 32000), // P

		// 堆喷射载荷
		strings.Repeat("\x0C\x0C\x0C\x0C", 4000),
		strings.Repeat("\x41\x41\x41\x41", 8000),
		strings.Repeat("\x90\x90\x90\x90", 16000),

		// 堆元数据破坏载荷
		strings.Repeat("A", 1000) + "\x00\x00\x00\x00" + strings.Repeat("B", 1000),
		strings.Repeat("X", 2000) + "\xFF\xFF\xFF\xFF" + strings.Repeat("Y", 2000),
		strings.Repeat("M", 4000) + "\x41\x42\x43\x44" + strings.Repeat("N", 4000),

		// 中文堆溢出载荷
		strings.Repeat("堆", 2000),
		strings.Repeat("内存", 1000),
		strings.Repeat("分配", 4000),
		strings.Repeat("释放", 8000),
	}
}

// initializeFormatStringPayloads 初始化格式化字符串载荷列表
func (d *BufferOverflowDetector) initializeFormatStringPayloads() {
	d.formatStringPayloads = []string{
		// 基础格式化字符串载荷
		"%s%s%s%s%s%s%s%s%s%s",
		"%x%x%x%x%x%x%x%x%x%x",
		"%p%p%p%p%p%p%p%p%p%p",
		"%n%n%n%n%n%n%n%n%n%n",

		// 栈读取载荷
		"%08x.%08x.%08x.%08x.%08x.%08x.%08x.%08x",
		"%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x",
		"%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p",

		// 内存写入载荷
		"AAAA%08x.%08x.%08x.%08x.%08x.%08x.%08x.%08x.%08x%n",
		"BBBB%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%n",
		"CCCC%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%n",

		// 直接参数访问载荷
		"%1$08x.%2$08x.%3$08x.%4$08x.%5$08x.%6$08x",
		"%7$08x.%8$08x.%9$08x.%10$08x.%11$08x.%12$08x",
		"%1$p.%2$p.%3$p.%4$p.%5$p.%6$p.%7$p.%8$p",

		// 宽度指定载荷
		"%*08x%*08x%*08x%*08x%*08x%*08x",
		"%*p%*p%*p%*p%*p%*p%*p%*p",
		"%*s%*s%*s%*s%*s%*s%*s%*s",

		// 长度修饰符载荷
		"%hn%hn%hn%hn%hn%hn%hn%hn",
		"%hhn%hhn%hhn%hhn%hhn%hhn%hhn%hhn",
		"%ln%ln%ln%ln%ln%ln%ln%ln",

		// 混合格式化字符串载荷
		"AAAA" + strings.Repeat("%08x.", 20) + "%n",
		"BBBB" + strings.Repeat("%x.", 30) + "%n",
		"CCCC" + strings.Repeat("%p.", 25) + "%n",

		// 中文格式化字符串载荷
		"测试%s%s%s%s%s",
		"格式%x%x%x%x%x",
		"字符串%p%p%p%p%p",
		"载荷%n%n%n%n%n",
	}
}

// initializeIntegerOverflowPayloads 初始化整数溢出载荷列表
func (d *BufferOverflowDetector) initializeIntegerOverflowPayloads() {
	d.integerOverflowPayloads = []string{
		// 32位整数溢出载荷
		"2147483647",  // INT_MAX
		"2147483648",  // INT_MAX + 1
		"4294967295",  // UINT_MAX
		"4294967296",  // UINT_MAX + 1
		"-2147483648", // INT_MIN
		"-2147483649", // INT_MIN - 1

		// 64位整数溢出载荷
		"9223372036854775807",  // LLONG_MAX
		"9223372036854775808",  // LLONG_MAX + 1
		"18446744073709551615", // ULLONG_MAX
		"18446744073709551616", // ULLONG_MAX + 1
		"-9223372036854775808", // LLONG_MIN
		"-9223372036854775809", // LLONG_MIN - 1

		// 16位整数溢出载荷
		"32767",  // SHRT_MAX
		"32768",  // SHRT_MAX + 1
		"65535",  // USHRT_MAX
		"65536",  // USHRT_MAX + 1
		"-32768", // SHRT_MIN
		"-32769", // SHRT_MIN - 1

		// 8位整数溢出载荷
		"127",  // CHAR_MAX
		"128",  // CHAR_MAX + 1
		"255",  // UCHAR_MAX
		"256",  // UCHAR_MAX + 1
		"-128", // CHAR_MIN
		"-129", // CHAR_MIN - 1

		// 特殊整数载荷
		"0",
		"-1",
		"999999999999999999999999999999",
		"-999999999999999999999999999999",

		// 十六进制整数载荷
		"0x7FFFFFFF",  // INT_MAX
		"0x80000000",  // INT_MAX + 1
		"0xFFFFFFFF",  // UINT_MAX
		"0x100000000", // UINT_MAX + 1

		// 八进制整数载荷
		"017777777777", // INT_MAX (octal)
		"020000000000", // INT_MAX + 1 (octal)
		"037777777777", // UINT_MAX (octal)

		// 中文整数载荷
		"最大整数2147483647",
		"最小整数-2147483648",
		"溢出4294967296",
		"下溢-2147483649",
	}
}

// initializeBufferUnderflowPayloads 初始化缓冲区下溢载荷列表
func (d *BufferOverflowDetector) initializeBufferUnderflowPayloads() {
	d.bufferUnderflowPayloads = []string{
		// 负数索引载荷
		"-1",
		"-10",
		"-100",
		"-1000",
		"-2147483648", // INT_MIN

		// 负数长度载荷
		"length=-1",
		"size=-10",
		"count=-100",
		"offset=-1000",

		// 负数偏移载荷
		"index=-1",
		"pos=-10",
		"start=-100",
		"begin=-1000",

		// 下溢边界载荷
		"0xFFFFFFFF", // -1 as unsigned
		"0xFFFFFFFE", // -2 as unsigned
		"0xFFFFFF00", // -256 as unsigned
		"0xFFFF0000", // -65536 as unsigned

		// 中文下溢载荷
		"负索引-1",
		"负长度-10",
		"负偏移-100",
		"下溢-1000",
	}
}

// initializeMemoryCorruptionPayloads 初始化内存损坏载荷列表
func (d *BufferOverflowDetector) initializeMemoryCorruptionPayloads() {
	d.memoryCorruptionPayloads = []string{
		// 空指针载荷
		"\x00\x00\x00\x00",
		"\x00\x00\x00\x00\x00\x00\x00\x00",
		"null",
		"NULL",
		"0x00000000",

		// 无效指针载荷
		"\xFF\xFF\xFF\xFF",
		"\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF",
		"0xFFFFFFFF",
		"0xFFFFFFFFFFFFFFFF",
		"0xDEADBEEF",
		"0xCAFEBABE",

		// 内存对齐载荷
		strings.Repeat("\x41", 3) + "\x00",  // 4字节对齐
		strings.Repeat("\x42", 7) + "\x00",  // 8字节对齐
		strings.Repeat("\x43", 15) + "\x00", // 16字节对齐
		strings.Repeat("\x44", 31) + "\x00", // 32字节对齐

		// 内存模式载荷
		"\xAA\xAA\xAA\xAA", // 10101010 pattern
		"\x55\x55\x55\x55", // 01010101 pattern
		"\xCC\xCC\xCC\xCC", // 11001100 pattern
		"\x33\x33\x33\x33", // 00110011 pattern

		// 堆元数据载荷
		"\x00\x00\x00\x08" + strings.Repeat("A", 8),  // 小块
		"\x00\x00\x00\x10" + strings.Repeat("B", 16), // 中块
		"\x00\x00\x00\x20" + strings.Repeat("C", 32), // 大块

		// 中文内存损坏载荷
		"空指针\x00\x00\x00\x00",
		"无效指针\xFF\xFF\xFF\xFF",
		"内存损坏" + strings.Repeat("\xCC", 10),
		"堆破坏" + strings.Repeat("\xAA", 20),
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *BufferOverflowDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 缓冲区相关参数
		"buffer", "buf", "data", "input", "content", "message", "text",
		"string", "str", "value", "val", "field", "param", "arg",
		"length", "len", "size", "count", "num", "amount", "quantity",
		"offset", "pos", "position", "index", "idx", "start", "begin",

		// 内存相关参数
		"memory", "mem", "heap", "stack", "ptr", "pointer", "addr", "address",
		"alloc", "malloc", "calloc", "realloc", "free", "new", "delete",
		"copy", "move", "set", "get", "read", "write", "load", "store",

		// 文件相关参数
		"file", "filename", "path", "filepath", "name", "upload", "download",
		"read", "write", "open", "close", "seek", "tell", "size", "length",

		// 网络相关参数
		"host", "hostname", "ip", "port", "url", "uri", "endpoint", "target",
		"request", "response", "header", "body", "payload", "packet", "frame",

		// 用户输入参数
		"user", "username", "password", "email", "phone", "comment", "review",
		"description", "title", "subject", "body", "content", "message", "note",

		// 数据库相关参数
		"query", "sql", "table", "column", "row", "record", "id", "key",
		"value", "data", "result", "output", "return", "response", "answer",

		// 格式化相关参数
		"format", "fmt", "template", "pattern", "mask", "filter", "transform",
		"convert", "parse", "encode", "decode", "serialize", "deserialize",

		// 中文参数
		"缓冲区", "缓存", "数据", "输入", "内容", "消息", "文本", "字符串",
		"长度", "大小", "数量", "偏移", "位置", "索引", "开始", "结束",
		"内存", "堆", "栈", "指针", "地址", "分配", "释放", "复制", "移动",
		"文件", "路径", "名称", "上传", "下载", "读取", "写入", "打开", "关闭",
		"用户", "用户名", "密码", "邮箱", "评论", "描述", "标题", "主题",
		"格式", "模板", "模式", "过滤", "转换", "解析", "编码", "解码",
	}
}

// initializePatterns 初始化检测模式
func (d *BufferOverflowDetector) initializePatterns() {
	// 栈溢出模式 - 检测栈溢出相关的响应内容
	stackPatternStrings := []string{
		// 栈溢出错误信息
		`(?i)stack.*overflow`,
		`(?i)stack.*smash`,
		`(?i)stack.*corruption`,
		`(?i)buffer.*overflow`,
		`(?i)segmentation.*fault`,
		`(?i)access.*violation`,
		`(?i)memory.*corruption`,

		// 栈保护机制触发
		`(?i)stack.*protector`,
		`(?i)canary.*died`,
		`(?i)stack.*guard`,
		`(?i)__stack_chk_fail`,
		`(?i)fortify.*source`,

		// 调试信息
		`(?i)core.*dump`,
		`(?i)signal.*11`,
		`(?i)sigsegv`,
		`(?i)sigbus`,
		`(?i)sigabrt`,

		// 中文栈溢出模式
		`(?i)(栈|堆栈).*溢出`,
		`(?i)(缓冲区|缓存).*溢出`,
		`(?i)(内存|存储).*错误`,
		`(?i)(段|分段).*错误`,
	}

	d.stackPatterns = make([]*regexp.Regexp, 0, len(stackPatternStrings))
	for _, pattern := range stackPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.stackPatterns = append(d.stackPatterns, compiled)
		}
	}

	// 堆溢出模式 - 检测堆溢出相关的响应内容
	heapPatternStrings := []string{
		// 堆溢出错误信息
		`(?i)heap.*overflow`,
		`(?i)heap.*corruption`,
		`(?i)heap.*buffer.*overflow`,
		`(?i)double.*free`,
		`(?i)use.*after.*free`,
		`(?i)invalid.*heap.*pointer`,
		`(?i)heap.*block.*corrupted`,

		// 堆管理器错误
		`(?i)malloc.*error`,
		`(?i)free.*error`,
		`(?i)realloc.*error`,
		`(?i)heap.*manager`,
		`(?i)memory.*allocator`,

		// 堆调试信息
		`(?i)heap.*debug`,
		`(?i)heap.*check`,
		`(?i)heap.*validate`,
		`(?i)heap.*walk`,

		// 中文堆溢出模式
		`(?i)堆.*溢出`,
		`(?i)堆.*损坏`,
		`(?i)内存.*分配.*错误`,
		`(?i)释放.*错误`,
	}

	d.heapPatterns = make([]*regexp.Regexp, 0, len(heapPatternStrings))
	for _, pattern := range heapPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.heapPatterns = append(d.heapPatterns, compiled)
		}
	}

	// 格式化字符串模式 - 检测格式化字符串相关的响应内容
	formatPatternStrings := []string{
		// 格式化字符串错误
		`(?i)format.*string`,
		`(?i)printf.*error`,
		`(?i)sprintf.*error`,
		`(?i)fprintf.*error`,
		`(?i)snprintf.*error`,
		`(?i)invalid.*format`,
		`(?i)format.*specifier`,

		// 格式化字符串攻击特征
		`(?i)%[0-9]*[xpns]`,
		`(?i)%[0-9]*\$[xpns]`,
		`(?i)%\*[xpns]`,
		`(?i)%h*n`,
		`(?i)%l*n`,

		// 栈内容泄露
		`(?i)0x[0-9a-f]{8}`,
		`(?i)[0-9a-f]{8}\.[0-9a-f]{8}`,
		`(?i)\(nil\)`,

		// 中文格式化字符串模式
		`(?i)格式.*字符串`,
		`(?i)格式.*错误`,
		`(?i)打印.*错误`,
		`(?i)输出.*错误`,
	}

	d.formatPatterns = make([]*regexp.Regexp, 0, len(formatPatternStrings))
	for _, pattern := range formatPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.formatPatterns = append(d.formatPatterns, compiled)
		}
	}

	// 整数溢出模式 - 检测整数溢出相关的响应内容
	integerPatternStrings := []string{
		// 整数溢出错误
		`(?i)integer.*overflow`,
		`(?i)integer.*underflow`,
		`(?i)arithmetic.*overflow`,
		`(?i)numeric.*overflow`,
		`(?i)value.*too.*large`,
		`(?i)value.*too.*small`,
		`(?i)out.*of.*range`,

		// 整数转换错误
		`(?i)conversion.*error`,
		`(?i)cast.*error`,
		`(?i)type.*error`,
		`(?i)size.*error`,
		`(?i)length.*error`,

		// 边界检查错误
		`(?i)bounds.*check`,
		`(?i)array.*bounds`,
		`(?i)index.*out.*of.*bounds`,
		`(?i)buffer.*bounds`,

		// 中文整数溢出模式
		`(?i)整数.*溢出`,
		`(?i)整数.*下溢`,
		`(?i)数值.*溢出`,
		`(?i)范围.*错误`,
	}

	d.integerPatterns = make([]*regexp.Regexp, 0, len(integerPatternStrings))
	for _, pattern := range integerPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.integerPatterns = append(d.integerPatterns, compiled)
		}
	}

	// 错误模式 - 检测缓冲区溢出相关的错误信息
	errorPatternStrings := []string{
		// 通用缓冲区错误
		`(?i)(buffer|memory).*error`,
		`(?i)(invalid|illegal).*buffer`,
		`(?i)(corrupt|corrupted).*buffer`,
		`(?i)(overflow|underflow).*detected`,
		`(?i)bounds.*violation`,

		// 系统错误
		`(?i)segmentation.*fault`,
		`(?i)access.*violation`,
		`(?i)general.*protection.*fault`,
		`(?i)page.*fault`,
		`(?i)memory.*protection`,

		// 运行时错误
		`(?i)runtime.*error`,
		`(?i)execution.*error`,
		`(?i)fatal.*error`,
		`(?i)critical.*error`,
		`(?i)system.*error`,

		// 中文错误模式
		`(?i)(缓冲区|内存).*错误`,
		`(?i)(无效|非法).*缓冲区`,
		`(?i)(损坏|破坏).*缓冲区`,
		`(?i)(溢出|下溢).*检测`,
		`(?i)边界.*违规`,
		`(?i)段.*错误`,
		`(?i)访问.*违规`,
		`(?i)运行时.*错误`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测缓冲区溢出成功的响应特征
	responsePatternStrings := []string{
		// 成功溢出指示器
		`(?i)(overflow|exploit).*success`,
		`(?i)(buffer|stack|heap).*overflowed`,
		`(?i)(code|shell).*execution`,
		`(?i)(command|system).*executed`,
		`(?i)(privilege|root).*access`,

		// 调试信息泄露
		`(?i)(debug|trace).*information`,
		`(?i)(stack|heap).*dump`,
		`(?i)(memory|address).*layout`,
		`(?i)(function|return).*address`,
		`(?i)(base|offset).*address`,

		// 系统信息泄露
		`(?i)(system|kernel).*information`,
		`(?i)(process|thread).*information`,
		`(?i)(module|library).*information`,
		`(?i)(environment|config).*variable`,

		// 控制流劫持
		`(?i)(control|flow).*hijack`,
		`(?i)(return|jump).*address`,
		`(?i)(function|procedure).*call`,
		`(?i)(instruction|code).*pointer`,

		// 中文响应模式
		`(?i)(溢出|利用).*成功`,
		`(?i)(缓冲区|栈|堆).*溢出`,
		`(?i)(代码|命令).*执行`,
		`(?i)(权限|管理员).*访问`,
		`(?i)(调试|跟踪).*信息`,
		`(?i)(系统|内核).*信息`,
		`(?i)(控制|流程).*劫持`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
