package detectors

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestXXEDetectorBasicFunctionality 测试XXE检测器基础功能
func TestXXEDetectorBasicFunctionality(t *testing.T) {
	detector := NewXXEDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "xxe-comprehensive", detector.GetID())
	assert.Equal(t, "XML外部实体注入(XXE)综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-611")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestXXEDetectorApplicability 测试XXE检测器适用性
func TestXXEDetectorApplicability(t *testing.T) {
	detector := NewXXEDetector()

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/upload",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/submit",
				Method: "POST",
				Fields: map[string]string{
					"xml": "textarea",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有XML技术栈的目标
	xmlTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "XML Parser", Version: "1.0", Confidence: 0.9},
		},
	}
	assert.True(t, detector.IsApplicable(xmlTarget))

	// 测试有SOAP技术栈的目标
	soapTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/webservice",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "SOAP", Version: "1.1", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(soapTarget))

	// 测试XML相关URL
	xmlURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/xml/process",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(xmlURLTarget))

	// 测试SOAP相关URL
	soapURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/soap/service.wsdl",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(soapURLTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无XML相关特征）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestXXEDetectorConfiguration 测试XXE检测器配置
func TestXXEDetectorConfiguration(t *testing.T) {
	detector := NewXXEDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.True(t, config.FollowRedirects) // XXE检测跟随重定向

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       10,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 4 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestXXEDetectorPayloads 测试XXE检测器载荷
func TestXXEDetectorPayloads(t *testing.T) {
	detector := NewXXEDetector()

	// 检查文件读取载荷
	assert.NotEmpty(t, detector.fileReadPayloads)
	assert.Greater(t, len(detector.fileReadPayloads), 5)

	// 检查SSRF载荷
	assert.NotEmpty(t, detector.ssrfPayloads)
	assert.Greater(t, len(detector.ssrfPayloads), 3)

	// 检查DoS载荷
	assert.NotEmpty(t, detector.dosPayloads)
	assert.Greater(t, len(detector.dosPayloads), 2)

	// 检查盲注载荷
	assert.NotEmpty(t, detector.blindPayloads)
	assert.Greater(t, len(detector.blindPayloads), 1)

	// 检查文件指示器
	assert.NotEmpty(t, detector.fileIndicators)
	assert.Greater(t, len(detector.fileIndicators), 30)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 10)
}

// TestXXEDetectorFileReadResponse 测试XXE文件读取响应检查
func TestXXEDetectorFileReadResponse(t *testing.T) {
	detector := NewXXEDetector()

	// 测试/etc/passwd文件内容
	passwdResponse := `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin`
	passwdPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>`
	confidence := detector.checkFileReadResponse(passwdResponse, passwdPayload)
	assert.Greater(t, confidence, 0.6)

	// 测试/etc/hosts文件内容
	hostsResponse := `127.0.0.1 localhost
::1 ip6-localhost ip6-loopback`
	hostsPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/hosts">]>`
	confidence2 := detector.checkFileReadResponse(hostsResponse, hostsPayload)
	assert.Greater(t, confidence2, 0.6)

	// 测试Windows文件内容
	windowsResponse := `[boot loader]
timeout=30
default=multi(0)disk(0)rdisk(0)partition(1)\WINDOWS`
	windowsPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///c:/boot.ini">]>`
	confidence3 := detector.checkFileReadResponse(windowsResponse, windowsPayload)
	assert.Greater(t, confidence3, 0.5)

	// 测试无XXE特征的响应
	normalResponse := "Hello World"
	normalPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>`
	confidence4 := detector.checkFileReadResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence4)
}

// TestXXEDetectorSSRFResponse 测试XXE SSRF响应检查
func TestXXEDetectorSSRFResponse(t *testing.T) {
	detector := NewXXEDetector()

	// 测试HTTP服务响应
	httpResponse := "HTTP/1.1 200 OK\nServer: nginx/1.18.0"
	httpPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://127.0.0.1:80">]>`
	confidence := detector.checkSSRFResponse(httpResponse, httpPayload)
	assert.Greater(t, confidence, 0.5)

	// 测试SSH服务响应
	sshResponse := "SSH-2.0-OpenSSH_7.4"
	sshPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://127.0.0.1:22">]>`
	confidence2 := detector.checkSSRFResponse(sshResponse, sshPayload)
	assert.Greater(t, confidence2, 0.3)

	// 测试云服务元数据响应
	cloudResponse := `{"instance-id": "i-1234567890abcdef0", "ami-id": "ami-0abcdef1234567890"}`
	cloudPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://169.254.169.254/latest/meta-data/">]>`
	confidence3 := detector.checkSSRFResponse(cloudResponse, cloudPayload)
	assert.Greater(t, confidence3, 0.4)

	// 测试无SSRF特征的响应
	normalResponse := "Hello World"
	normalPayload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://127.0.0.1:80">]>`
	confidence4 := detector.checkSSRFResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence4)
}

// TestXXEDetectorDoSResponse 测试XXE DoS响应检查
func TestXXEDetectorDoSResponse(t *testing.T) {
	detector := NewXXEDetector()

	// 测试超时错误
	timeoutError := fmt.Errorf("context deadline exceeded")
	dosPayload := `<!DOCTYPE lolz [<!ENTITY lol "lol"><!ENTITY lol2 "&lol;&lol;">]>`
	confidence := detector.checkDoSResponse("", timeoutError, dosPayload)
	assert.Greater(t, confidence, 0.5)

	// 测试内存错误响应
	memoryResponse := "out of memory: entity expansion limit exceeded"
	confidence2 := detector.checkDoSResponse(memoryResponse, nil, dosPayload)
	assert.Greater(t, confidence2, 0.4)

	// 测试递归错误响应
	recursionResponse := "recursion limit exceeded in entity expansion"
	recursionPayload := `<!DOCTYPE test [<!ENTITY a "&b;"><!ENTITY b "&a;">]>`
	confidence3 := detector.checkDoSResponse(recursionResponse, nil, recursionPayload)
	assert.Greater(t, confidence3, 0.4)

	// 测试正常响应
	normalResponse := "Hello World"
	normalPayload := `<!DOCTYPE test><test>hello</test>`
	confidence4 := detector.checkDoSResponse(normalResponse, nil, normalPayload)
	assert.Equal(t, 0.0, confidence4)
}

// TestXXEDetectorRiskScore 测试风险评分计算
func TestXXEDetectorRiskScore(t *testing.T) {
	detector := NewXXEDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestXXEDetectorLifecycle 测试检测器生命周期
func TestXXEDetectorLifecycle(t *testing.T) {
	detector := NewXXEDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkXXEDetectorFileReadCheck 基准测试文件读取检查性能
func BenchmarkXXEDetectorFileReadCheck(b *testing.B) {
	detector := NewXXEDetector()
	response := `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin`
	payload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkFileReadResponse(response, payload)
	}
}

// BenchmarkXXEDetectorSSRFCheck 基准测试SSRF检查性能
func BenchmarkXXEDetectorSSRFCheck(b *testing.B) {
	detector := NewXXEDetector()
	response := "HTTP/1.1 200 OK\nServer: nginx/1.18.0\nContent-Type: text/html"
	payload := `<!DOCTYPE test [<!ENTITY xxe SYSTEM "http://127.0.0.1:80">]>`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkSSRFResponse(response, payload)
	}
}
