package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// WeakPasswordDetector 弱密码检测器
// 支持Web登录页面发现、默认凭据检测、弱密码爆破等多种弱密码攻击检测
type WeakPasswordDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	loginPaths      []string         // 登录页面路径
	defaultCreds    []Credential     // 默认凭据
	commonCreds     []Credential     // 常见凭据
	weakPasswords   []string         // 弱密码列表
	commonUsernames []string         // 常见用户名
	loginIndicators []string         // 登录页面指示器
	authPatterns    []*regexp.Regexp // 认证模式
	successPatterns []*regexp.Regexp // 成功模式
	httpClient      *http.Client
}

// Credential 凭据结构
type Credential struct {
	Username string
	Password string
	Source   string // default, common, weak
}

// NewWeakPasswordDetector 创建弱密码检测器
func NewWeakPasswordDetector() *WeakPasswordDetector {
	detector := &WeakPasswordDetector{
		id:          "weak-password-comprehensive",
		name:        "弱密码漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // 弱密码是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-521", "CWE-798", "CWE-287"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测弱密码漏洞，包括登录页面发现、默认凭据检测、弱密码爆破等攻击",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         10 * time.Second, // 弱密码检测需要适中时间
		MaxRetries:      2,
		Concurrency:     2, // 较低并发，避免账户锁定
		RateLimit:       2, // 较低速率，避免触发防护
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeCredentials()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *WeakPasswordDetector) GetID() string                     { return d.id }
func (d *WeakPasswordDetector) GetName() string                   { return d.name }
func (d *WeakPasswordDetector) GetCategory() string               { return d.category }
func (d *WeakPasswordDetector) GetSeverity() string               { return d.severity }
func (d *WeakPasswordDetector) GetCVE() []string                  { return d.cve }
func (d *WeakPasswordDetector) GetCWE() []string                  { return d.cwe }
func (d *WeakPasswordDetector) GetVersion() string                { return d.version }
func (d *WeakPasswordDetector) GetAuthor() string                 { return d.author }
func (d *WeakPasswordDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *WeakPasswordDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *WeakPasswordDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *WeakPasswordDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *WeakPasswordDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *WeakPasswordDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *WeakPasswordDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *WeakPasswordDetector) GetDependencies() []string         { return []string{} }
func (d *WeakPasswordDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *WeakPasswordDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *WeakPasswordDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *WeakPasswordDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *WeakPasswordDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 10 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *WeakPasswordDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *WeakPasswordDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.loginPaths) == 0 {
		return fmt.Errorf("登录路径列表不能为空")
	}
	if len(d.defaultCreds) == 0 {
		return fmt.Errorf("默认凭据列表不能为空")
	}
	if len(d.loginIndicators) == 0 {
		return fmt.Errorf("登录指示器列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *WeakPasswordDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 弱密码检测适用于所有Web目标，因为很多应用都有认证功能
	// 但我们可以优先检查有表单的目标
	if len(target.Forms) > 0 {
		// 检查是否有登录表单
		for _, form := range target.Forms {
			for fieldName := range form.Fields {
				fieldLower := strings.ToLower(fieldName)
				if strings.Contains(fieldLower, "password") ||
					strings.Contains(fieldLower, "passwd") ||
					strings.Contains(fieldLower, "pwd") {
					return true
				}
			}
		}
	}

	// 检查是否有可能的认证相关的路径
	urlLower := strings.ToLower(target.URL)
	authIndicators := []string{
		"/login", "/signin", "/auth", "/admin", "/administrator",
		"/user", "/account", "/dashboard", "/panel", "/manage",
		"/wp-admin", "/wp-login", "/phpmyadmin", "/webmail",
	}

	for _, indicator := range authIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	// 对于其他Web目标，也进行检测（很多应用都有隐藏的认证功能）
	return true
}

// Detect 执行检测
func (d *WeakPasswordDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种弱密码检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 登录页面发现
	loginEvidence, loginConfidence, loginPayload, loginRequest, loginResponse := d.detectLoginPages(ctx, target)
	if loginConfidence > maxConfidence {
		maxConfidence = loginConfidence
		vulnerablePayload = loginPayload
		vulnerableRequest = loginRequest
		vulnerableResponse = loginResponse
	}
	evidence = append(evidence, loginEvidence...)

	// 2. 默认凭据检测
	defaultEvidence, defaultConfidence, defaultPayload, defaultRequest, defaultResponse := d.detectDefaultCredentials(ctx, target)
	if defaultConfidence > maxConfidence {
		maxConfidence = defaultConfidence
		vulnerablePayload = defaultPayload
		vulnerableRequest = defaultRequest
		vulnerableResponse = defaultResponse
	}
	evidence = append(evidence, defaultEvidence...)

	// 3. 弱密码检测
	weakEvidence, weakConfidence, weakPayload, weakRequest, weakResponse := d.detectWeakPasswords(ctx, target)
	if weakConfidence > maxConfidence {
		maxConfidence = weakConfidence
		vulnerablePayload = weakPayload
		vulnerableRequest = weakRequest
		vulnerableResponse = weakResponse
	}
	evidence = append(evidence, weakEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "弱密码漏洞",
		Description:       "检测到弱密码漏洞，攻击者可能能够通过弱密码或默认凭据获取系统访问权限",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "使用强密码策略，禁用默认账户，启用账户锁定机制，实施多因素认证",
		References:        []string{"https://owasp.org/www-community/attacks/Brute_force_attack", "https://cwe.mitre.org/data/definitions/521.html"},
		Tags:              []string{"weak-password", "authentication", "web", "medium"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *WeakPasswordDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的凭据进行验证
	verificationCreds := []Credential{
		{Username: "admin", Password: "admin", Source: "verification"},
		{Username: "admin", Password: "password", Source: "verification"},
		{Username: "test", Password: "test", Source: "verification"},
		{Username: "guest", Password: "guest", Source: "verification"},
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, cred := range verificationCreds {
		// 发送验证请求
		resp, err := d.sendAuthRequest(ctx, target.URL, cred.Username, cred.Password)
		if err != nil {
			continue
		}

		// 检查认证响应特征
		responseConfidence := d.checkAuthResponse(resp, cred)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证凭据触发了认证成功响应"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "auth-response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用认证响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *WeakPasswordDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("weak_password_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *WeakPasswordDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (弱密码通常是中等风险)
	baseScore := 6.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeCredentials 初始化凭据
func (d *WeakPasswordDetector) initializeCredentials() {
	// 登录页面路径
	d.loginPaths = []string{
		// 基础登录路径
		"/login",
		"/signin",
		"/auth",
		"/authentication",
		"/user/login",
		"/account/login",
		"/member/login",
		"/customer/login",

		// 管理员登录
		"/admin",
		"/admin/login",
		"/administrator",
		"/admin.php",
		"/admin/admin.php",
		"/admin/index.php",
		"/admin/auth.php",
		"/admin/signin",

		// CMS登录
		"/wp-admin",
		"/wp-login.php",
		"/wp-admin/admin.php",
		"/drupal/user/login",
		"/joomla/administrator",

		// 应用登录
		"/phpmyadmin",
		"/phpmyadmin/index.php",
		"/webmail",
		"/webmail/login",
		"/mail/login",
		"/cpanel",
		"/plesk",

		// API认证
		"/api/login",
		"/api/auth",
		"/api/signin",
		"/rest/auth",
		"/oauth/login",

		// 其他常见路径
		"/portal",
		"/dashboard",
		"/panel",
		"/manage",
		"/console",
		"/control",
	}

	// 默认凭据
	d.defaultCreds = []Credential{
		// 系统默认
		{Username: "admin", Password: "admin", Source: "default"},
		{Username: "admin", Password: "password", Source: "default"},
		{Username: "admin", Password: "123456", Source: "default"},
		{Username: "admin", Password: "", Source: "default"},
		{Username: "administrator", Password: "administrator", Source: "default"},
		{Username: "administrator", Password: "password", Source: "default"},
		{Username: "root", Password: "root", Source: "default"},
		{Username: "root", Password: "password", Source: "default"},
		{Username: "root", Password: "123456", Source: "default"},
		{Username: "root", Password: "", Source: "default"},

		// 应用默认
		{Username: "admin", Password: "admin123", Source: "default"},
		{Username: "admin", Password: "password123", Source: "default"},
		{Username: "admin", Password: "12345", Source: "default"},
		{Username: "admin", Password: "1234", Source: "default"},
		{Username: "test", Password: "test", Source: "default"},
		{Username: "guest", Password: "guest", Source: "default"},
		{Username: "user", Password: "user", Source: "default"},
		{Username: "demo", Password: "demo", Source: "default"},

		// 数据库默认
		{Username: "sa", Password: "", Source: "default"},
		{Username: "sa", Password: "sa", Source: "default"},
		{Username: "mysql", Password: "mysql", Source: "default"},
		{Username: "postgres", Password: "postgres", Source: "default"},
		{Username: "oracle", Password: "oracle", Source: "default"},

		// Web应用默认
		{Username: "webadmin", Password: "webadmin", Source: "default"},
		{Username: "siteadmin", Password: "siteadmin", Source: "default"},
		{Username: "manager", Password: "manager", Source: "default"},
		{Username: "operator", Password: "operator", Source: "default"},
	}

	// 常见凭据
	d.commonCreds = []Credential{
		// 常见组合
		{Username: "admin", Password: "admin", Source: "common"},
		{Username: "admin", Password: "password", Source: "common"},
		{Username: "admin", Password: "123456", Source: "common"},
		{Username: "admin", Password: "12345678", Source: "common"},
		{Username: "admin", Password: "qwerty", Source: "common"},
		{Username: "admin", Password: "abc123", Source: "common"},
		{Username: "user", Password: "password", Source: "common"},
		{Username: "test", Password: "test123", Source: "common"},
		{Username: "guest", Password: "123456", Source: "common"},

		// 弱密码组合
		{Username: "admin", Password: "pass", Source: "common"},
		{Username: "admin", Password: "pwd", Source: "common"},
		{Username: "admin", Password: "secret", Source: "common"},
		{Username: "admin", Password: "login", Source: "common"},
		{Username: "admin", Password: "welcome", Source: "common"},
		{Username: "admin", Password: "changeme", Source: "common"},
		{Username: "admin", Password: "letmein", Source: "common"},
		{Username: "admin", Password: "default", Source: "common"},
	}

	// 弱密码列表
	d.weakPasswords = []string{
		// 数字密码
		"123456", "12345678", "123456789", "1234567890",
		"12345", "1234", "123", "111111", "000000",
		"654321", "987654321", "1111", "2222", "3333",

		// 字母密码
		"password", "admin", "root", "user", "test",
		"guest", "qwerty", "abc123", "password123",
		"admin123", "root123", "test123", "pass",
		"pwd", "secret", "login", "welcome", "changeme",
		"letmein", "default", "master", "super",

		// 键盘模式
		"qwerty", "asdf", "zxcv", "qwertyuiop",
		"asdfghjkl", "zxcvbnm", "1qaz2wsx",
		"qazwsx", "123qwe", "qwe123",

		// 常见单词
		"love", "god", "sex", "money", "home",
		"work", "life", "family", "friend", "happy",
		"computer", "internet", "windows", "linux",
		"apple", "google", "facebook", "twitter",
	}

	// 常见用户名
	d.commonUsernames = []string{
		"admin", "administrator", "root", "user", "test",
		"guest", "demo", "operator", "manager", "webadmin",
		"siteadmin", "sa", "mysql", "postgres", "oracle",
		"www", "web", "ftp", "mail", "email", "support",
		"service", "system", "backup", "monitor", "log",
	}

	// 登录页面指示器
	d.loginIndicators = []string{
		// 表单元素
		"password", "passwd", "pwd", "username", "user",
		"email", "login", "signin", "auth", "authentication",

		// 页面内容
		"sign in", "log in", "login", "signin", "authentication",
		"用户名", "密码", "登录", "登陆", "验证", "认证",
		"username", "password", "email", "account",

		// 表单属性
		"type=\"password\"", "name=\"password\"", "id=\"password\"",
		"type=\"email\"", "name=\"username\"", "id=\"username\"",
		"name=\"user\"", "id=\"user\"", "name=\"email\"",

		// 按钮文本
		"submit", "login", "signin", "authenticate", "enter",
		"登录", "登陆", "提交", "确认", "验证", "进入",
	}
}

// initializePatterns 初始化检测模式
func (d *WeakPasswordDetector) initializePatterns() {
	// 认证模式 - 检测登录表单
	authPatterns := []string{
		`<input[^>]*type\s*=\s*["']?password["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?password["']?[^>]*>`,
		`<input[^>]*id\s*=\s*["']?password["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?passwd["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?pwd["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?username["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?user["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?email["']?[^>]*>`,
		`<form[^>]*action[^>]*login[^>]*>`,
		`<form[^>]*action[^>]*signin[^>]*>`,
		`<form[^>]*action[^>]*auth[^>]*>`,
		`<button[^>]*type\s*=\s*["']?submit["']?[^>]*>.*?login`,
		`<button[^>]*type\s*=\s*["']?submit["']?[^>]*>.*?signin`,
		`<input[^>]*type\s*=\s*["']?submit["']?[^>]*value[^>]*login`,
		`<input[^>]*type\s*=\s*["']?submit["']?[^>]*value[^>]*signin`,
	}

	d.authPatterns = make([]*regexp.Regexp, 0, len(authPatterns))
	for _, pattern := range authPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.authPatterns = append(d.authPatterns, compiled)
		}
	}

	// 成功模式 - 检测认证成功的响应特征
	successPatterns := []string{
		// 成功登录指示器
		`welcome\s+back`,
		`login\s+successful`,
		`authentication\s+successful`,
		`logged\s+in\s+successfully`,
		`sign\s+in\s+successful`,
		`welcome\s+to\s+dashboard`,
		`welcome\s+to\s+admin`,
		`welcome\s+to\s+panel`,

		// 重定向指示器
		`location:\s*/dashboard`,
		`location:\s*/admin`,
		`location:\s*/panel`,
		`location:\s*/home`,
		`location:\s*/profile`,
		`location:\s*/account`,
		`location:\s*/welcome`,

		// 页面内容指示器
		`dashboard`,
		`admin\s+panel`,
		`control\s+panel`,
		`management\s+console`,
		`user\s+profile`,
		`account\s+settings`,
		`logout`,
		`sign\s+out`,
		`注销`,
		`退出`,

		// 中文指示器
		`欢迎回来`,
		`登录成功`,
		`认证成功`,
		`欢迎来到`,
		`管理面板`,
		`控制台`,
		`用户中心`,
		`个人资料`,

		// Cookie指示器
		`set-cookie:.*session`,
		`set-cookie:.*auth`,
		`set-cookie:.*login`,
		`set-cookie:.*token`,
		`set-cookie:.*user`,

		// JavaScript指示器
		`window\.location\s*=\s*["']/dashboard`,
		`window\.location\s*=\s*["']/admin`,
		`window\.location\s*=\s*["']/panel`,
		`document\.location\s*=\s*["']/dashboard`,
		`document\.location\s*=\s*["']/admin`,
	}

	d.successPatterns = make([]*regexp.Regexp, 0, len(successPatterns))
	for _, pattern := range successPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.successPatterns = append(d.successPatterns, compiled)
		}
	}
}
