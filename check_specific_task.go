package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 查询任务ID 311的信息收集数据
	var infoData sql.NullString
	err = db.QueryRow("SELECT info_gathering_data FROM scan_tasks WHERE id = 311").Scan(&infoData)
	if err != nil {
		log.Fatal("查询失败:", err)
	}

	fmt.Println("=== 任务ID 311的信息收集数据 ===")
	if infoData.Valid && infoData.String != "" {
		fmt.Printf("数据长度: %d 字符\n", len(infoData.String))
		fmt.Println("数据内容:")
		fmt.Println(infoData.String)
	} else {
		fmt.Println("没有信息收集数据")
	}
}
