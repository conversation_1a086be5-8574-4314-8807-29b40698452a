package detectors

import (
	"context"
	"net/http"
	"net/http/httptest"
	"sync/atomic"
	"testing"
	"time"

	"scanner/internal/scanner/plugins"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAPIRateLimitingDetector_BasicFunctionality 测试基本功能
func TestAPIRateLimitingDetector_BasicFunctionality(t *testing.T) {
	detector := NewAPIRateLimitingDetector()

	// 测试基本属性
	assert.Equal(t, "api-rate-limiting-detector", detector.GetID())
	assert.Equal(t, "API速率限制检测器", detector.GetName())
	assert.Equal(t, "api_security", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.True(t, detector.IsEnabled())

	// 测试版本和作者信息
	assert.Equal(t, "1.0.0", detector.GetVersion())
	assert.Equal(t, "Security Scanner Team", detector.GetAuthor())

	// 测试CVE和CWE
	assert.Empty(t, detector.GetCVE())
	assert.Contains(t, detector.GetCWE(), "CWE-770")
	assert.Contains(t, detector.GetCWE(), "CWE-400")
}

// TestAPIRateLimitingDetector_IsApplicable 测试适用性检查
func TestAPIRateLimitingDetector_IsApplicable(t *testing.T) {
	detector := NewAPIRateLimitingDetector()

	tests := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "API URL with /api/ path",
			target: &plugins.ScanTarget{
				Type:     "url",
				Protocol: "https",
				URL:      "https://example.com/api/users",
			},
			expected: true,
		},
		{
			name: "REST API URL",
			target: &plugins.ScanTarget{
				Type:     "url",
				Protocol: "http",
				URL:      "http://api.example.com/rest/v1/data",
			},
			expected: true,
		},
		{
			name: "GraphQL endpoint",
			target: &plugins.ScanTarget{
				Type:     "url",
				Protocol: "https",
				URL:      "https://example.com/graphql",
			},
			expected: true,
		},
		{
			name: "JSON API endpoint",
			target: &plugins.ScanTarget{
				Type:     "url",
				Protocol: "https",
				URL:      "https://example.com/data.json",
			},
			expected: true,
		},
		{
			name: "Regular web page",
			target: &plugins.ScanTarget{
				Type:     "url",
				Protocol: "https",
				URL:      "https://example.com/index.html",
			},
			expected: false,
		},
		{
			name: "Non-HTTP protocol",
			target: &plugins.ScanTarget{
				Type:     "tcp",
				Protocol: "tcp",
				URL:      "tcp://example.com:22",
			},
			expected: false,
		},
		{
			name: "API technology stack",
			target: &plugins.ScanTarget{
				Type:     "url",
				Protocol: "https",
				URL:      "https://example.com/service",
				Technologies: []plugins.TechnologyInfo{
					{Name: "REST API", Category: "api"},
				},
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detector.IsApplicable(tt.target)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestAPIRateLimitingDetector_Configuration 测试配置功能
func TestAPIRateLimitingDetector_Configuration(t *testing.T) {
	detector := NewAPIRateLimitingDetector()

	// 测试默认配置
	assert.Equal(t, 20, detector.requestCount)
	assert.Equal(t, 100*time.Millisecond, detector.requestInterval)

	// 测试设置配置
	config := &plugins.DetectorConfig{
		Parameters: map[string]interface{}{
			"request_count":    10,
			"request_interval": "200ms",
		},
	}

	err := detector.SetConfiguration(config)
	assert.NoError(t, err)
	assert.Equal(t, 10, detector.requestCount)
	assert.Equal(t, 200*time.Millisecond, detector.requestInterval)
}

// TestAPIRateLimitingDetector_DetectWithoutRateLimit 测试检测无速率限制的API
func TestAPIRateLimitingDetector_DetectWithoutRateLimit(t *testing.T) {
	// 创建测试服务器 - 无速率限制
	var requestCount int64
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		atomic.AddInt64(&requestCount, 1)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer server.Close()

	detector := NewAPIRateLimitingDetector()
	// 减少测试请求数量以加快测试
	detector.requestCount = 5
	detector.requestInterval = 10 * time.Millisecond

	target := &plugins.ScanTarget{
		Type:     "url",
		Protocol: "http",
		URL:      server.URL + "/api/test",
		Domain:   "localhost",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := detector.Detect(ctx, target)
	require.NoError(t, err)
	require.NotNil(t, result)

	// 验证检测结果
	assert.True(t, result.IsVulnerable, "应该检测到缺乏速率限制")
	assert.Greater(t, result.Confidence, 0.7, "置信度应该较高")
	assert.Equal(t, "API速率限制缺失", result.Title)
	assert.Equal(t, "api_security", result.Category)
	assert.Contains(t, result.Description, "缺乏速率限制保护")
	assert.NotEmpty(t, result.Evidence)
	assert.Greater(t, result.RiskScore, 0.0)

	// 验证实际发送了请求
	assert.Equal(t, int64(5), atomic.LoadInt64(&requestCount))
}

// TestAPIRateLimitingDetector_DetectWithRateLimit 测试检测有速率限制的API
func TestAPIRateLimitingDetector_DetectWithRateLimit(t *testing.T) {
	// 创建测试服务器 - 有速率限制
	var requestCount int64
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		count := atomic.AddInt64(&requestCount, 1)
		
		if count > 3 {
			// 模拟速率限制
			w.Header().Set("X-RateLimit-Limit", "3")
			w.Header().Set("X-RateLimit-Remaining", "0")
			w.Header().Set("X-RateLimit-Reset", "60")
			w.WriteHeader(http.StatusTooManyRequests)
			w.Write([]byte(`{"error": "Rate limit exceeded"}`))
		} else {
			w.Header().Set("X-RateLimit-Limit", "3")
			w.Header().Set("X-RateLimit-Remaining", "2")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status": "ok"}`))
		}
	}))
	defer server.Close()

	detector := NewAPIRateLimitingDetector()
	detector.requestCount = 5
	detector.requestInterval = 10 * time.Millisecond

	target := &plugins.ScanTarget{
		Type:     "url",
		Protocol: "http",
		URL:      server.URL + "/api/test",
		Domain:   "localhost",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := detector.Detect(ctx, target)
	require.NoError(t, err)
	require.NotNil(t, result)

	// 验证检测结果
	assert.False(t, result.IsVulnerable, "应该检测到有速率限制保护")
	assert.Greater(t, result.Confidence, 0.8, "置信度应该很高")
	assert.Contains(t, result.Description, "具有速率限制保护")
	assert.NotEmpty(t, result.Evidence)
}

// TestAPIRateLimitingDetector_DetectWithRateLimitHeaders 测试检测有速率限制头的API
func TestAPIRateLimitingDetector_DetectWithRateLimitHeaders(t *testing.T) {
	// 创建测试服务器 - 有速率限制头但不阻止请求
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("X-RateLimit-Limit", "100")
		w.Header().Set("X-RateLimit-Remaining", "95")
		w.Header().Set("X-RateLimit-Reset", "3600")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer server.Close()

	detector := NewAPIRateLimitingDetector()
	detector.requestCount = 3
	detector.requestInterval = 10 * time.Millisecond

	target := &plugins.ScanTarget{
		Type:     "url",
		Protocol: "http",
		URL:      server.URL + "/api/test",
		Domain:   "localhost",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := detector.Detect(ctx, target)
	require.NoError(t, err)
	require.NotNil(t, result)

	// 验证检测结果
	assert.False(t, result.IsVulnerable, "应该检测到有速率限制保护")
	assert.Greater(t, result.Confidence, 0.8, "置信度应该很高")
	assert.Contains(t, result.Description, "具有速率限制保护")
}

// TestAPIRateLimitingDetector_Verify 测试验证功能
func TestAPIRateLimitingDetector_Verify(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer server.Close()

	detector := NewAPIRateLimitingDetector()
	detector.requestCount = 3
	detector.requestInterval = 10 * time.Millisecond

	target := &plugins.ScanTarget{
		Type:     "url",
		Protocol: "http",
		URL:      server.URL + "/api/test",
		Domain:   "localhost",
	}

	// 创建一个漏洞检测结果
	detectionResult := &plugins.DetectionResult{
		IsVulnerable: true,
		Confidence:   0.8,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	verifyResult, err := detector.Verify(ctx, target, detectionResult)
	require.NoError(t, err)
	require.NotNil(t, verifyResult)

	assert.Equal(t, "rate-limit-retest", verifyResult.Method)
	assert.NotEmpty(t, verifyResult.Notes)
}

// TestAPIRateLimitingDetector_ContextCancellation 测试上下文取消
func TestAPIRateLimitingDetector_ContextCancellation(t *testing.T) {
	// 创建慢响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer server.Close()

	detector := NewAPIRateLimitingDetector()
	detector.requestCount = 10
	detector.requestInterval = 50 * time.Millisecond

	target := &plugins.ScanTarget{
		Type:     "url",
		Protocol: "http",
		URL:      server.URL + "/api/test",
		Domain:   "localhost",
	}

	// 创建会很快取消的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	defer cancel()

	result, err := detector.Detect(ctx, target)
	
	// 应该因为上下文取消而提前结束，但不应该返回错误
	// 因为我们在检测逻辑中处理了上下文取消
	assert.NotNil(t, result)
	// err可能是nil或context.DeadlineExceeded，取决于具体的取消时机
}

// TestAPIRateLimitingDetector_Validation 测试验证功能
func TestAPIRateLimitingDetector_Validation(t *testing.T) {
	detector := NewAPIRateLimitingDetector()

	// 测试正常配置
	err := detector.Validate()
	assert.NoError(t, err)

	// 测试无效配置
	detector.requestCount = 0
	err = detector.Validate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "请求数量必须大于0")

	detector.requestCount = 5
	detector.requestInterval = 0
	err = detector.Validate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "请求间隔必须大于0")
}

// BenchmarkAPIRateLimitingDetector 基准测试
func BenchmarkAPIRateLimitingDetector(b *testing.B) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "ok"}`))
	}))
	defer server.Close()

	detector := NewAPIRateLimitingDetector()
	detector.requestCount = 3 // 减少请求数量以加快基准测试
	detector.requestInterval = 1 * time.Millisecond

	target := &plugins.ScanTarget{
		Type:     "url",
		Protocol: "http",
		URL:      server.URL + "/api/test",
		Domain:   "localhost",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		_, _ = detector.Detect(ctx, target)
		cancel()
	}
}
