package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// CSRFDetector 跨站请求伪造(CSRF)检测器
// 支持CSRF令牌检测、SameSite属性检查、Referer验证等多种CSRF防护机制检测
type CSRFDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	csrfTokenPatterns []string         // CSRF令牌模式
	formPatterns      []*regexp.Regexp // 表单模式
	cookiePatterns    []*regexp.Regexp // Cookie模式
	headerPatterns    []*regexp.Regexp // 头部模式
	httpClient        *http.Client
}

// NewCSRFDetector 创建CSRF检测器
func NewCSRFDetector() *CSRFDetector {
	detector := &CSRFDetector{
		id:          "csrf-comprehensive",
		name:        "跨站请求伪造(CSRF)综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // CSRF是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-352", "CWE-346"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测跨站请求伪造漏洞，包括CSRF令牌缺失、SameSite属性缺失、Referer验证缺失等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         10 * time.Second, // CSRF检测相对较快
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       10,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，主要检查HTML内容
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化模式
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *CSRFDetector) GetID() string                     { return d.id }
func (d *CSRFDetector) GetName() string                   { return d.name }
func (d *CSRFDetector) GetCategory() string               { return d.category }
func (d *CSRFDetector) GetSeverity() string               { return d.severity }
func (d *CSRFDetector) GetCVE() []string                  { return d.cve }
func (d *CSRFDetector) GetCWE() []string                  { return d.cwe }
func (d *CSRFDetector) GetVersion() string                { return d.version }
func (d *CSRFDetector) GetAuthor() string                 { return d.author }
func (d *CSRFDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *CSRFDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *CSRFDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *CSRFDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *CSRFDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *CSRFDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *CSRFDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *CSRFDetector) GetDependencies() []string         { return []string{} }
func (d *CSRFDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *CSRFDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *CSRFDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *CSRFDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *CSRFDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 10 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *CSRFDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *CSRFDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.csrfTokenPatterns) == 0 {
		return fmt.Errorf("CSRF令牌模式列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *CSRFDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// CSRF检测需要有表单或可能的状态改变操作
	if len(target.Forms) > 0 {
		return true
	}

	// 检查是否有可能的状态改变操作的链接
	for _, link := range target.Links {
		linkLower := strings.ToLower(link.URL)
		// 检查是否包含可能的状态改变操作
		stateChangeIndicators := []string{
			"delete", "remove", "update", "edit", "modify",
			"create", "add", "post", "submit", "save",
			"login", "logout", "register", "signup",
			"transfer", "payment", "buy", "purchase",
		}

		for _, indicator := range stateChangeIndicators {
			if strings.Contains(linkLower, indicator) {
				return true
			}
		}
	}

	// 检查URL路径是否暗示状态改变操作
	urlLower := strings.ToLower(target.URL)
	stateChangeURLs := []string{
		"/admin", "/dashboard", "/profile", "/settings",
		"/api", "/ajax", "/action", "/do", "/exec",
		"/login", "/logout", "/register", "/signup",
		"/delete", "/remove", "/update", "/edit",
		"/create", "/add", "/post", "/submit",
	}

	for _, indicator := range stateChangeURLs {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *CSRFDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种CSRF检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. CSRF令牌检测
	tokenEvidence, tokenConfidence, tokenRequest, tokenResponse := d.detectCSRFToken(ctx, target)
	if tokenConfidence > maxConfidence {
		maxConfidence = tokenConfidence
		vulnerableRequest = tokenRequest
		vulnerableResponse = tokenResponse
	}
	evidence = append(evidence, tokenEvidence...)

	// 2. SameSite Cookie检测
	cookieEvidence, cookieConfidence, cookieRequest, cookieResponse := d.detectSameSiteCookie(ctx, target)
	if cookieConfidence > maxConfidence {
		maxConfidence = cookieConfidence
		vulnerableRequest = cookieRequest
		vulnerableResponse = cookieResponse
	}
	evidence = append(evidence, cookieEvidence...)

	// 3. Referer验证检测
	refererEvidence, refererConfidence, refererRequest, refererResponse := d.detectRefererValidation(ctx, target)
	if refererConfidence > maxConfidence {
		maxConfidence = refererConfidence
		vulnerableRequest = refererRequest
		vulnerableResponse = refererResponse
	}
	evidence = append(evidence, refererEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "跨站请求伪造(CSRF)漏洞",
		Description:       "检测到跨站请求伪造漏洞，攻击者可能能够强制用户执行非预期的操作",
		Evidence:          evidence,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施CSRF令牌验证，使用SameSite Cookie属性，验证HTTP Referer头，使用双重提交Cookie模式",
		References:        []string{"https://owasp.org/www-community/attacks/csrf", "https://cwe.mitre.org/data/definitions/352.html"},
		Tags:              []string{"csrf", "web", "session", "authentication"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *CSRFDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 进行更深入的验证
	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 验证表单是否真的缺少CSRF保护
	if len(target.Forms) > 0 {
		for _, form := range target.Forms {
			hasCSRFProtection := d.checkFormCSRFProtection(form)
			if !hasCSRFProtection {
				verificationConfidence += 0.4
				evidence = append(evidence, plugins.Evidence{
					Type:        "form-analysis",
					Description: fmt.Sprintf("表单 %s 缺少CSRF保护", form.Action),
					Content:     fmt.Sprintf("Method: %s, Fields: %v", form.Method, form.Fields),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "form-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用表单分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *CSRFDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("csrf_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *CSRFDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (CSRF通常是中等风险)
	baseScore := 6.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePatterns 初始化检测模式
func (d *CSRFDetector) initializePatterns() {
	// CSRF令牌模式
	d.csrfTokenPatterns = []string{
		// 常见的CSRF令牌字段名
		"csrf_token",
		"_token",
		"authenticity_token",
		"__requestverificationtoken",
		"csrfmiddlewaretoken",
		"csrf-token",
		"_csrf",
		"csrf",
		"token",
		"security_token",
		"form_token",
		"session_token",
		"anti_csrf_token",
		"xsrf_token",
		"_xsrf",
		"xsrf",

		// 框架特定的令牌
		"__viewstate",                         // ASP.NET ViewState
		"__eventvalidation",                   // ASP.NET EventValidation
		"struts.token.name",                   // Struts
		"org.apache.struts.taglib.html.token", // Struts
		"_wpnonce",                            // WordPress
		"wp_nonce",                            // WordPress
		"laravel_token",                       // Laravel
		"_method",                             // Laravel方法欺骗
		"rails_token",                         // Rails
		"django_token",                        // Django
		"spring_token",                        // Spring
	}

	// 表单模式
	formPatterns := []string{
		`<form[^>]*>`,
		`<input[^>]*type\s*=\s*["']?hidden["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?[^"']*token[^"']*["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?[^"']*csrf[^"']*["']?[^>]*>`,
		`<input[^>]*name\s*=\s*["']?[^"']*xsrf[^"']*["']?[^>]*>`,
	}

	d.formPatterns = make([]*regexp.Regexp, 0, len(formPatterns))
	for _, pattern := range formPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.formPatterns = append(d.formPatterns, compiled)
		}
	}

	// Cookie模式
	cookiePatterns := []string{
		`samesite\s*=\s*strict`,
		`samesite\s*=\s*lax`,
		`samesite\s*=\s*none`,
		`secure`,
		`httponly`,
	}

	d.cookiePatterns = make([]*regexp.Regexp, 0, len(cookiePatterns))
	for _, pattern := range cookiePatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.cookiePatterns = append(d.cookiePatterns, compiled)
		}
	}

	// 头部模式
	headerPatterns := []string{
		`x-csrf-token`,
		`x-xsrf-token`,
		`x-requested-with`,
		`origin`,
		`referer`,
		`sec-fetch-site`,
		`sec-fetch-mode`,
		`sec-fetch-dest`,
	}

	d.headerPatterns = make([]*regexp.Regexp, 0, len(headerPatterns))
	for _, pattern := range headerPatterns {
		if compiled, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.headerPatterns = append(d.headerPatterns, compiled)
		}
	}
}
