package detectors

import (
	"context"
	"fmt"
	"net"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// extractHost 从目标中提取主机信息
func (d *JMXVulnerabilityDetector) extractHost(target *plugins.ScanTarget) string {
	// 如果目标有URL，从URL中提取主机
	if target.URL != "" {
		u, err := url.Parse(target.URL)
		if err == nil && u.Host != "" {
			// 移除端口号，只保留主机名/IP
			host := u.Hostname()
			if host != "" {
				return host
			}
		}
	}

	// 如果目标有IP地址
	if target.IP != "" {
		return target.IP
	}

	// 如果目标有域名
	if target.Domain != "" {
		return target.Domain
	}

	// 尝试从ID中提取（如果ID是IP或主机名格式）
	if target.ID != "" {
		// 简单验证是否为IP地址
		if net.ParseIP(target.ID) != nil {
			return target.ID
		}
		// 简单验证是否为主机名
		if strings.Contains(target.ID, ".") && !strings.Contains(target.ID, "/") {
			return target.ID
		}
	}

	return ""
}

// detectJMXPorts 检测JMX端口
func (d *JMXVulnerabilityDetector) detectJMXPorts(ctx context.Context, host string) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测JMX端口: %s", host)

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	openPorts := make([]int, 0)

	// 扫描JMX端口
	for _, port := range d.jmxPorts {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		if d.isPortOpen(host, port) {
			openPorts = append(openPorts, port)
			confidence := 0.3 // 基础端口开放置信度

			// 特定端口的置信度加权
			switch port {
			case 1099, 9999, 8983:
				confidence = 0.5 // 常见JMX端口
			case 1098, 9998, 8999:
				confidence = 0.4 // 次常见JMX端口
			}

			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("JMX端口: %d", port)
				vulnerableRequest = fmt.Sprintf("TCP连接: %s:%d", host, port)
				vulnerableResponse = fmt.Sprintf("端口 %d 开放", port)
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "port_scan",
				Description: fmt.Sprintf("JMX端口开放检测 (端口: %d, 置信度: %.2f)", port, confidence),
				Content:     fmt.Sprintf("端口 %d 在主机 %s 上开放", port, host),
				Location:    fmt.Sprintf("%s:%d", host, port),
				Timestamp:   time.Now(),
			})
		}
	}

	// 如果发现多个JMX端口，提高置信度
	if len(openPorts) > 1 {
		maxConfidence += 0.2
		if maxConfidence > 1.0 {
			maxConfidence = 1.0
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJMXService 检测JMX服务
func (d *JMXVulnerabilityDetector) detectJMXService(ctx context.Context, host string) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测JMX服务: %s", host)

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试关键JMX端口的服务
	keyPorts := []int{1099, 9999, 8983, 1098}

	for _, port := range keyPorts {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		if !d.isPortOpen(host, port) {
			continue
		}

		// 测试JMX连接
		confidence := d.testJMXConnection(host, port)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("service:jmx:rmi:///jndi/rmi://%s:%d/jmxrmi", host, port)
			vulnerableRequest = fmt.Sprintf("JMX连接测试: %s:%d", host, port)
			vulnerableResponse = fmt.Sprintf("JMX服务响应 (置信度: %.2f)", confidence)
		}

		if confidence > 0.3 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "service_detection",
				Description: fmt.Sprintf("JMX服务检测 (端口: %d, 置信度: %.2f)", port, confidence),
				Content:     fmt.Sprintf("JMX服务在端口 %d 上运行", port),
				Location:    fmt.Sprintf("%s:%d", host, port),
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJMXExploitability 检测JMX漏洞利用能力
func (d *JMXVulnerabilityDetector) detectJMXExploitability(ctx context.Context, host string) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测JMX漏洞利用能力: %s", host)

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试关键JMX端口的漏洞利用能力
	keyPorts := []int{1099, 9999, 8983}

	for _, port := range keyPorts {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		if !d.isPortOpen(host, port) {
			continue
		}

		// 测试JMX漏洞利用
		confidence := d.testJMXExploitability(host, port)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("JMX漏洞利用测试: %s:%d", host, port)
			vulnerableRequest = fmt.Sprintf("JMX MBean操作测试: %s:%d", host, port)
			vulnerableResponse = fmt.Sprintf("JMX漏洞利用测试结果 (置信度: %.2f)", confidence)
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "exploit_test",
				Description: fmt.Sprintf("JMX漏洞利用检测 (端口: %d, 置信度: %.2f)", port, confidence),
				Content:     fmt.Sprintf("JMX服务在端口 %d 上可能存在漏洞利用风险", port),
				Location:    fmt.Sprintf("%s:%d", host, port),
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// isPortOpen 检查端口是否开放
func (d *JMXVulnerabilityDetector) isPortOpen(host string, port int) bool {
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// testJMXConnection 测试JMX连接
func (d *JMXVulnerabilityDetector) testJMXConnection(host string, port int) float64 {
	timeout := 5 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return 0.0
	}
	defer conn.Close()

	confidence := 0.3 // 基础连接成功置信度

	// 发送JMX/RMI探测数据
	testPayloads := [][]byte{
		[]byte("JRMI\x00\x02K"),    // RMI协议探测
		[]byte("JRMI\x00\x02L"),    // RMI协议探测
		[]byte("\x4a\x52\x4d\x49"), // JRMI魔数
	}

	for _, payload := range testPayloads {
		conn.SetWriteDeadline(time.Now().Add(2 * time.Second))
		_, err := conn.Write(payload)
		if err != nil {
			continue
		}

		// 读取响应
		buffer := make([]byte, 1024)
		conn.SetReadDeadline(time.Now().Add(2 * time.Second))
		n, err := conn.Read(buffer)
		if err != nil {
			continue
		}

		response := string(buffer[:n])

		// 检查JMX/RMI响应特征
		if d.checkJMXResponse(response) {
			confidence += 0.3
			break
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// testJMXExploitability 测试JMX漏洞利用能力
func (d *JMXVulnerabilityDetector) testJMXExploitability(host string, port int) float64 {
	// 安全的漏洞利用能力测试
	timeout := 5 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return 0.0
	}
	defer conn.Close()

	confidence := 0.0

	// 测试是否可以进行JMX操作
	// 这里使用安全的测试方法，不进行实际的漏洞利用

	// 1. 测试RMI注册表访问
	if d.testRMIRegistry(conn) {
		confidence += 0.3
	}

	// 2. 测试MBean访问能力
	if d.testMBeanAccess(conn) {
		confidence += 0.4
	}

	// 3. 测试序列化对象处理
	if d.testSerializationHandling(conn) {
		confidence += 0.3
	}

	return confidence
}

// checkJMXResponse 检查JMX响应特征
func (d *JMXVulnerabilityDetector) checkJMXResponse(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查JMX指示器
	for _, indicator := range d.jmxIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			return true
		}
	}

	// 检查JMX响应模式
	for _, pattern := range d.jmxPatterns {
		if pattern.MatchString(response) {
			return true
		}
	}

	// 检查RMI响应模式
	for _, pattern := range d.rmiPatterns {
		if pattern.MatchString(response) {
			return true
		}
	}

	// 检查二进制特征
	if strings.Contains(response, "java.rmi") ||
		strings.Contains(response, "JRMI") ||
		strings.Contains(response, "javax.management") {
		return true
	}

	return false
}

// testRMIRegistry 测试RMI注册表访问
func (d *JMXVulnerabilityDetector) testRMIRegistry(conn net.Conn) bool {
	// 发送RMI注册表查询
	registryQuery := []byte{
		0x4a, 0x52, 0x4d, 0x49, // JRMI魔数
		0x00, 0x02, // 版本
		0x4b, // 协议
	}

	conn.SetWriteDeadline(time.Now().Add(2 * time.Second))
	_, err := conn.Write(registryQuery)
	if err != nil {
		return false
	}

	// 读取响应
	buffer := make([]byte, 512)
	conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := conn.Read(buffer)
	if err != nil {
		return false
	}

	response := string(buffer[:n])
	return strings.Contains(response, "java.rmi") || len(response) > 10
}

// testMBeanAccess 测试MBean访问能力
func (d *JMXVulnerabilityDetector) testMBeanAccess(conn net.Conn) bool {
	// 简化的MBean访问测试
	// 实际实现中可以使用JMX客户端库进行更详细的测试

	mbeanQuery := []byte{
		0xac, 0xed, 0x00, 0x05, // Java序列化魔数
		0x77, 0x22, // 块数据
	}

	conn.SetWriteDeadline(time.Now().Add(2 * time.Second))
	_, err := conn.Write(mbeanQuery)
	if err != nil {
		return false
	}

	// 读取响应
	buffer := make([]byte, 512)
	conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := conn.Read(buffer)
	if err != nil {
		return false
	}

	// 检查是否有序列化响应
	return n > 4 && buffer[0] == 0xac && buffer[1] == 0xed
}

// testSerializationHandling 测试序列化对象处理
func (d *JMXVulnerabilityDetector) testSerializationHandling(conn net.Conn) bool {
	// 发送安全的序列化测试数据
	serializationTest := []byte{
		0xac, 0xed, 0x00, 0x05, // Java序列化魔数
		0x73, 0x72, 0x00, 0x10, // 对象流
		0x6a, 0x61, 0x76, 0x61, // "java"
		0x2e, 0x6c, 0x61, 0x6e, // ".lan"
		0x67, 0x2e, 0x53, 0x74, // "g.St"
		0x72, 0x69, 0x6e, 0x67, // "ring"
	}

	conn.SetWriteDeadline(time.Now().Add(2 * time.Second))
	_, err := conn.Write(serializationTest)
	if err != nil {
		return false
	}

	// 读取响应
	buffer := make([]byte, 512)
	conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := conn.Read(buffer)
	if err != nil {
		return false
	}

	// 检查是否处理了序列化数据
	return n > 0
}

// verifyJMXPorts 验证JMX端口
func (d *JMXVulnerabilityDetector) verifyJMXPorts(ctx context.Context, host string) bool {
	// 验证关键JMX端口
	keyPorts := []int{1099, 9999, 8983}

	for _, port := range keyPorts {
		if d.isPortOpen(host, port) {
			return true
		}
	}

	return false
}

// verifyJMXService 验证JMX服务
func (d *JMXVulnerabilityDetector) verifyJMXService(ctx context.Context, host string) bool {
	// 验证关键JMX端口的服务
	keyPorts := []int{1099, 9999, 8983}

	for _, port := range keyPorts {
		if d.isPortOpen(host, port) && d.testJMXConnection(host, port) > 0.5 {
			return true
		}
	}

	return false
}

// verifyJMXExploitability 验证JMX漏洞利用能力
func (d *JMXVulnerabilityDetector) verifyJMXExploitability(ctx context.Context, host string) bool {
	// 验证关键JMX端口的漏洞利用能力
	keyPorts := []int{1099, 9999, 8983}

	for _, port := range keyPorts {
		if d.isPortOpen(host, port) && d.testJMXExploitability(host, port) > 0.6 {
			return true
		}
	}

	return false
}
