package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// SessionManagementDetector 会话管理检测器
// 支持会话固定、会话劫持、不安全的会话配置、会话超时、CSRF保护等多种会话管理检测
type SessionManagementDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	sessionCookies        []string         // 会话Cookie名称
	securityAttributes    []string         // 安全属性
	csrfTokenNames        []string         // CSRF令牌名称
	sessionPatterns       []*regexp.Regexp // 会话模式
	securityPatterns      []*regexp.Regexp // 安全模式
	vulnerabilityPatterns []*regexp.Regexp // 漏洞模式
	httpClient            *http.Client
}

// NewSessionManagementDetector 创建会话管理检测器
func NewSessionManagementDetector() *SessionManagementDetector {
	detector := &SessionManagementDetector{
		id:          "session-management-comprehensive",
		name:        "会话管理漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // 会话管理是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-384", "CWE-352", "CWE-613", "CWE-614", "CWE-598"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测会话管理漏洞，包括会话固定、会话劫持、不安全的会话配置、会话超时、CSRF保护等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         12 * time.Second, // 会话管理检测时间适中
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 512 * 1024, // 512KB，会话信息通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeCookies()
	detector.initializeAttributes()
	detector.initializeTokens()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *SessionManagementDetector) GetID() string                     { return d.id }
func (d *SessionManagementDetector) GetName() string                   { return d.name }
func (d *SessionManagementDetector) GetCategory() string               { return d.category }
func (d *SessionManagementDetector) GetSeverity() string               { return d.severity }
func (d *SessionManagementDetector) GetCVE() []string                  { return d.cve }
func (d *SessionManagementDetector) GetCWE() []string                  { return d.cwe }
func (d *SessionManagementDetector) GetVersion() string                { return d.version }
func (d *SessionManagementDetector) GetAuthor() string                 { return d.author }
func (d *SessionManagementDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *SessionManagementDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *SessionManagementDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *SessionManagementDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *SessionManagementDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *SessionManagementDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *SessionManagementDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *SessionManagementDetector) GetDependencies() []string         { return []string{} }
func (d *SessionManagementDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *SessionManagementDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *SessionManagementDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *SessionManagementDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *SessionManagementDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 12 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *SessionManagementDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *SessionManagementDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.sessionCookies) == 0 {
		return fmt.Errorf("会话Cookie列表不能为空")
	}
	if len(d.sessionPatterns) == 0 {
		return fmt.Errorf("会话模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *SessionManagementDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 会话管理检测适用于有会话功能的Web应用
	// 检查是否有会话相关的Cookie、表单或链接
	if d.hasSessionFeatures(target) {
		return true
	}

	// 对于登录、认证、用户相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	sessionKeywords := []string{
		"login", "signin", "auth", "session", "user", "profile",
		"account", "member", "dashboard", "admin", "manage",
		"登录", "认证", "会话", "用户", "账户", "管理",
	}

	for _, keyword := range sessionKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return false
}

// hasSessionFeatures 检查是否有会话功能
func (d *SessionManagementDetector) hasSessionFeatures(target *plugins.ScanTarget) bool {
	// 检查Cookie中是否有会话相关信息
	for _, cookie := range target.Cookies {
		cookieNameLower := strings.ToLower(cookie.Name)

		// 检查会话相关Cookie
		for _, sessionCookie := range d.sessionCookies {
			if strings.Contains(cookieNameLower, strings.ToLower(sessionCookie)) {
				return true
			}
		}
	}

	// 检查表单中是否有会话相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			// 检查会话相关字段
			sessionFields := []string{
				"session", "sessionid", "sess", "token", "csrf",
				"authenticity", "_token", "csrftoken", "xsrf",
				"会话", "令牌", "认证", "验证",
			}

			for _, sessionField := range sessionFields {
				if strings.Contains(fieldNameLower, sessionField) {
					return true
				}
			}
		}
	}

	// 检查头部中是否有会话相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "set-cookie" ||
			keyLower == "cookie" ||
			strings.Contains(valueLower, "session") ||
			strings.Contains(valueLower, "token") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *SessionManagementDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种会话管理检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 会话固定检测
	fixationEvidence, fixationConfidence, fixationPayload, fixationRequest, fixationResponse := d.detectSessionFixation(ctx, target)
	if fixationConfidence > maxConfidence {
		maxConfidence = fixationConfidence
		vulnerablePayload = fixationPayload
		vulnerableRequest = fixationRequest
		vulnerableResponse = fixationResponse
	}
	evidence = append(evidence, fixationEvidence...)

	// 2. 不安全的会话配置检测
	configEvidence, configConfidence, configPayload, configRequest, configResponse := d.detectInsecureSessionConfig(ctx, target)
	if configConfidence > maxConfidence {
		maxConfidence = configConfidence
		vulnerablePayload = configPayload
		vulnerableRequest = configRequest
		vulnerableResponse = configResponse
	}
	evidence = append(evidence, configEvidence...)

	// 3. 会话劫持检测
	hijackEvidence, hijackConfidence, hijackPayload, hijackRequest, hijackResponse := d.detectSessionHijacking(ctx, target)
	if hijackConfidence > maxConfidence {
		maxConfidence = hijackConfidence
		vulnerablePayload = hijackPayload
		vulnerableRequest = hijackRequest
		vulnerableResponse = hijackResponse
	}
	evidence = append(evidence, hijackEvidence...)

	// 4. CSRF保护检测
	csrfEvidence, csrfConfidence, csrfPayload, csrfRequest, csrfResponse := d.detectCSRFProtection(ctx, target)
	if csrfConfidence > maxConfidence {
		maxConfidence = csrfConfidence
		vulnerablePayload = csrfPayload
		vulnerableRequest = csrfRequest
		vulnerableResponse = csrfResponse
	}
	evidence = append(evidence, csrfEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.5

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "会话管理漏洞",
		Description:       "检测到会话管理漏洞，可能导致会话固定、会话劫持或其他会话安全问题",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施安全的会话管理，设置安全的Cookie属性，实施CSRF保护，定期更新会话ID",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Session_Management", "https://cwe.mitre.org/data/definitions/384.html"},
		Tags:              []string{"session-management", "session-fixation", "session-hijacking", "web", "medium"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *SessionManagementDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationPaths := []string{
		"/",
		"/login",
		"/session",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, path := range verificationPaths {
		// 构造验证URL
		verifyURL := d.buildSessionURL(target.URL, path)

		// 发送验证请求
		resp, err := d.sendSessionRequest(ctx, verifyURL)
		if err != nil {
			continue
		}

		// 检查会话管理响应特征
		responseConfidence := d.checkSessionManagementResponse(resp, path)
		if responseConfidence > 0.3 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证路径确认了会话管理问题"),
				Content:     resp,
				Location:    verifyURL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.4

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "session-management-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用会话管理验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *SessionManagementDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("session_management_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *SessionManagementDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (会话管理通常是中等风险)
	baseScore := 6.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeCookies 初始化会话Cookie列表
func (d *SessionManagementDetector) initializeCookies() {
	d.sessionCookies = []string{
		// 通用会话Cookie
		"sessionid",
		"session_id",
		"session",
		"sess",
		"sessid",
		"sid",
		"s",

		// Java会话Cookie
		"JSESSIONID",
		"jsessionid",
		"SESSIONID",

		// PHP会话Cookie
		"PHPSESSID",
		"phpsessid",
		"PHP_SESSID",

		// ASP.NET会话Cookie
		"ASP.NET_SessionId",
		"ASPSESSIONID",
		"aspnet_sessionid",

		// 其他框架会话Cookie
		"connect.sid",
		"express.sid",
		"laravel_session",
		"django_session",
		"rails_session",
		"flask.session",
		"beaker.session",
		"pyramid_session",

		// 自定义会话Cookie
		"user_session",
		"auth_session",
		"login_session",
		"admin_session",
		"member_session",
		"customer_session",
		"client_session",

		// 中文会话Cookie
		"会话",
		"用户会话",
		"登录会话",
	}
}

// initializeAttributes 初始化安全属性列表
func (d *SessionManagementDetector) initializeAttributes() {
	d.securityAttributes = []string{
		// 基础安全属性
		"Secure",
		"HttpOnly",
		"SameSite",

		// SameSite选项
		"SameSite=Strict",
		"SameSite=Lax",
		"SameSite=None",

		// 过期时间相关
		"Expires",
		"Max-Age",

		// 域和路径
		"Domain",
		"Path",

		// 其他属性
		"Priority",
		"Partitioned",
	}
}

// initializeTokens 初始化CSRF令牌名称列表
func (d *SessionManagementDetector) initializeTokens() {
	d.csrfTokenNames = []string{
		// 通用CSRF令牌
		"csrf_token",
		"csrftoken",
		"csrf",
		"_token",
		"token",
		"authenticity_token",
		"xsrf_token",
		"xsrftoken",
		"xsrf",

		// 框架特定CSRF令牌
		"_csrf",
		"__RequestVerificationToken",
		"csrfmiddlewaretoken",
		"_wpnonce",
		"security",
		"nonce",
		"anti_csrf_token",
		"form_token",
		"request_token",

		// 自定义CSRF令牌
		"form_csrf",
		"page_token",
		"action_token",
		"submit_token",
		"verify_token",
		"protection_token",

		// 中文CSRF令牌
		"防护令牌",
		"验证令牌",
		"安全令牌",
		"表单令牌",
	}
}

// initializePatterns 初始化检测模式
func (d *SessionManagementDetector) initializePatterns() {
	// 会话模式 - 检测会话相关的响应内容
	sessionPatternStrings := []string{
		// 会话标识符
		`(?i)(sessionid|session_id|jsessionid|phpsessid)`,
		`(?i)(sid|sess|session|sesskey|sessionkey)`,
		`(?i)(auth_session|login_session|user_session)`,
		`(?i)(admin_session|member_session|client_session)`,

		// Cookie相关
		`(?i)(set-cookie|cookie|httponly|secure|samesite)`,
		`(?i)(expires|max-age|domain|path)`,

		// 会话操作
		`(?i)(session\s*start|session\s*create|session\s*destroy)`,
		`(?i)(session\s*regenerate|session\s*invalidate)`,
		`(?i)(login|logout|signin|signout)`,

		// 会话状态
		`(?i)(logged\s*in|authenticated|authorized)`,
		`(?i)(session\s*active|session\s*valid|session\s*expired)`,

		// 中文模式
		`(?i)(会话|令牌|认证|登录|注销)`,
	}

	d.sessionPatterns = make([]*regexp.Regexp, 0, len(sessionPatternStrings))
	for _, pattern := range sessionPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.sessionPatterns = append(d.sessionPatterns, compiled)
		}
	}

	// 安全模式 - 检测安全配置相关的响应内容
	securityPatternStrings := []string{
		// 安全属性
		`(?i)(secure|httponly|samesite)`,
		`(?i)(samesite\s*=\s*(strict|lax|none))`,
		`(?i)(expires|max-age)`,

		// 安全头
		`(?i)(x-frame-options|x-xss-protection|x-content-type-options)`,
		`(?i)(strict-transport-security|content-security-policy)`,

		// CSRF保护
		`(?i)(csrf|xsrf|authenticity|token)`,
		`(?i)(anti.*csrf|csrf.*protection|token.*verification)`,

		// 会话安全
		`(?i)(session.*security|secure.*session|session.*protection)`,
		`(?i)(session.*timeout|session.*expiry|session.*lifetime)`,

		// 中文安全模式
		`(?i)(安全|保护|防护|验证|超时)`,
	}

	d.securityPatterns = make([]*regexp.Regexp, 0, len(securityPatternStrings))
	for _, pattern := range securityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.securityPatterns = append(d.securityPatterns, compiled)
		}
	}

	// 漏洞模式 - 检测会话管理漏洞的响应内容
	vulnerabilityPatternStrings := []string{
		// 会话固定
		`(?i)(session.*fixation|fixation.*attack|session.*not.*regenerated)`,
		`(?i)(same.*session.*id|session.*id.*unchanged|session.*reuse)`,

		// 会话劫持
		`(?i)(session.*hijack|session.*steal|session.*capture)`,
		`(?i)(session.*prediction|predictable.*session|weak.*session)`,

		// 不安全配置
		`(?i)(insecure.*session|session.*insecure|unsafe.*session)`,
		`(?i)(missing.*secure|missing.*httponly|missing.*samesite)`,
		`(?i)(no.*csrf.*protection|csrf.*missing|token.*missing)`,

		// 会话超时
		`(?i)(session.*timeout.*disabled|no.*session.*timeout)`,
		`(?i)(session.*never.*expires|permanent.*session)`,

		// 错误信息
		`(?i)(session.*error|session.*warning|session.*vulnerability)`,
		`(?i)(authentication.*bypass|session.*bypass)`,

		// 中文漏洞模式
		`(?i)(会话固定|会话劫持|会话超时|不安全会话)`,
		`(?i)(缺少保护|缺少验证|配置错误|安全缺陷)`,
	}

	d.vulnerabilityPatterns = make([]*regexp.Regexp, 0, len(vulnerabilityPatternStrings))
	for _, pattern := range vulnerabilityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.vulnerabilityPatterns = append(d.vulnerabilityPatterns, compiled)
		}
	}
}
