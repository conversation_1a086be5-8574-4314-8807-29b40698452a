package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// BusinessProcessDetector 业务流程检测器
// 专门检测业务流程相关的安全问题，包括工作流绕过、流程跳跃、状态篡改、审批绕过等
type BusinessProcessDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	workflowPaths      []string          // 工作流路径
	processSteps       []string          // 流程步骤
	statusParameters   []string          // 状态参数
	approvalParameters []string          // 审批参数
	workflowPayloads   []WorkflowPayload // 工作流载荷
	processPatterns    []*regexp.Regexp  // 流程模式
	bypassPatterns     []*regexp.Regexp  // 绕过模式
	securityPatterns   []*regexp.Regexp  // 安全模式
	httpClient         *http.Client
}

// WorkflowPayload 工作流载荷结构
type WorkflowPayload struct {
	Name        string
	Parameter   string
	Value       string
	Description string
	Category    string // step_bypass, status_manipulation, approval_bypass, workflow_injection
}

// NewBusinessProcessDetector 创建业务流程检测器
func NewBusinessProcessDetector() *BusinessProcessDetector {
	detector := &BusinessProcessDetector{
		id:          "business-process-comprehensive",
		name:        "业务流程安全检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-19781", "CVE-2018-13379"},
		cwe:         []string{"CWE-840", "CWE-841", "CWE-863", "CWE-285", "CWE-639", "CWE-284"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测业务流程安全问题，包括工作流绕过、流程跳跃、状态篡改、审批绕过等多种业务流程安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         25 * time.Second, // 业务流程检测需要较长时间
		MaxRetries:      2,                // 业务流程检测可以重试
		Concurrency:     4,                // 较低并发数避免触发防护
		RateLimit:       4,                // 较低速率限制
		FollowRedirects: true,             // 跟随重定向检查业务流程
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024, // 3MB，业务流程响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeWorkflowPaths()
	detector.initializeProcessSteps()
	detector.initializeStatusParameters()
	detector.initializeApprovalParameters()
	detector.initializeWorkflowPayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *BusinessProcessDetector) GetID() string            { return d.id }
func (d *BusinessProcessDetector) GetName() string          { return d.name }
func (d *BusinessProcessDetector) GetCategory() string      { return d.category }
func (d *BusinessProcessDetector) GetSeverity() string      { return d.severity }
func (d *BusinessProcessDetector) GetCVE() []string         { return d.cve }
func (d *BusinessProcessDetector) GetCWE() []string         { return d.cwe }
func (d *BusinessProcessDetector) GetVersion() string       { return d.version }
func (d *BusinessProcessDetector) GetAuthor() string        { return d.author }
func (d *BusinessProcessDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *BusinessProcessDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *BusinessProcessDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *BusinessProcessDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *BusinessProcessDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *BusinessProcessDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *BusinessProcessDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *BusinessProcessDetector) GetDependencies() []string         { return []string{} }
func (d *BusinessProcessDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *BusinessProcessDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *BusinessProcessDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *BusinessProcessDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *BusinessProcessDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 25 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *BusinessProcessDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *BusinessProcessDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.workflowPaths) == 0 {
		return fmt.Errorf("工作流路径不能为空")
	}
	if len(d.workflowPayloads) == 0 {
		return fmt.Errorf("工作流载荷不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *BusinessProcessDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 业务流程检测适用于有工作流功能的Web应用
	// 检查是否有业务流程相关的特征
	if d.hasBusinessProcessFeatures(target) {
		return true
	}

	// 对于有表单或多步骤流程的Web应用，也适用
	if len(target.Forms) > 0 {
		for _, form := range target.Forms {
			for fieldName := range form.Fields {
				fieldNameLower := strings.ToLower(fieldName)
				if strings.Contains(fieldNameLower, "step") ||
					strings.Contains(fieldNameLower, "stage") ||
					strings.Contains(fieldNameLower, "phase") ||
					strings.Contains(fieldNameLower, "status") ||
					strings.Contains(fieldNameLower, "state") ||
					strings.Contains(fieldNameLower, "workflow") ||
					strings.Contains(fieldNameLower, "process") ||
					strings.Contains(fieldNameLower, "approval") ||
					strings.Contains(fieldNameLower, "步骤") ||
					strings.Contains(fieldNameLower, "阶段") ||
					strings.Contains(fieldNameLower, "状态") ||
					strings.Contains(fieldNameLower, "流程") ||
					strings.Contains(fieldNameLower, "审批") {
					return true
				}
			}
		}
	}

	// 对于业务流程相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	processKeywords := []string{
		"workflow", "process", "step", "stage", "phase", "approval", "review",
		"submit", "confirm", "verify", "validate", "approve", "reject", "complete",
		"status", "state", "progress", "flow", "sequence", "order", "queue",
		"task", "job", "activity", "action", "operation", "procedure",
		"流程", "工作流", "步骤", "阶段", "审批", "审核", "提交", "确认",
		"验证", "批准", "拒绝", "完成", "状态", "进度", "顺序", "任务",
	}

	for _, keyword := range processKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 业务流程是通用Web安全问题，默认适用于所有Web目标
}

// hasBusinessProcessFeatures 检查是否有业务流程功能
func (d *BusinessProcessDetector) hasBusinessProcessFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有业务流程相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "workflow") ||
			strings.Contains(keyLower, "process") ||
			strings.Contains(keyLower, "step") ||
			strings.Contains(keyLower, "stage") ||
			strings.Contains(keyLower, "status") ||
			strings.Contains(valueLower, "workflow") ||
			strings.Contains(valueLower, "process") ||
			strings.Contains(valueLower, "step") ||
			strings.Contains(valueLower, "stage") ||
			strings.Contains(valueLower, "status") ||
			strings.Contains(valueLower, "approval") ||
			strings.Contains(valueLower, "流程") ||
			strings.Contains(valueLower, "工作流") ||
			strings.Contains(valueLower, "审批") {
			return true
		}
	}

	// 检查技术栈中是否有业务流程相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		processTechnologies := []string{
			"activiti", "camunda", "flowable", "jbpm", "zeebe", "temporal",
			"conductor", "airflow", "luigi", "prefect", "dagster", "argo",
			"spring boot", "spring cloud", "spring workflow", "spring state machine",
			"workflow", "process", "bpm", "业务流程", "工作流", "流程引擎",
		}

		for _, processTech := range processTechnologies {
			if strings.Contains(techNameLower, processTech) {
				return true
			}
		}
	}

	// 检查链接中是否有业务流程相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "workflow") ||
			strings.Contains(linkURLLower, "process") ||
			strings.Contains(linkURLLower, "step") ||
			strings.Contains(linkURLLower, "approval") ||
			strings.Contains(linkTextLower, "workflow") ||
			strings.Contains(linkTextLower, "process") ||
			strings.Contains(linkTextLower, "step") ||
			strings.Contains(linkTextLower, "approval") ||
			strings.Contains(linkTextLower, "submit") ||
			strings.Contains(linkTextLower, "流程") ||
			strings.Contains(linkTextLower, "工作流") ||
			strings.Contains(linkTextLower, "审批") ||
			strings.Contains(linkTextLower, "提交") {
			return true
		}
	}

	// 检查Cookie中是否有业务流程相关信息
	for _, cookie := range target.Cookies {
		cookieNameLower := strings.ToLower(cookie.Name)
		cookieValueLower := strings.ToLower(cookie.Value)

		if strings.Contains(cookieNameLower, "workflow") ||
			strings.Contains(cookieNameLower, "process") ||
			strings.Contains(cookieNameLower, "step") ||
			strings.Contains(cookieNameLower, "stage") ||
			strings.Contains(cookieNameLower, "status") ||
			strings.Contains(cookieValueLower, "workflow") ||
			strings.Contains(cookieValueLower, "process") ||
			strings.Contains(cookieValueLower, "step") ||
			strings.Contains(cookieValueLower, "stage") ||
			strings.Contains(cookieValueLower, "status") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *BusinessProcessDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种业务流程检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 工作流步骤绕过检测
	stepEvidence, stepConfidence, stepPayload, stepRequest, stepResponse := d.detectWorkflowStepBypass(ctx, target)
	if stepConfidence > maxConfidence {
		maxConfidence = stepConfidence
		vulnerablePayload = stepPayload
		vulnerableRequest = stepRequest
		vulnerableResponse = stepResponse
	}
	evidence = append(evidence, stepEvidence...)

	// 2. 状态篡改检测
	statusEvidence, statusConfidence, statusPayload, statusRequest, statusResponse := d.detectStatusManipulation(ctx, target)
	if statusConfidence > maxConfidence {
		maxConfidence = statusConfidence
		vulnerablePayload = statusPayload
		vulnerableRequest = statusRequest
		vulnerableResponse = statusResponse
	}
	evidence = append(evidence, statusEvidence...)

	// 3. 审批绕过检测
	approvalEvidence, approvalConfidence, approvalPayload, approvalRequest, approvalResponse := d.detectApprovalBypass(ctx, target)
	if approvalConfidence > maxConfidence {
		maxConfidence = approvalConfidence
		vulnerablePayload = approvalPayload
		vulnerableRequest = approvalRequest
		vulnerableResponse = approvalResponse
	}
	evidence = append(evidence, approvalEvidence...)

	// 4. 工作流注入检测
	injectionEvidence, injectionConfidence, injectionPayload, injectionRequest, injectionResponse := d.detectWorkflowInjection(ctx, target)
	if injectionConfidence > maxConfidence {
		maxConfidence = injectionConfidence
		vulnerablePayload = injectionPayload
		vulnerableRequest = injectionRequest
		vulnerableResponse = injectionResponse
	}
	evidence = append(evidence, injectionEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "业务流程安全漏洞",
		Description:       "检测到业务流程安全漏洞，应用程序的工作流程存在安全缺陷，可能导致流程绕过、状态篡改、审批绕过等攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强工作流步骤验证、实施状态完整性检查、完善审批机制、防止工作流注入攻击",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Business_logic_vulnerability", "https://cwe.mitre.org/data/definitions/840.html", "https://cwe.mitre.org/data/definitions/863.html"},
		Tags:              []string{"workflow", "process", "step", "status", "approval", "business", "logic"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *BusinessProcessDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"workflow-step-verification",
		"status-manipulation-verification",
		"approval-bypass-verification",
		"workflow-injection-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyBusinessProcessMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了业务流程漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "business-process-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用业务流程验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *BusinessProcessDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("business_process_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *BusinessProcessDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (业务流程通常是高风险漏洞)
	baseScore := 7.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyBusinessProcessMethod 验证业务流程方法
func (d *BusinessProcessDetector) verifyBusinessProcessMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "workflow-step-verification":
		return d.verifyWorkflowStep(ctx, target)
	case "status-manipulation-verification":
		return d.verifyStatusManipulation(ctx, target)
	case "approval-bypass-verification":
		return d.verifyApprovalBypass(ctx, target)
	case "workflow-injection-verification":
		return d.verifyWorkflowInjection(ctx, target)
	default:
		return 0.0
	}
}

// verifyWorkflowStep 验证工作流步骤
func (d *BusinessProcessDetector) verifyWorkflowStep(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的工作流步骤验证
	if d.hasBusinessProcessFeatures(target) {
		return 0.7 // 有业务流程特征的目标可能有步骤问题
	}
	return 0.3
}

// verifyStatusManipulation 验证状态篡改
func (d *BusinessProcessDetector) verifyStatusManipulation(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的状态篡改验证
	if d.hasBusinessProcessFeatures(target) {
		return 0.6 // 有业务流程特征的目标可能有状态问题
	}
	return 0.2
}

// verifyApprovalBypass 验证审批绕过
func (d *BusinessProcessDetector) verifyApprovalBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的审批绕过验证
	if d.hasBusinessProcessFeatures(target) {
		return 0.6 // 有业务流程特征的目标可能有审批问题
	}
	return 0.2
}

// verifyWorkflowInjection 验证工作流注入
func (d *BusinessProcessDetector) verifyWorkflowInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的工作流注入验证
	if d.hasBusinessProcessFeatures(target) {
		return 0.5 // 有业务流程特征的目标可能有注入问题
	}
	return 0.2
}
