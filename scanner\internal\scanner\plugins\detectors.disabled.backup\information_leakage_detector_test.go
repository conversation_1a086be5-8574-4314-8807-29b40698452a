package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestInformationLeakageDetectorBasicFunctionality 测试信息泄露检测器基础功能
func TestInformationLeakageDetectorBasicFunctionality(t *testing.T) {
	detector := NewInformationLeakageDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "information-leakage-comprehensive", detector.GetID())
	assert.Equal(t, "信息泄露漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-200")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestInformationLeakageDetectorApplicability 测试信息泄露检测器适用性
func TestInformationLeakageDetectorApplicability(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试Web目标
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试HTTPS目标
	httpsTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "https://example.com",
		Protocol: "https",
		Port:     443,
	}
	assert.True(t, detector.IsApplicable(httpsTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试任何Web应用目标（信息泄露检测适用于所有Web目标）
	anyWebTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/any/path",
		Protocol: "http",
		Port:     8080,
	}
	assert.True(t, detector.IsApplicable(anyWebTarget))
}

// TestInformationLeakageDetectorConfiguration 测试信息泄露检测器配置
func TestInformationLeakageDetectorConfiguration(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 12*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 4, config.Concurrency)
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       8,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestInformationLeakageDetectorFiles 测试信息泄露检测器文件列表
func TestInformationLeakageDetectorFiles(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 检查敏感文件列表
	assert.NotEmpty(t, detector.sensitiveFiles)
	assert.Greater(t, len(detector.sensitiveFiles), 50)

	// 检查调试端点
	assert.NotEmpty(t, detector.debugEndpoints)
	assert.Greater(t, len(detector.debugEndpoints), 20)

	// 检查备份文件扩展名
	assert.NotEmpty(t, detector.backupFiles)
	assert.Greater(t, len(detector.backupFiles), 10)

	// 检查配置文件扩展名
	assert.NotEmpty(t, detector.configFiles)
	assert.Greater(t, len(detector.configFiles), 10)

	// 检查敏感数据模式
	assert.NotEmpty(t, detector.sensitivePatterns)
	assert.Greater(t, len(detector.sensitivePatterns), 15)

	// 检查版本信息模式
	assert.NotEmpty(t, detector.versionPatterns)
	assert.Greater(t, len(detector.versionPatterns), 5)

	// 检查错误信息模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 8)

	// 检查调试信息模式
	assert.NotEmpty(t, detector.debugPatterns)
	assert.Greater(t, len(detector.debugPatterns), 8)

	// 检查特定的文件
	assert.Contains(t, detector.sensitiveFiles, "/.env")
	assert.Contains(t, detector.sensitiveFiles, "/robots.txt")
	assert.Contains(t, detector.sensitiveFiles, "/phpinfo.php")
	assert.Contains(t, detector.debugEndpoints, "/debug")
	assert.Contains(t, detector.debugEndpoints, "/debug/vars")
}

// TestInformationLeakageDetectorFileResponse 测试文件响应检查
func TestInformationLeakageDetectorFileResponse(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试.env文件响应
	envResponse := `Status: 200 OK
Content-Type: text/plain

APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:abcdefghijklmnopqrstuvwxyz
APP_DEBUG=true
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=secret`
	envFile := "/.env"
	confidence := detector.checkFileResponse(envResponse, envFile)
	assert.Greater(t, confidence, 0.8)

	// 测试robots.txt响应
	robotsResponse := `Status: 200 OK
Content-Type: text/plain

User-agent: *
Disallow: /admin/
Disallow: /private/
Disallow: /backup/
Allow: /public/

Sitemap: http://example.com/sitemap.xml`
	robotsFile := "/robots.txt"
	confidence2 := detector.checkFileResponse(robotsResponse, robotsFile)
	assert.Greater(t, confidence2, 0.7)

	// 测试phpinfo响应
	phpinfoResponse := `Status: 200 OK
Content-Type: text/html

<!DOCTYPE html>
<html>
<head><title>phpinfo()</title></head>
<body>
<h1>PHP Version 7.4.3</h1>
<h2>Configuration</h2>
<table>
<tr><td>PHP Version</td><td>7.4.3</td></tr>
<tr><td>System</td><td>Linux</td></tr>
</table>
</body>
</html>`
	phpinfoFile := "/phpinfo.php"
	confidence3 := detector.checkFileResponse(phpinfoResponse, phpinfoFile)
	assert.Greater(t, confidence3, 0.8)

	// 测试不存在的文件
	notFoundResponse := `Status: 404 Not Found
Content-Type: text/html

<html>
<body>
<h1>404 Not Found</h1>
<p>The requested file was not found.</p>
</body>
</html>`
	notFoundFile := "/nonexistent.txt"
	confidence4 := detector.checkFileResponse(notFoundResponse, notFoundFile)
	assert.Equal(t, 0.0, confidence4)
}

// TestInformationLeakageDetectorSensitiveDataConfidence 测试敏感数据置信度计算
func TestInformationLeakageDetectorSensitiveDataConfidence(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试API密钥（高风险）
	apiKeyMatches := []string{"api_key=sk_test_abcdefghijklmnopqrstuvwxyz"}
	apiKeyConfidence := detector.calculateSensitiveDataConfidence("API密钥", apiKeyMatches)
	assert.Greater(t, apiKeyConfidence, 0.8)

	// 测试私钥（高风险）
	privateKeyMatches := []string{"-----BEGIN RSA PRIVATE KEY-----"}
	privateKeyConfidence := detector.calculateSensitiveDataConfidence("私钥", privateKeyMatches)
	assert.Greater(t, privateKeyConfidence, 0.8)

	// 测试信用卡号（高风险）
	creditCardMatches := []string{"4111 1111 1111 1111", "5555-5555-5555-4444"}
	creditCardConfidence := detector.calculateSensitiveDataConfidence("信用卡号", creditCardMatches)
	assert.Greater(t, creditCardConfidence, 0.7)

	// 测试内部IP（中等风险）
	internalIPMatches := []string{"*************", "********"}
	internalIPConfidence := detector.calculateSensitiveDataConfidence("内部IP", internalIPMatches)
	assert.Greater(t, internalIPConfidence, 0.5)
	assert.Less(t, internalIPConfidence, 0.8)

	// 测试调试信息（中等风险）
	debugMatches := []string{"var_dump($data)", "print_r($array)"}
	debugConfidence := detector.calculateSensitiveDataConfidence("调试信息", debugMatches)
	assert.Greater(t, debugConfidence, 0.5)
	assert.Less(t, debugConfidence, 0.8)

	// 测试大量匹配的情况
	manyMatches := make([]string, 15)
	for i := range manyMatches {
		manyMatches[i] = "match" + string(rune(i))
	}
	manyMatchesConfidence := detector.calculateSensitiveDataConfidence("API密钥", manyMatches)
	assert.Greater(t, manyMatchesConfidence, 0.9)
}

// TestInformationLeakageDetectorDebugResponse 测试调试响应检查
func TestInformationLeakageDetectorDebugResponse(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试包含调试信息的响应
	debugResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Debug Information</h1>
<pre>
Debug: Application started
Trace: Loading configuration
Memory usage: 2.5MB
Execution time: 0.125s
</pre>
</body>
</html>`
	debugEndpoint := "/debug"
	confidence := detector.checkDebugResponse(debugResponse, debugEndpoint)
	assert.Greater(t, confidence, 0.6)

	// 测试包含错误信息的响应
	errorResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>System Status</h1>
<p>Fatal error: Uncaught exception in /var/www/html/index.php on line 25</p>
<p>Stack trace: #0 /var/www/html/lib.php(15): function()</p>
</body>
</html>`
	statusEndpoint := "/debug/status"
	confidence2 := detector.checkDebugResponse(errorResponse, statusEndpoint)
	assert.Greater(t, confidence2, 0.5)

	// 测试普通响应
	normalResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Welcome</h1>
<p>This is a normal page.</p>
</body>
</html>`
	normalEndpoint := "/debug"
	confidence3 := detector.checkDebugResponse(normalResponse, normalEndpoint)
	assert.Less(t, confidence3, 0.5)

	// 测试404响应
	notFoundResponse := `Status: 404 Not Found
Content-Type: text/html

<html>
<body>
<h1>404 Not Found</h1>
</body>
</html>`
	notFoundEndpoint := "/debug/nonexistent"
	confidence4 := detector.checkDebugResponse(notFoundResponse, notFoundEndpoint)
	assert.Equal(t, 0.0, confidence4)
}

// TestInformationLeakageDetectorBuildURL 测试URL构造
func TestInformationLeakageDetectorBuildURL(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试文件URL构造
	baseURL := "http://example.com"
	filePath := "/.env"
	fileURL := detector.buildFileURL(baseURL, filePath)
	assert.Equal(t, "http://example.com/.env", fileURL)

	// 测试调试URL构造
	debugEndpoint := "/debug/vars"
	debugURL := detector.buildDebugURL(baseURL, debugEndpoint)
	assert.Equal(t, "http://example.com/debug/vars", debugURL)

	// 测试带端口的URL
	baseURLWithPort := "http://example.com:8080"
	fileURLWithPort := detector.buildFileURL(baseURLWithPort, filePath)
	assert.Equal(t, "http://example.com:8080/.env", fileURLWithPort)

	// 测试HTTPS URL
	httpsURL := "https://example.com"
	httpsFileURL := detector.buildFileURL(httpsURL, filePath)
	assert.Equal(t, "https://example.com/.env", httpsFileURL)

	// 测试不以/开头的路径
	relativePath := "robots.txt"
	relativeURL := detector.buildFileURL(baseURL, relativePath)
	assert.Equal(t, "http://example.com/robots.txt", relativeURL)
}

// TestInformationLeakageDetectorRiskScore 测试风险评分计算
func TestInformationLeakageDetectorRiskScore(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestInformationLeakageDetectorLifecycle 测试检测器生命周期
func TestInformationLeakageDetectorLifecycle(t *testing.T) {
	detector := NewInformationLeakageDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkInformationLeakageDetectorFileCheck 基准测试文件检查性能
func BenchmarkInformationLeakageDetectorFileCheck(b *testing.B) {
	detector := NewInformationLeakageDetector()
	response := `Status: 200 OK\nContent-Type: text/plain\n\nAPP_KEY=secret\nDB_PASSWORD=password`
	filePath := "/.env"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkFileResponse(response, filePath)
	}
}

// BenchmarkInformationLeakageDetectorSensitiveDataCheck 基准测试敏感数据检查性能
func BenchmarkInformationLeakageDetectorSensitiveDataCheck(b *testing.B) {
	detector := NewInformationLeakageDetector()
	matches := []string{"api_key=sk_test_abcdefghijklmnopqrstuvwxyz"}
	patternName := "API密钥"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.calculateSensitiveDataConfidence(patternName, matches)
	}
}
