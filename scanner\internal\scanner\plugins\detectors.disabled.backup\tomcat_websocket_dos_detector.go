package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// TomcatWebSocketDoSDetector Tomcat WebSocket拒绝服务漏洞检测器
// 专门检测CVE-2020-13935等Tomcat WebSocket相关的拒绝服务漏洞
type TomcatWebSocketDoSDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	tomcatPorts       []int            // Tomcat常用端口
	websocketPaths    []string         // WebSocket路径
	tomcatIndicators  []string         // Tomcat服务指示器
	websocketHeaders  []string         // WebSocket头部
	dosTestPayloads   []string         // DoS测试载荷
	tomcatPatterns    []*regexp.Regexp // Tomcat响应模式
	websocketPatterns []*regexp.Regexp // WebSocket响应模式
	httpClient        *http.Client
}

// NewTomcatWebSocketDoSDetector 创建Tomcat WebSocket DoS检测器
func NewTomcatWebSocketDoSDetector() *TomcatWebSocketDoSDetector {
	detector := &TomcatWebSocketDoSDetector{
		id:          "tomcat-websocket-dos-detector",
		name:        "Tomcat WebSocket拒绝服务漏洞检测器",
		category:    "denial-of-service",
		severity:    "High",
		cve:         []string{"CVE-2020-13935"},
		cwe:         []string{"CWE-400", "CWE-835", "CWE-770"},
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测Apache Tomcat WebSocket拒绝服务漏洞(CVE-2020-13935)，包括包长度校验缺陷和无限循环处理",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         30 * time.Second,
			MaxRetries:      3,
			Concurrency:     3,
			RateLimit:       5,
			FollowRedirects: true,
			VerifySSL:       false,
			MaxResponseSize: 1024 * 1024, // 1MB
			Priority:        7,           // 高优先级
		},
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				if len(via) >= 10 {
					return fmt.Errorf("stopped after 10 redirects")
				}
				return nil
			},
		},
	}

	// 初始化检测规则
	detector.initializeRules()

	return detector
}

// initializeRules 初始化检测规则
func (d *TomcatWebSocketDoSDetector) initializeRules() {
	// Tomcat常用端口
	d.tomcatPorts = []int{8080, 8443, 80, 443, 8009, 8005, 8090, 9080}

	// WebSocket路径
	d.websocketPaths = []string{
		"/websocket",
		"/ws",
		"/socket",
		"/chat",
		"/echo",
		"/test",
		"/api/websocket",
		"/app/websocket",
		"/live",
		"/stream",
		"/realtime",
		"/notification",
		"/updates",
		"/events",
		"/socket.io",
		"/sockjs",
	}

	// Tomcat服务指示器
	d.tomcatIndicators = []string{
		"Apache Tomcat", "Tomcat", "Apache-Coyote", "Coyote",
		"Servlet", "JSP", "Java", "Catalina", "tomcat",
		"X-Powered-By: Servlet", "Server: Apache-Coyote",
		"Server: Tomcat", "Apache Tomcat/", "Coyote/",
	}

	// WebSocket头部
	d.websocketHeaders = []string{
		"Upgrade: websocket",
		"Connection: Upgrade",
		"Sec-WebSocket-Accept",
		"Sec-WebSocket-Key",
		"Sec-WebSocket-Version",
		"Sec-WebSocket-Protocol",
		"Sec-WebSocket-Extensions",
	}

	// DoS测试载荷
	d.dosTestPayloads = []string{
		// WebSocket升级请求
		"GET /websocket HTTP/1.1\r\nUpgrade: websocket\r\nConnection: Upgrade\r\nSec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\r\nSec-WebSocket-Version: 13\r\n\r\n",

		// 特制的WebSocket帧（模拟）
		"WebSocket-Frame-Test",

		// 大包测试
		"Large-Payload-Test",

		// 无限循环触发测试
		"Infinite-Loop-Test",
	}

	// 编译正则表达式模式
	d.compilePatterns()
}

// compilePatterns 编译正则表达式模式
func (d *TomcatWebSocketDoSDetector) compilePatterns() {
	// Tomcat响应模式
	tomcatPatterns := []string{
		`(?i)apache\s+tomcat`,
		`(?i)server:\s*apache-coyote`,
		`(?i)server:\s*tomcat`,
		`(?i)x-powered-by:\s*servlet`,
		`(?i)catalina`,
		`(?i)coyote`,
		`(?i)tomcat/\d+\.\d+`,
		`(?i)servlet.*container`,
		`(?i)jsp.*engine`,
	}

	for _, pattern := range tomcatPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.tomcatPatterns = append(d.tomcatPatterns, compiled)
		}
	}

	// WebSocket响应模式
	websocketPatterns := []string{
		`(?i)upgrade:\s*websocket`,
		`(?i)connection:\s*upgrade`,
		`(?i)sec-websocket-accept`,
		`(?i)sec-websocket-key`,
		`(?i)sec-websocket-version`,
		`(?i)websocket.*protocol`,
		`(?i)switching\s+protocols`,
		`(?i)101.*switching`,
	}

	for _, pattern := range websocketPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.websocketPatterns = append(d.websocketPatterns, compiled)
		}
	}
}

// GetID 获取检测器ID
func (d *TomcatWebSocketDoSDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *TomcatWebSocketDoSDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *TomcatWebSocketDoSDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *TomcatWebSocketDoSDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *TomcatWebSocketDoSDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *TomcatWebSocketDoSDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *TomcatWebSocketDoSDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *TomcatWebSocketDoSDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *TomcatWebSocketDoSDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *TomcatWebSocketDoSDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *TomcatWebSocketDoSDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// IsEnabled 检查是否启用
func (d *TomcatWebSocketDoSDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *TomcatWebSocketDoSDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// GetConfig 获取配置
func (d *TomcatWebSocketDoSDetector) GetConfig() *plugins.DetectorConfig {
	return d.config
}

// SetConfig 设置配置
func (d *TomcatWebSocketDoSDetector) SetConfig(config *plugins.DetectorConfig) {
	d.config = config
	d.updatedAt = time.Now()
}

// IsApplicable 检查是否适用于目标
func (d *TomcatWebSocketDoSDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Type != "web" && target.Type != "service" {
		return false
	}

	// 检查URL协议
	if target.URL != "" {
		if !strings.HasPrefix(target.URL, "http://") && !strings.HasPrefix(target.URL, "https://") {
			return false
		}
	}

	return true
}

// generateVulnID 生成漏洞ID
func (d *TomcatWebSocketDoSDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("%s-%s-%d", d.id, target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *TomcatWebSocketDoSDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 7.5 // 高风险基础分 (对应CVSS 7.5)

	// 根据置信度调整分数
	riskScore := baseScore * confidence

	// 确保分数在合理范围内
	if riskScore > 10.0 {
		riskScore = 10.0
	} else if riskScore < 0.0 {
		riskScore = 0.0
	}

	return riskScore
}

// Detect 执行Tomcat WebSocket DoS漏洞检测
func (d *TomcatWebSocketDoSDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	logger.Infof("开始Tomcat WebSocket DoS漏洞检测: %s", target.URL)

	// 执行多种检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. Tomcat服务检测
	tomcatEvidence, tomcatConfidence, tomcatPayload, tomcatRequest, tomcatResponse := d.detectTomcatService(ctx, target)
	if tomcatConfidence > maxConfidence {
		maxConfidence = tomcatConfidence
		vulnerablePayload = tomcatPayload
		vulnerableRequest = tomcatRequest
		vulnerableResponse = tomcatResponse
	}
	evidence = append(evidence, tomcatEvidence...)

	// 2. WebSocket端点检测
	wsEvidence, wsConfidence, wsPayload, wsRequest, wsResponse := d.detectWebSocketEndpoints(ctx, target)
	if wsConfidence > maxConfidence {
		maxConfidence = wsConfidence
		vulnerablePayload = wsPayload
		vulnerableRequest = wsRequest
		vulnerableResponse = wsResponse
	}
	evidence = append(evidence, wsEvidence...)

	// 3. CVE-2020-13935特定检测
	cveEvidence, cveConfidence, cvePayload, cveRequest, cveResponse := d.detectCVE202013935(ctx, target)
	if cveConfidence > maxConfidence {
		maxConfidence = cveConfidence
		vulnerablePayload = cvePayload
		vulnerableRequest = cveRequest
		vulnerableResponse = cveResponse
	}
	evidence = append(evidence, cveEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "Tomcat WebSocket拒绝服务漏洞",
		Description:       "检测到Apache Tomcat WebSocket拒绝服务漏洞(CVE-2020-13935)，可能导致服务不可用",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "升级Tomcat到安全版本，禁用不必要的WebSocket端点，配置请求大小限制",
		References:        []string{"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2020-13935", "https://tomcat.apache.org/security-9.html#Fixed_in_Apache_Tomcat_9.0.37"},
		Tags:              []string{"tomcat", "websocket", "dos", "cve-2020-13935", "denial-of-service"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *TomcatWebSocketDoSDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	logger.Infof("开始验证Tomcat WebSocket DoS漏洞: %s", target.URL)

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 验证Tomcat服务
	if d.verifyTomcatService(ctx, target) {
		verificationConfidence += 0.3
		evidence = append(evidence, plugins.Evidence{
			Type:        "service_verification",
			Description: "确认Tomcat服务运行",
			Content:     "Tomcat服务响应正常",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 验证WebSocket端点
	if d.verifyWebSocketEndpoints(ctx, target) {
		verificationConfidence += 0.4
		evidence = append(evidence, plugins.Evidence{
			Type:        "websocket_verification",
			Description: "确认WebSocket端点可访问",
			Content:     "WebSocket端点响应正常",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 验证DoS漏洞
	if d.verifyDoSVulnerability(ctx, target) {
		verificationConfidence += 0.3
		evidence = append(evidence, plugins.Evidence{
			Type:        "dos_verification",
			Description: "确认DoS漏洞存在",
			Content:     "DoS测试响应异常",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "tomcat-websocket-dos-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("Tomcat WebSocket DoS漏洞验证完成，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// GetTargetTypes 获取支持的目标类型
func (d *TomcatWebSocketDoSDetector) GetTargetTypes() []string {
	return []string{"http", "https", "ws", "wss"}
}

// GetRequiredPorts 获取需要的端口
func (d *TomcatWebSocketDoSDetector) GetRequiredPorts() []int {
	return []int{8080, 8443, 80, 443}
}

// GetRequiredServices 获取需要的服务
func (d *TomcatWebSocketDoSDetector) GetRequiredServices() []string {
	return []string{"tomcat", "websocket", "http", "https"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *TomcatWebSocketDoSDetector) GetRequiredHeaders() []string {
	return []string{"Server"}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *TomcatWebSocketDoSDetector) GetRequiredTechnologies() []string {
	return []string{"tomcat", "java", "websocket"}
}

// GetDependencies 获取依赖的其他检测器ID
func (d *TomcatWebSocketDoSDetector) GetDependencies() []string {
	return []string{}
}

// GetConfiguration 获取检测器配置
func (d *TomcatWebSocketDoSDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置检测器配置
func (d *TomcatWebSocketDoSDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// Validate 验证检测器有效性
func (d *TomcatWebSocketDoSDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if d.config == nil {
		return fmt.Errorf("检测器配置不能为空")
	}
	return nil
}

// Initialize 初始化检测器
func (d *TomcatWebSocketDoSDetector) Initialize() error {
	// 验证配置
	if err := d.Validate(); err != nil {
		return err
	}

	// 更新HTTP客户端配置
	if d.config.Timeout > 0 {
		d.httpClient.Timeout = d.config.Timeout
	}

	return nil
}

// Cleanup 清理资源
func (d *TomcatWebSocketDoSDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}
