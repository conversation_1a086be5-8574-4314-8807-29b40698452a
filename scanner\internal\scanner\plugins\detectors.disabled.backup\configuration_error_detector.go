package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// ConfigurationErrorDetector 配置错误检测器
// 支持安全头缺失、服务器配置错误、SSL/TLS配置问题、调试模式检测等多种配置错误检测
type ConfigurationErrorDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	securityHeaders    []string         // 安全头列表
	debugPaths         []string         // 调试路径
	configPaths        []string         // 配置文件路径
	defaultCredentials [][]string       // 默认凭据对
	securityPatterns   []*regexp.Regexp // 安全模式
	errorPatterns      []*regexp.Regexp // 错误模式
	configPatterns     []*regexp.Regexp // 配置模式
	httpClient         *http.Client
}

// NewConfigurationErrorDetector 创建配置错误检测器
func NewConfigurationErrorDetector() *ConfigurationErrorDetector {
	detector := &ConfigurationErrorDetector{
		id:          "configuration-error-comprehensive",
		name:        "配置错误漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // 配置错误是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-16", "CWE-200", "CWE-209", "CWE-215", "CWE-16"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测配置错误漏洞，包括安全头缺失、服务器配置错误、SSL/TLS配置问题、调试模式检测等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         10 * time.Second, // 配置错误检测时间适中
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 512 * 1024, // 512KB，配置信息通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeHeaders()
	detector.initializePaths()
	detector.initializeCredentials()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *ConfigurationErrorDetector) GetID() string                     { return d.id }
func (d *ConfigurationErrorDetector) GetName() string                   { return d.name }
func (d *ConfigurationErrorDetector) GetCategory() string               { return d.category }
func (d *ConfigurationErrorDetector) GetSeverity() string               { return d.severity }
func (d *ConfigurationErrorDetector) GetCVE() []string                  { return d.cve }
func (d *ConfigurationErrorDetector) GetCWE() []string                  { return d.cwe }
func (d *ConfigurationErrorDetector) GetVersion() string                { return d.version }
func (d *ConfigurationErrorDetector) GetAuthor() string                 { return d.author }
func (d *ConfigurationErrorDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *ConfigurationErrorDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *ConfigurationErrorDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *ConfigurationErrorDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *ConfigurationErrorDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *ConfigurationErrorDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *ConfigurationErrorDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *ConfigurationErrorDetector) GetDependencies() []string         { return []string{} }
func (d *ConfigurationErrorDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *ConfigurationErrorDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *ConfigurationErrorDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *ConfigurationErrorDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *ConfigurationErrorDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 10 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *ConfigurationErrorDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *ConfigurationErrorDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.securityHeaders) == 0 {
		return fmt.Errorf("安全头列表不能为空")
	}
	if len(d.securityPatterns) == 0 {
		return fmt.Errorf("安全模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *ConfigurationErrorDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 配置错误检测适用于所有Web应用
	// 任何Web应用都可能存在配置错误
	return true
}

// Detect 执行检测
func (d *ConfigurationErrorDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种配置错误检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 安全头检测
	headerEvidence, headerConfidence, headerPayload, headerRequest, headerResponse := d.detectSecurityHeaders(ctx, target)
	if headerConfidence > maxConfidence {
		maxConfidence = headerConfidence
		vulnerablePayload = headerPayload
		vulnerableRequest = headerRequest
		vulnerableResponse = headerResponse
	}
	evidence = append(evidence, headerEvidence...)

	// 2. 调试模式检测
	debugEvidence, debugConfidence, debugPayload, debugRequest, debugResponse := d.detectDebugMode(ctx, target)
	if debugConfidence > maxConfidence {
		maxConfidence = debugConfidence
		vulnerablePayload = debugPayload
		vulnerableRequest = debugRequest
		vulnerableResponse = debugResponse
	}
	evidence = append(evidence, debugEvidence...)

	// 3. 配置文件泄露检测
	configEvidence, configConfidence, configPayload, configRequest, configResponse := d.detectConfigurationLeakage(ctx, target)
	if configConfidence > maxConfidence {
		maxConfidence = configConfidence
		vulnerablePayload = configPayload
		vulnerableRequest = configRequest
		vulnerableResponse = configResponse
	}
	evidence = append(evidence, configEvidence...)

	// 4. 默认凭据检测
	credEvidence, credConfidence, credPayload, credRequest, credResponse := d.detectDefaultCredentials(ctx, target)
	if credConfidence > maxConfidence {
		maxConfidence = credConfidence
		vulnerablePayload = credPayload
		vulnerableRequest = credRequest
		vulnerableResponse = credResponse
	}
	evidence = append(evidence, credEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.5

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "配置错误漏洞",
		Description:       "检测到配置错误漏洞，可能导致信息泄露或安全防护失效",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "修复配置错误，启用安全头，禁用调试模式，保护配置文件，更改默认凭据",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Configuration", "https://cwe.mitre.org/data/definitions/16.html"},
		Tags:              []string{"configuration-error", "misconfiguration", "web", "medium"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *ConfigurationErrorDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationPaths := []string{
		"/",
		"/index.html",
		"/robots.txt",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, path := range verificationPaths {
		// 构造验证URL
		verifyURL := d.buildConfigURL(target.URL, path)

		// 发送验证请求
		resp, err := d.sendConfigRequest(ctx, verifyURL)
		if err != nil {
			continue
		}

		// 检查配置错误响应特征
		responseConfidence := d.checkConfigurationResponse(resp, path)
		if responseConfidence > 0.3 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证路径确认了配置错误"),
				Content:     resp,
				Location:    verifyURL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.4

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "configuration-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用配置验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *ConfigurationErrorDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("configuration_error_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *ConfigurationErrorDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (配置错误通常是中等风险)
	baseScore := 6.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeHeaders 初始化安全头列表
func (d *ConfigurationErrorDetector) initializeHeaders() {
	d.securityHeaders = []string{
		// 基础安全头
		"X-Frame-Options",
		"X-XSS-Protection",
		"X-Content-Type-Options",
		"Strict-Transport-Security",
		"Content-Security-Policy",
		"X-Permitted-Cross-Domain-Policies",
		"Referrer-Policy",
		"Feature-Policy",
		"Permissions-Policy",

		// 额外安全头
		"X-Download-Options",
		"X-DNS-Prefetch-Control",
		"Expect-CT",
		"Public-Key-Pins",
		"X-Robots-Tag",
		"Cache-Control",
		"Pragma",
		"Expires",

		// 服务器信息头（应该隐藏）
		"Server",
		"X-Powered-By",
		"X-AspNet-Version",
		"X-AspNetMvc-Version",
		"X-Generator",
		"X-Drupal-Cache",
		"X-Varnish",
	}
}

// initializePaths 初始化路径列表
func (d *ConfigurationErrorDetector) initializePaths() {
	// 调试路径
	d.debugPaths = []string{
		// 基础调试路径
		"/debug",
		"/debug/",
		"/debug.php",
		"/debug.jsp",
		"/debug.asp",
		"/debug.aspx",
		"/debug.html",

		// 开发调试路径
		"/dev",
		"/dev/",
		"/development",
		"/test",
		"/test/",
		"/testing",
		"/staging",
		"/demo",

		// 框架调试路径
		"/debug/vars",
		"/debug/pprof",
		"/debug/trace",
		"/debug/heap",
		"/debug/goroutine",
		"/_debug",
		"/_dev",

		// 应用调试路径
		"/phpinfo.php",
		"/info.php",
		"/test.php",
		"/status.php",
		"/health.php",
		"/ping.php",
		"/version.php",

		// 中文调试路径
		"/调试",
		"/测试",
		"/开发",
	}

	// 配置文件路径
	d.configPaths = []string{
		// 基础配置文件
		"/.env",
		"/.env.local",
		"/.env.production",
		"/.env.development",
		"/config.php",
		"/config.inc.php",
		"/configuration.php",
		"/settings.php",
		"/wp-config.php",
		"/config.xml",
		"/web.config",
		"/app.config",

		// 数据库配置
		"/database.php",
		"/db.php",
		"/dbconfig.php",
		"/database.xml",
		"/hibernate.cfg.xml",
		"/persistence.xml",

		// 应用配置
		"/application.properties",
		"/application.yml",
		"/application.yaml",
		"/config.json",
		"/config.yaml",
		"/config.yml",
		"/settings.json",
		"/appsettings.json",

		// 服务器配置
		"/httpd.conf",
		"/apache2.conf",
		"/nginx.conf",
		"/.htaccess",
		"/robots.txt",
		"/sitemap.xml",

		// 版本控制配置
		"/.git/config",
		"/.svn/entries",
		"/.hg/hgrc",
		"/.bzr/branch/branch.conf",

		// IDE配置
		"/.vscode/settings.json",
		"/.idea/workspace.xml",
		"/nbproject/project.properties",

		// 部署配置
		"/docker-compose.yml",
		"/Dockerfile",
		"/kubernetes.yaml",
		"/deployment.yaml",
	}
}

// initializeCredentials 初始化默认凭据
func (d *ConfigurationErrorDetector) initializeCredentials() {
	d.defaultCredentials = [][]string{
		// 通用默认凭据
		{"admin", "admin"},
		{"administrator", "administrator"},
		{"root", "root"},
		{"user", "user"},
		{"guest", "guest"},
		{"test", "test"},
		{"demo", "demo"},

		// 数据库默认凭据
		{"admin", "password"},
		{"admin", "123456"},
		{"admin", "admin123"},
		{"root", "password"},
		{"root", "123456"},
		{"root", "root123"},
		{"sa", "sa"},
		{"sa", "password"},
		{"postgres", "postgres"},
		{"mysql", "mysql"},

		// 应用默认凭据
		{"admin", ""},
		{"admin", "password"},
		{"admin", "123456"},
		{"admin", "admin123"},
		{"admin", "password123"},
		{"admin", "qwerty"},
		{"admin", "letmein"},
		{"admin", "welcome"},
		{"admin", "changeme"},

		// 中文默认凭据
		{"管理员", "管理员"},
		{"管理员", "密码"},
		{"管理员", "123456"},
		{"用户", "用户"},
		{"用户", "密码"},
	}
}

// initializePatterns 初始化检测模式
func (d *ConfigurationErrorDetector) initializePatterns() {
	// 安全模式 - 检测安全配置相关的响应内容
	securityPatternStrings := []string{
		// 安全头相关
		`(?i)(x-frame-options|x-xss-protection|x-content-type-options)`,
		`(?i)(strict-transport-security|content-security-policy)`,
		`(?i)(referrer-policy|feature-policy|permissions-policy)`,

		// 服务器信息泄露
		`(?i)(server:\s*apache|server:\s*nginx|server:\s*iis)`,
		`(?i)(x-powered-by|x-aspnet-version|x-generator)`,
		`(?i)(x-drupal-cache|x-varnish|x-cache)`,

		// 调试信息
		`(?i)(debug\s*mode|debug\s*enabled|debugging\s*on)`,
		`(?i)(development\s*mode|dev\s*mode|test\s*mode)`,
		`(?i)(phpinfo|server\s*info|system\s*info)`,

		// 配置信息
		`(?i)(database\s*connection|db\s*config|connection\s*string)`,
		`(?i)(api\s*key|secret\s*key|private\s*key)`,
		`(?i)(password|passwd|pwd|credential)`,

		// 版本信息
		`(?i)(version\s*\d+\.\d+|build\s*\d+|release\s*\d+)`,
		`(?i)(php\s*\d+\.\d+|apache\s*\d+\.\d+|nginx\s*\d+\.\d+)`,

		// 错误信息
		`(?i)(stack\s*trace|error\s*trace|exception\s*trace)`,
		`(?i)(fatal\s*error|warning|notice|deprecated)`,

		// 中文模式
		`(?i)(调试模式|开发模式|测试模式|错误信息)`,
		`(?i)(数据库连接|配置信息|版本信息|系统信息)`,
	}

	d.securityPatterns = make([]*regexp.Regexp, 0, len(securityPatternStrings))
	for _, pattern := range securityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.securityPatterns = append(d.securityPatterns, compiled)
		}
	}

	// 错误模式 - 检测配置错误
	errorPatternStrings := []string{
		// 配置错误
		`(?i)(configuration\s*error|config\s*error|配置错误)`,
		`(?i)(misconfiguration|misconfig|错误配置)`,
		`(?i)(invalid\s*configuration|无效配置)`,

		// 权限错误
		`(?i)(permission\s*denied|access\s*denied|权限被拒绝)`,
		`(?i)(forbidden|unauthorized|未授权)`,
		`(?i)(not\s*allowed|不允许|禁止访问)`,

		// 文件错误
		`(?i)(file\s*not\s*found|文件未找到|找不到文件)`,
		`(?i)(directory\s*not\s*found|目录未找到)`,
		`(?i)(path\s*not\s*found|路径未找到)`,

		// 服务错误
		`(?i)(service\s*unavailable|服务不可用)`,
		`(?i)(internal\s*server\s*error|内部服务器错误)`,
		`(?i)(bad\s*gateway|网关错误)`,

		// 通用错误
		`(?i)(error|错误|异常|失败)`,
		`(?i)(exception|例外|异常情况)`,
		`(?i)(failure|失败|故障)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 配置模式 - 检测配置文件内容
	configPatternStrings := []string{
		// 配置文件标识
		`(?i)(\[.*\]|<.*>|.*=.*|.*:.*)`, // 配置文件格式
		`(?i)(database|db|mysql|postgres|oracle)`,
		`(?i)(username|password|host|port|server)`,
		`(?i)(api_key|secret_key|private_key|public_key)`,
		`(?i)(debug|development|production|staging)`,

		// 环境变量
		`(?i)(APP_|DB_|API_|SECRET_|KEY_)`,
		`(?i)(DATABASE_URL|REDIS_URL|MONGODB_URL)`,
		`(?i)(AWS_|GOOGLE_|AZURE_|CLOUD_)`,

		// 配置关键词
		`(?i)(config|configuration|settings|options)`,
		`(?i)(connection|credential|authentication)`,
		`(?i)(encryption|security|ssl|tls)`,

		// 中文配置
		`(?i)(配置|设置|选项|连接|认证|加密|安全)`,
	}

	d.configPatterns = make([]*regexp.Regexp, 0, len(configPatternStrings))
	for _, pattern := range configPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.configPatterns = append(d.configPatterns, compiled)
		}
	}
}
