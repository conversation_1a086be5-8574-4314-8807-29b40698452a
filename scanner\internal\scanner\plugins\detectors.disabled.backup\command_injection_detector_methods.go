package detectors

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// detectUnixCommandInjection 检测Unix/Linux命令注入
func (d *CommandInjectionDetector) detectUnixCommandInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Unix/Linux载荷
	for i, payload := range d.unixPayloads {
		if i >= 8 { // 限制载荷数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送命令注入请求
		resp, err := d.sendCommandRequest(ctx, target.URL, "test", payload)
		if err != nil {
			continue
		}

		// 检查命令注入响应
		confidence := d.checkCommandResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "unix-command",
				Description: fmt.Sprintf("Unix命令载荷触发了命令注入响应 (置信度: %.2f)", confidence),
				Content:     d.extractCommandEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectWindowsCommandInjection 检测Windows命令注入
func (d *CommandInjectionDetector) detectWindowsCommandInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试Windows载荷
	for i, payload := range d.windowsPayloads {
		if i >= 6 { // 限制载荷数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送命令注入请求
		resp, err := d.sendCommandRequest(ctx, target.URL, "test", payload)
		if err != nil {
			continue
		}

		// 检查命令注入响应
		confidence := d.checkCommandResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "windows-command",
				Description: fmt.Sprintf("Windows命令载荷触发了命令注入响应 (置信度: %.2f)", confidence),
				Content:     d.extractCommandEvidence(resp, payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectTimeDelayInjection 检测时间延迟命令注入
func (d *CommandInjectionDetector) detectTimeDelayInjection(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试时间延迟载荷
	for i, payload := range d.timeDelayPayloads {
		if i >= 4 { // 限制时间延迟载荷数量，避免测试时间过长
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 记录开始时间
		startTime := time.Now()

		// 发送时间延迟请求
		resp, err := d.sendCommandRequest(ctx, target.URL, "test", payload)

		// 计算响应时间
		responseTime := time.Since(startTime)

		if err != nil {
			continue
		}

		// 检查时间延迟（期望延迟约5秒）
		confidence := d.checkTimeDelayResponse(responseTime, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "time-delay",
				Description: fmt.Sprintf("时间延迟载荷触发了延迟响应 (延迟: %.2fs, 置信度: %.2f)", responseTime.Seconds(), confidence),
				Content:     fmt.Sprintf("响应时间: %.2f秒, 载荷: %s", responseTime.Seconds(), payload),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendCommandRequest 发送命令注入请求
func (d *CommandInjectionDetector) sendCommandRequest(ctx context.Context, targetURL, paramName, payload string) (string, error) {
	// 构造请求URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加参数
	query := parsedURL.Query()
	query.Set(paramName, payload)
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkCommandResponse 检查命令注入响应
func (d *CommandInjectionDetector) checkCommandResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查命令执行指示器
	for _, indicator := range d.commandIndicators {
		if strings.Contains(response, strings.ToLower(indicator)) {
			confidence += 0.4
			// 特定指示器的额外加分
			if strings.Contains(indicator, "uid=") || strings.Contains(indicator, "gid=") {
				confidence += 0.3
			}
			if strings.Contains(indicator, "root:") || strings.Contains(indicator, "daemon:") {
				confidence += 0.3
			}
			if strings.Contains(indicator, "Volume in drive") || strings.Contains(indicator, "Directory of") {
				confidence += 0.3
			}
			break
		}
	}

	// 检查响应模式
	for _, pattern := range d.responsePatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查错误模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查载荷特定的响应
	if strings.Contains(payload, "id") &&
		(strings.Contains(response, "uid=") || strings.Contains(response, "gid=")) {
		confidence += 0.4
	}

	if strings.Contains(payload, "whoami") &&
		(strings.Contains(response, "root") || strings.Contains(response, "administrator") ||
			strings.Contains(response, "nt authority") || strings.Contains(response, "system")) {
		confidence += 0.4
	}

	if strings.Contains(payload, "dir") &&
		(strings.Contains(response, "volume in drive") || strings.Contains(response, "directory of")) {
		confidence += 0.4
	}

	if strings.Contains(payload, "/etc/passwd") &&
		(strings.Contains(response, "root:") || strings.Contains(response, "daemon:")) {
		confidence += 0.4
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkTimeDelayResponse 检查时间延迟响应
func (d *CommandInjectionDetector) checkTimeDelayResponse(responseTime time.Duration, payload string) float64 {
	confidence := 0.0

	// 检查是否包含延迟命令
	payloadLower := strings.ToLower(payload)
	hasDelayCommand := strings.Contains(payloadLower, "sleep") ||
		strings.Contains(payloadLower, "ping") ||
		strings.Contains(payloadLower, "timeout") ||
		strings.Contains(payloadLower, "start-sleep")

	if !hasDelayCommand {
		return confidence
	}

	// 检查响应时间（期望延迟约5秒）
	actualDelay := responseTime.Seconds()

	// 如果响应时间在4-8秒之间，认为可能存在时间延迟注入
	if actualDelay >= 4.0 && actualDelay <= 8.0 {
		confidence = 0.8
	} else if actualDelay >= 3.0 && actualDelay <= 10.0 {
		confidence = 0.6
	} else if actualDelay >= 2.0 && actualDelay <= 12.0 {
		confidence = 0.4
	}

	return confidence
}

// extractCommandEvidence 提取命令证据
func (d *CommandInjectionDetector) extractCommandEvidence(response, payload string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 5 {
			lineLower := strings.ToLower(line)

			// 查找包含命令执行结果的行
			for _, indicator := range d.commandIndicators {
				if strings.Contains(lineLower, strings.ToLower(indicator)) {
					// 返回相关行及其上下文
					start := i
					if start > 0 {
						start--
					}
					end := i + 2
					if end >= len(lines) {
						end = len(lines)
					}
					return strings.Join(lines[start:end], "\n")
				}
			}
		}
	}

	// 如果没有找到特定证据，返回响应的前500个字符
	if len(response) > 500 {
		return response[:500] + "..."
	}
	return response
}

// detectAdvancedCommandInjection 高级命令注入检测
func (d *CommandInjectionDetector) detectAdvancedCommandInjection(ctx context.Context, target *plugins.ScanTarget, param string) (*plugins.DetectionResult, error) {
	logger.Infof("开始高级命令注入检测: %s", target.URL)

	// 1. 编码绕过检测
	if result := d.detectEncodingBypass(ctx, target, param); result != nil {
		return result, nil
	}

	// 2. 文件操作检测
	if result := d.detectFileOperations(ctx, target, param); result != nil {
		return result, nil
	}

	// 3. 网络操作检测
	if result := d.detectNetworkOperations(ctx, target, param); result != nil {
		return result, nil
	}

	// 4. 系统信息泄露检测
	if result := d.detectSystemInfoLeakage(ctx, target, param); result != nil {
		return result, nil
	}

	// 5. OOB (Out-of-Band) 检测
	if result := d.detectOOBCommandInjection(ctx, target, param); result != nil {
		return result, nil
	}

	return nil, nil
}

// detectEncodingBypass 编码绕过检测
func (d *CommandInjectionDetector) detectEncodingBypass(ctx context.Context, target *plugins.ScanTarget, param string) *plugins.DetectionResult {
	logger.Debugf("测试编码绕过载荷...")

	for _, payload := range d.advancedPayloads {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		// 构造请求
		testURL := d.buildTestURL(target.URL, param, payload)

		// 发送请求
		resp, err := d.httpClient.Get(testURL)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			continue
		}

		responseStr := string(body)

		// 检查命令执行指示器
		if d.containsCommandIndicators(responseStr) {
			return &plugins.DetectionResult{
				VulnerabilityID: fmt.Sprintf("cmd-injection-encoding-%d", time.Now().Unix()),
				DetectorID:      d.GetID(),
				IsVulnerable:    true,
				Confidence:      0.85,
				Severity:        "High",
				Title:           "命令注入漏洞 (编码绕过)",
				Description:     "检测到通过编码绕过的命令注入漏洞",
				Evidence: []plugins.Evidence{
					{
						Type:        "response",
						Description: "命令执行响应",
						Content:     d.extractCommandEvidence(responseStr, payload),
						Location:    testURL,
						Timestamp:   time.Now(),
					},
				},
				Payload:     payload,
				Request:     testURL,
				Response:    responseStr,
				RiskScore:   8.5,
				Remediation: "对用户输入进行严格验证和过滤，避免直接执行用户输入",
				References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
				Tags:        []string{"command-injection", "encoding-bypass"},
				DetectedAt:  time.Now(),
			}
		}
	}

	return nil
}

// detectFileOperations 文件操作检测
func (d *CommandInjectionDetector) detectFileOperations(ctx context.Context, target *plugins.ScanTarget, param string) *plugins.DetectionResult {
	logger.Debugf("测试文件操作载荷...")

	for _, payload := range d.fileOperationPayloads {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		// 构造请求
		testURL := d.buildTestURL(target.URL, param, payload)

		// 发送请求
		resp, err := d.httpClient.Get(testURL)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			continue
		}

		responseStr := string(body)

		// 检查文件内容特征
		if d.containsFileContent(responseStr) {
			return &plugins.DetectionResult{
				VulnerabilityID: fmt.Sprintf("cmd-injection-file-%d", time.Now().Unix()),
				DetectorID:      d.GetID(),
				IsVulnerable:    true,
				Confidence:      0.90,
				Severity:        "High",
				Title:           "命令注入漏洞 (文件操作)",
				Description:     "检测到通过文件操作的命令注入漏洞",
				Evidence: []plugins.Evidence{
					{
						Type:        "response",
						Description: "文件操作响应",
						Content:     d.extractCommandEvidence(responseStr, payload),
						Location:    testURL,
						Timestamp:   time.Now(),
					},
				},
				Payload:     payload,
				Request:     testURL,
				Response:    responseStr,
				RiskScore:   9.0,
				Remediation: "对用户输入进行严格验证和过滤，避免直接执行文件操作命令",
				References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
				Tags:        []string{"command-injection", "file-operations"},
				DetectedAt:  time.Now(),
			}
		}
	}

	return nil
}

// detectNetworkOperations 网络操作检测
func (d *CommandInjectionDetector) detectNetworkOperations(ctx context.Context, target *plugins.ScanTarget, param string) *plugins.DetectionResult {
	logger.Debugf("测试网络操作载荷...")

	for _, payload := range d.networkPayloads {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		// 构造请求
		testURL := d.buildTestURL(target.URL, param, payload)

		// 发送请求
		resp, err := d.httpClient.Get(testURL)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			continue
		}

		responseStr := string(body)

		// 检查网络操作结果
		if d.containsNetworkInfo(responseStr) {
			return &plugins.DetectionResult{
				VulnerabilityID: fmt.Sprintf("cmd-injection-network-%d", time.Now().Unix()),
				DetectorID:      d.GetID(),
				IsVulnerable:    true,
				Confidence:      0.88,
				Severity:        "High",
				Title:           "命令注入漏洞 (网络操作)",
				Description:     "检测到通过网络操作的命令注入漏洞",
				Evidence: []plugins.Evidence{
					{
						Type:        "response",
						Description: "网络操作响应",
						Content:     d.extractCommandEvidence(responseStr, payload),
						Location:    testURL,
						Timestamp:   time.Now(),
					},
				},
				Payload:     payload,
				Request:     testURL,
				Response:    responseStr,
				RiskScore:   8.8,
				Remediation: "对用户输入进行严格验证和过滤，避免直接执行网络操作命令",
				References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
				Tags:        []string{"command-injection", "network-operations"},
				DetectedAt:  time.Now(),
			}
		}
	}

	return nil
}

// detectSystemInfoLeakage 系统信息泄露检测
func (d *CommandInjectionDetector) detectSystemInfoLeakage(ctx context.Context, target *plugins.ScanTarget, param string) *plugins.DetectionResult {
	logger.Debugf("测试系统信息泄露载荷...")

	for _, payload := range d.systemInfoPayloads {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		// 构造请求
		testURL := d.buildTestURL(target.URL, param, payload)

		// 发送请求
		resp, err := d.httpClient.Get(testURL)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			continue
		}

		responseStr := string(body)

		// 检查系统信息特征
		if d.containsSystemInfo(responseStr) {
			return &plugins.DetectionResult{
				VulnerabilityID: fmt.Sprintf("cmd-injection-sysinfo-%d", time.Now().Unix()),
				DetectorID:      d.GetID(),
				IsVulnerable:    true,
				Confidence:      0.80,
				Severity:        "Medium",
				Title:           "命令注入漏洞 (系统信息泄露)",
				Description:     "检测到通过系统信息泄露的命令注入漏洞",
				Evidence: []plugins.Evidence{
					{
						Type:        "response",
						Description: "系统信息响应",
						Content:     d.extractCommandEvidence(responseStr, payload),
						Location:    testURL,
						Timestamp:   time.Now(),
					},
				},
				Payload:     payload,
				Request:     testURL,
				Response:    responseStr,
				RiskScore:   6.0,
				Remediation: "对用户输入进行严格验证和过滤，避免泄露系统信息",
				References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
				Tags:        []string{"command-injection", "information-disclosure"},
				DetectedAt:  time.Now(),
			}
		}
	}

	return nil
}

// detectOOBCommandInjection OOB命令注入检测
func (d *CommandInjectionDetector) detectOOBCommandInjection(ctx context.Context, target *plugins.ScanTarget, param string) *plugins.DetectionResult {
	logger.Debugf("测试OOB命令注入载荷...")

	// 生成唯一标识符
	uniqueID := fmt.Sprintf("cmd-%d", time.Now().Unix())

	for _, payload := range d.blindPayloads {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		// 替换载荷中的占位符
		oobPayload := strings.ReplaceAll(payload, "burpcollaborator.net", fmt.Sprintf("%s.example.com", uniqueID))

		// 构造请求
		testURL := d.buildTestURL(target.URL, param, oobPayload)

		// 发送请求
		resp, err := d.httpClient.Get(testURL)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		// 等待OOB响应
		time.Sleep(2 * time.Second)

		// 检查是否有OOB交互（这里需要实际的OOB检测服务）
		if d.checkOOBInteraction(uniqueID) {
			return &plugins.DetectionResult{
				VulnerabilityID: fmt.Sprintf("cmd-injection-oob-%d", time.Now().Unix()),
				DetectorID:      d.GetID(),
				IsVulnerable:    true,
				Confidence:      0.95,
				Severity:        "High",
				Title:           "命令注入漏洞 (带外检测)",
				Description:     "检测到基于OOB的命令注入漏洞",
				Evidence: []plugins.Evidence{
					{
						Type:        "oob",
						Description: "带外交互证据",
						Content:     fmt.Sprintf("检测到OOB交互: %s", uniqueID),
						Location:    testURL,
						Timestamp:   time.Now(),
					},
				},
				Payload:     oobPayload,
				Request:     testURL,
				RiskScore:   9.5,
				Remediation: "对用户输入进行严格验证和过滤，避免直接执行用户输入",
				References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
				Tags:        []string{"command-injection", "out-of-band"},
				DetectedAt:  time.Now(),
			}
		}
	}

	return nil
}

// detectTimeDelayCommandInjection 时间延迟命令注入检测
func (d *CommandInjectionDetector) detectTimeDelayCommandInjection(ctx context.Context, target *plugins.ScanTarget, param string) *plugins.DetectionResult {
	logger.Debugf("测试时间延迟命令注入载荷...")

	// 时间延迟载荷
	timeDelayPayloads := []string{
		"; sleep 3",
		"| sleep 3",
		"& sleep 3",
		"; ping -c 3 127.0.0.1",
		"| ping -c 3 127.0.0.1",
		"& ping -n 3 127.0.0.1",
		"; timeout 3",
		"| timeout 3",
	}

	for _, payload := range timeDelayPayloads {
		select {
		case <-ctx.Done():
			return nil
		default:
		}

		// 构造请求
		testURL := d.buildTestURL(target.URL, param, payload)

		// 记录开始时间
		startTime := time.Now()

		// 发送请求
		resp, err := d.httpClient.Get(testURL)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		// 计算响应时间
		responseTime := time.Since(startTime)

		// 如果响应时间超过2.5秒，认为可能存在时间延迟注入
		if responseTime > 2500*time.Millisecond {
			// 读取响应
			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				continue
			}

			responseStr := string(body)

			return &plugins.DetectionResult{
				VulnerabilityID: fmt.Sprintf("cmd-injection-timedelay-%d", time.Now().Unix()),
				DetectorID:      d.GetID(),
				IsVulnerable:    true,
				Confidence:      0.85,
				Severity:        "High",
				Title:           "命令注入漏洞 (时间延迟)",
				Description:     "检测到基于时间延迟的命令注入漏洞",
				Evidence: []plugins.Evidence{
					{
						Type:        "timing",
						Description: "时间延迟证据",
						Content:     fmt.Sprintf("响应时间: %v, 载荷: %s", responseTime, payload),
						Location:    testURL,
						Timestamp:   time.Now(),
					},
				},
				Payload:     payload,
				Request:     testURL,
				Response:    responseStr,
				RiskScore:   8.5,
				Remediation: "对用户输入进行严格验证和过滤，避免直接执行用户输入",
				References:  []string{"https://owasp.org/www-community/attacks/Command_Injection"},
				Tags:        []string{"command-injection", "time-delay"},
				DetectedAt:  time.Now(),
			}
		}
	}

	return nil
}

// buildTestURL 构造测试URL
func (d *CommandInjectionDetector) buildTestURL(baseURL, param, payload string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	values := u.Query()
	values.Set(param, payload)
	u.RawQuery = values.Encode()

	return u.String()
}

// containsCommandIndicators 检查是否包含命令执行指示器
func (d *CommandInjectionDetector) containsCommandIndicators(response string) bool {
	response = strings.ToLower(response)

	for _, indicator := range d.commandIndicators {
		if strings.Contains(response, strings.ToLower(indicator)) {
			return true
		}
	}

	return false
}

// containsFileContent 检查是否包含文件内容特征
func (d *CommandInjectionDetector) containsFileContent(response string) bool {
	response = strings.ToLower(response)

	// 检查常见文件内容特征
	fileIndicators := []string{
		"root:", "daemon:", "bin:", "sys:", // /etc/passwd
		"localhost", "127.0.0.1", // /etc/hosts
		"# copyright", "# this file", // 文件头注释
		"permission denied", "no such file", // 文件错误
		"directory", "file", "folder", // 文件系统
	}

	for _, indicator := range fileIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// containsNetworkInfo 检查是否包含网络信息
func (d *CommandInjectionDetector) containsNetworkInfo(response string) bool {
	response = strings.ToLower(response)

	// 检查网络信息特征
	networkIndicators := []string{
		"tcp", "udp", "listen", "established", // netstat
		"inet", "inet6", "lo", "eth", "wlan", // 网络接口
		"ping", "traceroute", "nslookup", "dig", // 网络工具
		"curl", "wget", "nc", "netcat", // 网络客户端
		"connection", "socket", "port", // 网络连接
	}

	for _, indicator := range networkIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// containsSystemInfo 检查是否包含系统信息
func (d *CommandInjectionDetector) containsSystemInfo(response string) bool {
	response = strings.ToLower(response)

	// 检查系统信息特征
	systemIndicators := []string{
		"linux", "windows", "unix", "darwin", // 操作系统
		"kernel", "version", "release", // 内核信息
		"cpu", "memory", "disk", "filesystem", // 硬件信息
		"process", "pid", "ppid", "cmd", // 进程信息
		"user", "group", "uid", "gid", // 用户信息
		"environment", "path", "home", "shell", // 环境变量
	}

	for _, indicator := range systemIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// checkOOBInteraction 检查OOB交互（模拟实现）
func (d *CommandInjectionDetector) checkOOBInteraction(uniqueID string) bool {
	// 这里应该实现实际的OOB检测逻辑
	// 例如检查DNS查询日志、HTTP请求日志等
	// 目前返回false作为占位符
	logger.Debugf("检查OOB交互: %s", uniqueID)
	return false
}
