package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// MemoryLeakDetector 内存泄露检测器
// 支持敏感信息泄露、内存溢出、堆栈信息泄露、调试信息泄露等多种内存泄露漏洞检测
type MemoryLeakDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	sensitiveInfoPayloads  []string         // 敏感信息泄露载荷
	memoryOverflowPayloads []string         // 内存溢出载荷
	stackTracePayloads     []string         // 堆栈跟踪载荷
	debugInfoPayloads      []string         // 调试信息载荷
	errorInfoPayloads      []string         // 错误信息载荷
	testParameters         []string         // 测试参数
	sensitivePatterns      []*regexp.Regexp // 敏感信息模式
	memoryPatterns         []*regexp.Regexp // 内存泄露模式
	stackPatterns          []*regexp.Regexp // 堆栈跟踪模式
	debugPatterns          []*regexp.Regexp // 调试信息模式
	errorPatterns          []*regexp.Regexp // 错误信息模式
	httpClient             *http.Client
}

// NewMemoryLeakDetector 创建内存泄露检测器
func NewMemoryLeakDetector() *MemoryLeakDetector {
	detector := &MemoryLeakDetector{
		id:          "memory-leak-comprehensive",
		name:        "内存泄露漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708", "CVE-2018-1000861"},
		cwe:         []string{"CWE-200", "CWE-209", "CWE-532", "CWE-401", "CWE-404"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测内存泄露漏洞，包括敏感信息泄露、内存溢出、堆栈信息泄露、调试信息泄露等多种内存泄露安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 内存泄露检测需要适中时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       2,
		FollowRedirects: true, // 跟随重定向检查内存泄露
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024, // 3MB，内存泄露响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeSensitiveInfoPayloads()
	detector.initializeMemoryOverflowPayloads()
	detector.initializeStackTracePayloads()
	detector.initializeDebugInfoPayloads()
	detector.initializeErrorInfoPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *MemoryLeakDetector) GetID() string            { return d.id }
func (d *MemoryLeakDetector) GetName() string          { return d.name }
func (d *MemoryLeakDetector) GetCategory() string      { return d.category }
func (d *MemoryLeakDetector) GetSeverity() string      { return d.severity }
func (d *MemoryLeakDetector) GetCVE() []string         { return d.cve }
func (d *MemoryLeakDetector) GetCWE() []string         { return d.cwe }
func (d *MemoryLeakDetector) GetVersion() string       { return d.version }
func (d *MemoryLeakDetector) GetAuthor() string        { return d.author }
func (d *MemoryLeakDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *MemoryLeakDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *MemoryLeakDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *MemoryLeakDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *MemoryLeakDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *MemoryLeakDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *MemoryLeakDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *MemoryLeakDetector) GetDependencies() []string         { return []string{} }
func (d *MemoryLeakDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *MemoryLeakDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *MemoryLeakDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *MemoryLeakDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *MemoryLeakDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *MemoryLeakDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *MemoryLeakDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.sensitiveInfoPayloads) == 0 {
		return fmt.Errorf("敏感信息载荷不能为空")
	}
	if len(d.memoryOverflowPayloads) == 0 {
		return fmt.Errorf("内存溢出载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *MemoryLeakDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 内存泄露检测适用于有内存处理功能的Web应用
	// 检查是否有内存相关的特征
	if d.hasMemoryFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于内存相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	memoryKeywords := []string{
		"memory", "mem", "heap", "stack", "buffer", "cache", "session",
		"debug", "error", "exception", "trace", "dump", "log", "info",
		"status", "health", "monitor", "stats", "metrics", "profile",
		"内存", "缓存", "会话", "调试", "错误", "异常", "跟踪", "转储",
		"日志", "信息", "状态", "健康", "监控", "统计", "指标", "配置",
	}

	for _, keyword := range memoryKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 内存泄露是通用Web漏洞，默认适用于所有Web目标
}

// hasMemoryFeatures 检查是否有内存功能
func (d *MemoryLeakDetector) hasMemoryFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有内存相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "debug") ||
			strings.Contains(keyLower, "error") ||
			strings.Contains(keyLower, "trace") ||
			strings.Contains(keyLower, "memory") ||
			strings.Contains(keyLower, "cache") ||
			strings.Contains(valueLower, "debug") ||
			strings.Contains(valueLower, "error") ||
			strings.Contains(valueLower, "exception") ||
			strings.Contains(valueLower, "trace") ||
			strings.Contains(valueLower, "memory") {
			return true
		}
	}

	// 检查技术栈中是否有内存处理相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		memoryTechnologies := []string{
			"java", "python", "ruby", "php", "node.js", "go", "rust",
			"spring", "django", "rails", "laravel", "express", "gin",
			"tomcat", "jetty", "gunicorn", "unicorn", "puma",
			"redis", "memcached", "elasticsearch", "mongodb",
			"内存", "缓存", "调试", "监控",
		}

		for _, memoryTech := range memoryTechnologies {
			if strings.Contains(techNameLower, memoryTech) {
				return true
			}
		}
	}

	// 检查链接中是否有内存相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "debug") ||
			strings.Contains(linkURLLower, "error") ||
			strings.Contains(linkURLLower, "memory") ||
			strings.Contains(linkURLLower, "cache") ||
			strings.Contains(linkURLLower, "status") ||
			strings.Contains(linkTextLower, "debug") ||
			strings.Contains(linkTextLower, "error") ||
			strings.Contains(linkTextLower, "memory") ||
			strings.Contains(linkTextLower, "调试") ||
			strings.Contains(linkTextLower, "错误") ||
			strings.Contains(linkTextLower, "内存") {
			return true
		}
	}

	// 检查表单中是否有内存相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			memoryFields := []string{
				"memory", "mem", "heap", "stack", "buffer", "cache", "session",
				"debug", "error", "exception", "trace", "dump", "log", "info",
				"status", "health", "monitor", "stats", "metrics", "profile",
				"内存", "缓存", "会话", "调试", "错误", "异常", "跟踪", "转储",
				"日志", "信息", "状态", "健康", "监控", "统计", "指标", "配置",
			}

			for _, memoryField := range memoryFields {
				if strings.Contains(fieldNameLower, memoryField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *MemoryLeakDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种内存泄露检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 敏感信息泄露检测
	sensitiveEvidence, sensitiveConfidence, sensitivePayload, sensitiveRequest, sensitiveResponse := d.detectSensitiveInfoLeak(ctx, target)
	if sensitiveConfidence > maxConfidence {
		maxConfidence = sensitiveConfidence
		vulnerablePayload = sensitivePayload
		vulnerableRequest = sensitiveRequest
		vulnerableResponse = sensitiveResponse
	}
	evidence = append(evidence, sensitiveEvidence...)

	// 2. 内存溢出检测
	overflowEvidence, overflowConfidence, overflowPayload, overflowRequest, overflowResponse := d.detectMemoryOverflow(ctx, target)
	if overflowConfidence > maxConfidence {
		maxConfidence = overflowConfidence
		vulnerablePayload = overflowPayload
		vulnerableRequest = overflowRequest
		vulnerableResponse = overflowResponse
	}
	evidence = append(evidence, overflowEvidence...)

	// 3. 堆栈跟踪检测
	stackEvidence, stackConfidence, stackPayload, stackRequest, stackResponse := d.detectStackTrace(ctx, target)
	if stackConfidence > maxConfidence {
		maxConfidence = stackConfidence
		vulnerablePayload = stackPayload
		vulnerableRequest = stackRequest
		vulnerableResponse = stackResponse
	}
	evidence = append(evidence, stackEvidence...)

	// 4. 调试信息检测
	debugEvidence, debugConfidence, debugPayload, debugRequest, debugResponse := d.detectDebugInfo(ctx, target)
	if debugConfidence > maxConfidence {
		maxConfidence = debugConfidence
		vulnerablePayload = debugPayload
		vulnerableRequest = debugRequest
		vulnerableResponse = debugResponse
	}
	evidence = append(evidence, debugEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "内存泄露漏洞",
		Description:       "检测到内存泄露漏洞，可能导致敏感信息泄露、系统性能下降或拒绝服务攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "修复内存泄露问题，清理敏感信息输出，实施适当的错误处理和日志管理",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Information_exposure_through_error_messages", "https://cwe.mitre.org/data/definitions/200.html", "https://cwe.mitre.org/data/definitions/401.html"},
		Tags:              []string{"memory", "leak", "sensitive", "information", "disclosure", "debug", "error", "trace"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *MemoryLeakDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"sensitive-info-verification",
		"memory-overflow-verification",
		"stack-trace-verification",
		"debug-info-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyMemoryLeakMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了内存泄露漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "memory-leak-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用内存泄露验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *MemoryLeakDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("memory_leak_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *MemoryLeakDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (内存泄露通常是中等风险漏洞)
	baseScore := 6.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyMemoryLeakMethod 验证内存泄露方法
func (d *MemoryLeakDetector) verifyMemoryLeakMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "sensitive-info-verification":
		return d.verifySensitiveInfo(ctx, target)
	case "memory-overflow-verification":
		return d.verifyMemoryOverflow(ctx, target)
	case "stack-trace-verification":
		return d.verifyStackTrace(ctx, target)
	case "debug-info-verification":
		return d.verifyDebugInfo(ctx, target)
	default:
		return 0.0
	}
}

// verifySensitiveInfo 验证敏感信息泄露
func (d *MemoryLeakDetector) verifySensitiveInfo(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的敏感信息验证
	if d.hasMemoryFeatures(target) {
		return 0.7 // 有内存特征的目标可能有敏感信息泄露
	}
	return 0.3
}

// verifyMemoryOverflow 验证内存溢出
func (d *MemoryLeakDetector) verifyMemoryOverflow(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的内存溢出验证
	if d.hasMemoryFeatures(target) {
		return 0.6 // 有内存特征的目标可能有内存溢出
	}
	return 0.2
}

// verifyStackTrace 验证堆栈跟踪
func (d *MemoryLeakDetector) verifyStackTrace(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的堆栈跟踪验证
	if d.hasMemoryFeatures(target) {
		return 0.6 // 有内存特征的目标可能有堆栈跟踪
	}
	return 0.2
}

// verifyDebugInfo 验证调试信息
func (d *MemoryLeakDetector) verifyDebugInfo(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的调试信息验证
	if d.hasMemoryFeatures(target) {
		return 0.5 // 有内存特征的目标可能有调试信息
	}
	return 0.1
}

// initializeSensitiveInfoPayloads 初始化敏感信息泄露载荷列表
func (d *MemoryLeakDetector) initializeSensitiveInfoPayloads() {
	d.sensitiveInfoPayloads = []string{
		// 数据库连接信息
		"database", "db", "mysql", "postgresql", "oracle", "mongodb", "redis",
		"connection", "connect", "conn", "dsn", "jdbc", "odbc",
		"username", "password", "passwd", "pwd", "user", "pass",
		"host", "hostname", "server", "port", "schema", "database_name",

		// API密钥和令牌
		"api_key", "apikey", "api-key", "secret_key", "secretkey", "secret-key",
		"access_token", "accesstoken", "access-token", "refresh_token", "refreshtoken",
		"bearer", "authorization", "auth", "token", "key", "secret",
		"private_key", "privatekey", "private-key", "public_key", "publickey",

		// 配置文件信息
		"config", "configuration", "settings", "properties", "env", "environment",
		"ini", "conf", "cfg", "xml", "json", "yaml", "yml", "toml",
		"application.properties", "config.ini", "settings.xml", ".env",

		// 系统信息
		"version", "build", "commit", "revision", "branch", "tag",
		"os", "platform", "architecture", "arch", "system", "kernel",
		"java_home", "path", "classpath", "pythonpath", "node_path",

		// 会话和Cookie信息
		"session", "sessionid", "session_id", "session-id", "jsessionid",
		"cookie", "cookies", "csrf", "csrftoken", "csrf_token", "csrf-token",
		"xsrf", "xsrftoken", "xsrf_token", "xsrf-token",

		// 错误和调试信息
		"error", "exception", "stacktrace", "stack_trace", "stack-trace",
		"debug", "trace", "log", "logs", "logging", "logger",
		"dump", "core", "memory", "heap", "stack", "buffer",

		// 文件路径信息
		"file", "filename", "filepath", "path", "directory", "folder",
		"upload", "download", "temp", "tmp", "cache", "backup",
		"home", "root", "admin", "user", "users", "var", "etc", "opt",

		// 网络信息
		"ip", "ipaddress", "ip_address", "ip-address", "localhost", "127.0.0.1",
		"internal", "private", "local", "lan", "intranet", "subnet",
		"proxy", "gateway", "dns", "nameserver", "resolver",

		// 中文敏感信息
		"数据库", "连接", "用户名", "密码", "密钥", "令牌", "配置", "设置",
		"版本", "系统", "会话", "错误", "异常", "调试", "日志", "文件",
		"路径", "目录", "网络", "地址", "内部", "私有", "本地",
	}
}

// initializeMemoryOverflowPayloads 初始化内存溢出载荷列表
func (d *MemoryLeakDetector) initializeMemoryOverflowPayloads() {
	d.memoryOverflowPayloads = []string{
		// 大数据载荷
		strings.Repeat("A", 1024),    // 1KB
		strings.Repeat("B", 4096),    // 4KB
		strings.Repeat("C", 8192),    // 8KB
		strings.Repeat("D", 16384),   // 16KB
		strings.Repeat("E", 32768),   // 32KB
		strings.Repeat("F", 65536),   // 64KB
		strings.Repeat("G", 131072),  // 128KB
		strings.Repeat("H", 262144),  // 256KB
		strings.Repeat("I", 524288),  // 512KB
		strings.Repeat("J", 1048576), // 1MB

		// 特殊字符载荷
		strings.Repeat("0", 10000),
		strings.Repeat("1", 10000),
		strings.Repeat("9", 10000),
		strings.Repeat("a", 10000),
		strings.Repeat("z", 10000),
		strings.Repeat("@", 10000),
		strings.Repeat("#", 10000),
		strings.Repeat("$", 10000),
		strings.Repeat("%", 10000),
		strings.Repeat("&", 10000),
		strings.Repeat("*", 10000),
		strings.Repeat("+", 10000),
		strings.Repeat("-", 10000),
		strings.Repeat("=", 10000),
		strings.Repeat("_", 10000),
		strings.Repeat("|", 10000),
		strings.Repeat("\\", 10000),
		strings.Repeat("/", 10000),
		strings.Repeat("?", 10000),
		strings.Repeat(".", 10000),
		strings.Repeat(",", 10000),
		strings.Repeat(";", 10000),
		strings.Repeat(":", 10000),
		strings.Repeat("'", 10000),
		strings.Repeat("\"", 10000),
		strings.Repeat("`", 10000),
		strings.Repeat("~", 10000),
		strings.Repeat("!", 10000),
		strings.Repeat("^", 10000),
		strings.Repeat("(", 10000),
		strings.Repeat(")", 10000),
		strings.Repeat("[", 10000),
		strings.Repeat("]", 10000),
		strings.Repeat("{", 10000),
		strings.Repeat("}", 10000),
		strings.Repeat("<", 10000),
		strings.Repeat(">", 10000),

		// Unicode字符载荷
		strings.Repeat("中", 5000),
		strings.Repeat("文", 5000),
		strings.Repeat("测", 5000),
		strings.Repeat("试", 5000),
		strings.Repeat("数", 5000),
		strings.Repeat("据", 5000),
		strings.Repeat("内", 5000),
		strings.Repeat("存", 5000),
		strings.Repeat("溢", 5000),
		strings.Repeat("出", 5000),

		// 混合载荷
		strings.Repeat("Aa1@", 2500),
		strings.Repeat("Bb2#", 2500),
		strings.Repeat("Cc3$", 2500),
		strings.Repeat("Dd4%", 2500),
		strings.Repeat("Ee5&", 2500),
		strings.Repeat("Ff6*", 2500),
		strings.Repeat("Gg7+", 2500),
		strings.Repeat("Hh8-", 2500),
		strings.Repeat("Ii9=", 2500),
		strings.Repeat("Jj0_", 2500),

		// 特殊模式载荷
		strings.Repeat("AAAA", 2500),
		strings.Repeat("BBBB", 2500),
		strings.Repeat("CCCC", 2500),
		strings.Repeat("DDDD", 2500),
		strings.Repeat("EEEE", 2500),
		strings.Repeat("FFFF", 2500),
		strings.Repeat("1111", 2500),
		strings.Repeat("2222", 2500),
		strings.Repeat("3333", 2500),
		strings.Repeat("9999", 2500),
		strings.Repeat("0000", 2500),
		strings.Repeat("@@@@", 2500),
		strings.Repeat("####", 2500),
		strings.Repeat("$$$$", 2500),
		strings.Repeat("%%%%", 2500),
		strings.Repeat("&&&&", 2500),
		strings.Repeat("****", 2500),
		strings.Repeat("++++", 2500),
		strings.Repeat("----", 2500),
		strings.Repeat("====", 2500),
		strings.Repeat("____", 2500),
		strings.Repeat("||||", 2500),
		strings.Repeat("\\\\\\\\", 2500),
		strings.Repeat("////", 2500),
		strings.Repeat("????", 2500),
		strings.Repeat("....", 2500),
		strings.Repeat(",,,,", 2500),
		strings.Repeat(";;;;", 2500),
		strings.Repeat("::::", 2500),
		strings.Repeat("''''", 2500),
		strings.Repeat("\"\"\"\"", 2500),
		strings.Repeat("````", 2500),
		strings.Repeat("~~~~", 2500),
		strings.Repeat("!!!!", 2500),
		strings.Repeat("^^^^", 2500),
		strings.Repeat("((((", 2500),
		strings.Repeat("))))", 2500),
		strings.Repeat("[[[[", 2500),
		strings.Repeat("]]]]", 2500),
		strings.Repeat("{{{{", 2500),
		strings.Repeat("}}}}", 2500),
		strings.Repeat("<<<<", 2500),
		strings.Repeat(">>>>", 2500),
	}
}

// initializeStackTracePayloads 初始化堆栈跟踪载荷列表
func (d *MemoryLeakDetector) initializeStackTracePayloads() {
	d.stackTracePayloads = []string{
		// Java堆栈跟踪触发
		"java.lang.NullPointerException",
		"java.lang.OutOfMemoryError",
		"java.lang.StackOverflowError",
		"java.lang.ClassNotFoundException",
		"java.lang.NoSuchMethodException",
		"java.lang.IllegalArgumentException",
		"java.lang.IllegalStateException",
		"java.lang.UnsupportedOperationException",
		"java.lang.RuntimeException",
		"java.lang.Exception",
		"java.lang.Error",
		"java.lang.Throwable",
		"javax.servlet.ServletException",
		"org.springframework.web.servlet.DispatcherServlet",
		"org.apache.catalina.core.ApplicationFilterChain",

		// Python堆栈跟踪触发
		"Traceback (most recent call last):",
		"NameError:",
		"TypeError:",
		"ValueError:",
		"AttributeError:",
		"KeyError:",
		"IndexError:",
		"ImportError:",
		"ModuleNotFoundError:",
		"FileNotFoundError:",
		"PermissionError:",
		"MemoryError:",
		"RecursionError:",
		"ZeroDivisionError:",
		"SyntaxError:",
		"IndentationError:",
		"UnboundLocalError:",
		"RuntimeError:",
		"SystemError:",
		"OSError:",
		"IOError:",

		// .NET堆栈跟踪触发
		"System.NullReferenceException",
		"System.OutOfMemoryException",
		"System.StackOverflowException",
		"System.ArgumentException",
		"System.ArgumentNullException",
		"System.InvalidOperationException",
		"System.NotSupportedException",
		"System.NotImplementedException",
		"System.UnauthorizedAccessException",
		"System.IO.FileNotFoundException",
		"System.IO.DirectoryNotFoundException",
		"System.Security.SecurityException",
		"System.Web.HttpException",
		"System.Data.SqlException",
		"Microsoft.AspNetCore.Mvc.Controller",

		// PHP错误触发
		"Fatal error:",
		"Parse error:",
		"Warning:",
		"Notice:",
		"Strict Standards:",
		"Deprecated:",
		"Call to undefined function",
		"Call to undefined method",
		"Class not found",
		"Function not found",
		"Method not found",
		"Property not found",
		"Variable not found",
		"Array index not found",
		"Object not found",
		"Resource not found",
		"Memory limit exceeded",
		"Maximum execution time exceeded",
		"Allowed memory size exhausted",

		// Node.js错误触发
		"ReferenceError:",
		"TypeError:",
		"SyntaxError:",
		"RangeError:",
		"EvalError:",
		"URIError:",
		"Error:",
		"InternalError:",
		"at Object.",
		"at Function.",
		"at Module.",
		"at require",
		"at Object.Module._extensions",
		"at Module.load",
		"at Function.Module._load",
		"at Module.require",
		"at Object.<anonymous>",
		"at Module._compile",
		"at Object.Module._extensions..js",

		// Ruby错误触发
		"NoMethodError:",
		"NameError:",
		"ArgumentError:",
		"TypeError:",
		"RuntimeError:",
		"StandardError:",
		"SystemStackError:",
		"NoMemoryError:",
		"ScriptError:",
		"SyntaxError:",
		"LoadError:",
		"NotImplementedError:",
		"SecurityError:",
		"SystemCallError:",
		"Errno::",
		"from ",
		"in `",
		"block in",
		"rescue in",
		"ensure in",

		// 中文错误信息
		"空指针异常",
		"内存溢出",
		"堆栈溢出",
		"类未找到",
		"方法未找到",
		"参数错误",
		"状态错误",
		"运行时错误",
		"系统错误",
		"文件未找到",
		"权限错误",
		"访问被拒绝",
		"连接超时",
		"网络错误",
		"数据库错误",
		"配置错误",
		"解析错误",
		"编译错误",
		"执行错误",
		"初始化错误",
	}
}

// initializeDebugInfoPayloads 初始化调试信息载荷列表
func (d *MemoryLeakDetector) initializeDebugInfoPayloads() {
	d.debugInfoPayloads = []string{
		// 调试模式触发
		"debug", "DEBUG", "Debug", "TRACE", "trace", "Trace",
		"verbose", "VERBOSE", "Verbose", "info", "INFO", "Info",
		"development", "dev", "DEV", "test", "TEST", "Test",
		"staging", "STAGING", "Staging", "local", "LOCAL", "Local",

		// 调试参数
		"debug=true", "debug=1", "debug=on", "debug=yes",
		"trace=true", "trace=1", "trace=on", "trace=yes",
		"verbose=true", "verbose=1", "verbose=on", "verbose=yes",
		"log=debug", "log=trace", "log=verbose", "log=info",
		"level=debug", "level=trace", "level=verbose", "level=info",
		"mode=debug", "mode=development", "mode=dev", "mode=test",

		// 调试头部
		"X-Debug", "X-Trace", "X-Verbose", "X-Info", "X-Log",
		"Debug-Mode", "Trace-Mode", "Verbose-Mode", "Info-Mode",
		"Debug-Level", "Trace-Level", "Verbose-Level", "Info-Level",
		"Debug-Output", "Trace-Output", "Verbose-Output", "Info-Output",

		// 调试Cookie
		"debug_mode", "trace_mode", "verbose_mode", "info_mode",
		"debug_level", "trace_level", "verbose_level", "info_level",
		"debug_output", "trace_output", "verbose_output", "info_output",
		"debug_session", "trace_session", "verbose_session", "info_session",

		// 调试URL参数
		"?debug", "?trace", "?verbose", "?info", "?log",
		"&debug", "&trace", "&verbose", "&info", "&log",
		"debug.php", "trace.php", "verbose.php", "info.php", "log.php",
		"debug.jsp", "trace.jsp", "verbose.jsp", "info.jsp", "log.jsp",
		"debug.asp", "trace.asp", "verbose.asp", "info.asp", "log.asp",

		// 调试文件
		"debug.log", "trace.log", "verbose.log", "info.log", "error.log",
		"debug.txt", "trace.txt", "verbose.txt", "info.txt", "error.txt",
		"debug.xml", "trace.xml", "verbose.xml", "info.xml", "error.xml",
		"debug.json", "trace.json", "verbose.json", "info.json", "error.json",

		// 调试路径
		"/debug", "/trace", "/verbose", "/info", "/log",
		"/debug/", "/trace/", "/verbose/", "/info/", "/log/",
		"/admin/debug", "/admin/trace", "/admin/verbose", "/admin/info",
		"/system/debug", "/system/trace", "/system/verbose", "/system/info",
		"/api/debug", "/api/trace", "/api/verbose", "/api/info",

		// 中文调试信息
		"调试", "跟踪", "详细", "信息", "日志",
		"调试模式", "跟踪模式", "详细模式", "信息模式", "日志模式",
		"调试级别", "跟踪级别", "详细级别", "信息级别", "日志级别",
		"调试输出", "跟踪输出", "详细输出", "信息输出", "日志输出",
		"开发模式", "测试模式", "本地模式", "暂存模式",
	}
}

// initializeErrorInfoPayloads 初始化错误信息载荷列表
func (d *MemoryLeakDetector) initializeErrorInfoPayloads() {
	d.errorInfoPayloads = []string{
		// 错误触发参数
		"error", "ERROR", "Error", "exception", "EXCEPTION", "Exception",
		"fault", "FAULT", "Fault", "failure", "FAILURE", "Failure",
		"crash", "CRASH", "Crash", "abort", "ABORT", "Abort",

		// 错误参数
		"error=true", "error=1", "error=on", "error=yes",
		"exception=true", "exception=1", "exception=on", "exception=yes",
		"fault=true", "fault=1", "fault=on", "fault=yes",
		"failure=true", "failure=1", "failure=on", "failure=yes",
		"crash=true", "crash=1", "crash=on", "crash=yes",
		"abort=true", "abort=1", "abort=on", "abort=yes",

		// 错误头部
		"X-Error", "X-Exception", "X-Fault", "X-Failure", "X-Crash",
		"Error-Mode", "Exception-Mode", "Fault-Mode", "Failure-Mode",
		"Error-Level", "Exception-Level", "Fault-Level", "Failure-Level",
		"Error-Output", "Exception-Output", "Fault-Output", "Failure-Output",

		// 错误Cookie
		"error_mode", "exception_mode", "fault_mode", "failure_mode",
		"error_level", "exception_level", "fault_level", "failure_level",
		"error_output", "exception_output", "fault_output", "failure_output",
		"error_session", "exception_session", "fault_session", "failure_session",

		// 错误URL参数
		"?error", "?exception", "?fault", "?failure", "?crash",
		"&error", "&exception", "&fault", "&failure", "&crash",
		"error.php", "exception.php", "fault.php", "failure.php", "crash.php",
		"error.jsp", "exception.jsp", "fault.jsp", "failure.jsp", "crash.jsp",
		"error.asp", "exception.asp", "fault.asp", "failure.asp", "crash.asp",

		// 错误文件
		"error.log", "exception.log", "fault.log", "failure.log", "crash.log",
		"error.txt", "exception.txt", "fault.txt", "failure.txt", "crash.txt",
		"error.xml", "exception.xml", "fault.xml", "failure.xml", "crash.xml",
		"error.json", "exception.json", "fault.json", "failure.json", "crash.json",

		// 错误路径
		"/error", "/exception", "/fault", "/failure", "/crash",
		"/error/", "/exception/", "/fault/", "/failure/", "/crash/",
		"/admin/error", "/admin/exception", "/admin/fault", "/admin/failure",
		"/system/error", "/system/exception", "/system/fault", "/system/failure",
		"/api/error", "/api/exception", "/api/fault", "/api/failure",

		// 中文错误信息
		"错误", "异常", "故障", "失败", "崩溃",
		"错误模式", "异常模式", "故障模式", "失败模式", "崩溃模式",
		"错误级别", "异常级别", "故障级别", "失败级别", "崩溃级别",
		"错误输出", "异常输出", "故障输出", "失败输出", "崩溃输出",
		"错误信息", "异常信息", "故障信息", "失败信息", "崩溃信息",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *MemoryLeakDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 内存相关参数
		"memory", "mem", "heap", "stack", "buffer", "cache", "pool",
		"memory_limit", "memory_size", "memory_usage", "memory_info",
		"heap_size", "heap_limit", "heap_usage", "heap_info",
		"stack_size", "stack_limit", "stack_usage", "stack_info",
		"buffer_size", "buffer_limit", "buffer_usage", "buffer_info",
		"cache_size", "cache_limit", "cache_usage", "cache_info",

		// 调试相关参数
		"debug", "trace", "verbose", "info", "log", "logging",
		"debug_mode", "trace_mode", "verbose_mode", "info_mode", "log_mode",
		"debug_level", "trace_level", "verbose_level", "info_level", "log_level",
		"debug_output", "trace_output", "verbose_output", "info_output", "log_output",
		"debug_info", "trace_info", "verbose_info", "info_info", "log_info",

		// 错误相关参数
		"error", "exception", "fault", "failure", "crash", "abort",
		"error_mode", "exception_mode", "fault_mode", "failure_mode", "crash_mode",
		"error_level", "exception_level", "fault_level", "failure_level", "crash_level",
		"error_output", "exception_output", "fault_output", "failure_output", "crash_output",
		"error_info", "exception_info", "fault_info", "failure_info", "crash_info",

		// 状态相关参数
		"status", "state", "health", "monitor", "stats", "metrics",
		"status_info", "state_info", "health_info", "monitor_info", "stats_info",
		"status_check", "state_check", "health_check", "monitor_check", "stats_check",
		"status_report", "state_report", "health_report", "monitor_report", "stats_report",

		// 系统相关参数
		"system", "sys", "os", "platform", "arch", "version",
		"system_info", "sys_info", "os_info", "platform_info", "arch_info", "version_info",
		"system_status", "sys_status", "os_status", "platform_status", "arch_status",
		"system_config", "sys_config", "os_config", "platform_config", "arch_config",

		// 配置相关参数
		"config", "configuration", "settings", "properties", "options",
		"config_info", "configuration_info", "settings_info", "properties_info", "options_info",
		"config_dump", "configuration_dump", "settings_dump", "properties_dump", "options_dump",
		"config_debug", "configuration_debug", "settings_debug", "properties_debug", "options_debug",

		// 会话相关参数
		"session", "sess", "cookie", "cookies", "token", "tokens",
		"session_info", "sess_info", "cookie_info", "cookies_info", "token_info", "tokens_info",
		"session_debug", "sess_debug", "cookie_debug", "cookies_debug", "token_debug", "tokens_debug",
		"session_dump", "sess_dump", "cookie_dump", "cookies_dump", "token_dump", "tokens_dump",

		// 数据库相关参数
		"database", "db", "sql", "query", "connection", "conn",
		"database_info", "db_info", "sql_info", "query_info", "connection_info", "conn_info",
		"database_debug", "db_debug", "sql_debug", "query_debug", "connection_debug", "conn_debug",
		"database_dump", "db_dump", "sql_dump", "query_dump", "connection_dump", "conn_dump",

		// 网络相关参数
		"network", "net", "ip", "host", "port", "url", "uri",
		"network_info", "net_info", "ip_info", "host_info", "port_info", "url_info", "uri_info",
		"network_debug", "net_debug", "ip_debug", "host_debug", "port_debug", "url_debug", "uri_debug",
		"network_dump", "net_dump", "ip_dump", "host_dump", "port_dump", "url_dump", "uri_dump",

		// 文件相关参数
		"file", "filename", "filepath", "path", "directory", "folder",
		"file_info", "filename_info", "filepath_info", "path_info", "directory_info", "folder_info",
		"file_debug", "filename_debug", "filepath_debug", "path_debug", "directory_debug", "folder_debug",
		"file_dump", "filename_dump", "filepath_dump", "path_dump", "directory_dump", "folder_dump",

		// 用户相关参数
		"user", "username", "userid", "account", "profile", "identity",
		"user_info", "username_info", "userid_info", "account_info", "profile_info", "identity_info",
		"user_debug", "username_debug", "userid_debug", "account_debug", "profile_debug", "identity_debug",
		"user_dump", "username_dump", "userid_dump", "account_dump", "profile_dump", "identity_dump",

		// 应用相关参数
		"app", "application", "service", "module", "component", "plugin",
		"app_info", "application_info", "service_info", "module_info", "component_info", "plugin_info",
		"app_debug", "application_debug", "service_debug", "module_debug", "component_debug", "plugin_debug",
		"app_dump", "application_dump", "service_dump", "module_dump", "component_dump", "plugin_dump",

		// 中文参数
		"内存", "堆", "栈", "缓冲区", "缓存", "池",
		"调试", "跟踪", "详细", "信息", "日志", "记录",
		"错误", "异常", "故障", "失败", "崩溃", "中止",
		"状态", "健康", "监控", "统计", "指标", "报告",
		"系统", "操作系统", "平台", "架构", "版本", "配置",
		"会话", "Cookie", "令牌", "数据库", "网络", "文件",
		"用户", "用户名", "账户", "配置文件", "身份", "应用",
		"服务", "模块", "组件", "插件", "设置", "属性", "选项",
	}
}

// initializePatterns 初始化检测模式
func (d *MemoryLeakDetector) initializePatterns() {
	// 敏感信息检测模式 - 检测敏感信息泄露相关的响应内容
	sensitivePatternStrings := []string{
		// 数据库连接信息模式
		`(?i)(database|db).*=.*`,
		`(?i)(username|user).*=.*`,
		`(?i)(password|passwd|pwd).*=.*`,
		`(?i)(host|hostname|server).*=.*`,
		`(?i)(port|portnum).*=.*`,
		`(?i)(dsn|jdbc|odbc).*=.*`,

		// API密钥和令牌模式
		`(?i)(api_key|apikey|api-key).*=.*`,
		`(?i)(secret_key|secretkey|secret-key).*=.*`,
		`(?i)(access_token|accesstoken|access-token).*=.*`,
		`(?i)(refresh_token|refreshtoken).*=.*`,
		`(?i)(bearer|authorization|auth).*=.*`,
		`(?i)(private_key|privatekey|private-key).*=.*`,

		// 配置文件信息模式
		`(?i)(config|configuration|settings).*=.*`,
		`(?i)(properties|env|environment).*=.*`,
		`(?i)(ini|conf|cfg|xml|json|yaml|yml).*=.*`,

		// 系统信息模式
		`(?i)(version|build|commit|revision).*=.*`,
		`(?i)(os|platform|architecture|arch).*=.*`,
		`(?i)(java_home|path|classpath|pythonpath).*=.*`,

		// 会话和Cookie信息模式
		`(?i)(session|sessionid|session_id|session-id).*=.*`,
		`(?i)(cookie|cookies).*=.*`,
		`(?i)(csrf|csrftoken|csrf_token|csrf-token).*=.*`,
		`(?i)(xsrf|xsrftoken|xsrf_token|xsrf-token).*=.*`,

		// 文件路径信息模式
		`(?i)(file|filename|filepath|path).*=.*`,
		`(?i)(directory|folder|home|root).*=.*`,
		`(?i)(upload|download|temp|tmp|cache|backup).*=.*`,

		// 网络信息模式
		`(?i)(ip|ipaddress|ip_address|ip-address).*=.*`,
		`(?i)(localhost|127\.0\.0\.1|internal|private).*`,
		`(?i)(proxy|gateway|dns|nameserver).*=.*`,

		// 中文敏感信息模式
		`(?i)(数据库|连接|用户名|密码|密钥|令牌).*=.*`,
		`(?i)(配置|设置|版本|系统|会话|文件|路径|网络).*=.*`,
	}

	d.sensitivePatterns = make([]*regexp.Regexp, 0, len(sensitivePatternStrings))
	for _, pattern := range sensitivePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.sensitivePatterns = append(d.sensitivePatterns, compiled)
		}
	}

	// 内存泄露检测模式 - 检测内存溢出相关的响应内容
	memoryPatternStrings := []string{
		// 内存溢出错误模式
		`(?i)out\s+of\s+memory`,
		`(?i)memory\s+limit\s+(exceeded|reached)`,
		`(?i)memory\s+(exhausted|depleted)`,
		`(?i)heap\s+(space|overflow|exhausted)`,
		`(?i)stack\s+overflow`,
		`(?i)buffer\s+overflow`,
		`(?i)memory\s+allocation\s+(failed|error)`,

		// Java内存错误模式
		`(?i)java\.lang\.OutOfMemoryError`,
		`(?i)java\.lang\.StackOverflowError`,
		`(?i)OutOfMemoryError`,
		`(?i)StackOverflowError`,
		`(?i)MemoryError`,

		// .NET内存错误模式
		`(?i)System\.OutOfMemoryException`,
		`(?i)System\.StackOverflowException`,
		`(?i)OutOfMemoryException`,
		`(?i)StackOverflowException`,

		// Python内存错误模式
		`(?i)MemoryError`,
		`(?i)RecursionError`,
		`(?i)maximum\s+recursion\s+depth`,

		// PHP内存错误模式
		`(?i)Fatal\s+error.*memory\s+limit`,
		`(?i)Allowed\s+memory\s+size.*exhausted`,
		`(?i)Maximum\s+execution\s+time.*exceeded`,

		// Node.js内存错误模式
		`(?i)RangeError.*Maximum\s+call\s+stack`,
		`(?i)FATAL\s+ERROR.*Reached\s+heap\s+limit`,
		`(?i)JavaScript\s+heap\s+out\s+of\s+memory`,

		// 垃圾回收相关模式
		`(?i)gc\s+overhead\s+limit`,
		`(?i)garbage\s+collection\s+(failed|error)`,
		`(?i)heap\s+dump`,
		`(?i)memory\s+leak`,

		// 中文内存错误模式
		`(?i)(内存|堆栈|缓冲区).*溢出`,
		`(?i)内存.*不足`,
		`(?i)内存.*超限`,
		`(?i)内存.*分配.*失败`,
		`(?i)内存.*泄露`,
		`(?i)内存.*耗尽`,
		`(?i)垃圾.*回收.*失败`,
		`(?i)堆.*转储`,
	}

	d.memoryPatterns = make([]*regexp.Regexp, 0, len(memoryPatternStrings))
	for _, pattern := range memoryPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.memoryPatterns = append(d.memoryPatterns, compiled)
		}
	}

	// 堆栈跟踪检测模式 - 检测堆栈跟踪相关的响应内容
	stackPatternStrings := []string{
		// 通用堆栈跟踪模式
		`(?i)traceback\s*\(most\s+recent\s+call\s+last\):`,
		`(?i)stack\s+trace`,
		`(?i)stacktrace`,
		`(?i)call\s+stack`,

		// Java堆栈跟踪模式
		`(?i)at\s+java\.`,
		`(?i)at\s+org\.`,
		`(?i)at\s+com\.`,
		`(?i)at\s+javax\.`,
		`(?i)Exception\s+in\s+thread`,
		`(?i)Caused\s+by:`,

		// .NET堆栈跟踪模式
		`(?i)at\s+System\.`,
		`(?i)at\s+Microsoft\.`,
		`(?i)Unhandled\s+exception`,
		`(?i)Inner\s+Exception`,

		// Python堆栈跟踪模式
		`(?i)File\s+".*",\s+line\s+\d+`,
		`(?i)in\s+<module>`,
		`(?i)in\s+\w+`,

		// PHP堆栈跟踪模式
		`(?i)Fatal\s+error:`,
		`(?i)Parse\s+error:`,
		`(?i)Warning:`,
		`(?i)Notice:`,
		`(?i)in\s+/.+\s+on\s+line\s+\d+`,

		// Node.js堆栈跟踪模式
		`(?i)at\s+Object\.`,
		`(?i)at\s+Function\.`,
		`(?i)at\s+Module\.`,
		`(?i)at\s+require`,

		// Ruby堆栈跟踪模式
		`(?i)from\s+/.+:\d+:in\s+`,
		`(?i)block\s+in\s+`,
		`(?i)rescue\s+in\s+`,

		// 中文堆栈跟踪模式
		`(?i)(异常|错误).*堆栈`,
		`(?i)堆栈.*跟踪`,
		`(?i)(调用|函数).*堆栈`,
		`(?i)(文件|行号).*错误`,
		`(?i)(空指针|内存|堆栈).*异常`,
	}

	d.stackPatterns = make([]*regexp.Regexp, 0, len(stackPatternStrings))
	for _, pattern := range stackPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.stackPatterns = append(d.stackPatterns, compiled)
		}
	}

	// 调试信息检测模式 - 检测调试信息相关的响应内容
	debugPatternStrings := []string{
		// 调试模式模式
		`(?i)debug\s*(mode|level|output|info)`,
		`(?i)trace\s*(mode|level|output|info)`,
		`(?i)verbose\s*(mode|level|output|info)`,
		`(?i)info\s*(mode|level|output)`,
		`(?i)log\s*(mode|level|output|info)`,

		// 调试参数模式
		`(?i)debug\s*=\s*(true|1|on|yes)`,
		`(?i)trace\s*=\s*(true|1|on|yes)`,
		`(?i)verbose\s*=\s*(true|1|on|yes)`,
		`(?i)info\s*=\s*(true|1|on|yes)`,
		`(?i)log\s*=\s*(debug|trace|verbose|info)`,

		// 调试头部模式
		`(?i)x-debug`,
		`(?i)x-trace`,
		`(?i)x-verbose`,
		`(?i)x-info`,
		`(?i)debug-mode`,
		`(?i)trace-mode`,

		// 开发环境模式
		`(?i)development\s+(mode|environment)`,
		`(?i)dev\s+(mode|environment)`,
		`(?i)test\s+(mode|environment)`,
		`(?i)staging\s+(mode|environment)`,
		`(?i)local\s+(mode|environment)`,

		// 调试文件模式
		`(?i)debug\.(log|txt|xml|json)`,
		`(?i)trace\.(log|txt|xml|json)`,
		`(?i)verbose\.(log|txt|xml|json)`,
		`(?i)info\.(log|txt|xml|json)`,
		`(?i)error\.(log|txt|xml|json)`,

		// 中文调试信息模式
		`(?i)(调试|跟踪|详细|信息).*模式`,
		`(?i)(调试|跟踪|详细|信息).*级别`,
		`(?i)(调试|跟踪|详细|信息).*输出`,
		`(?i)(开发|测试|本地|暂存).*模式`,
		`(?i)(调试|跟踪|详细|信息).*日志`,
	}

	d.debugPatterns = make([]*regexp.Regexp, 0, len(debugPatternStrings))
	for _, pattern := range debugPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.debugPatterns = append(d.debugPatterns, compiled)
		}
	}

	// 错误信息检测模式 - 检测错误信息相关的响应内容
	errorPatternStrings := []string{
		// 通用错误模式
		`(?i)error\s*(message|info|details)`,
		`(?i)exception\s*(message|info|details)`,
		`(?i)fault\s*(message|info|details)`,
		`(?i)failure\s*(message|info|details)`,
		`(?i)crash\s*(message|info|details)`,

		// 错误参数模式
		`(?i)error\s*=\s*(true|1|on|yes)`,
		`(?i)exception\s*=\s*(true|1|on|yes)`,
		`(?i)fault\s*=\s*(true|1|on|yes)`,
		`(?i)failure\s*=\s*(true|1|on|yes)`,
		`(?i)crash\s*=\s*(true|1|on|yes)`,

		// 错误头部模式
		`(?i)x-error`,
		`(?i)x-exception`,
		`(?i)x-fault`,
		`(?i)x-failure`,
		`(?i)error-mode`,
		`(?i)exception-mode`,

		// 错误文件模式
		`(?i)error\.(log|txt|xml|json)`,
		`(?i)exception\.(log|txt|xml|json)`,
		`(?i)fault\.(log|txt|xml|json)`,
		`(?i)failure\.(log|txt|xml|json)`,
		`(?i)crash\.(log|txt|xml|json)`,

		// 中文错误信息模式
		`(?i)(错误|异常|故障|失败|崩溃).*信息`,
		`(?i)(错误|异常|故障|失败|崩溃).*模式`,
		`(?i)(错误|异常|故障|失败|崩溃).*级别`,
		`(?i)(错误|异常|故障|失败|崩溃).*输出`,
		`(?i)(错误|异常|故障|失败|崩溃).*日志`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}
}
