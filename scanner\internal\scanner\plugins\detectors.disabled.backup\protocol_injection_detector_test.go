package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestProtocolInjectionDetectorBasicFunctionality 测试协议注入检测器基础功能
func TestProtocolInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewProtocolInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "protocol-injection-comprehensive", detector.GetID())
	assert.Equal(t, "协议注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-74")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestProtocolInjectionDetectorApplicability 测试协议注入检测器适用性
func TestProtocolInjectionDetectorApplicability(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 测试有协议头部的目标
	headerTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/protocol",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Server":           "Apache/2.4.41",
			"X-Forwarded-For":  "127.0.0.1",
			"X-Forwarded-Host": "evil.com",
		},
	}
	assert.True(t, detector.IsApplicable(headerTarget))

	// 测试有协议技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Apache", Version: "2.4.41", Confidence: 0.9},
			{Name: "Nginx", Version: "1.18.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有协议链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/protocol/test", Text: "Protocol Test"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有协议表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"protocol": "text", "http": "text", "smtp": "text"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/test?protocol=http",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试协议相关URL
	protocolURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/smtp/injection",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(protocolURLTarget))

	// 测试普通Web目标（协议注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestProtocolInjectionDetectorConfiguration 测试协议注入检测器配置
func TestProtocolInjectionDetectorConfiguration(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 2, config.Concurrency)
	assert.True(t, config.FollowRedirects) // 协议注入跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 4 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 3, updatedConfig.Concurrency)
}

// TestProtocolInjectionDetectorHTTPPayloads 测试HTTP协议载荷
func TestProtocolInjectionDetectorHTTPPayloads(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查HTTP协议载荷列表
	assert.NotEmpty(t, detector.httpProtocolPayloads)
	assert.GreaterOrEqual(t, len(detector.httpProtocolPayloads), 20)

	// 检查HTTP头部注入载荷
	assert.Contains(t, detector.httpProtocolPayloads, "\r\nSet-Cookie: injected=true")
	assert.Contains(t, detector.httpProtocolPayloads, "\r\nLocation: http://evil.com")
	assert.Contains(t, detector.httpProtocolPayloads, "\r\nContent-Type: text/html\r\n\r\n<script>alert('XSS')</script>")
	assert.Contains(t, detector.httpProtocolPayloads, "\r\nX-Injected-Header: true")

	// 检查CRLF注入载荷
	assert.Contains(t, detector.httpProtocolPayloads, "%0d%0aSet-Cookie: injected=true")
	assert.Contains(t, detector.httpProtocolPayloads, "%0d%0aLocation: http://evil.com")
	assert.Contains(t, detector.httpProtocolPayloads, "%0a%0dSet-Cookie: injected=true")
	assert.Contains(t, detector.httpProtocolPayloads, "%0a%0dLocation: http://evil.com")

	// 检查HTTP响应分割载荷
	assert.Contains(t, detector.httpProtocolPayloads, "\r\n\r\nHTTP/1.1 200 OK\r\nContent-Type: text/html\r\n\r\n<html><body>Injected</body></html>")
	assert.Contains(t, detector.httpProtocolPayloads, "%0d%0a%0d%0aHTTP/1.1 200 OK%0d%0aContent-Type: text/html%0d%0a%0d%0a<html><body>Injected</body></html>")

	// 检查HTTP请求走私载荷
	assert.Contains(t, detector.httpProtocolPayloads, "GET /admin HTTP/1.1\r\nHost: localhost\r\n\r\n")
	assert.Contains(t, detector.httpProtocolPayloads, "POST /admin HTTP/1.1\r\nHost: localhost\r\nContent-Length: 0\r\n\r\n")

	// 检查HTTP方法注入载荷
	assert.Contains(t, detector.httpProtocolPayloads, "GET\r\nHost: evil.com\r\n\r\n")
	assert.Contains(t, detector.httpProtocolPayloads, "POST\r\nHost: evil.com\r\n\r\n")

	// 检查中文HTTP协议载荷
	assert.Contains(t, detector.httpProtocolPayloads, "\r\n注入头部: 测试")
	assert.Contains(t, detector.httpProtocolPayloads, "%0d%0a注入头部: 测试")
}

// TestProtocolInjectionDetectorSMTPPayloads 测试SMTP协议载荷
func TestProtocolInjectionDetectorSMTPPayloads(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查SMTP协议载荷列表
	assert.NotEmpty(t, detector.smtpProtocolPayloads)
	assert.GreaterOrEqual(t, len(detector.smtpProtocolPayloads), 20)

	// 检查SMTP命令注入载荷
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nMAIL FROM: <<EMAIL>>")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nRCPT TO: <<EMAIL>>")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nDATA\r\nSubject: Injected Email\r\n\r\nThis is an injected email.\r\n.")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nQUIT")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nHELO evil.com")

	// 检查SMTP头部注入载荷
	assert.Contains(t, detector.smtpProtocolPayloads, "%0d%0aMAIL FROM: <<EMAIL>>")
	assert.Contains(t, detector.smtpProtocolPayloads, "%0d%0aRCPT TO: <<EMAIL>>")
	assert.Contains(t, detector.smtpProtocolPayloads, "%0a%0dMAIL FROM: <<EMAIL>>")

	// 检查SMTP邮件头注入载荷
	assert.Contains(t, detector.smtpProtocolPayloads, "\nBcc: <EMAIL>")
	assert.Contains(t, detector.smtpProtocolPayloads, "\nCc: <EMAIL>")
	assert.Contains(t, detector.smtpProtocolPayloads, "\nSubject: Injected Subject")
	assert.Contains(t, detector.smtpProtocolPayloads, "\nFrom: <EMAIL>")

	// 检查SMTP内容注入载荷
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\n\r\nThis is injected content in the email body.")
	assert.Contains(t, detector.smtpProtocolPayloads, "%0d%0a%0d%0aThis is injected content in the email body.")

	// 检查SMTP认证绕过载荷
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nAUTH PLAIN dGVzdAB0ZXN0AHRlc3Q=")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nAUTH LOGIN")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\nSTARTTLS")

	// 检查中文SMTP协议载荷
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\n发件人: <攻击者@evil.com>")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\n收件人: <受害者@target.com>")
	assert.Contains(t, detector.smtpProtocolPayloads, "\r\n主题: 注入邮件")
}

// TestProtocolInjectionDetectorFTPPayloads 测试FTP协议载荷
func TestProtocolInjectionDetectorFTPPayloads(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查FTP协议载荷列表
	assert.NotEmpty(t, detector.ftpProtocolPayloads)
	assert.GreaterOrEqual(t, len(detector.ftpProtocolPayloads), 20)

	// 检查FTP命令注入载荷
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nUSER anonymous")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nPASS guest")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nLIST")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nPWD")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nCWD /")

	// 检查FTP路径遍历载荷
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nRETR ../../../etc/passwd")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nSTOR ../../../tmp/injected.txt")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nCWD ../../../")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nLIST ../../../")

	// 检查FTP端口注入载荷
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nPORT 127,0,0,1,0,22")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nPASV")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nEPRT |1|127.0.0.1|22|")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nEPSV")

	// 检查FTP数据注入载荷
	assert.Contains(t, detector.ftpProtocolPayloads, "%0d%0aUSER anonymous")
	assert.Contains(t, detector.ftpProtocolPayloads, "%0d%0aPASS guest")
	assert.Contains(t, detector.ftpProtocolPayloads, "%0a%0dUSER anonymous")

	// 检查FTP认证绕过载荷
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nUSER root")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nPASS admin")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\nUSER ftp")

	// 检查中文FTP协议载荷
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\n用户 匿名")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\n密码 访客")
	assert.Contains(t, detector.ftpProtocolPayloads, "\r\n列表")
}

// TestProtocolInjectionDetectorLDAPPayloads 测试LDAP协议载荷
func TestProtocolInjectionDetectorLDAPPayloads(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查LDAP协议载荷列表
	assert.NotEmpty(t, detector.ldapProtocolPayloads)
	assert.GreaterOrEqual(t, len(detector.ldapProtocolPayloads), 20)

	// 检查LDAP过滤器注入载荷
	assert.Contains(t, detector.ldapProtocolPayloads, "*)(uid=*))(|(uid=*")
	assert.Contains(t, detector.ldapProtocolPayloads, "*)(|(password=*))")
	assert.Contains(t, detector.ldapProtocolPayloads, "admin)(&(password=*))")
	assert.Contains(t, detector.ldapProtocolPayloads, "*))%00")
	assert.Contains(t, detector.ldapProtocolPayloads, "*()|%00")

	// 检查LDAP搜索注入载荷
	assert.Contains(t, detector.ldapProtocolPayloads, "(&(objectClass=*)(uid=*))")
	assert.Contains(t, detector.ldapProtocolPayloads, "(|(uid=*)(cn=*))")
	assert.Contains(t, detector.ldapProtocolPayloads, "(&(!(uid=*))(password=*))")
	assert.Contains(t, detector.ldapProtocolPayloads, "(objectClass=*)")
	assert.Contains(t, detector.ldapProtocolPayloads, "(uid=*)")

	// 检查LDAP DN注入载荷
	assert.Contains(t, detector.ldapProtocolPayloads, "cn=admin,dc=example,dc=com")
	assert.Contains(t, detector.ldapProtocolPayloads, "uid=*,ou=people,dc=example,dc=com")
	assert.Contains(t, detector.ldapProtocolPayloads, "ou=*,dc=example,dc=com")
	assert.Contains(t, detector.ldapProtocolPayloads, "dc=*")

	// 检查LDAP属性注入载荷
	assert.Contains(t, detector.ldapProtocolPayloads, "userPassword")
	assert.Contains(t, detector.ldapProtocolPayloads, "sambaNTPassword")
	assert.Contains(t, detector.ldapProtocolPayloads, "sambaLMPassword")
	assert.Contains(t, detector.ldapProtocolPayloads, "unicodePwd")
	assert.Contains(t, detector.ldapProtocolPayloads, "userAccountControl")

	// 检查LDAP操作注入载荷
	assert.Contains(t, detector.ldapProtocolPayloads, "add: objectClass")
	assert.Contains(t, detector.ldapProtocolPayloads, "replace: userPassword")
	assert.Contains(t, detector.ldapProtocolPayloads, "delete: uid")
	assert.Contains(t, detector.ldapProtocolPayloads, "modify: cn")

	// 检查中文LDAP协议载荷
	assert.Contains(t, detector.ldapProtocolPayloads, "用户=*")
	assert.Contains(t, detector.ldapProtocolPayloads, "密码=*")
	assert.Contains(t, detector.ldapProtocolPayloads, "组织=*")
	assert.Contains(t, detector.ldapProtocolPayloads, "部门=*")
	assert.Contains(t, detector.ldapProtocolPayloads, "姓名=*")
}

// TestProtocolInjectionDetectorDNSPayloads 测试DNS协议载荷
func TestProtocolInjectionDetectorDNSPayloads(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查DNS协议载荷列表
	assert.NotEmpty(t, detector.dnsProtocolPayloads)
	assert.GreaterOrEqual(t, len(detector.dnsProtocolPayloads), 15)

	// 检查DNS查询注入载荷
	assert.Contains(t, detector.dnsProtocolPayloads, "evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "*.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "subdomain.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "127.0.0.1.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "localhost.evil.com")

	// 检查DNS记录类型注入载荷
	assert.Contains(t, detector.dnsProtocolPayloads, "A evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "AAAA evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "CNAME evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "MX evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "TXT evil.com")

	// 检查DNS缓存投毒载荷
	assert.Contains(t, detector.dnsProtocolPayloads, "cache.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "poison.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "spoof.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "fake.evil.com")

	// 检查DNS隧道载荷
	assert.Contains(t, detector.dnsProtocolPayloads, "tunnel.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "exfiltrate.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "data.evil.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "command.evil.com")

	// 检查中文DNS协议载荷
	assert.Contains(t, detector.dnsProtocolPayloads, "恶意.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "攻击.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "隧道.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "数据.com")
	assert.Contains(t, detector.dnsProtocolPayloads, "命令.com")
}

// TestProtocolInjectionDetectorSSHPayloads 测试SSH协议载荷
func TestProtocolInjectionDetectorSSHPayloads(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查SSH协议载荷列表
	assert.NotEmpty(t, detector.sshProtocolPayloads)
	assert.GreaterOrEqual(t, len(detector.sshProtocolPayloads), 15)

	// 检查SSH命令注入载荷
	assert.Contains(t, detector.sshProtocolPayloads, "SSH-2.0-OpenSSH_7.4")
	assert.Contains(t, detector.sshProtocolPayloads, "SSH-1.99-OpenSSH_3.9p1")
	assert.Contains(t, detector.sshProtocolPayloads, "SSH-2.0-libssh_0.6.3")
	assert.Contains(t, detector.sshProtocolPayloads, "SSH-2.0-PuTTY_Release_0.70")

	// 检查SSH认证注入载荷
	assert.Contains(t, detector.sshProtocolPayloads, "root")
	assert.Contains(t, detector.sshProtocolPayloads, "admin")
	assert.Contains(t, detector.sshProtocolPayloads, "user")
	assert.Contains(t, detector.sshProtocolPayloads, "guest")
	assert.Contains(t, detector.sshProtocolPayloads, "test")

	// 检查SSH密钥注入载荷
	assert.Contains(t, detector.sshProtocolPayloads, "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ")
	assert.Contains(t, detector.sshProtocolPayloads, "ssh-dss AAAAB3NzaC1kc3MAAACBA")
	assert.Contains(t, detector.sshProtocolPayloads, "ecdsa-sha2-nistp256 AAAAE2VjZHNh")
	assert.Contains(t, detector.sshProtocolPayloads, "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5")

	// 检查SSH隧道注入载荷
	assert.Contains(t, detector.sshProtocolPayloads, "-L 8080:localhost:80")
	assert.Contains(t, detector.sshProtocolPayloads, "-R 8080:localhost:80")
	assert.Contains(t, detector.sshProtocolPayloads, "-D 1080")
	assert.Contains(t, detector.sshProtocolPayloads, "-N -f")

	// 检查中文SSH协议载荷
	assert.Contains(t, detector.sshProtocolPayloads, "用户名")
	assert.Contains(t, detector.sshProtocolPayloads, "密码")
	assert.Contains(t, detector.sshProtocolPayloads, "密钥")
	assert.Contains(t, detector.sshProtocolPayloads, "隧道")
	assert.Contains(t, detector.sshProtocolPayloads, "转发")
}

// TestProtocolInjectionDetectorTestParameters 测试参数列表
func TestProtocolInjectionDetectorTestParameters(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查协议相关参数
	assert.Contains(t, detector.testParameters, "protocol")
	assert.Contains(t, detector.testParameters, "proto")
	assert.Contains(t, detector.testParameters, "scheme")
	assert.Contains(t, detector.testParameters, "method")
	assert.Contains(t, detector.testParameters, "type")
	assert.Contains(t, detector.testParameters, "format")
	assert.Contains(t, detector.testParameters, "encoding")
	assert.Contains(t, detector.testParameters, "http")
	assert.Contains(t, detector.testParameters, "https")
	assert.Contains(t, detector.testParameters, "smtp")
	assert.Contains(t, detector.testParameters, "ftp")
	assert.Contains(t, detector.testParameters, "ldap")
	assert.Contains(t, detector.testParameters, "dns")
	assert.Contains(t, detector.testParameters, "ssh")

	// 检查网络相关参数
	assert.Contains(t, detector.testParameters, "host")
	assert.Contains(t, detector.testParameters, "hostname")
	assert.Contains(t, detector.testParameters, "server")
	assert.Contains(t, detector.testParameters, "domain")
	assert.Contains(t, detector.testParameters, "subdomain")
	assert.Contains(t, detector.testParameters, "ip")
	assert.Contains(t, detector.testParameters, "address")
	assert.Contains(t, detector.testParameters, "port")
	assert.Contains(t, detector.testParameters, "endpoint")
	assert.Contains(t, detector.testParameters, "gateway")
	assert.Contains(t, detector.testParameters, "proxy")
	assert.Contains(t, detector.testParameters, "tunnel")
	assert.Contains(t, detector.testParameters, "redirect")
	assert.Contains(t, detector.testParameters, "forward")

	// 检查邮件相关参数
	assert.Contains(t, detector.testParameters, "email")
	assert.Contains(t, detector.testParameters, "mail")
	assert.Contains(t, detector.testParameters, "from")
	assert.Contains(t, detector.testParameters, "to")
	assert.Contains(t, detector.testParameters, "cc")
	assert.Contains(t, detector.testParameters, "bcc")
	assert.Contains(t, detector.testParameters, "subject")
	assert.Contains(t, detector.testParameters, "message")
	assert.Contains(t, detector.testParameters, "sender")
	assert.Contains(t, detector.testParameters, "recipient")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "协议")
	assert.Contains(t, detector.testParameters, "方案")
	assert.Contains(t, detector.testParameters, "方法")
	assert.Contains(t, detector.testParameters, "类型")
	assert.Contains(t, detector.testParameters, "格式")
	assert.Contains(t, detector.testParameters, "编码")
	assert.Contains(t, detector.testParameters, "地址")
	assert.Contains(t, detector.testParameters, "主机")
	assert.Contains(t, detector.testParameters, "服务器")
	assert.Contains(t, detector.testParameters, "域名")
	assert.Contains(t, detector.testParameters, "端口")
	assert.Contains(t, detector.testParameters, "网关")
	assert.Contains(t, detector.testParameters, "代理")
	assert.Contains(t, detector.testParameters, "隧道")
	assert.Contains(t, detector.testParameters, "重定向")
	assert.Contains(t, detector.testParameters, "转发")
	assert.Contains(t, detector.testParameters, "邮件")
	assert.Contains(t, detector.testParameters, "发件人")
	assert.Contains(t, detector.testParameters, "收件人")
	assert.Contains(t, detector.testParameters, "主题")
	assert.Contains(t, detector.testParameters, "消息")
}

// TestProtocolInjectionDetectorPatterns 测试模式
func TestProtocolInjectionDetectorPatterns(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 检查HTTP协议模式
	assert.NotEmpty(t, detector.httpPatterns)
	assert.GreaterOrEqual(t, len(detector.httpPatterns), 15)

	// 检查SMTP协议模式
	assert.NotEmpty(t, detector.smtpPatterns)
	assert.GreaterOrEqual(t, len(detector.smtpPatterns), 15)

	// 检查FTP协议模式
	assert.NotEmpty(t, detector.ftpPatterns)
	assert.GreaterOrEqual(t, len(detector.ftpPatterns), 15)

	// 检查LDAP协议模式
	assert.NotEmpty(t, detector.ldapPatterns)
	assert.GreaterOrEqual(t, len(detector.ldapPatterns), 15)

	// 检查DNS协议模式
	assert.NotEmpty(t, detector.dnsPatterns)
	assert.GreaterOrEqual(t, len(detector.dnsPatterns), 15)

	// 检查SSH协议模式
	assert.NotEmpty(t, detector.sshPatterns)
	assert.GreaterOrEqual(t, len(detector.sshPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestProtocolInjectionDetectorProtocolFeatures 测试协议功能检查
func TestProtocolInjectionDetectorProtocolFeatures(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 测试有协议头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Server":           "Apache/2.4.41",
			"X-Forwarded-For":  "127.0.0.1",
			"X-Forwarded-Host": "evil.com",
		},
	}
	assert.True(t, detector.hasProtocolFeatures(headerTarget))

	// 测试有协议技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Apache", Version: "2.4.41", Confidence: 0.9},
			{Name: "Nginx", Version: "1.18.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasProtocolFeatures(techTarget))

	// 测试有协议链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/protocol/test", Text: "Protocol Test"},
		},
	}
	assert.True(t, detector.hasProtocolFeatures(linkTarget))

	// 测试有协议表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"protocol": "text", "http": "text", "smtp": "text"}},
		},
	}
	assert.True(t, detector.hasProtocolFeatures(formTarget))

	// 测试无协议功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasProtocolFeatures(simpleTarget))
}

// TestProtocolInjectionDetectorRiskScore 测试风险评分计算
func TestProtocolInjectionDetectorRiskScore(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.6)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.5)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestProtocolInjectionDetectorLifecycle 测试检测器生命周期
func TestProtocolInjectionDetectorLifecycle(t *testing.T) {
	detector := NewProtocolInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
