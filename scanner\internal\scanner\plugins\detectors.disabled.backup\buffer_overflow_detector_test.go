package detectors

import (
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestBufferOverflowDetectorBasicFunctionality 测试缓冲区溢出检测器基础功能
func TestBufferOverflowDetectorBasicFunctionality(t *testing.T) {
	detector := NewBufferOverflowDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "buffer-overflow-comprehensive", detector.GetID())
	assert.Equal(t, "缓冲区溢出漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "critical", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-120")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-44228")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestBufferOverflowDetectorApplicability 测试缓冲区溢出检测器适用性
func TestBufferOverflowDetectorApplicability(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 测试有缓冲区头部的目标
	headerTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/buffer",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Server":       "Apache/2.4.41",
			"X-Powered-By": "PHP/7.4.0",
		},
	}
	assert.True(t, detector.IsApplicable(headerTarget))

	// 测试有缓冲区技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Apache", Version: "2.4.41", Confidence: 0.9},
			{Name: "PHP", Version: "7.4.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有缓冲区链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/buffer/test", Text: "Buffer Test"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有缓冲区表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"buffer": "text", "data": "textarea"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/test?buffer=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试缓冲区相关URL
	bufferURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/memory/overflow",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(bufferURLTarget))

	// 测试普通Web目标（缓冲区溢出是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestBufferOverflowDetectorConfiguration 测试缓冲区溢出检测器配置
func TestBufferOverflowDetectorConfiguration(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 2, config.Concurrency)
	assert.True(t, config.FollowRedirects) // 缓冲区溢出跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         25 * time.Second,
		MaxRetries:      3,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 6 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 25*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 3, updatedConfig.Concurrency)
}

// TestBufferOverflowDetectorStackPayloads 测试栈溢出载荷
func TestBufferOverflowDetectorStackPayloads(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查栈溢出载荷列表
	assert.NotEmpty(t, detector.stackOverflowPayloads)
	assert.GreaterOrEqual(t, len(detector.stackOverflowPayloads), 25)

	// 检查基础栈溢出载荷
	found100 := false
	found256 := false
	found1024 := false
	found4096 := false

	for _, payload := range detector.stackOverflowPayloads {
		if len(payload) == 100 && payload == strings.Repeat("A", 100) {
			found100 = true
		}
		if len(payload) == 256 && payload == strings.Repeat("A", 256) {
			found256 = true
		}
		if len(payload) == 1024 && payload == strings.Repeat("A", 1024) {
			found1024 = true
		}
		if len(payload) == 4096 && payload == strings.Repeat("A", 4096) {
			found4096 = true
		}
	}

	assert.True(t, found100, "应该包含100字节的栈溢出载荷")
	assert.True(t, found256, "应该包含256字节的栈溢出载荷")
	assert.True(t, found1024, "应该包含1024字节的栈溢出载荷")
	assert.True(t, found4096, "应该包含4096字节的栈溢出载荷")

	// 检查模式化栈溢出载荷
	foundAAAA := false
	foundBBBB := false

	for _, payload := range detector.stackOverflowPayloads {
		if payload == strings.Repeat("AAAA", 64) {
			foundAAAA = true
		}
		if payload == strings.Repeat("BBBB", 128) {
			foundBBBB = true
		}
	}

	assert.True(t, foundAAAA, "应该包含AAAA模式的栈溢出载荷")
	assert.True(t, foundBBBB, "应该包含BBBB模式的栈溢出载荷")

	// 检查特殊字符栈溢出载荷
	foundHex41 := false
	foundNOP := false

	for _, payload := range detector.stackOverflowPayloads {
		if payload == strings.Repeat("\x41", 1024) {
			foundHex41 = true
		}
		if payload == strings.Repeat("\x90", 1024) {
			foundNOP = true
		}
	}

	assert.True(t, foundHex41, "应该包含十六进制字符的栈溢出载荷")
	assert.True(t, foundNOP, "应该包含NOP滑板的栈溢出载荷")

	// 检查格式化字符串栈溢出
	foundFormatS := false
	foundFormatX := false

	for _, payload := range detector.stackOverflowPayloads {
		if payload == strings.Repeat("%s", 100) {
			foundFormatS = true
		}
		if payload == strings.Repeat("%x", 100) {
			foundFormatX = true
		}
	}

	assert.True(t, foundFormatS, "应该包含%s格式化字符串载荷")
	assert.True(t, foundFormatX, "应该包含%x格式化字符串载荷")

	// 检查中文栈溢出载荷
	foundChinese := false

	for _, payload := range detector.stackOverflowPayloads {
		if payload == strings.Repeat("测试", 512) {
			foundChinese = true
			break
		}
	}

	assert.True(t, foundChinese, "应该包含中文栈溢出载荷")
}

// TestBufferOverflowDetectorHeapPayloads 测试堆溢出载荷
func TestBufferOverflowDetectorHeapPayloads(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查堆溢出载荷列表
	assert.NotEmpty(t, detector.heapOverflowPayloads)
	assert.GreaterOrEqual(t, len(detector.heapOverflowPayloads), 20)

	// 检查基础堆溢出载荷
	found1000 := false
	found4000 := false
	found16000 := false
	found65536 := false

	for _, payload := range detector.heapOverflowPayloads {
		if len(payload) == 1000 && payload == strings.Repeat("H", 1000) {
			found1000 = true
		}
		if len(payload) == 4000 && payload == strings.Repeat("H", 4000) {
			found4000 = true
		}
		if len(payload) == 16000 && payload == strings.Repeat("H", 16000) {
			found16000 = true
		}
		if len(payload) == 65536 && payload == strings.Repeat("H", 65536) {
			found65536 = true
		}
	}

	assert.True(t, found1000, "应该包含1000字节的堆溢出载荷")
	assert.True(t, found4000, "应该包含4000字节的堆溢出载荷")
	assert.True(t, found16000, "应该包含16000字节的堆溢出载荷")
	assert.True(t, found65536, "应该包含65536字节的堆溢出载荷")

	// 检查模式化堆溢出载荷
	foundHEAP := false
	foundOVER := false

	for _, payload := range detector.heapOverflowPayloads {
		if payload == strings.Repeat("HEAP", 250) {
			foundHEAP = true
		}
		if payload == strings.Repeat("OVER", 500) {
			foundOVER = true
		}
	}

	assert.True(t, foundHEAP, "应该包含HEAP模式的堆溢出载荷")
	assert.True(t, foundOVER, "应该包含OVER模式的堆溢出载荷")

	// 检查堆喷射载荷
	foundSpray := false

	for _, payload := range detector.heapOverflowPayloads {
		if payload == strings.Repeat("\x0C\x0C\x0C\x0C", 4000) {
			foundSpray = true
			break
		}
	}

	assert.True(t, foundSpray, "应该包含堆喷射载荷")

	// 检查中文堆溢出载荷
	foundChinese := false

	for _, payload := range detector.heapOverflowPayloads {
		if payload == strings.Repeat("堆", 2000) {
			foundChinese = true
			break
		}
	}

	assert.True(t, foundChinese, "应该包含中文堆溢出载荷")
}

// TestBufferOverflowDetectorFormatStringPayloads 测试格式化字符串载荷
func TestBufferOverflowDetectorFormatStringPayloads(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查格式化字符串载荷列表
	assert.NotEmpty(t, detector.formatStringPayloads)
	assert.GreaterOrEqual(t, len(detector.formatStringPayloads), 20)

	// 检查基础格式化字符串载荷
	assert.Contains(t, detector.formatStringPayloads, "%s%s%s%s%s%s%s%s%s%s")
	assert.Contains(t, detector.formatStringPayloads, "%x%x%x%x%x%x%x%x%x%x")
	assert.Contains(t, detector.formatStringPayloads, "%p%p%p%p%p%p%p%p%p%p")
	assert.Contains(t, detector.formatStringPayloads, "%n%n%n%n%n%n%n%n%n%n")

	// 检查栈读取载荷
	assert.Contains(t, detector.formatStringPayloads, "%08x.%08x.%08x.%08x.%08x.%08x.%08x.%08x")
	assert.Contains(t, detector.formatStringPayloads, "%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x")
	assert.Contains(t, detector.formatStringPayloads, "%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p")

	// 检查内存写入载荷
	assert.Contains(t, detector.formatStringPayloads, "AAAA%08x.%08x.%08x.%08x.%08x.%08x.%08x.%08x.%08x%n")
	assert.Contains(t, detector.formatStringPayloads, "BBBB%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%x.%n")
	assert.Contains(t, detector.formatStringPayloads, "CCCC%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%p.%n")

	// 检查直接参数访问载荷
	assert.Contains(t, detector.formatStringPayloads, "%1$08x.%2$08x.%3$08x.%4$08x.%5$08x.%6$08x")
	assert.Contains(t, detector.formatStringPayloads, "%7$08x.%8$08x.%9$08x.%10$08x.%11$08x.%12$08x")
	assert.Contains(t, detector.formatStringPayloads, "%1$p.%2$p.%3$p.%4$p.%5$p.%6$p.%7$p.%8$p")

	// 检查中文格式化字符串载荷
	assert.Contains(t, detector.formatStringPayloads, "测试%s%s%s%s%s")
	assert.Contains(t, detector.formatStringPayloads, "格式%x%x%x%x%x")
	assert.Contains(t, detector.formatStringPayloads, "字符串%p%p%p%p%p")
	assert.Contains(t, detector.formatStringPayloads, "载荷%n%n%n%n%n")
}

// TestBufferOverflowDetectorIntegerPayloads 测试整数溢出载荷
func TestBufferOverflowDetectorIntegerPayloads(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查整数溢出载荷列表
	assert.NotEmpty(t, detector.integerOverflowPayloads)
	assert.GreaterOrEqual(t, len(detector.integerOverflowPayloads), 30)

	// 检查32位整数溢出载荷
	assert.Contains(t, detector.integerOverflowPayloads, "2147483647")  // INT_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "2147483648")  // INT_MAX + 1
	assert.Contains(t, detector.integerOverflowPayloads, "4294967295")  // UINT_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "4294967296")  // UINT_MAX + 1
	assert.Contains(t, detector.integerOverflowPayloads, "-2147483648") // INT_MIN
	assert.Contains(t, detector.integerOverflowPayloads, "-2147483649") // INT_MIN - 1

	// 检查64位整数溢出载荷
	assert.Contains(t, detector.integerOverflowPayloads, "9223372036854775807")  // LLONG_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "9223372036854775808")  // LLONG_MAX + 1
	assert.Contains(t, detector.integerOverflowPayloads, "18446744073709551615") // ULLONG_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "18446744073709551616") // ULLONG_MAX + 1

	// 检查16位整数溢出载荷
	assert.Contains(t, detector.integerOverflowPayloads, "32767") // SHRT_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "32768") // SHRT_MAX + 1
	assert.Contains(t, detector.integerOverflowPayloads, "65535") // USHRT_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "65536") // USHRT_MAX + 1

	// 检查8位整数溢出载荷
	assert.Contains(t, detector.integerOverflowPayloads, "127") // CHAR_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "128") // CHAR_MAX + 1
	assert.Contains(t, detector.integerOverflowPayloads, "255") // UCHAR_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "256") // UCHAR_MAX + 1

	// 检查特殊整数载荷
	assert.Contains(t, detector.integerOverflowPayloads, "0")
	assert.Contains(t, detector.integerOverflowPayloads, "-1")
	assert.Contains(t, detector.integerOverflowPayloads, "999999999999999999999999999999")
	assert.Contains(t, detector.integerOverflowPayloads, "-999999999999999999999999999999")

	// 检查十六进制整数载荷
	assert.Contains(t, detector.integerOverflowPayloads, "0x7FFFFFFF")  // INT_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "0x80000000")  // INT_MAX + 1
	assert.Contains(t, detector.integerOverflowPayloads, "0xFFFFFFFF")  // UINT_MAX
	assert.Contains(t, detector.integerOverflowPayloads, "0x100000000") // UINT_MAX + 1

	// 检查中文整数载荷
	assert.Contains(t, detector.integerOverflowPayloads, "最大整数2147483647")
	assert.Contains(t, detector.integerOverflowPayloads, "最小整数-2147483648")
	assert.Contains(t, detector.integerOverflowPayloads, "溢出4294967296")
	assert.Contains(t, detector.integerOverflowPayloads, "下溢-2147483649")
}

// TestBufferOverflowDetectorUnderflowPayloads 测试缓冲区下溢载荷
func TestBufferOverflowDetectorUnderflowPayloads(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查缓冲区下溢载荷列表
	assert.NotEmpty(t, detector.bufferUnderflowPayloads)
	assert.GreaterOrEqual(t, len(detector.bufferUnderflowPayloads), 15)

	// 检查负数索引载荷
	assert.Contains(t, detector.bufferUnderflowPayloads, "-1")
	assert.Contains(t, detector.bufferUnderflowPayloads, "-10")
	assert.Contains(t, detector.bufferUnderflowPayloads, "-100")
	assert.Contains(t, detector.bufferUnderflowPayloads, "-1000")
	assert.Contains(t, detector.bufferUnderflowPayloads, "-2147483648") // INT_MIN

	// 检查负数长度载荷
	assert.Contains(t, detector.bufferUnderflowPayloads, "length=-1")
	assert.Contains(t, detector.bufferUnderflowPayloads, "size=-10")
	assert.Contains(t, detector.bufferUnderflowPayloads, "count=-100")
	assert.Contains(t, detector.bufferUnderflowPayloads, "offset=-1000")

	// 检查下溢边界载荷
	assert.Contains(t, detector.bufferUnderflowPayloads, "0xFFFFFFFF") // -1 as unsigned
	assert.Contains(t, detector.bufferUnderflowPayloads, "0xFFFFFFFE") // -2 as unsigned
	assert.Contains(t, detector.bufferUnderflowPayloads, "0xFFFFFF00") // -256 as unsigned
	assert.Contains(t, detector.bufferUnderflowPayloads, "0xFFFF0000") // -65536 as unsigned

	// 检查中文下溢载荷
	assert.Contains(t, detector.bufferUnderflowPayloads, "负索引-1")
	assert.Contains(t, detector.bufferUnderflowPayloads, "负长度-10")
	assert.Contains(t, detector.bufferUnderflowPayloads, "负偏移-100")
	assert.Contains(t, detector.bufferUnderflowPayloads, "下溢-1000")
}

// TestBufferOverflowDetectorMemoryCorruptionPayloads 测试内存损坏载荷
func TestBufferOverflowDetectorMemoryCorruptionPayloads(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查内存损坏载荷列表
	assert.NotEmpty(t, detector.memoryCorruptionPayloads)
	assert.GreaterOrEqual(t, len(detector.memoryCorruptionPayloads), 15)

	// 检查空指针载荷
	assert.Contains(t, detector.memoryCorruptionPayloads, "\x00\x00\x00\x00")
	assert.Contains(t, detector.memoryCorruptionPayloads, "\x00\x00\x00\x00\x00\x00\x00\x00")
	assert.Contains(t, detector.memoryCorruptionPayloads, "null")
	assert.Contains(t, detector.memoryCorruptionPayloads, "NULL")
	assert.Contains(t, detector.memoryCorruptionPayloads, "0x00000000")

	// 检查无效指针载荷
	assert.Contains(t, detector.memoryCorruptionPayloads, "\xFF\xFF\xFF\xFF")
	assert.Contains(t, detector.memoryCorruptionPayloads, "\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF")
	assert.Contains(t, detector.memoryCorruptionPayloads, "0xFFFFFFFF")
	assert.Contains(t, detector.memoryCorruptionPayloads, "0xFFFFFFFFFFFFFFFF")
	assert.Contains(t, detector.memoryCorruptionPayloads, "0xDEADBEEF")
	assert.Contains(t, detector.memoryCorruptionPayloads, "0xCAFEBABE")

	// 检查内存模式载荷
	assert.Contains(t, detector.memoryCorruptionPayloads, "\xAA\xAA\xAA\xAA") // 10101010 pattern
	assert.Contains(t, detector.memoryCorruptionPayloads, "\x55\x55\x55\x55") // 01010101 pattern
	assert.Contains(t, detector.memoryCorruptionPayloads, "\xCC\xCC\xCC\xCC") // 11001100 pattern
	assert.Contains(t, detector.memoryCorruptionPayloads, "\x33\x33\x33\x33") // 00110011 pattern

	// 检查中文内存损坏载荷
	foundChineseNull := false
	foundChineseInvalid := false
	foundChineseCorruption := false
	foundChineseHeap := false

	for _, payload := range detector.memoryCorruptionPayloads {
		if payload == "空指针\x00\x00\x00\x00" {
			foundChineseNull = true
		}
		if payload == "无效指针\xFF\xFF\xFF\xFF" {
			foundChineseInvalid = true
		}
		if strings.HasPrefix(payload, "内存损坏") && strings.Contains(payload, "\xCC") {
			foundChineseCorruption = true
		}
		if strings.HasPrefix(payload, "堆破坏") && strings.Contains(payload, "\xAA") {
			foundChineseHeap = true
		}
	}

	assert.True(t, foundChineseNull, "应该包含中文空指针载荷")
	assert.True(t, foundChineseInvalid, "应该包含中文无效指针载荷")
	assert.True(t, foundChineseCorruption, "应该包含中文内存损坏载荷")
	assert.True(t, foundChineseHeap, "应该包含中文堆破坏载荷")
}

// TestBufferOverflowDetectorTestParameters 测试参数列表
func TestBufferOverflowDetectorTestParameters(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查缓冲区相关参数
	assert.Contains(t, detector.testParameters, "buffer")
	assert.Contains(t, detector.testParameters, "buf")
	assert.Contains(t, detector.testParameters, "data")
	assert.Contains(t, detector.testParameters, "input")
	assert.Contains(t, detector.testParameters, "content")
	assert.Contains(t, detector.testParameters, "message")
	assert.Contains(t, detector.testParameters, "text")
	assert.Contains(t, detector.testParameters, "string")

	// 检查内存相关参数
	assert.Contains(t, detector.testParameters, "memory")
	assert.Contains(t, detector.testParameters, "mem")
	assert.Contains(t, detector.testParameters, "heap")
	assert.Contains(t, detector.testParameters, "stack")
	assert.Contains(t, detector.testParameters, "ptr")
	assert.Contains(t, detector.testParameters, "pointer")
	assert.Contains(t, detector.testParameters, "addr")
	assert.Contains(t, detector.testParameters, "address")

	// 检查文件相关参数
	assert.Contains(t, detector.testParameters, "file")
	assert.Contains(t, detector.testParameters, "filename")
	assert.Contains(t, detector.testParameters, "path")
	assert.Contains(t, detector.testParameters, "filepath")
	assert.Contains(t, detector.testParameters, "upload")
	assert.Contains(t, detector.testParameters, "download")

	// 检查网络相关参数
	assert.Contains(t, detector.testParameters, "host")
	assert.Contains(t, detector.testParameters, "hostname")
	assert.Contains(t, detector.testParameters, "ip")
	assert.Contains(t, detector.testParameters, "port")
	assert.Contains(t, detector.testParameters, "url")
	assert.Contains(t, detector.testParameters, "uri")

	// 检查用户输入参数
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "password")
	assert.Contains(t, detector.testParameters, "email")
	assert.Contains(t, detector.testParameters, "comment")
	assert.Contains(t, detector.testParameters, "review")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "缓冲区")
	assert.Contains(t, detector.testParameters, "缓存")
	assert.Contains(t, detector.testParameters, "数据")
	assert.Contains(t, detector.testParameters, "输入")
	assert.Contains(t, detector.testParameters, "内容")
	assert.Contains(t, detector.testParameters, "消息")
	assert.Contains(t, detector.testParameters, "文本")
	assert.Contains(t, detector.testParameters, "字符串")
	assert.Contains(t, detector.testParameters, "内存")
	assert.Contains(t, detector.testParameters, "堆")
	assert.Contains(t, detector.testParameters, "栈")
	assert.Contains(t, detector.testParameters, "指针")
}

// TestBufferOverflowDetectorPatterns 测试模式
func TestBufferOverflowDetectorPatterns(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 检查栈溢出模式
	assert.NotEmpty(t, detector.stackPatterns)
	assert.GreaterOrEqual(t, len(detector.stackPatterns), 15)

	// 检查堆溢出模式
	assert.NotEmpty(t, detector.heapPatterns)
	assert.GreaterOrEqual(t, len(detector.heapPatterns), 15)

	// 检查格式化字符串模式
	assert.NotEmpty(t, detector.formatPatterns)
	assert.GreaterOrEqual(t, len(detector.formatPatterns), 15)

	// 检查整数溢出模式
	assert.NotEmpty(t, detector.integerPatterns)
	assert.GreaterOrEqual(t, len(detector.integerPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestBufferOverflowDetectorBufferFeatures 测试缓冲区功能检查
func TestBufferOverflowDetectorBufferFeatures(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 测试有缓冲区头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Server":       "Apache/2.4.41",
			"X-Powered-By": "PHP/7.4.0",
		},
	}
	assert.True(t, detector.hasBufferFeatures(headerTarget))

	// 测试有缓冲区技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Apache", Version: "2.4.41", Confidence: 0.9},
			{Name: "PHP", Version: "7.4.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasBufferFeatures(techTarget))

	// 测试有缓冲区链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/buffer/test", Text: "Buffer Test"},
		},
	}
	assert.True(t, detector.hasBufferFeatures(linkTarget))

	// 测试有缓冲区表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"buffer": "text", "data": "textarea"}},
		},
	}
	assert.True(t, detector.hasBufferFeatures(formTarget))

	// 测试无缓冲区功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasBufferFeatures(simpleTarget))
}

// TestBufferOverflowDetectorRiskScore 测试风险评分计算
func TestBufferOverflowDetectorRiskScore(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 8.8)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.8)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.9)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestBufferOverflowDetectorLifecycle 测试检测器生命周期
func TestBufferOverflowDetectorLifecycle(t *testing.T) {
	detector := NewBufferOverflowDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
