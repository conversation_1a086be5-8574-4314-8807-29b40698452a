package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestAPISecurityDetectorBasicFunctionality 测试API安全检测器基础功能
func TestAPISecurityDetectorBasicFunctionality(t *testing.T) {
	detector := NewAPISecurityDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "api-security-comprehensive", detector.GetID())
	assert.Equal(t, "API安全漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-285")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestAPISecurityDetectorApplicability 测试API安全检测器适用性
func TestAPISecurityDetectorApplicability(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 测试API URL目标
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/v1/users",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试GraphQL目标
	graphqlTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/graphql",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(graphqlTarget))

	// 测试有API头部的目标
	apiHeaderTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}
	assert.True(t, detector.IsApplicable(apiHeaderTarget))

	// 测试有API技术的目标
	apiTechTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "REST API", Version: "1.0", Confidence: 0.9},
		},
	}
	assert.True(t, detector.IsApplicable(apiTechTarget))

	// 测试有API链接的目标
	apiLinkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api", Text: "API Documentation"},
		},
	}
	assert.True(t, detector.IsApplicable(apiLinkTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无API功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestAPISecurityDetectorConfiguration 测试API安全检测器配置
func TestAPISecurityDetectorConfiguration(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 25*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 4, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       6,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestAPISecurityDetectorAPIEndpoints 测试API安全检测器API端点
func TestAPISecurityDetectorAPIEndpoints(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查API端点列表
	assert.NotEmpty(t, detector.apiEndpoints)
	assert.Greater(t, len(detector.apiEndpoints), 60)

	// 检查基础API端点
	assert.Contains(t, detector.apiEndpoints, "/api")
	assert.Contains(t, detector.apiEndpoints, "/api/v1")
	assert.Contains(t, detector.apiEndpoints, "/api/v2")
	assert.Contains(t, detector.apiEndpoints, "/rest")
	assert.Contains(t, detector.apiEndpoints, "/graphql")

	// 检查用户API
	assert.Contains(t, detector.apiEndpoints, "/api/users")
	assert.Contains(t, detector.apiEndpoints, "/api/user")
	assert.Contains(t, detector.apiEndpoints, "/api/accounts")

	// 检查认证API
	assert.Contains(t, detector.apiEndpoints, "/api/auth")
	assert.Contains(t, detector.apiEndpoints, "/api/login")
	assert.Contains(t, detector.apiEndpoints, "/api/token")

	// 检查管理API
	assert.Contains(t, detector.apiEndpoints, "/api/admin")
	assert.Contains(t, detector.apiEndpoints, "/api/management")

	// 检查文档API
	assert.Contains(t, detector.apiEndpoints, "/swagger")
	assert.Contains(t, detector.apiEndpoints, "/api-docs")
}

// TestAPISecurityDetectorAPIMethods 测试API安全检测器API方法
func TestAPISecurityDetectorAPIMethods(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查API方法列表
	assert.NotEmpty(t, detector.apiMethods)
	assert.Equal(t, 9, len(detector.apiMethods))

	// 检查基础HTTP方法
	assert.Contains(t, detector.apiMethods, "GET")
	assert.Contains(t, detector.apiMethods, "POST")
	assert.Contains(t, detector.apiMethods, "PUT")
	assert.Contains(t, detector.apiMethods, "DELETE")
	assert.Contains(t, detector.apiMethods, "PATCH")

	// 检查其他HTTP方法
	assert.Contains(t, detector.apiMethods, "HEAD")
	assert.Contains(t, detector.apiMethods, "OPTIONS")
	assert.Contains(t, detector.apiMethods, "TRACE")
	assert.Contains(t, detector.apiMethods, "CONNECT")
}

// TestAPISecurityDetectorAuthBypassPayloads 测试API安全检测器认证绕过载荷
func TestAPISecurityDetectorAuthBypassPayloads(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查认证绕过载荷列表
	assert.NotEmpty(t, detector.authBypassPayloads)
	assert.GreaterOrEqual(t, len(detector.authBypassPayloads), 40)

	// 检查空认证
	assert.Contains(t, detector.authBypassPayloads, "")
	assert.Contains(t, detector.authBypassPayloads, " ")
	assert.Contains(t, detector.authBypassPayloads, "null")

	// 检查弱认证
	assert.Contains(t, detector.authBypassPayloads, "admin")
	assert.Contains(t, detector.authBypassPayloads, "password")
	assert.Contains(t, detector.authBypassPayloads, "123456")

	// 检查JWT绕过
	assert.Contains(t, detector.authBypassPayloads, "Bearer ")
	assert.Contains(t, detector.authBypassPayloads, "Bearer null")

	// 检查API Key绕过
	assert.Contains(t, detector.authBypassPayloads, "api_key=")
	assert.Contains(t, detector.authBypassPayloads, "api_key=null")

	// 检查基础认证绕过
	assert.Contains(t, detector.authBypassPayloads, "Basic ")
	assert.Contains(t, detector.authBypassPayloads, "Basic YWRtaW46YWRtaW4=")
}

// TestAPISecurityDetectorSensitiveDataTypes 测试API安全检测器敏感数据类型
func TestAPISecurityDetectorSensitiveDataTypes(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查敏感数据类型列表
	assert.NotEmpty(t, detector.sensitiveDataTypes)
	assert.Greater(t, len(detector.sensitiveDataTypes), 60)

	// 检查个人信息
	assert.Contains(t, detector.sensitiveDataTypes, "password")
	assert.Contains(t, detector.sensitiveDataTypes, "email")
	assert.Contains(t, detector.sensitiveDataTypes, "phone")
	assert.Contains(t, detector.sensitiveDataTypes, "address")

	// 检查身份信息
	assert.Contains(t, detector.sensitiveDataTypes, "id_card")
	assert.Contains(t, detector.sensitiveDataTypes, "passport")
	assert.Contains(t, detector.sensitiveDataTypes, "birth_date")

	// 检查财务信息
	assert.Contains(t, detector.sensitiveDataTypes, "credit_card")
	assert.Contains(t, detector.sensitiveDataTypes, "bank_account")
	assert.Contains(t, detector.sensitiveDataTypes, "salary")

	// 检查技术信息
	assert.Contains(t, detector.sensitiveDataTypes, "api_key")
	assert.Contains(t, detector.sensitiveDataTypes, "access_token")
	assert.Contains(t, detector.sensitiveDataTypes, "private_key")
}

// TestAPISecurityDetectorGraphQLQueries 测试API安全检测器GraphQL查询
func TestAPISecurityDetectorGraphQLQueries(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查GraphQL查询列表
	assert.NotEmpty(t, detector.graphqlQueries)
	assert.Greater(t, len(detector.graphqlQueries), 20)

	// 检查基础查询
	assert.Contains(t, detector.graphqlQueries, "query { __schema { types { name } } }")
	assert.Contains(t, detector.graphqlQueries, "query { __type(name: \"Query\") { fields { name } } }")

	// 检查内省查询
	assert.Contains(t, detector.graphqlQueries, "{ __schema { queryType { name } } }")
	assert.Contains(t, detector.graphqlQueries, "{ __schema { mutationType { name } } }")

	// 检查用户查询
	assert.Contains(t, detector.graphqlQueries, "query { users { id name email } }")
	assert.Contains(t, detector.graphqlQueries, "query { user(id: 1) { id name email password } }")

	// 检查注入查询
	assert.Contains(t, detector.graphqlQueries, "query { user(id: \"1' OR '1'='1\") { id name } }")
}

// TestAPISecurityDetectorMassAssignmentFields 测试API安全检测器批量赋值字段
func TestAPISecurityDetectorMassAssignmentFields(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查批量赋值字段列表
	assert.NotEmpty(t, detector.massAssignmentFields)
	assert.Greater(t, len(detector.massAssignmentFields), 50)

	// 检查用户权限字段
	assert.Contains(t, detector.massAssignmentFields, "role")
	assert.Contains(t, detector.massAssignmentFields, "permission")
	assert.Contains(t, detector.massAssignmentFields, "is_admin")
	assert.Contains(t, detector.massAssignmentFields, "is_superuser")

	// 检查用户状态字段
	assert.Contains(t, detector.massAssignmentFields, "status")
	assert.Contains(t, detector.massAssignmentFields, "enabled")
	assert.Contains(t, detector.massAssignmentFields, "verified")

	// 检查系统字段
	assert.Contains(t, detector.massAssignmentFields, "id")
	assert.Contains(t, detector.massAssignmentFields, "user_id")
	assert.Contains(t, detector.massAssignmentFields, "created_at")

	// 检查安全字段
	assert.Contains(t, detector.massAssignmentFields, "password")
	assert.Contains(t, detector.massAssignmentFields, "token")
	assert.Contains(t, detector.massAssignmentFields, "api_key")
}

// TestAPISecurityDetectorPatterns 测试API安全检测器模式
func TestAPISecurityDetectorPatterns(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 检查API模式
	assert.NotEmpty(t, detector.apiPatterns)
	assert.Greater(t, len(detector.apiPatterns), 15)

	// 检查数据泄露模式
	assert.NotEmpty(t, detector.dataLeakagePatterns)
	assert.Greater(t, len(detector.dataLeakagePatterns), 15)

	// 检查GraphQL模式
	assert.NotEmpty(t, detector.graphqlPatterns)
	assert.Greater(t, len(detector.graphqlPatterns), 10)
}

// TestAPISecurityDetectorAPIFeatures 测试API功能检查
func TestAPISecurityDetectorAPIFeatures(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 测试有API头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}
	assert.True(t, detector.hasAPIFeatures(headerTarget))

	// 测试有API技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "REST API", Version: "1.0", Confidence: 0.9},
		},
	}
	assert.True(t, detector.hasAPIFeatures(techTarget))

	// 测试有API链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api", Text: "API"},
		},
	}
	assert.True(t, detector.hasAPIFeatures(linkTarget))

	// 测试无API功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
	}
	assert.False(t, detector.hasAPIFeatures(simpleTarget))
}

// TestAPISecurityDetectorRiskScore 测试风险评分计算
func TestAPISecurityDetectorRiskScore(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.5)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestAPISecurityDetectorLifecycle 测试检测器生命周期
func TestAPISecurityDetectorLifecycle(t *testing.T) {
	detector := NewAPISecurityDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
