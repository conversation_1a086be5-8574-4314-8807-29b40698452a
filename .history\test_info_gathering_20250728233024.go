package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 扫描任务请求结构
type ScanRequest struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Targets     []string               `json:"targets"`
	Config      map[string]interface{} `json:"config"`
}

// 扫描任务响应结构
type ScanResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		TaskID int `json:"task_id"`
	} `json:"data"`
}

// 任务状态响应结构
type TaskStatusResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		ID     int    `json:"id"`
		Status string `json:"status"`
	} `json:"data"`
}

func main() {
	fmt.Println("🔍 测试信息收集功能")
	fmt.Println("==================")

	// 创建Web扫描任务
	taskID := createWebScanTask()
	if taskID == 0 {
		fmt.Println("❌ 创建扫描任务失败")
		return
	}

	fmt.Printf("✅ 扫描任务创建成功，任务ID: %d\n", taskID)

	// 等待任务完成
	fmt.Println("⏳ 等待扫描任务完成...")
	waitForTaskCompletion(taskID)

	// 检查信息收集数据
	fmt.Println("📊 检查信息收集数据...")
	checkInfoGatheringData(taskID)
}

func createWebScanTask() int {
	// 构造扫描请求
	scanReq := ScanRequest{
		Name:        "信息收集测试任务",
		Type:        "web",
		Description: "测试Web扫描引擎的信息收集功能",
		Targets:     []string{"https://httpbin.org"},
		Config: map[string]interface{}{
			"depth":       1,
			"timeout":     30,
			"concurrent":  1,
			"enable_info": true,
		},
	}

	// 发送请求
	jsonData, _ := json.Marshal(scanReq)
	req, _ := http.NewRequest("POST", "http://localhost:8080/api/v1/scans", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoic2Nhbm5lciIsInN1YiI6IjEiLCJleHAiOjE3NTM4MDI5MzgsIm5iZiI6MTc1MzcxNjUzOCwiaWF0IjoxNzUzNzE2NTM4fQ.HSkfYBdaVk8zv6bvGNB4k4sVDyztTTov8WuIIrk4zxk") // 添加认证头

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ 发送请求失败: %v\n", err)
		return 0
	}
	defer resp.Body.Close()

	// 解析响应
	body, _ := io.ReadAll(resp.Body)
	var scanResp ScanResponse
	if err := json.Unmarshal(body, &scanResp); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		return 0
	}

	if scanResp.Code != 200 {
		fmt.Printf("❌ 创建任务失败: %s\n", scanResp.Message)
		return 0
	}

	return scanResp.Data.TaskID
}

func waitForTaskCompletion(taskID int) {
	for i := 0; i < 60; i++ { // 最多等待5分钟
		time.Sleep(5 * time.Second)

		// 检查任务状态
		req, _ := http.NewRequest("GET", fmt.Sprintf("http://localhost:8080/api/v1/scans/%d", taskID), nil)
		req.Header.Set("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoic2Nhbm5lciIsInN1YiI6IjEiLCJleHAiOjE3NTM4MDI5MzgsIm5iZiI6MTc1MzcxNjUzOCwiaWF0IjoxNzUzNzE2NTM4fQ.HSkfYBdaVk8zv6bvGNB4k4sVDyztTTov8WuIIrk4zxk")

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()

		var statusResp TaskStatusResponse
		if err := json.Unmarshal(body, &statusResp); err != nil {
			continue
		}

		fmt.Printf("📋 任务状态: %s\n", statusResp.Data.Status)

		if statusResp.Data.Status == "completed" || statusResp.Data.Status == "failed" {
			break
		}
	}
}

func checkInfoGatheringData(taskID int) {
	// 获取目标信息
	req, _ := http.NewRequest("GET", fmt.Sprintf("http://localhost:8080/api/v1/scans/%d/target-info", taskID), nil)
	req.Header.Set("Authorization", "Bearer test-token")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ 获取目标信息失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	// 检查响应内容
	if len(body) > 100 {
		fmt.Println("✅ 目标信息数据获取成功")
		fmt.Printf("📊 数据长度: %d 字符\n", len(body))

		// 显示部分数据内容
		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err == nil {
			if data, ok := result["data"].(map[string]interface{}); ok {
				if basicInfo, ok := data["basic_info"].(map[string]interface{}); ok {
					fmt.Printf("🌐 基本信息: URL=%v, 域名=%v, 状态码=%v\n",
						basicInfo["url"], basicInfo["domain"], basicInfo["status_code"])
				}
				if techStack, ok := data["tech_stack"].(map[string]interface{}); ok {
					fmt.Printf("🔧 技术栈信息: %v\n", techStack)
				}
			}
		}
	} else {
		fmt.Println("❌ 目标信息数据为空或过短")
		fmt.Printf("📊 响应内容: %s\n", string(body))
	}
}
