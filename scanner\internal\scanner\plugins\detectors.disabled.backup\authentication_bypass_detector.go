package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// AuthenticationBypassDetector 认证绕过检测器
// 支持HTTP方法绕过、路径遍历绕过、未授权访问、JWT漏洞、会话管理缺陷等多种认证绕过检测
type AuthenticationBypassDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	httpMethods     []string         // HTTP方法列表
	bypassPaths     []string         // 绕过路径
	authEndpoints   []string         // 认证端点
	jwtSecrets      []string         // JWT弱密钥
	sessionPatterns []*regexp.Regexp // 会话模式
	authPatterns    []*regexp.Regexp // 认证模式
	bypassPatterns  []*regexp.Regexp // 绕过模式
	httpClient      *http.Client
}

// NewAuthenticationBypassDetector 创建认证绕过检测器
func NewAuthenticationBypassDetector() *AuthenticationBypassDetector {
	detector := &AuthenticationBypassDetector{
		id:          "authentication-bypass-comprehensive",
		name:        "认证绕过漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 认证绕过是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-287", "CWE-288", "CWE-290", "CWE-306", "CWE-307"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测认证绕过漏洞，包括HTTP方法绕过、路径遍历绕过、未授权访问、JWT漏洞、会话管理缺陷等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 认证绕过检测需要较长时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，响应内容适中
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeMethods()
	detector.initializePaths()
	detector.initializeEndpoints()
	detector.initializeSecrets()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *AuthenticationBypassDetector) GetID() string            { return d.id }
func (d *AuthenticationBypassDetector) GetName() string          { return d.name }
func (d *AuthenticationBypassDetector) GetCategory() string      { return d.category }
func (d *AuthenticationBypassDetector) GetSeverity() string      { return d.severity }
func (d *AuthenticationBypassDetector) GetCVE() []string         { return d.cve }
func (d *AuthenticationBypassDetector) GetCWE() []string         { return d.cwe }
func (d *AuthenticationBypassDetector) GetVersion() string       { return d.version }
func (d *AuthenticationBypassDetector) GetAuthor() string        { return d.author }
func (d *AuthenticationBypassDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *AuthenticationBypassDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *AuthenticationBypassDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *AuthenticationBypassDetector) GetRequiredPorts() []int  { return []int{80, 443, 8080, 8443} }
func (d *AuthenticationBypassDetector) GetRequiredServices() []string {
	return []string{"http", "https"}
}
func (d *AuthenticationBypassDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *AuthenticationBypassDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *AuthenticationBypassDetector) GetDependencies() []string         { return []string{} }
func (d *AuthenticationBypassDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *AuthenticationBypassDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *AuthenticationBypassDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *AuthenticationBypassDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *AuthenticationBypassDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *AuthenticationBypassDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *AuthenticationBypassDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.httpMethods) == 0 {
		return fmt.Errorf("HTTP方法列表不能为空")
	}
	if len(d.authPatterns) == 0 {
		return fmt.Errorf("认证模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *AuthenticationBypassDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 认证绕过检测适用于有认证功能的Web应用
	// 检查是否有认证相关的表单、链接或路径
	if d.hasAuthenticationFeatures(target) {
		return true
	}

	// 对于管理、登录、API等认证相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	authKeywords := []string{
		"login", "signin", "auth", "admin", "manage", "dashboard",
		"api", "user", "profile", "account", "member", "customer",
		"登录", "认证", "管理", "用户", "账户", "会员",
	}

	for _, keyword := range authKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return false
}

// hasAuthenticationFeatures 检查是否有认证功能
func (d *AuthenticationBypassDetector) hasAuthenticationFeatures(target *plugins.ScanTarget) bool {
	// 检查表单中是否有认证相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			// 检查认证相关字段
			authFields := []string{
				"username", "password", "email", "login", "auth",
				"user", "pass", "pwd", "token", "session",
				"用户名", "密码", "邮箱", "登录", "认证",
			}

			for _, authField := range authFields {
				if strings.Contains(fieldNameLower, authField) {
					return true
				}
			}
		}
	}

	// 检查链接中是否有认证相关参数
	for _, link := range target.Links {
		linkLower := strings.ToLower(link.URL)
		if strings.Contains(linkLower, "token=") ||
			strings.Contains(linkLower, "session=") ||
			strings.Contains(linkLower, "auth=") ||
			strings.Contains(linkLower, "login=") {
			return true
		}
	}

	// 检查头部中是否有认证相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "authorization" ||
			keyLower == "www-authenticate" ||
			strings.Contains(valueLower, "bearer") ||
			strings.Contains(valueLower, "basic") ||
			strings.Contains(valueLower, "jwt") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *AuthenticationBypassDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种认证绕过检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. HTTP方法绕过检测
	methodEvidence, methodConfidence, methodPayload, methodRequest, methodResponse := d.detectHTTPMethodBypass(ctx, target)
	if methodConfidence > maxConfidence {
		maxConfidence = methodConfidence
		vulnerablePayload = methodPayload
		vulnerableRequest = methodRequest
		vulnerableResponse = methodResponse
	}
	evidence = append(evidence, methodEvidence...)

	// 2. 路径遍历绕过检测
	pathEvidence, pathConfidence, pathPayload, pathRequest, pathResponse := d.detectPathTraversalBypass(ctx, target)
	if pathConfidence > maxConfidence {
		maxConfidence = pathConfidence
		vulnerablePayload = pathPayload
		vulnerableRequest = pathRequest
		vulnerableResponse = pathResponse
	}
	evidence = append(evidence, pathEvidence...)

	// 3. 未授权访问检测
	unauthorizedEvidence, unauthorizedConfidence, unauthorizedPayload, unauthorizedRequest, unauthorizedResponse := d.detectUnauthorizedAccess(ctx, target)
	if unauthorizedConfidence > maxConfidence {
		maxConfidence = unauthorizedConfidence
		vulnerablePayload = unauthorizedPayload
		vulnerableRequest = unauthorizedRequest
		vulnerableResponse = unauthorizedResponse
	}
	evidence = append(evidence, unauthorizedEvidence...)

	// 4. JWT漏洞检测
	jwtEvidence, jwtConfidence, jwtPayload, jwtRequest, jwtResponse := d.detectJWTVulnerabilities(ctx, target)
	if jwtConfidence > maxConfidence {
		maxConfidence = jwtConfidence
		vulnerablePayload = jwtPayload
		vulnerableRequest = jwtRequest
		vulnerableResponse = jwtResponse
	}
	evidence = append(evidence, jwtEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "认证绕过漏洞",
		Description:       "检测到认证绕过漏洞，可能允许攻击者绕过认证机制获得未授权访问",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强认证机制，实施适当的访问控制，验证所有HTTP方法，使用安全的会话管理",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Authentication_bypass", "https://cwe.mitre.org/data/definitions/287.html"},
		Tags:              []string{"authentication-bypass", "access-control", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *AuthenticationBypassDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{"GET", "POST", "PUT"}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 发送验证请求
		resp, err := d.sendAuthRequest(ctx, target.URL, method, nil)
		if err != nil {
			continue
		}

		// 检查认证绕过响应特征
		responseConfidence := d.checkAuthenticationBypassResponse(resp, method)
		if responseConfidence > 0.3 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证方法确认了认证绕过"),
				Content:     resp,
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.4

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "authentication-bypass-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用认证绕过验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *AuthenticationBypassDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("authentication_bypass_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *AuthenticationBypassDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (认证绕过通常是高风险)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeMethods 初始化HTTP方法列表
func (d *AuthenticationBypassDetector) initializeMethods() {
	d.httpMethods = []string{
		// 标准HTTP方法
		"GET",
		"POST",
		"PUT",
		"DELETE",
		"PATCH",
		"HEAD",
		"OPTIONS",
		"TRACE",
		"CONNECT",

		// 非标准HTTP方法
		"PROPFIND",
		"PROPPATCH",
		"MKCOL",
		"COPY",
		"MOVE",
		"LOCK",
		"UNLOCK",
		"VERSION-CONTROL",
		"REPORT",
		"CHECKOUT",
		"CHECKIN",
		"UNCHECKOUT",
		"MKWORKSPACE",
		"UPDATE",
		"LABEL",
		"MERGE",
		"BASELINE-CONTROL",
		"MKACTIVITY",

		// 自定义方法
		"DEBUG",
		"TRACK",
		"PURGE",
		"LINK",
		"UNLINK",
	}
}

// initializePaths 初始化绕过路径列表
func (d *AuthenticationBypassDetector) initializePaths() {
	d.bypassPaths = []string{
		// 路径遍历绕过
		"../",
		"..\\",
		"..%2f",
		"..%5c",
		"....//",
		"....\\\\",
		"%2e%2e%2f",
		"%2e%2e%5c",
		"..%252f",
		"..%255c",

		// URL编码绕过
		"%2e%2e/",
		"%2e%2e\\",
		"..%c0%af",
		"..%c1%9c",

		// 双重编码绕过
		"%252e%252e%252f",
		"%252e%252e%255c",

		// Unicode绕过
		"..%u2215",
		"..%u2216",
		"..%u002f",
		"..%u005c",

		// 空字节绕过
		"..%00/",
		"..%00\\",

		// 长路径绕过
		"../../../../../../../../../../../",
		"..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\",

		// 混合绕过
		"..;/",
		"..;\\",
		"../;/",
		"..\\;\\",

		// 特殊字符绕过
		"..?/",
		"..#/",
		"..*/",
		"..!/",
		"..@/",
		"..$/",
		"..%/",
		"..^/",
		"..&/",
		"..(/",
		"..)/",
		"..+/",
		"..=/",
		"..~/",
		"..`/",

		// 中文路径绕过
		"../上级目录/",
		"../父目录/",
		"../返回/",
	}
}

// initializeEndpoints 初始化认证端点列表
func (d *AuthenticationBypassDetector) initializeEndpoints() {
	d.authEndpoints = []string{
		// 登录端点
		"/login",
		"/signin",
		"/auth",
		"/authenticate",
		"/session",
		"/sso",
		"/oauth",
		"/saml",
		"/ldap",

		// 管理端点
		"/admin",
		"/administrator",
		"/manage",
		"/manager",
		"/control",
		"/dashboard",
		"/panel",
		"/console",

		// API端点
		"/api/login",
		"/api/auth",
		"/api/session",
		"/api/token",
		"/api/oauth",
		"/api/user",
		"/api/admin",

		// 用户端点
		"/user",
		"/users",
		"/profile",
		"/account",
		"/member",
		"/customer",
		"/client",

		// 特殊端点
		"/wp-admin",
		"/wp-login.php",
		"/phpmyadmin",
		"/adminer",
		"/webmin",
		"/cpanel",
		"/plesk",

		// 中文端点
		"/登录",
		"/认证",
		"/管理",
		"/用户",
		"/账户",
		"/会员",
	}
}

// initializeSecrets 初始化JWT弱密钥列表
func (d *AuthenticationBypassDetector) initializeSecrets() {
	d.jwtSecrets = []string{
		// 常见弱密钥
		"secret",
		"password",
		"123456",
		"admin",
		"test",
		"key",
		"jwt",
		"token",
		"auth",
		"session",

		// JWT特定弱密钥
		"jwt_secret",
		"your-256-bit-secret",
		"your-secret-key",
		"jwt-secret",
		"jwt_key",
		"jsonwebtoken",
		"jwtsecret",
		"jwtkey",

		// 默认密钥
		"default",
		"changeme",
		"please-change-me",
		"your-secret",
		"mysecret",
		"secretkey",
		"privatekey",
		"publickey",

		// 简单密钥
		"a",
		"1",
		"abc",
		"123",
		"qwerty",
		"letmein",
		"welcome",
		"hello",
		"world",

		// 中文密钥
		"密码",
		"密钥",
		"秘密",
		"认证",
		"令牌",
	}
}

// initializePatterns 初始化检测模式
func (d *AuthenticationBypassDetector) initializePatterns() {
	// 会话模式 - 检测会话相关的响应内容
	sessionPatternStrings := []string{
		// 会话标识符
		`(?i)(sessionid|session_id|jsessionid|phpsessid)`,
		`(?i)(sid|sess|session|sesskey|sessionkey)`,
		`(?i)(auth_token|authtoken|access_token|accesstoken)`,
		`(?i)(csrf_token|csrftoken|_token|authenticity_token)`,

		// Cookie相关
		`(?i)(set-cookie|cookie|httponly|secure|samesite)`,
		`(?i)(expires|max-age|domain|path)`,

		// JWT相关
		`(?i)(bearer|jwt|json\s*web\s*token)`,
		`(?i)(eyj[a-za-z0-9_-]*\.[a-za-z0-9_-]*\.[a-za-z0-9_-]*)`, // JWT格式

		// 认证头
		`(?i)(authorization|www-authenticate|proxy-authenticate)`,
		`(?i)(basic|digest|ntlm|negotiate|oauth)`,

		// 中文模式
		`(?i)(会话|令牌|认证|授权|登录|用户)`,
	}

	d.sessionPatterns = make([]*regexp.Regexp, 0, len(sessionPatternStrings))
	for _, pattern := range sessionPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.sessionPatterns = append(d.sessionPatterns, compiled)
		}
	}

	// 认证模式 - 检测认证相关的响应内容
	authPatternStrings := []string{
		// 认证成功指示器
		`(?i)(welcome|dashboard|profile|admin\s*panel)`,
		`(?i)(logged\s*in|login\s*successful|authentication\s*successful)`,
		`(?i)(access\s*granted|permission\s*granted|authorized)`,
		`(?i)(user\s*authenticated|session\s*created|login\s*ok)`,

		// 认证失败指示器
		`(?i)(login\s*failed|authentication\s*failed|access\s*denied)`,
		`(?i)(unauthorized|forbidden|invalid\s*credentials)`,
		`(?i)(wrong\s*password|incorrect\s*username|bad\s*login)`,

		// 认证表单
		`(?i)(<form.*login|<form.*auth|<form.*signin)`,
		`(?i)(type\s*=\s*"password"|name\s*=\s*"password")`,
		`(?i)(username|email|user|login|signin)`,

		// 重定向指示器
		`(?i)(location:|redirect|forward|goto)`,
		`(?i)(302\s*found|301\s*moved|303\s*see\s*other)`,

		// 中文认证模式
		`(?i)(欢迎|仪表板|个人资料|管理面板)`,
		`(?i)(登录成功|认证成功|访问授权|权限授予)`,
		`(?i)(登录失败|认证失败|访问被拒绝|未授权)`,
		`(?i)(用户名|密码|邮箱|登录|注册)`,
	}

	d.authPatterns = make([]*regexp.Regexp, 0, len(authPatternStrings))
	for _, pattern := range authPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.authPatterns = append(d.authPatterns, compiled)
		}
	}

	// 绕过模式 - 检测认证绕过的响应内容
	bypassPatternStrings := []string{
		// 绕过成功指示器
		`(?i)(bypass\s*successful|authentication\s*bypassed)`,
		`(?i)(unauthorized\s*access|access\s*without\s*auth)`,
		`(?i)(method\s*not\s*allowed|405\s*method\s*not\s*allowed)`,
		`(?i)(forbidden\s*bypassed|403\s*bypassed)`,

		// 管理员访问指示器
		`(?i)(admin\s*access|administrator\s*panel|root\s*access)`,
		`(?i)(privileged\s*access|elevated\s*access|superuser)`,

		// 敏感信息泄露
		`(?i)(database\s*connection|config\s*file|secret\s*key)`,
		`(?i)(api\s*key|private\s*key|password\s*hash)`,
		`(?i)(user\s*list|admin\s*list|credential\s*dump)`,

		// 错误信息
		`(?i)(stack\s*trace|debug\s*info|error\s*details)`,
		`(?i)(exception|traceback|call\s*stack)`,

		// HTTP状态码
		`(?i)(200\s*ok|201\s*created|204\s*no\s*content)`,
		`(?i)(302\s*found|301\s*moved\s*permanently)`,

		// 中文绕过模式
		`(?i)(绕过成功|认证绕过|未授权访问)`,
		`(?i)(管理员访问|特权访问|超级用户)`,
		`(?i)(敏感信息|配置文件|密钥泄露)`,
	}

	d.bypassPatterns = make([]*regexp.Regexp, 0, len(bypassPatternStrings))
	for _, pattern := range bypassPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.bypassPatterns = append(d.bypassPatterns, compiled)
		}
	}
}
