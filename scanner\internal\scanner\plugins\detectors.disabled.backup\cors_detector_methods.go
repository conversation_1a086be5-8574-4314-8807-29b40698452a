package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectWildcardOrigin 检测通配符Origin
func (d *CORSDetector) detectWildcardOrigin(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试通配符Origin
	testOrigin := "*"

	select {
	case <-ctx.Done():
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	default:
	}

	// 发送带通配符Origin的请求
	resp, err := d.sendCORSRequest(ctx, target.URL, testOrigin)
	if err != nil {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 检查通配符Origin响应
	confidence := d.checkWildcardOriginResponse(resp, testOrigin)
	if confidence > maxConfidence {
		maxConfidence = confidence
		vulnerablePayload = fmt.Sprintf("通配符Origin: %s", testOrigin)
		vulnerableRequest = target.URL
		vulnerableResponse = resp
	}

	if confidence > 0.6 {
		evidence = append(evidence, plugins.Evidence{
			Type:        "wildcard-origin",
			Description: fmt.Sprintf("发现通配符Origin: %s (置信度: %.2f)", testOrigin, confidence),
			Content:     d.extractCORSEvidence(resp, "wildcard-origin"),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 添加延迟避免触发防护
	time.Sleep(time.Millisecond * 200)

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectReflectedOrigin 检测反射Origin
func (d *CORSDetector) detectReflectedOrigin(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试反射Origin
	for _, testOrigin := range d.testOrigins {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送带测试Origin的请求
		resp, err := d.sendCORSRequest(ctx, target.URL, testOrigin)
		if err != nil {
			continue
		}

		// 检查反射Origin响应
		confidence := d.checkReflectedOriginResponse(resp, testOrigin)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("反射Origin: %s", testOrigin)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "reflected-origin",
				Description: fmt.Sprintf("发现反射Origin: %s (置信度: %.2f)", testOrigin, confidence),
				Content:     d.extractCORSEvidence(resp, "reflected-origin"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCORSBypass 检测CORS绕过
func (d *CORSDetector) detectCORSBypass(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试CORS绕过Origin
	for _, bypassOrigin := range d.bypassOrigins {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造基于目标的绕过Origin
		targetURL, err := url.Parse(target.URL)
		if err != nil {
			continue
		}

		// 替换占位符
		actualBypassOrigin := strings.ReplaceAll(bypassOrigin, "target.com", targetURL.Host)

		// 发送绕过Origin请求
		resp, err := d.sendCORSRequest(ctx, target.URL, actualBypassOrigin)
		if err != nil {
			continue
		}

		// 检查CORS绕过响应
		confidence := d.checkCORSBypassResponse(resp, actualBypassOrigin)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("CORS绕过: %s", actualBypassOrigin)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "cors-bypass",
				Description: fmt.Sprintf("发现CORS绕过: %s (置信度: %.2f)", actualBypassOrigin, confidence),
				Content:     d.extractCORSEvidence(resp, "cors-bypass"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDangerousHeaders 检测危险头部
func (d *CORSDetector) detectDangerousHeaders(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试危险头部
	testOrigin := "https://evil.com"

	select {
	case <-ctx.Done():
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	default:
	}

	// 发送带危险头部的请求
	resp, err := d.sendCORSRequestWithHeaders(ctx, target.URL, testOrigin, d.dangerousHeaders)
	if err != nil {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 检查危险头部响应
	confidence := d.checkDangerousHeadersResponse(resp, testOrigin)
	if confidence > maxConfidence {
		maxConfidence = confidence
		vulnerablePayload = fmt.Sprintf("危险头部: %s", strings.Join(d.dangerousHeaders, ", "))
		vulnerableRequest = target.URL
		vulnerableResponse = resp
	}

	if confidence > 0.6 {
		evidence = append(evidence, plugins.Evidence{
			Type:        "dangerous-headers",
			Description: fmt.Sprintf("发现危险头部配置: %s (置信度: %.2f)", testOrigin, confidence),
			Content:     d.extractCORSEvidence(resp, "dangerous-headers"),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 添加延迟避免触发防护
	time.Sleep(time.Millisecond * 200)

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendCORSRequest 发送CORS请求
func (d *CORSDetector) sendCORSRequest(ctx context.Context, targetURL, origin string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置CORS相关头部
	req.Header.Set("Origin", origin)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendCORSRequestWithHeaders 发送带头部的CORS请求
func (d *CORSDetector) sendCORSRequestWithHeaders(ctx context.Context, targetURL, origin string, headers []string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "OPTIONS", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置CORS相关头部
	req.Header.Set("Origin", origin)
	req.Header.Set("Access-Control-Request-Method", "GET")
	req.Header.Set("Access-Control-Request-Headers", strings.Join(headers, ", "))
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkWildcardOriginResponse 检查通配符Origin响应
func (d *CORSDetector) checkWildcardOriginResponse(response, origin string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能表示CORS配置
	}

	// 检查通配符Origin头部
	if strings.Contains(response, "access-control-allow-origin: *") {
		confidence += 0.8 // 通配符Origin的强指示器
	}

	// 检查凭据头部
	if strings.Contains(response, "access-control-allow-credentials: true") {
		if strings.Contains(response, "access-control-allow-origin: *") {
			confidence += 0.9 // 通配符+凭据是严重漏洞
		} else {
			confidence += 0.4 // 仅凭据头部
		}
	}

	// 检查CORS模式匹配
	for _, pattern := range d.corsPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查漏洞模式匹配
	for _, pattern := range d.vulnerablePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查通配符特定错误
	wildcardErrors := []string{
		"wildcard origin", "allow any origin", "cors wildcard",
		"origin *", "allow-origin *", "通配符来源", "任意来源",
	}

	for _, error := range wildcardErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // 通配符特定错误的强指示器
			break
		}
	}

	return confidence
}

// checkReflectedOriginResponse 检查反射Origin响应
func (d *CORSDetector) checkReflectedOriginResponse(response, origin string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)
	originLower := strings.ToLower(origin)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能表示CORS配置
	}

	// 检查Origin是否被反射
	if strings.Contains(response, fmt.Sprintf("access-control-allow-origin: %s", originLower)) {
		confidence += 0.7 // Origin反射的强指示器
	}

	// 检查凭据头部
	if strings.Contains(response, "access-control-allow-credentials: true") {
		confidence += 0.4 // 凭据头部增加风险
	}

	// 检查CORS模式匹配
	for _, pattern := range d.corsPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查漏洞模式匹配
	for _, pattern := range d.vulnerablePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查反射特定错误
	reflectedErrors := []string{
		"reflected origin", "dynamic origin", "echo origin",
		"origin reflection", "cors reflection", "来源反射", "动态来源",
	}

	for _, error := range reflectedErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // 反射特定错误的强指示器
			break
		}
	}

	// 检查Origin在响应中的反映
	if originLower != "" && strings.Contains(response, originLower) {
		confidence += 0.4
	}

	return confidence
}

// checkCORSBypassResponse 检查CORS绕过响应
func (d *CORSDetector) checkCORSBypassResponse(response, origin string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)
	originLower := strings.ToLower(origin)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能表示绕过成功
	}

	// 检查绕过Origin是否被接受
	if strings.Contains(response, fmt.Sprintf("access-control-allow-origin: %s", originLower)) {
		confidence += 0.6 // 绕过Origin被接受
	}

	// 检查凭据头部
	if strings.Contains(response, "access-control-allow-credentials: true") {
		confidence += 0.4 // 凭据头部增加风险
	}

	// 检查CORS模式匹配
	for _, pattern := range d.corsPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查漏洞模式匹配
	for _, pattern := range d.vulnerablePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查绕过特定错误
	bypassErrors := []string{
		"cors bypass", "origin bypass", "subdomain bypass",
		"bypass success", "cors vulnerable", "绕过成功", "跨域绕过",
	}

	for _, error := range bypassErrors {
		if strings.Contains(response, error) {
			confidence += 0.6 // 绕过特定错误的强指示器
			break
		}
	}

	// 检查绕过Origin在响应中的反映
	if originLower != "" && strings.Contains(response, originLower) {
		confidence += 0.3
	}

	return confidence
}

// checkDangerousHeadersResponse 检查危险头部响应
func (d *CORSDetector) checkDangerousHeadersResponse(response, origin string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能表示头部被接受
	}

	// 检查危险头部是否被允许
	if strings.Contains(response, "access-control-allow-headers:") {
		for _, header := range d.dangerousHeaders {
			headerLower := strings.ToLower(header)
			if strings.Contains(response, headerLower) {
				confidence += 0.2 // 每个危险头部增加置信度
			}
		}
	}

	// 检查通配符头部
	if strings.Contains(response, "access-control-allow-headers: *") {
		confidence += 0.7 // 通配符头部的强指示器
	}

	// 检查凭据头部
	if strings.Contains(response, "access-control-allow-credentials: true") {
		confidence += 0.4 // 凭据头部增加风险
	}

	// 检查CORS模式匹配
	for _, pattern := range d.corsPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查漏洞模式匹配
	for _, pattern := range d.vulnerablePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查危险头部特定错误
	dangerousHeaderErrors := []string{
		"dangerous headers", "sensitive headers", "cors headers",
		"allow headers", "header bypass", "危险头部", "敏感头部",
	}

	for _, error := range dangerousHeaderErrors {
		if strings.Contains(response, error) {
			confidence += 0.5 // 危险头部特定错误的指示器
			break
		}
	}

	return confidence
}

// extractCORSEvidence 提取CORS证据
func (d *CORSDetector) extractCORSEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var corsLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "wildcard-origin":
			if strings.Contains(lineLower, "access-control-allow-origin") ||
				strings.Contains(lineLower, "access-control-allow-credentials") ||
				strings.Contains(lineLower, "wildcard") ||
				strings.Contains(lineLower, "*") ||
				strings.Contains(lineLower, "通配符") ||
				strings.Contains(lineLower, "任意来源") {
				corsLines = append(corsLines, line)
			}
		case "reflected-origin":
			if strings.Contains(lineLower, "access-control-allow-origin") ||
				strings.Contains(lineLower, "access-control-allow-credentials") ||
				strings.Contains(lineLower, "origin") ||
				strings.Contains(lineLower, "reflect") ||
				strings.Contains(lineLower, "来源") ||
				strings.Contains(lineLower, "反射") {
				corsLines = append(corsLines, line)
			}
		case "cors-bypass":
			if strings.Contains(lineLower, "access-control-allow-origin") ||
				strings.Contains(lineLower, "access-control-allow-credentials") ||
				strings.Contains(lineLower, "bypass") ||
				strings.Contains(lineLower, "subdomain") ||
				strings.Contains(lineLower, "绕过") ||
				strings.Contains(lineLower, "子域名") {
				corsLines = append(corsLines, line)
			}
		case "dangerous-headers":
			if strings.Contains(lineLower, "access-control-allow-headers") ||
				strings.Contains(lineLower, "access-control-allow-credentials") ||
				strings.Contains(lineLower, "authorization") ||
				strings.Contains(lineLower, "cookie") ||
				strings.Contains(lineLower, "危险头部") ||
				strings.Contains(lineLower, "敏感头部") {
				corsLines = append(corsLines, line)
			}
		default:
			if strings.Contains(lineLower, "access-control") ||
				strings.Contains(lineLower, "cors") ||
				strings.Contains(lineLower, "origin") ||
				strings.Contains(lineLower, "跨域") ||
				strings.Contains(lineLower, "来源") {
				corsLines = append(corsLines, line)
			}
		}

		if len(corsLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(corsLines) > 0 {
		return strings.Join(corsLines, "\n")
	}

	// 如果没有找到特定的CORS信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
