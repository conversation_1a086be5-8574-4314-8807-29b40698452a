package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectUploadPages 检测上传页面
func (d *FileUploadDetector) detectUploadPages(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 首先检查目标URL本身是否是上传页面
	resp, err := d.makeRequest(ctx, target.URL, "GET", nil)
	if err == nil {
		if d.isUploadPage(resp) {
			confidence = 0.7
			evidence = append(evidence, plugins.Evidence{
				Type:        "upload-page",
				Description: "发现文件上传页面",
				Content:     d.extractUploadEvidence(resp),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
			return evidence, confidence, target.URL, resp
		}
	}

	// 扫描常见的上传路径
	baseURL := d.getBaseURL(target.URL)
	for i, path := range d.uploadPaths {
		if i >= 10 { // 限制扫描数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, confidence, target.URL, ""
		default:
		}

		testURL := baseURL + path
		resp, err := d.makeRequest(ctx, testURL, "GET", nil)
		if err != nil {
			continue
		}

		if d.isUploadPage(resp) {
			pageConfidence := 0.6
			if confidence < pageConfidence {
				confidence = pageConfidence
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "upload-endpoint",
				Description: fmt.Sprintf("发现上传端点: %s", path),
				Content:     d.extractUploadEvidence(resp),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, confidence, target.URL, ""
}

// detectFileTypeRestrictions 检测文件类型限制
func (d *FileUploadDetector) detectFileTypeRestrictions(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 获取上传页面内容
	resp, err := d.makeRequest(ctx, target.URL, "GET", nil)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}

	// 检查是否是上传页面
	if !d.isUploadPage(resp) {
		return evidence, confidence, target.URL, resp
	}

	// 检查文件类型限制
	hasSecurityIssues := d.hasUploadSecurityIssues(resp)
	hasFileTypeRestrictions := d.hasFileTypeRestrictions(resp)

	if hasSecurityIssues {
		confidence = 0.8
		evidence = append(evidence, plugins.Evidence{
			Type:        "security-issue",
			Description: "发现文件上传安全问题",
			Content:     d.extractSecurityIssues(resp),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	} else if !hasFileTypeRestrictions {
		confidence = 0.6
		evidence = append(evidence, plugins.Evidence{
			Type:        "missing-restrictions",
			Description: "缺少明显的文件类型限制",
			Content:     "未发现明显的文件类型限制机制",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, confidence, target.URL, resp
}

// detectMaliciousUpload 检测恶意文件上传
func (d *FileUploadDetector) detectMaliciousUpload(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string) {
	var evidence []plugins.Evidence
	confidence := 0.0

	// 这里只进行理论检测，不实际上传恶意文件
	// 检查是否存在可能的恶意文件上传风险

	resp, err := d.makeRequest(ctx, target.URL, "GET", nil)
	if err != nil {
		return evidence, confidence, target.URL, ""
	}

	if !d.isUploadPage(resp) {
		return evidence, confidence, target.URL, resp
	}

	// 检查是否允许危险文件扩展名
	allowsDangerousFiles := d.allowsDangerousFileTypes(resp)
	if allowsDangerousFiles {
		confidence = 0.9
		evidence = append(evidence, plugins.Evidence{
			Type:        "dangerous-upload",
			Description: "可能允许上传危险文件类型",
			Content:     d.extractDangerousFileEvidence(resp),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 检查是否有路径遍历风险
	hasPathTraversal := d.hasPathTraversalRisk(resp)
	if hasPathTraversal {
		if confidence < 0.7 {
			confidence = 0.7
		}
		evidence = append(evidence, plugins.Evidence{
			Type:        "path-traversal",
			Description: "可能存在路径遍历风险",
			Content:     d.extractPathTraversalEvidence(resp),
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	return evidence, confidence, target.URL, resp
}

// makeRequest 发送HTTP请求
func (d *FileUploadDetector) makeRequest(ctx context.Context, url, method string, headers map[string]string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return "", err
	}

	// 设置默认头部
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// getBaseURL 获取基础URL
func (d *FileUploadDetector) getBaseURL(targetURL string) string {
	if strings.HasSuffix(targetURL, "/") {
		return targetURL[:len(targetURL)-1]
	}

	// 找到最后一个斜杠的位置
	lastSlash := strings.LastIndex(targetURL, "/")
	if lastSlash > 8 { // 确保不是协议部分的斜杠
		return targetURL[:lastSlash]
	}

	return targetURL
}

// isUploadPage 检查是否是上传页面
func (d *FileUploadDetector) isUploadPage(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查上传页面模式
	for _, pattern := range d.uploadPatterns {
		if pattern.MatchString(response) {
			return true
		}
	}

	// 检查是否包含文件上传相关的关键词
	uploadKeywords := []string{
		"type=\"file\"",
		"type='file'",
		"type=file",
		"enctype=\"multipart/form-data\"",
		"enctype='multipart/form-data'",
		"file upload",
		"upload file",
		"choose file",
		"select file",
		"browse file",
		"文件上传",
		"选择文件",
		"浏览文件",
		"上传文件",
		"附件上传",
	}

	for _, keyword := range uploadKeywords {
		if strings.Contains(responseLower, keyword) {
			return true
		}
	}

	return false
}

// hasUploadSecurityIssues 检查上传功能是否有安全问题
func (d *FileUploadDetector) hasUploadSecurityIssues(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查安全问题模式
	for _, issue := range d.securityIssues {
		if strings.Contains(responseLower, strings.ToLower(issue)) {
			return true
		}
	}

	return false
}

// hasFileTypeRestrictions 检查是否有文件类型限制
func (d *FileUploadDetector) hasFileTypeRestrictions(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查文件类型模式
	for _, pattern := range d.fileTypePatterns {
		if pattern.MatchString(response) {
			return true
		}
	}

	// 检查是否包含文件类型限制关键词
	restrictionKeywords := []string{
		"accept=", "file type", "文件类型",
		"allowed types", "supported formats",
		"valid extensions", "permitted files",
		"支持格式", "允许类型", "有效扩展名",
	}

	for _, keyword := range restrictionKeywords {
		if strings.Contains(responseLower, keyword) {
			return true
		}
	}

	return false
}

// allowsDangerousFileTypes 检查是否允许危险文件类型
func (d *FileUploadDetector) allowsDangerousFileTypes(response string) bool {
	responseLower := strings.ToLower(response)

	// 检查是否明确允许所有文件类型
	if strings.Contains(responseLower, "accept=\"*/*\"") ||
		strings.Contains(responseLower, "accept='*/*'") ||
		strings.Contains(responseLower, "accept=*/*") {
		return true
	}

	// 检查是否包含危险扩展名
	for _, ext := range d.dangerousExtensions {
		if strings.Contains(responseLower, "."+ext) {
			return true
		}
	}

	return false
}

// hasPathTraversalRisk 检查是否有路径遍历风险
func (d *FileUploadDetector) hasPathTraversalRisk(response string) bool {
	responseLower := strings.ToLower(response)

	pathTraversalIndicators := []string{
		"../", "..\\", "directory traversal",
		"path traversal", "路径遍历", "目录遍历",
		"upload path", "save path", "file path",
		"destination", "target directory",
	}

	for _, indicator := range pathTraversalIndicators {
		if strings.Contains(responseLower, indicator) {
			return true
		}
	}

	return false
}

// extractUploadEvidence 提取上传证据
func (d *FileUploadDetector) extractUploadEvidence(response string) string {
	lines := strings.Split(response, "\n")
	var evidenceLines []string

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			// 查找包含上传相关内容的行
			if strings.Contains(lineLower, "input") && strings.Contains(lineLower, "file") ||
				strings.Contains(lineLower, "upload") ||
				strings.Contains(lineLower, "multipart/form-data") ||
				strings.Contains(lineLower, "文件上传") ||
				strings.Contains(lineLower, "选择文件") {
				evidenceLines = append(evidenceLines, strings.TrimSpace(line))
				if len(evidenceLines) >= 5 { // 限制证据行数
					break
				}
			}
		}
	}

	if len(evidenceLines) > 0 {
		return strings.Join(evidenceLines, "\n")
	}
	return "发现文件上传功能"
}

// extractSecurityIssues 提取安全问题证据
func (d *FileUploadDetector) extractSecurityIssues(response string) string {
	lines := strings.Split(response, "\n")
	var issueLines []string

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			// 查找包含安全问题的行
			for _, issue := range d.securityIssues {
				if strings.Contains(lineLower, strings.ToLower(issue)) {
					issueLines = append(issueLines, strings.TrimSpace(line))
					if len(issueLines) >= 3 {
						break
					}
				}
			}
			if len(issueLines) >= 3 {
				break
			}
		}
	}

	if len(issueLines) > 0 {
		return strings.Join(issueLines, "\n")
	}
	return "发现文件上传安全问题"
}

// extractDangerousFileEvidence 提取危险文件证据
func (d *FileUploadDetector) extractDangerousFileEvidence(response string) string {
	lines := strings.Split(response, "\n")
	var dangerousLines []string

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			// 查找包含危险文件类型的行
			if strings.Contains(lineLower, "accept=\"*/*\"") ||
				strings.Contains(lineLower, "accept='*/*'") ||
				strings.Contains(lineLower, "accept=*/*") {
				dangerousLines = append(dangerousLines, strings.TrimSpace(line))
			} else {
				// 检查是否包含危险扩展名
				for _, ext := range d.dangerousExtensions {
					if strings.Contains(lineLower, "."+ext) {
						dangerousLines = append(dangerousLines, strings.TrimSpace(line))
						break
					}
				}
			}
			if len(dangerousLines) >= 3 {
				break
			}
		}
	}

	if len(dangerousLines) > 0 {
		return strings.Join(dangerousLines, "\n")
	}
	return "可能允许上传危险文件类型"
}

// extractPathTraversalEvidence 提取路径遍历证据
func (d *FileUploadDetector) extractPathTraversalEvidence(response string) string {
	lines := strings.Split(response, "\n")
	var traversalLines []string

	pathTraversalIndicators := []string{
		"../", "..\\", "directory traversal",
		"path traversal", "路径遍历", "目录遍历",
		"upload path", "save path", "file path",
	}

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			for _, indicator := range pathTraversalIndicators {
				if strings.Contains(lineLower, indicator) {
					traversalLines = append(traversalLines, strings.TrimSpace(line))
					break
				}
			}
			if len(traversalLines) >= 3 {
				break
			}
		}
	}

	if len(traversalLines) > 0 {
		return strings.Join(traversalLines, "\n")
	}
	return "可能存在路径遍历风险"
}
