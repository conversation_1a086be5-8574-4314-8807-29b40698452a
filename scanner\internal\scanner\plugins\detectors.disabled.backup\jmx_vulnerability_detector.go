package detectors

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// JMXVulnerabilityDetector JMX漏洞检测器
// 专门检测Java Management Extensions (JMX)相关的安全漏洞
type JMXVulnerabilityDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	jmxPorts           []int            // JMX常用端口
	rmiPorts           []int            // RMI端口
	jmxIndicators      []string         // JMX服务指示器
	jmxURLPatterns     []string         // JMX URL模式
	mbeanPatterns      []string         // MBean模式
	vulnerabilityTests []string         // 漏洞测试载荷
	jmxPatterns        []*regexp.Regexp // JMX响应模式
	rmiPatterns        []*regexp.Regexp // RMI响应模式
}

// NewJMXVulnerabilityDetector 创建JMX漏洞检测器
func NewJMXVulnerabilityDetector() *JMXVulnerabilityDetector {
	detector := &JMXVulnerabilityDetector{
		id:          "jmx-vulnerability-detector",
		name:        "JMX漏洞检测器",
		category:    "remote-code-execution",
		severity:    "Critical",
		cve:         []string{"CVE-2019-12409", "CVE-2016-3427", "CVE-2016-8735"},
		cwe:         []string{"CWE-94", "CWE-502", "CWE-284", "CWE-200"},
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测Java Management Extensions (JMX)端口暴露和远程代码执行漏洞",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         30 * time.Second,
			MaxRetries:      3,
			Concurrency:     5,
			RateLimit:       10,
			FollowRedirects: false,
			VerifySSL:       false,
			MaxResponseSize: 1024 * 1024, // 1MB
			Priority:        8,           // 高优先级
		},
	}

	// 初始化检测规则
	detector.initializeRules()

	return detector
}

// initializeRules 初始化检测规则
func (d *JMXVulnerabilityDetector) initializeRules() {
	// JMX常用端口
	d.jmxPorts = []int{
		1099, 1098, 1097, 1096, // 标准RMI端口
		8999, 9999, 9998, 9997, // 常见JMX端口
		8983, 8984, 7574, // Solr JMX端口
		9000, 9001, 9002, // 应用JMX端口
		11099, 12099, 13099, // 高位JMX端口
		7091, 7092, 7093, // WebLogic JMX端口
		8686, 8687, 8688, // JBoss JMX端口
		9990, 9999, 10099, // WildFly JMX端口
	}

	// RMI端口
	d.rmiPorts = []int{1099, 1098, 1097, 1096, 1095}

	// JMX服务指示器
	d.jmxIndicators = []string{
		"jmxrmi", "JMX", "javax.management", "MBeanServer", "ObjectName",
		"MLet", "MLetMBean", "javax.management.loading", "java.lang:type=Runtime",
		"java.lang:type=Memory", "java.lang:type=Threading", "java.lang:type=ClassLoading",
		"java.lang:type=Compilation", "java.lang:type=GarbageCollector",
		"java.lang:type=MemoryManager", "java.lang:type=MemoryPool",
		"java.lang:type=OperatingSystem", "java.rmi.server", "sun.management",
		"com.sun.management", "javax.management.remote", "javax.management.openmbean",
	}

	// JMX URL模式
	d.jmxURLPatterns = []string{
		"service:jmx:rmi:///jndi/rmi://%s:%d/jmxrmi",
		"service:jmx:rmi://%s:%d/jndi/rmi://%s:%d/jmxrmi",
		"service:jmx:iiop:///jndi/iiop://%s:%d/jmxrmi",
		"service:jmx:jmxmp://%s:%d",
		"service:jmx:rmi:///jndi/ldap://%s:%d/jmxrmi",
	}

	// MBean模式
	d.mbeanPatterns = []string{
		"javax.management.loading:type=MLet",
		"java.lang:type=Runtime",
		"java.lang:type=Memory",
		"java.lang:type=Threading",
		"java.util.logging:type=Logging",
		"com.sun.management:type=HotSpotDiagnostic",
		"com.sun.management:type=DiagnosticCommand",
	}

	// 漏洞测试载荷
	d.vulnerabilityTests = []string{
		// JMX连接测试
		"JRMI\x00\x02K",
		"JRMI\x00\x02L",

		// RMI协议测试
		"java.rmi.MarshalledObject",
		"java.rmi.server.UnicastRef",

		// MBean操作测试
		"javax.management.ObjectName",
		"javax.management.MBeanInfo",
		"javax.management.loading.MLet",

		// 序列化对象测试
		"\xac\xed\x00\x05", // Java序列化魔数
		"rO0ABQ==",         // Base64编码的Java序列化
	}

	// 编译正则表达式模式
	d.compilePatterns()
}

// compilePatterns 编译正则表达式模式
func (d *JMXVulnerabilityDetector) compilePatterns() {
	// JMX响应模式
	jmxPatterns := []string{
		`(?i)javax\.management`,
		`(?i)MBeanServer`,
		`(?i)ObjectName`,
		`(?i)jmxrmi`,
		`(?i)MLet.*MBean`,
		`(?i)java\.lang:type=Runtime`,
		`(?i)java\.lang:type=Memory`,
		`(?i)javax\.management\.loading`,
		`(?i)java\.rmi\.server`,
		`(?i)sun\.management`,
		`(?i)com\.sun\.management`,
		`(?i)javax\.management\.remote`,
	}

	for _, pattern := range jmxPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.jmxPatterns = append(d.jmxPatterns, compiled)
		}
	}

	// RMI响应模式
	rmiPatterns := []string{
		`(?i)java\.rmi`,
		`(?i)rmi\.server`,
		`(?i)UnicastRef`,
		`(?i)MarshalledObject`,
		`(?i)RemoteException`,
		`(?i)rmi\.registry`,
		`(?i)rmi\.activation`,
		`(?i)JRMI`,
	}

	for _, pattern := range rmiPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.rmiPatterns = append(d.rmiPatterns, compiled)
		}
	}
}

// GetID 获取检测器ID
func (d *JMXVulnerabilityDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *JMXVulnerabilityDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *JMXVulnerabilityDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *JMXVulnerabilityDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *JMXVulnerabilityDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *JMXVulnerabilityDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *JMXVulnerabilityDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *JMXVulnerabilityDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *JMXVulnerabilityDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *JMXVulnerabilityDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *JMXVulnerabilityDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// IsEnabled 检查是否启用
func (d *JMXVulnerabilityDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *JMXVulnerabilityDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// GetConfig 获取配置
func (d *JMXVulnerabilityDetector) GetConfig() *plugins.DetectorConfig {
	return d.config
}

// SetConfig 设置配置
func (d *JMXVulnerabilityDetector) SetConfig(config *plugins.DetectorConfig) {
	d.config = config
	d.updatedAt = time.Now()
}

// IsApplicable 检查是否适用于目标
func (d *JMXVulnerabilityDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// JMX检测适用于网络目标和服务目标
	if target.Type != "ip" && target.Type != "host" && target.Type != "service" && target.Type != "url" {
		return false
	}

	return true
}

// generateVulnID 生成漏洞ID
func (d *JMXVulnerabilityDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("%s-%s-%d", d.id, target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *JMXVulnerabilityDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 9.0 // 高风险基础分

	// 根据置信度调整分数
	riskScore := baseScore * confidence

	// 确保分数在合理范围内
	if riskScore > 10.0 {
		riskScore = 10.0
	} else if riskScore < 0.0 {
		riskScore = 0.0
	}

	return riskScore
}

// Detect 执行JMX漏洞检测
func (d *JMXVulnerabilityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	logger.Infof("开始JMX漏洞检测: %s", target.URL)

	// 执行多种JMX漏洞检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 获取目标主机
	host := d.extractHost(target)
	if host == "" {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 1. JMX端口扫描
	portEvidence, portConfidence, portPayload, portRequest, portResponse := d.detectJMXPorts(ctx, host)
	if portConfidence > maxConfidence {
		maxConfidence = portConfidence
		vulnerablePayload = portPayload
		vulnerableRequest = portRequest
		vulnerableResponse = portResponse
	}
	evidence = append(evidence, portEvidence...)

	// 2. JMX服务检测
	serviceEvidence, serviceConfidence, servicePayload, serviceRequest, serviceResponse := d.detectJMXService(ctx, host)
	if serviceConfidence > maxConfidence {
		maxConfidence = serviceConfidence
		vulnerablePayload = servicePayload
		vulnerableRequest = serviceRequest
		vulnerableResponse = serviceResponse
	}
	evidence = append(evidence, serviceEvidence...)

	// 3. JMX漏洞利用检测
	exploitEvidence, exploitConfidence, exploitPayload, exploitRequest, exploitResponse := d.detectJMXExploitability(ctx, host)
	if exploitConfidence > maxConfidence {
		maxConfidence = exploitConfidence
		vulnerablePayload = exploitPayload
		vulnerableRequest = exploitRequest
		vulnerableResponse = exploitResponse
	}
	evidence = append(evidence, exploitEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "JMX远程管理漏洞",
		Description:       "检测到JMX服务暴露，可能导致远程代码执行和信息泄露",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "禁用JMX远程访问，配置JMX认证和授权，使用防火墙限制JMX端口访问",
		References:        []string{"https://docs.oracle.com/javase/8/docs/technotes/guides/management/agent.html", "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2016-3427"},
		Tags:              []string{"jmx", "rmi", "java", "remote-code-execution", "information-disclosure"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *JMXVulnerabilityDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	logger.Infof("开始验证JMX漏洞: %s", target.URL)

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	host := d.extractHost(target)
	if host == "" {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "host-extraction-failed",
			Notes:      "无法提取目标主机信息",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 验证JMX端口
	if d.verifyJMXPorts(ctx, host) {
		verificationConfidence += 0.4
		evidence = append(evidence, plugins.Evidence{
			Type:        "port_verification",
			Description: "确认JMX端口开放",
			Content:     "JMX端口可访问",
			Location:    host,
			Timestamp:   time.Now(),
		})
	}

	// 验证JMX服务
	if d.verifyJMXService(ctx, host) {
		verificationConfidence += 0.4
		evidence = append(evidence, plugins.Evidence{
			Type:        "service_verification",
			Description: "确认JMX服务运行",
			Content:     "JMX服务响应正常",
			Location:    host,
			Timestamp:   time.Now(),
		})
	}

	// 验证漏洞利用能力
	if d.verifyJMXExploitability(ctx, host) {
		verificationConfidence += 0.2
		evidence = append(evidence, plugins.Evidence{
			Type:        "exploit_verification",
			Description: "确认JMX漏洞可利用",
			Content:     "JMX漏洞利用测试成功",
			Location:    host,
			Timestamp:   time.Now(),
		})
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "jmx-vulnerability-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("JMX漏洞验证完成，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}
