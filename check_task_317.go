package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 查询任务317的详细信息
	fmt.Println("=== 任务317详细信息 ===")
	
	var id int
	var name, taskType, status, errorMessage, infoData string
	var progress int
	
	err = db.QueryRow(`
		SELECT id, name, type, status, progress, error_message, 
		       CASE 
		           WHEN info_gathering_data IS NULL THEN 'NULL'
		           WHEN info_gathering_data = '' THEN 'EMPTY'
		           ELSE 'HAS_DATA'
		       END as data_status
		FROM scan_tasks 
		WHERE id = 317
	`).Scan(&id, &name, &taskType, &status, &progress, &errorMessage, &infoData)
	
	if err != nil {
		log.Fatal("查询失败:", err)
	}

	fmt.Printf("任务ID: %d\n", id)
	fmt.Printf("任务名称: %s\n", name)
	fmt.Printf("任务类型: %s\n", taskType)
	fmt.Printf("任务状态: %s\n", status)
	fmt.Printf("进度: %d%%\n", progress)
	fmt.Printf("错误信息: %s\n", errorMessage)
	fmt.Printf("信息收集数据状态: %s\n", infoData)

	// 查询任务日志
	fmt.Println("\n=== 任务日志 ===")
	
	rows, err := db.Query(`
		SELECT level, stage, message, created_at 
		FROM scan_logs 
		WHERE task_id = 317 
		ORDER BY created_at DESC 
		LIMIT 20
	`)
	if err != nil {
		log.Printf("查询日志失败: %v", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var level, stage, message, createdAt string
		err := rows.Scan(&level, &stage, &message, &createdAt)
		if err != nil {
			log.Printf("扫描日志行失败: %v", err)
			continue
		}
		
		fmt.Printf("[%s] %s - %s: %s\n", level, createdAt, stage, message)
	}
}
