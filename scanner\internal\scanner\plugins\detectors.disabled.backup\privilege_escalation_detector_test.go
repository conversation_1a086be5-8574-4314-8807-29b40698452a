package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestPrivilegeEscalationDetectorBasicFunctionality 测试权限提升检测器基础功能
func TestPrivilegeEscalationDetectorBasicFunctionality(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "privilege-escalation-comprehensive", detector.GetID())
	assert.Equal(t, "权限提升漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-269")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestPrivilegeEscalationDetectorApplicability 测试权限提升检测器适用性
func TestPrivilegeEscalationDetectorApplicability(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试有认证功能的目标
	authTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/login",
				Method: "POST",
				Fields: map[string]string{
					"username": "text",
					"password": "password",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(authTarget))

	// 测试有认证Cookie的目标
	cookieTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com",
		Protocol: "http",
		Port:     80,
		Cookies: []plugins.CookieInfo{
			{
				Name:  "session_id",
				Value: "abc123",
			},
		},
	}
	assert.True(t, detector.IsApplicable(cookieTarget))

	// 测试有认证Header的目标
	headerTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Authorization": "Bearer token123",
		},
	}
	assert.True(t, detector.IsApplicable(headerTarget))

	// 测试API目标
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/users",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试管理相关目标
	adminTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(adminTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无认证功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestPrivilegeEscalationDetectorConfiguration 测试权限提升检测器配置
func TestPrivilegeEscalationDetectorConfiguration(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       6,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestPrivilegeEscalationDetectorPaths 测试权限提升检测器路径列表
func TestPrivilegeEscalationDetectorPaths(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 检查管理员路径
	assert.NotEmpty(t, detector.adminPaths)
	assert.Greater(t, len(detector.adminPaths), 40)

	// 检查用户路径
	assert.NotEmpty(t, detector.userPaths)
	assert.Greater(t, len(detector.userPaths), 15)

	// 检查API端点
	assert.NotEmpty(t, detector.apiEndpoints)
	assert.Greater(t, len(detector.apiEndpoints), 10)

	// 检查认证绕过载荷
	assert.NotEmpty(t, detector.authBypassPayloads)
	assert.Greater(t, len(detector.authBypassPayloads), 20)

	// 检查权限模式
	assert.NotEmpty(t, detector.privilegePatterns)
	assert.Greater(t, len(detector.privilegePatterns), 15)

	// 检查访问拒绝模式
	assert.NotEmpty(t, detector.accessDeniedPatterns)
	assert.Greater(t, len(detector.accessDeniedPatterns), 10)

	// 检查成功访问模式
	assert.NotEmpty(t, detector.successPatterns)
	assert.Greater(t, len(detector.successPatterns), 10)

	// 检查特定的路径
	assert.Contains(t, detector.adminPaths, "/admin")
	assert.Contains(t, detector.adminPaths, "/admin/users")
	assert.Contains(t, detector.userPaths, "/user/{id}")
	assert.Contains(t, detector.apiEndpoints, "/api/admin")
	assert.Contains(t, detector.authBypassPayloads, "' OR '1'='1")
}

// TestPrivilegeEscalationDetectorVerticalCheck 测试垂直权限提升检查
func TestPrivilegeEscalationDetectorVerticalCheck(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试成功的管理员访问
	adminResponse := `Status: 200 OK
Content-Type: text/html

<html>
<head><title>Admin Panel</title></head>
<body>
<h1>Welcome to Admin Dashboard</h1>
<div class="admin-menu">
	<a href="/admin/users">User Management</a>
	<a href="/admin/settings">System Settings</a>
	<a href="/admin/logs">System Logs</a>
</div>
<p>Welcome back, administrator!</p>
</body>
</html>`
	adminPath := "/admin"
	confidence := detector.checkVerticalPrivilegeEscalation(adminResponse, adminPath)
	assert.Greater(t, confidence, 0.8)

	// 测试被拒绝的访问
	deniedResponse := `Status: 403 Forbidden
Content-Type: text/html

<html>
<body>
<h1>Access Denied</h1>
<p>You do not have permission to access this resource.</p>
<p>Please login with administrator credentials.</p>
</body>
</html>`
	deniedPath := "/admin"
	confidence2 := detector.checkVerticalPrivilegeEscalation(deniedResponse, deniedPath)
	assert.LessOrEqual(t, confidence2, 0.0) // 403状态码会直接返回0.0

	// 测试重定向响应
	redirectResponse := `Status: 302 Found
Location: /login
Content-Type: text/html

<html>
<body>
<p>Redirecting to login page...</p>
</body>
</html>`
	redirectPath := "/admin"
	confidence3 := detector.checkVerticalPrivilegeEscalation(redirectResponse, redirectPath)
	assert.GreaterOrEqual(t, confidence3, 0.0) // 重定向可能返回0.2的置信度
	assert.LessOrEqual(t, confidence3, 0.5)
}

// TestPrivilegeEscalationDetectorHorizontalCheck 测试水平权限提升检查
func TestPrivilegeEscalationDetectorHorizontalCheck(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试成功的用户信息访问
	userResponse := `Status: 200 OK
Content-Type: application/json

{
	"user_id": "123",
	"username": "testuser",
	"email": "<EMAIL>",
	"phone": "************",
	"address": "123 Main St",
	"profile": {
		"personal_data": "sensitive info",
		"billing_information": "credit card data"
	}
}`
	userPath := "/user/123"
	userID := "123"
	confidence := detector.checkHorizontalPrivilegeEscalation(userResponse, userPath, userID)
	assert.Greater(t, confidence, 0.7)

	// 测试访问被拒绝
	deniedUserResponse := `Status: 403 Forbidden
Content-Type: application/json

{
	"error": "Access denied",
	"message": "You can only access your own profile"
}`
	confidence2 := detector.checkHorizontalPrivilegeEscalation(deniedUserResponse, userPath, userID)
	assert.Less(t, confidence2, 0.3)

	// 测试不存在的用户
	notFoundResponse := `Status: 404 Not Found
Content-Type: application/json

{
	"error": "User not found"
}`
	confidence3 := detector.checkHorizontalPrivilegeEscalation(notFoundResponse, userPath, userID)
	assert.Equal(t, 0.0, confidence3)
}

// TestPrivilegeEscalationDetectorAuthBypassCheck 测试认证绕过检查
func TestPrivilegeEscalationDetectorAuthBypassCheck(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试成功的认证绕过
	bypassResponse := `Status: 200 OK
Set-Cookie: session=abc123; Path=/
Location: /dashboard

<html>
<body>
<h1>Welcome to Dashboard</h1>
<p>Login successful!</p>
<a href="/logout">Sign out</a>
</body>
</html>`
	payload := "' OR '1'='1"
	confidence := detector.checkAuthenticationBypass(bypassResponse, payload)
	assert.Greater(t, confidence, 0.8)

	// 测试认证失败
	failedResponse := `Status: 200 OK
Content-Type: text/html

<html>
<body>
<h1>Login Failed</h1>
<p>Invalid credentials. Access denied.</p>
<form action="/login" method="post">
	<input type="text" name="username">
	<input type="password" name="password">
	<input type="submit" value="Login">
</form>
</body>
</html>`
	confidence2 := detector.checkAuthenticationBypass(failedResponse, payload)
	assert.Less(t, confidence2, 0.3)

	// 测试SQL错误响应
	errorResponse := `Status: 500 Internal Server Error
Content-Type: text/html

<html>
<body>
<h1>Database Error</h1>
<p>SQL syntax error near 'OR'</p>
</body>
</html>`
	sqlPayload := "' OR 1=1--"
	confidence3 := detector.checkAuthenticationBypass(errorResponse, sqlPayload)
	assert.LessOrEqual(t, confidence3, 0.6) // SQL错误可能仍有一定置信度
}

// TestPrivilegeEscalationDetectorAccessControlCheck 测试访问控制检查
func TestPrivilegeEscalationDetectorAccessControlCheck(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试API访问控制缺陷
	apiResponse := `Status: 200 OK
Content-Type: application/json

{
	"users": [
		{
			"id": 1,
			"username": "admin",
			"role": "administrator",
			"permissions": ["read", "write", "delete"],
			"is_admin": true,
			"password": "secret123"
		},
		{
			"id": 2,
			"username": "user",
			"role": "user",
			"permissions": ["read"]
		}
	]
}`
	endpoint := "/api/admin/users"
	confidence := detector.checkAccessControlFlaws(apiResponse, endpoint)
	assert.Greater(t, confidence, 0.8)

	// 测试普通API响应
	normalResponse := `Status: 200 OK
Content-Type: application/json

{
	"message": "Hello World",
	"status": "success"
}`
	normalEndpoint := "/api/hello"
	confidence2 := detector.checkAccessControlFlaws(normalResponse, normalEndpoint)
	assert.LessOrEqual(t, confidence2, 0.7) // JSON响应可能有0.7的置信度

	// 测试访问被拒绝的API
	deniedAPIResponse := `Status: 403 Forbidden
Content-Type: application/json

{
	"error": "Insufficient privileges"
}`
	confidence3 := detector.checkAccessControlFlaws(deniedAPIResponse, endpoint)
	assert.Equal(t, 0.0, confidence3)
}

// TestPrivilegeEscalationDetectorBuildURL 测试URL构造
func TestPrivilegeEscalationDetectorBuildURL(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试基础URL构造
	baseURL := "http://example.com"
	path := "/admin"
	privilegeURL := detector.buildPrivilegeURL(baseURL, path)
	assert.Equal(t, "http://example.com/admin", privilegeURL)

	// 测试带端口的URL
	baseURLWithPort := "http://example.com:8080"
	privilegeURLWithPort := detector.buildPrivilegeURL(baseURLWithPort, path)
	assert.Equal(t, "http://example.com:8080/admin", privilegeURLWithPort)

	// 测试HTTPS URL
	httpsURL := "https://example.com"
	httpsPrivilegeURL := detector.buildPrivilegeURL(httpsURL, path)
	assert.Equal(t, "https://example.com/admin", httpsPrivilegeURL)

	// 测试不以/开头的路径
	relativePath := "admin/users"
	relativeURL := detector.buildPrivilegeURL(baseURL, relativePath)
	assert.Equal(t, "http://example.com/admin/users", relativeURL)
}

// TestPrivilegeEscalationDetectorRiskScore 测试风险评分计算
func TestPrivilegeEscalationDetectorRiskScore(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestPrivilegeEscalationDetectorLifecycle 测试检测器生命周期
func TestPrivilegeEscalationDetectorLifecycle(t *testing.T) {
	detector := NewPrivilegeEscalationDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkPrivilegeEscalationDetectorVerticalCheck 基准测试垂直权限提升检查性能
func BenchmarkPrivilegeEscalationDetectorVerticalCheck(b *testing.B) {
	detector := NewPrivilegeEscalationDetector()
	response := `Status: 200 OK\nContent-Type: text/html\n\n<html><body><h1>Admin Panel</h1><p>Welcome administrator</p></body></html>`
	path := "/admin"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkVerticalPrivilegeEscalation(response, path)
	}
}

// BenchmarkPrivilegeEscalationDetectorHorizontalCheck 基准测试水平权限提升检查性能
func BenchmarkPrivilegeEscalationDetectorHorizontalCheck(b *testing.B) {
	detector := NewPrivilegeEscalationDetector()
	response := `Status: 200 OK\nContent-Type: application/json\n\n{"user_id":"123","email":"<EMAIL>","profile":"data"}`
	path := "/user/123"
	userID := "123"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkHorizontalPrivilegeEscalation(response, path, userID)
	}
}
