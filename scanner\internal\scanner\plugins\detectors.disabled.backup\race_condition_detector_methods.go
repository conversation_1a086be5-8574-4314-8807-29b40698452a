package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectTimeWindowRace 检测时间窗口竞争
func (d *RaceConditionDetector) detectTimeWindowRace(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试时间窗口载荷
	for _, payload := range d.timeWindowPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送并发时间窗口请求
		responses, err := d.sendConcurrentRaceRequests(ctx, target.URL, payload, 5)
		if err != nil {
			continue
		}

		// 检查时间窗口响应
		confidence := d.checkTimeWindowResponse(responses, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("时间窗口竞争: %s", payload)
			vulnerableRequest = target.URL
			if len(responses) > 0 {
				vulnerableResponse = responses[0]
			}
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "time-window-race",
				Description: fmt.Sprintf("发现时间窗口竞争: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRaceEvidence(responses, "time-window"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 100)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectResourceRace 检测资源竞争
func (d *RaceConditionDetector) detectResourceRace(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试资源竞争载荷
	for _, payload := range d.resourceRacePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送并发资源竞争请求
		responses, err := d.sendConcurrentRaceRequests(ctx, target.URL, payload, 10)
		if err != nil {
			continue
		}

		// 检查资源竞争响应
		confidence := d.checkResourceRaceResponse(responses, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("资源竞争: %s", payload)
			vulnerableRequest = target.URL
			if len(responses) > 0 {
				vulnerableResponse = responses[0]
			}
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "resource-race",
				Description: fmt.Sprintf("发现资源竞争: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRaceEvidence(responses, "resource-race"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 150)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectStateRace 检测状态竞争
func (d *RaceConditionDetector) detectStateRace(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试状态竞争载荷
	for _, payload := range d.stateRacePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送并发状态竞争请求
		responses, err := d.sendConcurrentRaceRequests(ctx, target.URL, payload, 8)
		if err != nil {
			continue
		}

		// 检查状态竞争响应
		confidence := d.checkStateRaceResponse(responses, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("状态竞争: %s", payload)
			vulnerableRequest = target.URL
			if len(responses) > 0 {
				vulnerableResponse = responses[0]
			}
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "state-race",
				Description: fmt.Sprintf("发现状态竞争: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRaceEvidence(responses, "state-race"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 120)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectConcurrencyRace 检测并发控制竞争
func (d *RaceConditionDetector) detectConcurrencyRace(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试并发控制载荷
	for _, payload := range d.concurrencyPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送并发控制竞争请求
		responses, err := d.sendConcurrentRaceRequests(ctx, target.URL, payload, 15)
		if err != nil {
			continue
		}

		// 检查并发控制响应
		confidence := d.checkConcurrencyRaceResponse(responses, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("并发控制竞争: %s", payload)
			vulnerableRequest = target.URL
			if len(responses) > 0 {
				vulnerableResponse = responses[0]
			}
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "concurrency-race",
				Description: fmt.Sprintf("发现并发控制竞争: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractRaceEvidence(responses, "concurrency-race"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendConcurrentRaceRequests 发送并发竞态条件请求
func (d *RaceConditionDetector) sendConcurrentRaceRequests(ctx context.Context, targetURL, payload string, concurrency int) ([]string, error) {
	var responses []string
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 创建并发请求
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			// 尝试GET参数注入
			getResp, err := d.sendRaceGETRequest(ctx, targetURL, payload)
			if err == nil && getResp != "" {
				mu.Lock()
				responses = append(responses, getResp)
				mu.Unlock()
				return
			}

			// 尝试POST表单注入
			postResp, err := d.sendRacePOSTRequest(ctx, targetURL, payload)
			if err == nil && postResp != "" {
				mu.Lock()
				responses = append(responses, postResp)
				mu.Unlock()
				return
			}

			// 如果有POST响应（即使有错误），也添加到结果中
			if postResp != "" {
				mu.Lock()
				responses = append(responses, postResp)
				mu.Unlock()
			}
		}()
	}

	// 等待所有请求完成
	wg.Wait()

	if len(responses) == 0 {
		return nil, fmt.Errorf("所有并发请求都失败")
	}

	return responses, nil
}

// sendRaceGETRequest 发送竞态条件GET请求
func (d *RaceConditionDetector) sendRaceGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendRacePOSTRequest 发送竞态条件POST请求
func (d *RaceConditionDetector) sendRacePOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkTimeWindowResponse 检查时间窗口响应
func (d *RaceConditionDetector) checkTimeWindowResponse(responses []string, payload string) float64 {
	if len(responses) == 0 {
		return 0.0
	}

	confidence := 0.0

	// 检查响应时间差异
	timingDifferences := d.analyzeTimingDifferences(responses)
	if timingDifferences > 0.3 {
		confidence += 0.4 // 时间差异表明可能存在时间窗口竞争
	}

	// 检查响应内容差异
	contentDifferences := d.analyzeContentDifferences(responses)
	if contentDifferences > 0.2 {
		confidence += 0.3 // 内容差异表明可能存在竞态条件
	}

	// 检查时间窗口模式匹配
	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, pattern := range d.timePatterns {
			if pattern.MatchString(responseLower) {
				confidence += 0.3
				break
			}
		}
	}

	// 检查时间窗口特定指示器
	timeIndicators := []string{
		"time", "timing", "timestamp", "delay", "sleep", "wait",
		"timeout", "interval", "duration", "window", "race",
		"concurrent", "parallel", "async", "thread", "process",
		"时间", "时机", "时间戳", "延迟", "等待", "超时",
		"间隔", "持续", "窗口", "竞争", "并发", "并行",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range timeIndicators {
			if strings.Contains(responseLower, indicator) {
				confidence += 0.2
				break
			}
		}
	}

	return confidence
}

// checkResourceRaceResponse 检查资源竞争响应
func (d *RaceConditionDetector) checkResourceRaceResponse(responses []string, payload string) float64 {
	if len(responses) == 0 {
		return 0.0
	}

	confidence := 0.0

	// 检查HTTP状态码差异
	statusDifferences := d.analyzeStatusDifferences(responses)
	if statusDifferences > 0.2 {
		confidence += 0.4 // 状态码差异表明可能存在资源竞争
	}

	// 检查响应内容差异
	contentDifferences := d.analyzeContentDifferences(responses)
	if contentDifferences > 0.3 {
		confidence += 0.3 // 内容差异表明可能存在资源竞争
	}

	// 检查资源竞争模式匹配
	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, pattern := range d.resourcePatterns {
			if pattern.MatchString(responseLower) {
				confidence += 0.4
				break
			}
		}
	}

	// 检查资源竞争特定指示器
	resourceIndicators := []string{
		"lock", "unlock", "mutex", "semaphore", "resource", "file",
		"database", "connection", "pool", "queue", "deadlock",
		"contention", "busy", "unavailable", "in_use", "locked",
		"锁", "解锁", "互斥", "信号量", "资源", "文件",
		"数据库", "连接", "池", "队列", "死锁", "争用",
		"忙碌", "不可用", "使用中", "已锁定",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range resourceIndicators {
			if strings.Contains(responseLower, indicator) {
				confidence += 0.3
				break
			}
		}
	}

	return confidence
}

// checkStateRaceResponse 检查状态竞争响应
func (d *RaceConditionDetector) checkStateRaceResponse(responses []string, payload string) float64 {
	if len(responses) == 0 {
		return 0.0
	}

	confidence := 0.0

	// 检查状态值差异
	stateDifferences := d.analyzeStateDifferences(responses)
	if stateDifferences > 0.3 {
		confidence += 0.5 // 状态差异表明可能存在状态竞争
	}

	// 检查响应序列差异
	sequenceDifferences := d.analyzeSequenceDifferences(responses)
	if sequenceDifferences > 0.2 {
		confidence += 0.3 // 序列差异表明可能存在状态竞争
	}

	// 检查状态竞争模式匹配
	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, pattern := range d.statePatterns {
			if pattern.MatchString(responseLower) {
				confidence += 0.4
				break
			}
		}
	}

	// 检查状态竞争特定指示器
	stateIndicators := []string{
		"state", "status", "flag", "counter", "sequence", "order",
		"number", "id", "session", "transaction", "inconsistent",
		"invalid", "corrupted", "conflict", "mismatch", "error",
		"状态", "状态", "标志", "计数器", "序列", "顺序",
		"编号", "标识", "会话", "事务", "不一致", "无效",
		"损坏", "冲突", "不匹配", "错误",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range stateIndicators {
			if strings.Contains(responseLower, indicator) {
				confidence += 0.3
				break
			}
		}
	}

	return confidence
}

// checkConcurrencyRaceResponse 检查并发控制响应
func (d *RaceConditionDetector) checkConcurrencyRaceResponse(responses []string, payload string) float64 {
	if len(responses) == 0 {
		return 0.0
	}

	confidence := 0.0

	// 检查并发响应差异
	concurrencyDifferences := d.analyzeConcurrencyDifferences(responses)
	if concurrencyDifferences > 0.4 {
		confidence += 0.5 // 并发差异表明可能存在并发控制问题
	}

	// 检查响应顺序差异
	orderDifferences := d.analyzeOrderDifferences(responses)
	if orderDifferences > 0.3 {
		confidence += 0.3 // 顺序差异表明可能存在并发控制问题
	}

	// 检查并发控制模式匹配
	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, pattern := range d.concurrencyPatterns {
			if pattern.MatchString(responseLower) {
				confidence += 0.4
				break
			}
		}
	}

	// 检查并发控制特定指示器
	concurrencyIndicators := []string{
		"concurrent", "parallel", "thread", "process", "atomic",
		"synchronization", "coordination", "barrier", "latch",
		"monitor", "condition", "signal", "notify", "broadcast",
		"并发", "并行", "线程", "进程", "原子", "同步",
		"协调", "屏障", "门闩", "监视器", "条件", "信号",
		"通知", "广播",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range concurrencyIndicators {
			if strings.Contains(responseLower, indicator) {
				confidence += 0.3
				break
			}
		}
	}

	return confidence
}

// 分析方法

// analyzeTimingDifferences 分析时间差异
func (d *RaceConditionDetector) analyzeTimingDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 简化的时间差异分析
	// 在实际实现中，这里会分析响应时间的差异
	// 目前返回一个基于响应数量的简化值
	if len(responses) >= 5 {
		return 0.4 // 多个响应可能表明时间差异
	} else if len(responses) >= 3 {
		return 0.3
	} else {
		return 0.2
	}
}

// analyzeContentDifferences 分析内容差异
func (d *RaceConditionDetector) analyzeContentDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 计算响应内容的差异
	uniqueResponses := make(map[string]bool)
	for _, response := range responses {
		// 简化响应内容（去除时间戳等动态内容）
		simplified := d.simplifyResponse(response)
		uniqueResponses[simplified] = true
	}

	// 计算差异比例
	differenceRatio := float64(len(uniqueResponses)) / float64(len(responses))

	// 如果有多种不同的响应，可能表明存在竞态条件
	if differenceRatio > 0.5 {
		return differenceRatio
	}

	return 0.0
}

// analyzeStatusDifferences 分析状态码差异
func (d *RaceConditionDetector) analyzeStatusDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 提取状态码
	statusCodes := make(map[string]int)
	for _, response := range responses {
		statusCode := d.extractStatusCode(response)
		statusCodes[statusCode]++
	}

	// 计算状态码差异
	if len(statusCodes) > 1 {
		return float64(len(statusCodes)) / float64(len(responses))
	}

	return 0.0
}

// analyzeStateDifferences 分析状态差异
func (d *RaceConditionDetector) analyzeStateDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 提取状态相关信息
	states := make(map[string]int)
	for _, response := range responses {
		state := d.extractStateInfo(response)
		if state != "" {
			states[state]++
		}
	}

	// 计算状态差异
	if len(states) > 1 {
		return float64(len(states)) / float64(len(responses))
	}

	return 0.0
}

// analyzeSequenceDifferences 分析序列差异
func (d *RaceConditionDetector) analyzeSequenceDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 提取序列相关信息
	sequences := make(map[string]int)
	for _, response := range responses {
		sequence := d.extractSequenceInfo(response)
		if sequence != "" {
			sequences[sequence]++
		}
	}

	// 计算序列差异
	if len(sequences) > 1 {
		return float64(len(sequences)) / float64(len(responses))
	}

	return 0.0
}

// analyzeConcurrencyDifferences 分析并发差异
func (d *RaceConditionDetector) analyzeConcurrencyDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 提取并发相关信息
	concurrencyInfo := make(map[string]int)
	for _, response := range responses {
		info := d.extractConcurrencyInfo(response)
		if info != "" {
			concurrencyInfo[info]++
		}
	}

	// 计算并发差异
	if len(concurrencyInfo) > 1 {
		return float64(len(concurrencyInfo)) / float64(len(responses))
	}

	return 0.0
}

// analyzeOrderDifferences 分析顺序差异
func (d *RaceConditionDetector) analyzeOrderDifferences(responses []string) float64 {
	if len(responses) < 2 {
		return 0.0
	}

	// 提取顺序相关信息
	orders := make(map[string]int)
	for _, response := range responses {
		order := d.extractOrderInfo(response)
		if order != "" {
			orders[order]++
		}
	}

	// 计算顺序差异
	if len(orders) > 1 {
		return float64(len(orders)) / float64(len(responses))
	}

	return 0.0
}

// 辅助提取方法

// simplifyResponse 简化响应内容
func (d *RaceConditionDetector) simplifyResponse(response string) string {
	// 移除时间戳和动态内容
	simplified := response

	// 移除常见的动态内容模式
	timePatterns := []string{
		`\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}`,
		`\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}`,
		`\d{10,13}`, // Unix时间戳
		`Date: [^\n]+`,
		`Last-Modified: [^\n]+`,
		`ETag: [^\n]+`,
	}

	for _, pattern := range timePatterns {
		re := regexp.MustCompile(pattern)
		simplified = re.ReplaceAllString(simplified, "TIMESTAMP")
	}

	return simplified
}

// extractStatusCode 提取状态码
func (d *RaceConditionDetector) extractStatusCode(response string) string {
	lines := strings.Split(response, "\n")
	if len(lines) > 0 && strings.HasPrefix(lines[0], "Status: ") {
		parts := strings.Fields(lines[0])
		if len(parts) >= 2 {
			return parts[1]
		}
	}
	return "unknown"
}

// extractStateInfo 提取状态信息
func (d *RaceConditionDetector) extractStateInfo(response string) string {
	responseLower := strings.ToLower(response)

	// 查找状态相关信息
	statePatterns := []string{
		`state["\s]*[:=]["\s]*([^"\s,}]+)`,
		`status["\s]*[:=]["\s]*([^"\s,}]+)`,
		`flag["\s]*[:=]["\s]*([^"\s,}]+)`,
		`counter["\s]*[:=]["\s]*([^"\s,}]+)`,
	}

	for _, pattern := range statePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(responseLower)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// extractSequenceInfo 提取序列信息
func (d *RaceConditionDetector) extractSequenceInfo(response string) string {
	responseLower := strings.ToLower(response)

	// 查找序列相关信息
	sequencePatterns := []string{
		`sequence["\s]*[:=]["\s]*([^"\s,}]+)`,
		`order["\s]*[:=]["\s]*([^"\s,}]+)`,
		`number["\s]*[:=]["\s]*([^"\s,}]+)`,
		`id["\s]*[:=]["\s]*([^"\s,}]+)`,
	}

	for _, pattern := range sequencePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(responseLower)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// extractConcurrencyInfo 提取并发信息
func (d *RaceConditionDetector) extractConcurrencyInfo(response string) string {
	responseLower := strings.ToLower(response)

	// 查找并发相关信息
	concurrencyPatterns := []string{
		`thread["\s]*[:=]["\s]*([^"\s,}]+)`,
		`process["\s]*[:=]["\s]*([^"\s,}]+)`,
		`concurrent["\s]*[:=]["\s]*([^"\s,}]+)`,
		`parallel["\s]*[:=]["\s]*([^"\s,}]+)`,
	}

	for _, pattern := range concurrencyPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(responseLower)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// extractOrderInfo 提取顺序信息
func (d *RaceConditionDetector) extractOrderInfo(response string) string {
	responseLower := strings.ToLower(response)

	// 查找顺序相关信息
	orderPatterns := []string{
		`order["\s]*[:=]["\s]*([^"\s,}]+)`,
		`position["\s]*[:=]["\s]*([^"\s,}]+)`,
		`index["\s]*[:=]["\s]*([^"\s,}]+)`,
		`rank["\s]*[:=]["\s]*([^"\s,}]+)`,
	}

	for _, pattern := range orderPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(responseLower)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// extractRaceEvidence 提取竞态条件证据
func (d *RaceConditionDetector) extractRaceEvidence(responses []string, raceType string) string {
	// 限制证据长度
	maxLength := 1000
	evidence := fmt.Sprintf("竞态条件证据 (%s):\n", raceType)

	// 添加响应统计信息
	evidence += fmt.Sprintf("并发请求数: %d\n", len(responses))

	// 分析响应差异
	uniqueResponses := make(map[string]int)
	statusCodes := make(map[string]int)

	for i, response := range responses {
		if i >= 5 { // 最多显示5个响应
			break
		}

		// 提取状态码
		statusCode := d.extractStatusCode(response)
		statusCodes[statusCode]++

		// 简化响应内容
		simplified := d.simplifyResponse(response)
		uniqueResponses[simplified]++

		// 添加响应摘要
		if len(response) > 200 {
			evidence += fmt.Sprintf("响应 %d: %s...\n", i+1, response[:200])
		} else {
			evidence += fmt.Sprintf("响应 %d: %s\n", i+1, response)
		}
	}

	// 添加差异分析
	evidence += fmt.Sprintf("\n差异分析:\n")
	evidence += fmt.Sprintf("- 唯一响应数: %d\n", len(uniqueResponses))
	evidence += fmt.Sprintf("- 状态码种类: %d\n", len(statusCodes))

	// 添加状态码分布
	if len(statusCodes) > 1 {
		evidence += "- 状态码分布:\n"
		for code, count := range statusCodes {
			evidence += fmt.Sprintf("  %s: %d次\n", code, count)
		}
	}

	// 根据竞态条件类型添加特定证据
	switch raceType {
	case "time-window":
		evidence += d.extractTimeWindowEvidence(responses)
	case "resource-race":
		evidence += d.extractResourceRaceEvidence(responses)
	case "state-race":
		evidence += d.extractStateRaceEvidence(responses)
	case "concurrency-race":
		evidence += d.extractConcurrencyRaceEvidence(responses)
	}

	// 限制证据长度
	if len(evidence) > maxLength {
		evidence = evidence[:maxLength] + "..."
	}

	return evidence
}

// extractTimeWindowEvidence 提取时间窗口证据
func (d *RaceConditionDetector) extractTimeWindowEvidence(responses []string) string {
	evidence := "\n时间窗口分析:\n"

	// 查找时间相关内容
	timeIndicators := []string{
		"time", "timing", "timestamp", "delay", "sleep", "wait",
		"timeout", "interval", "duration", "window",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range timeIndicators {
			if strings.Contains(responseLower, indicator) {
				evidence += fmt.Sprintf("- 发现时间指示器: %s\n", indicator)
				break
			}
		}
	}

	return evidence
}

// extractResourceRaceEvidence 提取资源竞争证据
func (d *RaceConditionDetector) extractResourceRaceEvidence(responses []string) string {
	evidence := "\n资源竞争分析:\n"

	// 查找资源相关内容
	resourceIndicators := []string{
		"lock", "unlock", "mutex", "semaphore", "resource",
		"file", "database", "connection", "pool", "queue",
		"deadlock", "contention", "busy", "unavailable",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range resourceIndicators {
			if strings.Contains(responseLower, indicator) {
				evidence += fmt.Sprintf("- 发现资源指示器: %s\n", indicator)
				break
			}
		}
	}

	return evidence
}

// extractStateRaceEvidence 提取状态竞争证据
func (d *RaceConditionDetector) extractStateRaceEvidence(responses []string) string {
	evidence := "\n状态竞争分析:\n"

	// 查找状态相关内容
	stateIndicators := []string{
		"state", "status", "flag", "counter", "sequence",
		"order", "number", "id", "session", "transaction",
		"inconsistent", "invalid", "corrupted", "conflict",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range stateIndicators {
			if strings.Contains(responseLower, indicator) {
				evidence += fmt.Sprintf("- 发现状态指示器: %s\n", indicator)
				break
			}
		}
	}

	return evidence
}

// extractConcurrencyRaceEvidence 提取并发控制证据
func (d *RaceConditionDetector) extractConcurrencyRaceEvidence(responses []string) string {
	evidence := "\n并发控制分析:\n"

	// 查找并发相关内容
	concurrencyIndicators := []string{
		"concurrent", "parallel", "thread", "process", "atomic",
		"synchronization", "coordination", "barrier", "latch",
		"monitor", "condition", "signal", "notify", "broadcast",
	}

	for _, response := range responses {
		responseLower := strings.ToLower(response)
		for _, indicator := range concurrencyIndicators {
			if strings.Contains(responseLower, indicator) {
				evidence += fmt.Sprintf("- 发现并发指示器: %s\n", indicator)
				break
			}
		}
	}

	return evidence
}
