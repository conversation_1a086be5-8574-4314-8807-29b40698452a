package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectLFI 检测本地文件包含
func (d *FileInclusionDetector) detectLFI(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试LFI载荷
	for _, payload := range d.lfiPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送LFI请求
		resp, err := d.sendFileInclusionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查LFI响应
		confidence := d.checkLFIResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("LFI: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "lfi",
				Description: fmt.Sprintf("发现本地文件包含: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractFileEvidence(resp, "lfi"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectRFI 检测远程文件包含
func (d *FileInclusionDetector) detectRFI(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试RFI载荷
	for _, payload := range d.rfiPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送RFI请求
		resp, err := d.sendFileInclusionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查RFI响应
		confidence := d.checkRFIResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("RFI: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "rfi",
				Description: fmt.Sprintf("发现远程文件包含: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractFileEvidence(resp, "rfi"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPathTraversal 检测路径遍历
func (d *FileInclusionDetector) detectPathTraversal(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试路径遍历载荷
	for _, payload := range d.pathTraversalPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 组合路径遍历和文件名
		testFiles := []string{"etc/passwd", "windows/win.ini", "etc/hosts", "windows/system32/drivers/etc/hosts"}
		for _, file := range testFiles {
			fullPayload := payload + file

			// 发送路径遍历请求
			resp, err := d.sendFileInclusionRequest(ctx, target.URL, fullPayload)
			if err != nil {
				continue
			}

			// 检查路径遍历响应
			confidence := d.checkPathTraversalResponse(resp, fullPayload)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("路径遍历: %s", fullPayload)
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "path-traversal",
					Description: fmt.Sprintf("发现路径遍历: %s (置信度: %.2f)", fullPayload, confidence),
					Content:     d.extractFileEvidence(resp, "path-traversal"),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 200)
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectPHPWrapper 检测PHP包装器
func (d *FileInclusionDetector) detectPHPWrapper(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试PHP包装器载荷
	for _, payload := range d.wrapperPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送PHP包装器请求
		resp, err := d.sendFileInclusionRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查PHP包装器响应
		confidence := d.checkWrapperResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("PHP包装器: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "php-wrapper",
				Description: fmt.Sprintf("发现PHP包装器漏洞: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractFileEvidence(resp, "php-wrapper"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendFileInclusionRequest 发送文件包含请求
func (d *FileInclusionDetector) sendFileInclusionRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendFileInclusionGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendFileInclusionPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 返回POST响应（即使有错误）
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendFileInclusionGETRequest 发送文件包含GET请求
func (d *FileInclusionDetector) sendFileInclusionGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendFileInclusionPOSTRequest 发送文件包含POST请求
func (d *FileInclusionDetector) sendFileInclusionPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkLFIResponse 检查LFI响应
func (d *FileInclusionDetector) checkLFIResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含文件内容
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.2 // 服务器错误可能表示文件包含尝试
	}

	// 检查LFI模式匹配
	for _, pattern := range d.lfiPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查成功模式匹配
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查LFI特定指示器
	lfiIndicators := []string{
		"root:x:", "daemon:x:", "bin:x:", "sys:x:", "sync:x:", "games:x:", "man:x:",
		"lp:x:", "mail:x:", "news:x:", "uucp:x:", "proxy:x:", "www-data:x:",
		"backup:x:", "list:x:", "irc:x:", "gnats:x:", "nobody:x:", "systemd-network:x:",
		"[extensions]", "[fonts]", "[mci extensions]", "[files]", "[mail]",
		"for 16-bit app support", "microsoft windows", "windows registry editor",
		"<?php", "<?=", "<%", "<script", "function", "class", "include", "require",
		"warning:", "error:", "notice:", "fatal error:", "parse error:",
		"failed to open stream", "no such file or directory", "permission denied",
		"文件不存在", "权限被拒绝", "无法打开文件", "路径不存在", "访问被拒绝",
	}

	for _, indicator := range lfiIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // LFI特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映（可能表示文件包含成功）
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// checkRFIResponse 检查RFI响应
func (d *FileInclusionDetector) checkRFIResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含远程文件内容
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示RFI尝试
	}

	// 检查RFI模式匹配
	for _, pattern := range d.rfiPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查错误模式匹配
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查成功模式匹配
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查RFI特定指示器
	rfiIndicators := []string{
		"http://", "https://", "ftp://", "file://", "data://", "php://",
		"remote file", "url include", "url fopen", "allow_url_include",
		"allow_url_fopen", "curl error", "connection refused", "connection timeout",
		"host not found", "dns resolution failed", "network unreachable",
		"evil.com", "attacker.com", "malicious.com", "exploit.com", "hack.com",
		"shell", "backdoor", "webshell", "cmd", "eval", "system", "exec",
		"远程文件", "网络错误", "连接被拒绝", "连接超时", "主机未找到", "DNS解析失败",
		"网络不可达", "恶意文件", "后门", "木马", "命令执行", "代码执行",
	}

	for _, indicator := range rfiIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // RFI特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// checkPathTraversalResponse 检查路径遍历响应
func (d *FileInclusionDetector) checkPathTraversalResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含遍历的文件内容
	} else if strings.Contains(response, "status: 403") {
		confidence += 0.2 // 禁止访问可能表示路径遍历尝试
	} else if strings.Contains(response, "status: 404") {
		confidence += 0.1 // 文件未找到可能表示路径遍历尝试
	}

	// 检查路径遍历特定指示器
	pathTraversalIndicators := []string{
		"../", "..\\", "%2e%2e/", "%2e%2e\\", "%2e%2e%2f", "%2e%2e%5c",
		"directory traversal", "path traversal", "file not found", "access denied",
		"permission denied", "forbidden", "unauthorized", "invalid path",
		"illegal path", "path not allowed", "directory not allowed",
		"路径遍历", "目录遍历", "文件未找到", "访问被拒绝", "权限被拒绝",
		"禁止访问", "未授权", "无效路径", "非法路径", "路径不允许", "目录不允许",
	}

	for _, indicator := range pathTraversalIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 路径遍历特定指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// checkWrapperResponse 检查包装器响应
func (d *FileInclusionDetector) checkWrapperResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含包装器内容
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示包装器尝试
	}

	// 检查包装器特定指示器
	wrapperIndicators := []string{
		"php://", "data://", "compress.zlib://", "compress.bzip2://", "expect://",
		"php://filter", "php://input", "php://memory", "php://temp", "php://output",
		"base64", "convert.base64-encode", "convert.base64-decode", "phpinfo()",
		"system(", "exec(", "shell_exec(", "passthru(", "eval(", "assert(",
		"file_get_contents(", "readfile(", "fopen(", "fread(", "fgets(",
		"wrapper", "stream", "filter", "protocol", "scheme", "resource",
		"包装器", "流", "过滤器", "协议", "方案", "资源", "编码", "解码",
		"文件内容", "读取文件", "打开文件", "文件流", "数据流", "输入流",
	}

	for _, indicator := range wrapperIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // 包装器特定指示器的强指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// extractFileEvidence 提取文件包含证据
func (d *FileInclusionDetector) extractFileEvidence(response, fileType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据文件包含类型提取相关证据
	switch fileType {
	case "lfi":
		return d.extractLFIEvidence(response)
	case "rfi":
		return d.extractRFIEvidence(response)
	case "path-traversal":
		return d.extractPathTraversalEvidence(response)
	case "php-wrapper":
		return d.extractWrapperEvidence(response)
	default:
		return response
	}
}

// extractLFIEvidence 提取LFI证据
func (d *FileInclusionDetector) extractLFIEvidence(response string) string {
	evidence := "本地文件包含证据:\n"

	// 查找LFI特定内容
	lfiIndicators := []string{
		"root:x:", "daemon:x:", "bin:x:", "www-data:x:",
		"[extensions]", "[fonts]", "[mci extensions]",
		"<?php", "<?=", "<%", "function", "class",
		"warning:", "error:", "notice:", "fatal error:",
	}

	for _, indicator := range lfiIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现LFI指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractRFIEvidence 提取RFI证据
func (d *FileInclusionDetector) extractRFIEvidence(response string) string {
	evidence := "远程文件包含证据:\n"

	// 查找RFI特定内容
	rfiIndicators := []string{
		"http://", "https://", "ftp://", "data://", "php://",
		"evil.com", "attacker.com", "shell", "backdoor", "webshell",
		"curl error", "connection refused", "host not found",
	}

	for _, indicator := range rfiIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现RFI指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractPathTraversalEvidence 提取路径遍历证据
func (d *FileInclusionDetector) extractPathTraversalEvidence(response string) string {
	evidence := "路径遍历证据:\n"

	// 查找路径遍历特定内容
	pathIndicators := []string{
		"../", "..\\", "%2e%2e/", "%2e%2e\\",
		"directory traversal", "path traversal", "access denied",
		"permission denied", "file not found", "invalid path",
	}

	for _, indicator := range pathIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现路径遍历指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractWrapperEvidence 提取包装器证据
func (d *FileInclusionDetector) extractWrapperEvidence(response string) string {
	evidence := "PHP包装器证据:\n"

	// 查找包装器特定内容
	wrapperIndicators := []string{
		"php://", "data://", "compress.zlib://", "expect://",
		"base64", "convert.base64-encode", "phpinfo()", "system(",
		"wrapper", "stream", "filter", "protocol",
	}

	for _, indicator := range wrapperIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现包装器指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
