package detectors

import (
	"fmt"
	"net/http"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// JWTVulnerabilityDetector JWT漏洞检测器
// 专门检测JWT相关的安全漏洞，包括算法混淆、密钥泄露、签名绕过等
type JWTVulnerabilityDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	commonSecrets     []string         // 常见密钥
	weakSecrets       []string         // 弱密钥
	algorithmTests    []AlgorithmTest  // 算法测试
	jwtPatterns       []*regexp.Regexp // JWT模式匹配
	vulnerableHeaders []string         // 易受攻击的头部
	httpClient        *http.Client
}

// AlgorithmTest 算法测试结构
type AlgorithmTest struct {
	Name        string `json:"name"`        // 测试名称
	Algorithm   string `json:"algorithm"`   // 算法类型
	Description string `json:"description"` // 测试描述
	Payload     string `json:"payload"`     // 测试载荷
	Expected    string `json:"expected"`    // 期望结果
}

// JWTHeader JWT头部结构
type JWTHeader struct {
	Algorithm string `json:"alg"`
	Type      string `json:"typ"`
	KeyID     string `json:"kid,omitempty"`
}

// JWTPayload JWT载荷结构
type JWTPayload struct {
	Subject   string      `json:"sub,omitempty"`
	Issuer    string      `json:"iss,omitempty"`
	Audience  interface{} `json:"aud,omitempty"`
	ExpiresAt int64       `json:"exp,omitempty"`
	NotBefore int64       `json:"nbf,omitempty"`
	IssuedAt  int64       `json:"iat,omitempty"`
	JWTID     string      `json:"jti,omitempty"`
	Role      string      `json:"role,omitempty"`
	Admin     bool        `json:"admin,omitempty"`
	UserID    interface{} `json:"user_id,omitempty"`
}

// NewJWTVulnerabilityDetector 创建JWT漏洞检测器
func NewJWTVulnerabilityDetector() *JWTVulnerabilityDetector {
	detector := &JWTVulnerabilityDetector{
		id:          "jwt-vulnerability-detector",
		name:        "JWT漏洞检测器",
		category:    "authentication",
		severity:    "high",
		cve:         []string{"CVE-2018-0114", "CVE-2019-7644"}, // JWT相关CVE
		cwe:         []string{"CWE-287", "CWE-347", "CWE-352"},  // 认证绕过、签名验证不当、CSRF
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测JWT相关的安全漏洞，包括算法混淆、密钥泄露、签名绕过、权限提升等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         20 * time.Second,
			MaxRetries:      3,
			Concurrency:     2,
			RateLimit:       5,
			FollowRedirects: true,
			VerifySSL:       false,
			MaxResponseSize: 512 * 1024, // 512KB
		},
		httpClient: &http.Client{
			Timeout: 20 * time.Second,
		},
	}

	// 初始化检测数据
	detector.initializeSecrets()
	detector.initializeAlgorithmTests()
	detector.initializePatterns()
	detector.initializeVulnerableHeaders()

	return detector
}

// initializeSecrets 初始化常见密钥
func (d *JWTVulnerabilityDetector) initializeSecrets() {
	// 常见的JWT密钥
	d.commonSecrets = []string{
		"secret",
		"jwt_secret",
		"your-256-bit-secret",
		"your-secret-key",
		"mysecretkey",
		"secretkey",
		"key",
		"password",
		"123456",
		"admin",
		"test",
		"demo",
		"default",
		"changeme",
		"qwerty",
		"abc123",
		"",
	}

	// 弱密钥（短密钥）
	d.weakSecrets = []string{
		"a", "b", "c", "1", "2", "3",
		"aa", "bb", "cc", "11", "22", "33",
		"aaa", "bbb", "ccc", "111", "222", "333",
		"test", "key", "jwt", "api",
	}
}

// initializeAlgorithmTests 初始化算法测试
func (d *JWTVulnerabilityDetector) initializeAlgorithmTests() {
	d.algorithmTests = []AlgorithmTest{
		{
			Name:        "None算法绕过",
			Algorithm:   "none",
			Description: "测试是否接受none算法的JWT token",
			Payload:     "",
			Expected:    "bypass",
		},
		{
			Name:        "算法混淆攻击",
			Algorithm:   "HS256",
			Description: "将RS256算法改为HS256进行算法混淆攻击",
			Payload:     "",
			Expected:    "confusion",
		},
		{
			Name:        "空签名绕过",
			Algorithm:   "HS256",
			Description: "测试空签名是否被接受",
			Payload:     "",
			Expected:    "empty_signature",
		},
		{
			Name:        "弱密钥爆破",
			Algorithm:   "HS256",
			Description: "使用常见弱密钥尝试签名验证",
			Payload:     "",
			Expected:    "weak_key",
		},
	}
}

// initializePatterns 初始化JWT模式
func (d *JWTVulnerabilityDetector) initializePatterns() {
	patterns := []string{
		// JWT Token模式
		`eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*`,
		// Authorization头中的Bearer token
		`Bearer\s+eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*`,
		// Cookie中的JWT
		`jwt=eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*`,
		`token=eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*`,
		// URL参数中的JWT
		`[?&]jwt=eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*`,
		`[?&]token=eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*`,
	}

	d.jwtPatterns = make([]*regexp.Regexp, len(patterns))
	for i, pattern := range patterns {
		d.jwtPatterns[i] = regexp.MustCompile(pattern)
	}
}

// initializeVulnerableHeaders 初始化易受攻击的头部
func (d *JWTVulnerabilityDetector) initializeVulnerableHeaders() {
	d.vulnerableHeaders = []string{
		"Authorization",
		"X-Auth-Token",
		"X-Access-Token",
		"X-JWT-Token",
		"Authentication",
		"Token",
		"Bearer",
		"JWT",
	}
}

// GetID 获取检测器ID
func (d *JWTVulnerabilityDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *JWTVulnerabilityDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *JWTVulnerabilityDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *JWTVulnerabilityDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *JWTVulnerabilityDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *JWTVulnerabilityDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *JWTVulnerabilityDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *JWTVulnerabilityDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *JWTVulnerabilityDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *JWTVulnerabilityDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *JWTVulnerabilityDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// GetTargetTypes 获取支持的目标类型
func (d *JWTVulnerabilityDetector) GetTargetTypes() []string {
	return []string{"http", "https"}
}

// GetRequiredPorts 获取需要的端口
func (d *JWTVulnerabilityDetector) GetRequiredPorts() []int {
	return []int{80, 443, 3000, 4000, 8000, 8080, 8443}
}

// GetRequiredServices 获取需要的服务
func (d *JWTVulnerabilityDetector) GetRequiredServices() []string {
	return []string{"http", "https", "api"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *JWTVulnerabilityDetector) GetRequiredHeaders() []string {
	return []string{}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *JWTVulnerabilityDetector) GetRequiredTechnologies() []string {
	return []string{"jwt", "oauth", "api", "rest", "json"}
}

// GetDependencies 获取依赖的其他检测器
func (d *JWTVulnerabilityDetector) GetDependencies() []string {
	return []string{}
}

// GetConfiguration 获取检测器配置
func (d *JWTVulnerabilityDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置检测器配置
func (d *JWTVulnerabilityDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// IsEnabled 检查是否启用
func (d *JWTVulnerabilityDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *JWTVulnerabilityDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// Validate 验证检测器
func (d *JWTVulnerabilityDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if d.config == nil {
		return fmt.Errorf("检测器配置不能为空")
	}
	return nil
}

// Initialize 初始化检测器
func (d *JWTVulnerabilityDetector) Initialize() error {
	// 验证配置
	if err := d.Validate(); err != nil {
		return err
	}

	// 更新HTTP客户端配置
	if d.config.Timeout > 0 {
		d.httpClient.Timeout = d.config.Timeout
	}

	return nil
}

// Cleanup 清理资源
func (d *JWTVulnerabilityDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}
