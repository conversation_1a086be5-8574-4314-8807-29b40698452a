package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// AuthorizationDetector 授权检测器
// 支持访问控制绕过、权限提升、水平/垂直权限绕过、RBAC缺陷、API访问控制等多种授权检测
type AuthorizationDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	adminPaths        []string         // 管理员路径
	userPaths         []string         // 用户路径模板
	apiEndpoints      []string         // API端点
	roleNames         []string         // 角色名称
	permissionNames   []string         // 权限名称
	bypassPayloads    []string         // 绕过载荷
	authPatterns      []*regexp.Regexp // 认证模式
	accessPatterns    []*regexp.Regexp // 访问模式
	privilegePatterns []*regexp.Regexp // 权限模式
	httpClient        *http.Client
}

// NewAuthorizationDetector 创建授权检测器
func NewAuthorizationDetector() *AuthorizationDetector {
	detector := &AuthorizationDetector{
		id:          "authorization-comprehensive",
		name:        "授权漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // 授权是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-285", "CWE-862", "CWE-863", "CWE-269", "CWE-284"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测授权漏洞，包括访问控制绕过、权限提升、水平/垂直权限绕过、RBAC缺陷、API访问控制等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // 授权检测需要较长时间
		MaxRetries:      2,
		Concurrency:     2,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 512 * 1024, // 512KB，授权响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeAdminPaths()
	detector.initializeUserPaths()
	detector.initializeAPIEndpoints()
	detector.initializeRoleNames()
	detector.initializePermissionNames()
	detector.initializeBypassPayloads()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *AuthorizationDetector) GetID() string                     { return d.id }
func (d *AuthorizationDetector) GetName() string                   { return d.name }
func (d *AuthorizationDetector) GetCategory() string               { return d.category }
func (d *AuthorizationDetector) GetSeverity() string               { return d.severity }
func (d *AuthorizationDetector) GetCVE() []string                  { return d.cve }
func (d *AuthorizationDetector) GetCWE() []string                  { return d.cwe }
func (d *AuthorizationDetector) GetVersion() string                { return d.version }
func (d *AuthorizationDetector) GetAuthor() string                 { return d.author }
func (d *AuthorizationDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *AuthorizationDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *AuthorizationDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *AuthorizationDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *AuthorizationDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *AuthorizationDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *AuthorizationDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *AuthorizationDetector) GetDependencies() []string         { return []string{} }
func (d *AuthorizationDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *AuthorizationDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *AuthorizationDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *AuthorizationDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *AuthorizationDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 2
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *AuthorizationDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *AuthorizationDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.adminPaths) == 0 {
		return fmt.Errorf("管理员路径列表不能为空")
	}
	if len(d.authPatterns) == 0 {
		return fmt.Errorf("认证模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *AuthorizationDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 授权检测适用于有认证功能的Web应用
	// 检查是否有认证相关的特征
	if d.hasAuthorizationFeatures(target) {
		return true
	}

	// 对于管理、API、用户相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	authKeywords := []string{
		"admin", "administrator", "manage", "management", "control",
		"api", "user", "profile", "account", "dashboard", "panel",
		"auth", "login", "signin", "member", "customer",
		"管理", "用户", "账户", "仪表板", "面板", "认证", "登录",
	}

	for _, keyword := range authKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return false
}

// hasAuthorizationFeatures 检查是否有授权功能
func (d *AuthorizationDetector) hasAuthorizationFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有授权相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "authorization" ||
			keyLower == "x-api-key" ||
			keyLower == "x-auth-token" ||
			strings.Contains(valueLower, "bearer") ||
			strings.Contains(valueLower, "basic") ||
			strings.Contains(valueLower, "token") {
			return true
		}
	}

	// 检查Cookie中是否有授权相关信息
	for _, cookie := range target.Cookies {
		cookieNameLower := strings.ToLower(cookie.Name)

		authCookies := []string{
			"auth", "token", "session", "login", "user",
			"admin", "role", "permission", "access",
			"认证", "令牌", "会话", "登录", "用户", "管理", "角色", "权限",
		}

		for _, authCookie := range authCookies {
			if strings.Contains(cookieNameLower, authCookie) {
				return true
			}
		}
	}

	// 检查表单中是否有授权相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			authFields := []string{
				"username", "password", "email", "login", "auth",
				"token", "role", "permission", "access", "admin",
				"用户名", "密码", "邮箱", "登录", "认证", "令牌", "角色", "权限",
			}

			for _, authField := range authFields {
				if strings.Contains(fieldNameLower, authField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *AuthorizationDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种授权检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 垂直权限提升检测
	verticalEvidence, verticalConfidence, verticalPayload, verticalRequest, verticalResponse := d.detectVerticalPrivilegeEscalation(ctx, target)
	if verticalConfidence > maxConfidence {
		maxConfidence = verticalConfidence
		vulnerablePayload = verticalPayload
		vulnerableRequest = verticalRequest
		vulnerableResponse = verticalResponse
	}
	evidence = append(evidence, verticalEvidence...)

	// 2. 水平权限提升检测
	horizontalEvidence, horizontalConfidence, horizontalPayload, horizontalRequest, horizontalResponse := d.detectHorizontalPrivilegeEscalation(ctx, target)
	if horizontalConfidence > maxConfidence {
		maxConfidence = horizontalConfidence
		vulnerablePayload = horizontalPayload
		vulnerableRequest = horizontalRequest
		vulnerableResponse = horizontalResponse
	}
	evidence = append(evidence, horizontalEvidence...)

	// 3. API访问控制检测
	apiEvidence, apiConfidence, apiPayload, apiRequest, apiResponse := d.detectAPIAccessControl(ctx, target)
	if apiConfidence > maxConfidence {
		maxConfidence = apiConfidence
		vulnerablePayload = apiPayload
		vulnerableRequest = apiRequest
		vulnerableResponse = apiResponse
	}
	evidence = append(evidence, apiEvidence...)

	// 4. RBAC缺陷检测
	rbacEvidence, rbacConfidence, rbacPayload, rbacRequest, rbacResponse := d.detectRBACFlaws(ctx, target)
	if rbacConfidence > maxConfidence {
		maxConfidence = rbacConfidence
		vulnerablePayload = rbacPayload
		vulnerableRequest = rbacRequest
		vulnerableResponse = rbacResponse
	}
	evidence = append(evidence, rbacEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "授权漏洞",
		Description:       "检测到授权漏洞，可能导致访问控制绕过、权限提升或其他授权安全问题",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施严格的访问控制，包括基于角色的访问控制(RBAC)、权限矩阵验证、API访问控制和最小权限原则",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Authorization", "https://cwe.mitre.org/data/definitions/285.html"},
		Tags:              []string{"authorization", "access-control", "privilege-escalation", "rbac", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *AuthorizationDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationPaths := []string{
		"/admin",
		"/api/admin",
		"/management",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, path := range verificationPaths {
		// 构造验证URL
		verifyURL := d.buildAuthorizationURL(target.URL, path)

		// 发送验证请求
		resp, err := d.sendAuthorizationRequest(ctx, verifyURL)
		if err != nil {
			continue
		}

		// 检查授权响应特征
		responseConfidence := d.checkAuthorizationResponse(resp, path)
		if responseConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "response",
				Description: fmt.Sprintf("验证路径确认了授权问题"),
				Content:     resp,
				Location:    verifyURL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "authorization-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用授权验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *AuthorizationDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("authorization_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *AuthorizationDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (授权通常是高风险)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// initializeAdminPaths 初始化管理员路径列表
func (d *AuthorizationDetector) initializeAdminPaths() {
	d.adminPaths = []string{
		// 基础管理路径
		"/admin",
		"/administrator",
		"/manage",
		"/management",
		"/control",
		"/panel",
		"/dashboard",
		"/console",

		// 用户管理
		"/admin/users",
		"/admin/accounts",
		"/admin/members",
		"/manage/users",
		"/user/admin",
		"/users/admin",

		// 系统管理
		"/admin/settings",
		"/admin/config",
		"/admin/configuration",
		"/admin/system",
		"/admin/logs",
		"/admin/audit",
		"/settings/admin",
		"/config/admin",

		// API管理
		"/api/admin",
		"/api/management",
		"/admin/api",
		"/api/v1/admin",
		"/api/v2/admin",
		"/v1/admin",
		"/v2/admin",

		// 特定应用管理
		"/wp-admin",
		"/wp-admin/",
		"/phpmyadmin",
		"/phpmyadmin/",
		"/cpanel",
		"/cpanel/",
		"/webmin",
		"/plesk",
		"/directadmin",

		// 数据库管理
		"/admin/database",
		"/admin/db",
		"/database/admin",
		"/db/admin",
		"/mysql/admin",
		"/postgres/admin",

		// 文件管理
		"/admin/files",
		"/admin/filemanager",
		"/filemanager/admin",
		"/files/admin",

		// 监控管理
		"/admin/monitor",
		"/admin/monitoring",
		"/monitor/admin",
		"/monitoring/admin",
		"/admin/stats",
		"/stats/admin",

		// 安全管理
		"/admin/security",
		"/security/admin",
		"/admin/permissions",
		"/permissions/admin",
		"/admin/roles",
		"/roles/admin",

		// 中文管理路径
		"/管理",
		"/管理员",
		"/后台",
		"/控制台",
		"/仪表板",
	}
}

// initializeUserPaths 初始化用户路径模板列表
func (d *AuthorizationDetector) initializeUserPaths() {
	d.userPaths = []string{
		// 基础用户路径
		"/user/{id}",
		"/users/{id}",
		"/profile/{id}",
		"/profiles/{id}",
		"/account/{id}",
		"/accounts/{id}",
		"/member/{id}",
		"/members/{id}",
		"/customer/{id}",
		"/customers/{id}",

		// API用户路径
		"/api/user/{id}",
		"/api/users/{id}",
		"/api/profile/{id}",
		"/api/account/{id}",
		"/api/member/{id}",
		"/v1/users/{id}",
		"/v2/users/{id}",
		"/v1/user/{id}",
		"/v2/user/{id}",

		// 用户操作路径
		"/user/{id}/edit",
		"/user/{id}/delete",
		"/user/{id}/settings",
		"/user/{id}/profile",
		"/user/{id}/account",
		"/users/{id}/edit",
		"/users/{id}/delete",
		"/users/{id}/settings",

		// 中文用户路径
		"/用户/{id}",
		"/用户/{id}/编辑",
		"/用户/{id}/删除",
		"/用户/{id}/设置",
	}
}

// initializeAPIEndpoints 初始化API端点列表
func (d *AuthorizationDetector) initializeAPIEndpoints() {
	d.apiEndpoints = []string{
		// 基础API端点
		"/api",
		"/api/",
		"/api/v1",
		"/api/v2",
		"/api/v3",
		"/v1",
		"/v2",
		"/v3",

		// 用户API
		"/api/users",
		"/api/user",
		"/api/accounts",
		"/api/account",
		"/api/profiles",
		"/api/profile",
		"/api/members",
		"/api/member",

		// 管理API
		"/api/admin",
		"/api/management",
		"/api/system",
		"/api/config",
		"/api/settings",

		// 认证API
		"/api/auth",
		"/api/login",
		"/api/token",
		"/api/oauth",
		"/api/session",

		// 数据API
		"/api/data",
		"/api/database",
		"/api/files",
		"/api/upload",
		"/api/download",

		// GraphQL API
		"/graphql",
		"/api/graphql",
		"/v1/graphql",
		"/v2/graphql",

		// REST API
		"/rest",
		"/api/rest",
		"/restapi",

		// 中文API
		"/接口",
		"/api/用户",
		"/api/管理",
	}
}

// initializeRoleNames 初始化角色名称列表
func (d *AuthorizationDetector) initializeRoleNames() {
	d.roleNames = []string{
		// 基础角色
		"admin",
		"administrator",
		"root",
		"superuser",
		"super",
		"manager",
		"moderator",
		"operator",
		"editor",
		"author",
		"contributor",
		"user",
		"guest",
		"anonymous",

		// 系统角色
		"system",
		"service",
		"daemon",
		"process",
		"application",
		"api",

		// 业务角色
		"owner",
		"member",
		"customer",
		"client",
		"partner",
		"vendor",
		"supplier",
		"employee",
		"staff",
		"developer",
		"tester",
		"analyst",

		// 权限级别
		"read",
		"write",
		"execute",
		"delete",
		"create",
		"update",
		"view",
		"edit",
		"publish",
		"approve",

		// 中文角色
		"管理员",
		"超级用户",
		"管理者",
		"编辑者",
		"作者",
		"用户",
		"访客",
		"员工",
		"开发者",
	}
}

// initializePermissionNames 初始化权限名称列表
func (d *AuthorizationDetector) initializePermissionNames() {
	d.permissionNames = []string{
		// 基础权限
		"read",
		"write",
		"execute",
		"delete",
		"create",
		"update",
		"view",
		"edit",
		"modify",
		"access",
		"manage",
		"control",
		"admin",
		"administrate",

		// 用户权限
		"user.read",
		"user.write",
		"user.create",
		"user.delete",
		"user.update",
		"user.manage",
		"users.admin",

		// 系统权限
		"system.read",
		"system.write",
		"system.admin",
		"system.config",
		"system.manage",
		"config.read",
		"config.write",
		"settings.read",
		"settings.write",

		// 文件权限
		"file.read",
		"file.write",
		"file.upload",
		"file.download",
		"file.delete",
		"files.manage",

		// API权限
		"api.read",
		"api.write",
		"api.admin",
		"api.manage",
		"api.access",

		// 数据库权限
		"db.read",
		"db.write",
		"db.admin",
		"database.manage",

		// 中文权限
		"读取",
		"写入",
		"执行",
		"删除",
		"创建",
		"更新",
		"查看",
		"编辑",
		"管理",
		"控制",
	}
}

// initializeBypassPayloads 初始化绕过载荷列表
func (d *AuthorizationDetector) initializeBypassPayloads() {
	d.bypassPayloads = []string{
		// HTTP方法绕过
		"OPTIONS",
		"HEAD",
		"TRACE",
		"CONNECT",
		"PATCH",
		"PROPFIND",
		"PROPPATCH",
		"MKCOL",
		"COPY",
		"MOVE",
		"LOCK",
		"UNLOCK",

		// 路径绕过
		"../admin",
		"./admin",
		"/./admin",
		"/../admin",
		"//admin",
		"/admin/",
		"/admin//",
		"/admin/../admin",
		"/admin/./",

		// 编码绕过
		"%2e%2e%2fadmin",
		"%2e%2e%5cadmin",
		"%252e%252e%252fadmin",
		"..%2fadmin",
		"..%5cadmin",
		"%2e%2e/admin",
		"%2e%2e\\admin",

		// 大小写绕过
		"/Admin",
		"/ADMIN",
		"/aDmIn",
		"/AdMiN",
		"/admin",
		"/Admin/",
		"/ADMIN/",

		// 参数绕过
		"/admin?bypass=true",
		"/admin?admin=true",
		"/admin?role=admin",
		"/admin?user=admin",
		"/admin?auth=bypass",
		"/admin?access=true",

		// 头部绕过
		"X-Original-URL: /admin",
		"X-Rewrite-URL: /admin",
		"X-Forwarded-For: 127.0.0.1",
		"X-Real-IP: 127.0.0.1",
		"X-Remote-IP: 127.0.0.1",
		"X-Client-IP: 127.0.0.1",
		"X-Forwarded-Host: localhost",
		"X-Host: localhost",

		// 用户ID绕过
		"user_id=1",
		"user_id=0",
		"user_id=-1",
		"user_id=admin",
		"user_id=root",
		"id=1",
		"id=0",
		"id=-1",

		// 角色绕过
		"role=admin",
		"role=administrator",
		"role=root",
		"role=manager",
		"role=super",
		"user_role=admin",
		"user_type=admin",

		// 权限绕过
		"permission=admin",
		"permissions=all",
		"access=admin",
		"level=admin",
		"privilege=admin",

		// 中文绕过
		"角色=管理员",
		"权限=管理",
		"用户=管理员",
	}
}

// initializePatterns 初始化检测模式
func (d *AuthorizationDetector) initializePatterns() {
	// 认证模式 - 检测认证相关的响应内容
	authPatternStrings := []string{
		// 认证成功
		`(?i)(welcome|dashboard|logged\s*in|authenticated|authorized)`,
		`(?i)(access\s*granted|login\s*successful|auth\s*success)`,
		`(?i)(admin\s*panel|management\s*console|control\s*panel)`,
		`(?i)(user\s*dashboard|member\s*area|profile\s*page)`,

		// 认证失败
		`(?i)(access\s*denied|unauthorized|forbidden|not\s*authorized)`,
		`(?i)(login\s*required|authentication\s*required|please\s*login)`,
		`(?i)(invalid\s*credentials|login\s*failed|auth\s*failed)`,
		`(?i)(permission\s*denied|insufficient\s*privileges)`,

		// 认证相关
		`(?i)(session|token|cookie|authorization|bearer)`,
		`(?i)(login|logout|signin|signout|authenticate)`,
		`(?i)(username|password|email|credentials)`,
		`(?i)(role|permission|privilege|access|admin)`,

		// 中文认证模式
		`(?i)(欢迎|仪表板|已登录|已认证|已授权)`,
		`(?i)(访问被拒绝|未授权|禁止访问|需要登录)`,
		`(?i)(登录|注销|认证|授权|权限|管理)`,
	}

	d.authPatterns = make([]*regexp.Regexp, 0, len(authPatternStrings))
	for _, pattern := range authPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.authPatterns = append(d.authPatterns, compiled)
		}
	}

	// 访问模式 - 检测访问控制相关的响应内容
	accessPatternStrings := []string{
		// 访问控制
		`(?i)(access\s*control|access\s*denied|access\s*granted)`,
		`(?i)(permission\s*denied|permission\s*granted|insufficient\s*permission)`,
		`(?i)(authorization\s*failed|authorization\s*required)`,
		`(?i)(forbidden|not\s*allowed|restricted\s*access)`,

		// 管理访问
		`(?i)(admin\s*access|administrator\s*access|management\s*access)`,
		`(?i)(admin\s*panel|admin\s*console|admin\s*dashboard)`,
		`(?i)(control\s*panel|management\s*console|system\s*admin)`,

		// 用户访问
		`(?i)(user\s*access|member\s*access|customer\s*access)`,
		`(?i)(profile\s*access|account\s*access|personal\s*area)`,

		// API访问
		`(?i)(api\s*access|api\s*key|api\s*token|api\s*auth)`,
		`(?i)(endpoint\s*access|resource\s*access|service\s*access)`,

		// 中文访问模式
		`(?i)(访问控制|访问被拒绝|访问授权|权限不足)`,
		`(?i)(管理访问|用户访问|接口访问|资源访问)`,
	}

	d.accessPatterns = make([]*regexp.Regexp, 0, len(accessPatternStrings))
	for _, pattern := range accessPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.accessPatterns = append(d.accessPatterns, compiled)
		}
	}

	// 权限模式 - 检测权限相关的响应内容
	privilegePatternStrings := []string{
		// 权限提升
		`(?i)(privilege\s*escalation|privilege\s*elevation|elevated\s*privileges)`,
		`(?i)(admin\s*privileges|root\s*privileges|super\s*privileges)`,
		`(?i)(escalated\s*access|elevated\s*access|higher\s*privileges)`,

		// 权限检查
		`(?i)(privilege\s*check|permission\s*check|access\s*check)`,
		`(?i)(role\s*check|authorization\s*check|auth\s*check)`,
		`(?i)(insufficient\s*privileges|missing\s*privileges)`,

		// 角色权限
		`(?i)(role\s*based|rbac|role\s*permission|user\s*role)`,
		`(?i)(admin\s*role|manager\s*role|user\s*role|guest\s*role)`,
		`(?i)(role\s*assignment|role\s*management|permission\s*management)`,

		// 权限绕过
		`(?i)(bypass\s*authorization|bypass\s*access|bypass\s*permission)`,
		`(?i)(unauthorized\s*access|privilege\s*bypass|access\s*bypass)`,
		`(?i)(permission\s*bypass|role\s*bypass|auth\s*bypass)`,

		// 中文权限模式
		`(?i)(权限提升|权限检查|角色权限|权限绕过)`,
		`(?i)(管理权限|用户权限|访问权限|系统权限)`,
	}

	d.privilegePatterns = make([]*regexp.Regexp, 0, len(privilegePatternStrings))
	for _, pattern := range privilegePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.privilegePatterns = append(d.privilegePatterns, compiled)
		}
	}
}
