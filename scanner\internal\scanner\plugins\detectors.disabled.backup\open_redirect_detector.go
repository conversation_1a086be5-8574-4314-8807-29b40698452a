package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// OpenRedirectDetector 开放重定向检测器
// 支持开放重定向检测，包括参数重定向、头部重定向、JavaScript重定向等多种开放重定向攻击技术
type OpenRedirectDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	parameterPayloads  []string         // 参数重定向载荷
	headerPayloads     []string         // 头部重定向载荷
	javascriptPayloads []string         // JavaScript重定向载荷
	protocolPayloads   []string         // 协议重定向载荷
	encodingPayloads   []string         // 编码重定向载荷
	testParameters     []string         // 测试参数
	redirectPatterns   []*regexp.Regexp // 重定向模式
	javascriptPatterns []*regexp.Regexp // JavaScript模式
	responsePatterns   []*regexp.Regexp // 响应模式
	httpClient         *http.Client
}

// NewOpenRedirectDetector 创建开放重定向检测器
func NewOpenRedirectDetector() *OpenRedirectDetector {
	detector := &OpenRedirectDetector{
		id:          "open-redirect-comprehensive",
		name:        "开放重定向漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{}, // 开放重定向是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-601", "CWE-79", "CWE-20"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测开放重定向漏洞，包括参数重定向、头部重定向、JavaScript重定向等多种开放重定向攻击技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         10 * time.Second, // 开放重定向检测需要较短时间
		MaxRetries:      2,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: false, // 不跟随重定向，检查重定向响应
		VerifySSL:       false,
		MaxResponseSize: 512 * 1024, // 512KB，重定向响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 不跟随重定向，直接返回错误以获取重定向响应
			return http.ErrUseLastResponse
		},
	}

	// 初始化检测数据
	detector.initializeParameterPayloads()
	detector.initializeHeaderPayloads()
	detector.initializeJavascriptPayloads()
	detector.initializeProtocolPayloads()
	detector.initializeEncodingPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *OpenRedirectDetector) GetID() string                     { return d.id }
func (d *OpenRedirectDetector) GetName() string                   { return d.name }
func (d *OpenRedirectDetector) GetCategory() string               { return d.category }
func (d *OpenRedirectDetector) GetSeverity() string               { return d.severity }
func (d *OpenRedirectDetector) GetCVE() []string                  { return d.cve }
func (d *OpenRedirectDetector) GetCWE() []string                  { return d.cwe }
func (d *OpenRedirectDetector) GetVersion() string                { return d.version }
func (d *OpenRedirectDetector) GetAuthor() string                 { return d.author }
func (d *OpenRedirectDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *OpenRedirectDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *OpenRedirectDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *OpenRedirectDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *OpenRedirectDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *OpenRedirectDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *OpenRedirectDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *OpenRedirectDetector) GetDependencies() []string         { return []string{} }
func (d *OpenRedirectDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *OpenRedirectDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *OpenRedirectDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *OpenRedirectDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *OpenRedirectDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 10 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *OpenRedirectDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *OpenRedirectDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.parameterPayloads) == 0 {
		return fmt.Errorf("参数重定向载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *OpenRedirectDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 开放重定向检测适用于有重定向功能的Web应用
	// 检查是否有重定向相关的特征
	if d.hasRedirectFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于登录、注销、跳转相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	redirectKeywords := []string{
		"login", "logout", "signin", "signout", "auth", "redirect", "return",
		"next", "continue", "goto", "jump", "forward", "back", "url",
		"登录", "注销", "跳转", "重定向", "返回", "继续", "前往",
	}

	for _, keyword := range redirectKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 开放重定向是通用Web漏洞，默认适用于所有Web目标
}

// hasRedirectFeatures 检查是否有重定向功能
func (d *OpenRedirectDetector) hasRedirectFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有重定向相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "location" ||
			keyLower == "refresh" ||
			strings.Contains(valueLower, "redirect") ||
			strings.Contains(valueLower, "location") {
			return true
		}
	}

	// 检查链接中是否有重定向相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "redirect") ||
			strings.Contains(linkURLLower, "goto") ||
			strings.Contains(linkURLLower, "jump") ||
			strings.Contains(linkTextLower, "redirect") ||
			strings.Contains(linkTextLower, "跳转") ||
			strings.Contains(linkTextLower, "重定向") {
			return true
		}
	}

	// 检查表单中是否有重定向相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			redirectFields := []string{
				"redirect", "return", "next", "continue", "goto", "jump",
				"url", "link", "target", "destination", "forward", "back",
				"重定向", "跳转", "返回", "继续", "前往", "目标",
			}

			for _, redirectField := range redirectFields {
				if strings.Contains(fieldNameLower, redirectField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *OpenRedirectDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种开放重定向检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 参数重定向检测
	paramEvidence, paramConfidence, paramPayload, paramRequest, paramResponse := d.detectParameterRedirect(ctx, target)
	if paramConfidence > maxConfidence {
		maxConfidence = paramConfidence
		vulnerablePayload = paramPayload
		vulnerableRequest = paramRequest
		vulnerableResponse = paramResponse
	}
	evidence = append(evidence, paramEvidence...)

	// 2. 头部重定向检测
	headerEvidence, headerConfidence, headerPayload, headerRequest, headerResponse := d.detectHeaderRedirect(ctx, target)
	if headerConfidence > maxConfidence {
		maxConfidence = headerConfidence
		vulnerablePayload = headerPayload
		vulnerableRequest = headerRequest
		vulnerableResponse = headerResponse
	}
	evidence = append(evidence, headerEvidence...)

	// 3. JavaScript重定向检测
	jsEvidence, jsConfidence, jsPayload, jsRequest, jsResponse := d.detectJavascriptRedirect(ctx, target)
	if jsConfidence > maxConfidence {
		maxConfidence = jsConfidence
		vulnerablePayload = jsPayload
		vulnerableRequest = jsRequest
		vulnerableResponse = jsResponse
	}
	evidence = append(evidence, jsEvidence...)

	// 4. 协议重定向检测
	protocolEvidence, protocolConfidence, protocolPayload, protocolRequest, protocolResponse := d.detectProtocolRedirect(ctx, target)
	if protocolConfidence > maxConfidence {
		maxConfidence = protocolConfidence
		vulnerablePayload = protocolPayload
		vulnerableRequest = protocolRequest
		vulnerableResponse = protocolResponse
	}
	evidence = append(evidence, protocolEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "开放重定向漏洞",
		Description:       "检测到开放重定向漏洞，攻击者可能通过恶意重定向实现钓鱼攻击或其他恶意行为",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对重定向URL进行严格验证和过滤，使用白名单机制，避免直接使用用户输入作为重定向目标",
		References:        []string{"https://owasp.org/www-community/attacks/Unvalidated_Redirects_and_Forwards", "https://cwe.mitre.org/data/definitions/601.html"},
		Tags:              []string{"redirect", "phishing", "web", "medium"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *OpenRedirectDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"parameter-redirect",
		"header-redirect",
		"javascript-redirect",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyRedirectMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了开放重定向漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "redirect-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用重定向验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *OpenRedirectDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("open_redirect_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *OpenRedirectDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (开放重定向通常是中等风险漏洞)
	baseScore := 6.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyRedirectMethod 验证重定向方法
func (d *OpenRedirectDetector) verifyRedirectMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "parameter-redirect":
		return d.verifyParameterRedirect(ctx, target)
	case "header-redirect":
		return d.verifyHeaderRedirect(ctx, target)
	case "javascript-redirect":
		return d.verifyJavascriptRedirect(ctx, target)
	default:
		return 0.0
	}
}

// verifyParameterRedirect 验证参数重定向
func (d *OpenRedirectDetector) verifyParameterRedirect(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的参数重定向验证
	if d.hasRedirectFeatures(target) {
		return 0.7 // 有重定向特征的目标可能有参数重定向
	}
	return 0.3
}

// verifyHeaderRedirect 验证头部重定向
func (d *OpenRedirectDetector) verifyHeaderRedirect(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的头部重定向验证
	if d.hasRedirectFeatures(target) {
		return 0.6 // 有重定向特征的目标可能有头部重定向
	}
	return 0.2
}

// verifyJavascriptRedirect 验证JavaScript重定向
func (d *OpenRedirectDetector) verifyJavascriptRedirect(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的JavaScript重定向验证
	if d.hasRedirectFeatures(target) {
		return 0.5 // 有重定向特征的目标可能有JavaScript重定向
	}
	return 0.1
}

// initializeParameterPayloads 初始化参数重定向载荷列表
func (d *OpenRedirectDetector) initializeParameterPayloads() {
	d.parameterPayloads = []string{
		// 基础HTTP/HTTPS重定向
		"http://evil.com",
		"https://evil.com",
		"http://evil.com/",
		"https://evil.com/",
		"http://evil.com/malicious",
		"https://evil.com/malicious",

		// 协议相对重定向
		"//evil.com",
		"///evil.com",
		"////evil.com",
		"//evil.com/",
		"///evil.com/",
		"////evil.com/",
		"//evil.com/malicious",

		// 域名混淆
		"http://<EMAIL>",
		"https://<EMAIL>",
		"http://legitimate.com.evil.com",
		"https://legitimate.com.evil.com",
		"http://evil.com#legitimate.com",
		"https://evil.com#legitimate.com",
		"http://evil.com?legitimate.com",
		"https://evil.com?legitimate.com",

		// 子域名攻击
		"http://sub.evil.com",
		"https://sub.evil.com",
		"http://www.evil.com",
		"https://www.evil.com",
		"http://mail.evil.com",
		"https://mail.evil.com",

		// IP地址重定向
		"http://*************",
		"https://*************",
		"http://127.0.0.1",
		"https://127.0.0.1",
		"http://********",
		"https://********",
		"http://**********",
		"https://**********",

		// 端口变化
		"http://evil.com:8080",
		"https://evil.com:8443",
		"http://evil.com:80",
		"https://evil.com:443",
		"http://evil.com:3000",
		"https://evil.com:3000",

		// 路径遍历
		"../../../evil.com",
		"..\\..\\..\\evil.com",
		"/evil.com",
		"\\evil.com",
		"./evil.com",
		".\\evil.com",

		// 编码绕过
		"http%3A%2F%2Fevil.com",
		"https%3A%2F%2Fevil.com",
		"http%3a%2f%2fevil.com",
		"https%3a%2f%2fevil.com",
		"http://evil%2ecom",
		"https://evil%2ecom",

		// 中文域名
		"http://恶意.com",
		"https://恶意.com",
		"http://钓鱼.com",
		"https://钓鱼.com",
	}
}

// initializeHeaderPayloads 初始化头部重定向载荷列表
func (d *OpenRedirectDetector) initializeHeaderPayloads() {
	d.headerPayloads = []string{
		// Location头部重定向
		"Location: http://evil.com",
		"Location: https://evil.com",
		"Location: //evil.com",
		"Location: ///evil.com",
		"Location: http://evil.com/malicious",

		// Refresh头部重定向
		"Refresh: 0; url=http://evil.com",
		"Refresh: 0; url=https://evil.com",
		"Refresh: 0; url=//evil.com",
		"Refresh: 1; url=http://evil.com",
		"Refresh: 5; url=https://evil.com",

		// 其他重定向头部
		"X-Redirect: http://evil.com",
		"X-Redirect: https://evil.com",
		"X-Location: http://evil.com",
		"X-Location: https://evil.com",
		"Redirect-To: http://evil.com",
		"Redirect-To: https://evil.com",

		// 中文头部
		"Location: http://恶意.com",
		"Refresh: 0; url=http://钓鱼.com",
	}
}

// initializeJavascriptPayloads 初始化JavaScript重定向载荷列表
func (d *OpenRedirectDetector) initializeJavascriptPayloads() {
	d.javascriptPayloads = []string{
		// JavaScript协议
		"javascript:location.href='http://evil.com'",
		"javascript:location='https://evil.com'",
		"javascript:window.location='http://evil.com'",
		"javascript:window.location.href='https://evil.com'",
		"javascript:document.location='http://evil.com'",
		"javascript:document.location.href='https://evil.com'",

		// JavaScript函数调用
		"javascript:redirect('http://evil.com')",
		"javascript:goto('https://evil.com')",
		"javascript:jump('http://evil.com')",
		"javascript:forward('https://evil.com')",

		// JavaScript表达式
		"javascript:alert('XSS')",
		"javascript:confirm('XSS')",
		"javascript:prompt('XSS')",
		"javascript:eval('location=\"http://evil.com\"')",

		// Data URI
		"data:text/html,<script>location='http://evil.com'</script>",
		"data:text/html,<script>window.location='https://evil.com'</script>",
		"data:text/html,<meta http-equiv=\"refresh\" content=\"0;url=http://evil.com\">",
		"data:text/html,<iframe src=\"http://evil.com\"></iframe>",

		// VBScript (IE)
		"vbscript:location.href=\"http://evil.com\"",
		"vbscript:window.location=\"https://evil.com\"",

		// 中文JavaScript
		"javascript:location.href='http://恶意.com'",
		"javascript:alert('恶意代码')",
	}
}

// initializeProtocolPayloads 初始化协议重定向载荷列表
func (d *OpenRedirectDetector) initializeProtocolPayloads() {
	d.protocolPayloads = []string{
		// 文件协议
		"file:///etc/passwd",
		"file:///c:/windows/system32/drivers/etc/hosts",
		"file://evil.com/malicious",
		"file:///tmp/malicious",

		// FTP协议
		"ftp://evil.com",
		"ftp://evil.com/malicious",
		"ftp://user:<EMAIL>",
		"ftps://evil.com",

		// 邮件协议
		"mailto:<EMAIL>",
		"mailto:<EMAIL>?subject=Phishing",
		"mailto:<EMAIL>?body=Malicious",

		// 其他协议
		"tel:+1234567890",
		"sms:+1234567890",
		"skype:evil.user",
		"steam://evil.com",
		"discord://evil.com",

		// 自定义协议
		"custom://evil.com",
		"app://evil.com",
		"scheme://evil.com",

		// 中文协议
		"mailto:管理员@恶意.com",
		"file:///恶意文件",
	}
}

// initializeEncodingPayloads 初始化编码重定向载荷列表
func (d *OpenRedirectDetector) initializeEncodingPayloads() {
	d.encodingPayloads = []string{
		// URL编码
		"http%3A%2F%2Fevil.com",
		"https%3A%2F%2Fevil.com",
		"http%3a%2f%2fevil.com",
		"https%3a%2f%2fevil.com",
		"%2F%2Fevil.com",
		"%2f%2fevil.com",

		// 双重编码
		"http%253A%252F%252Fevil.com",
		"https%253A%252F%252Fevil.com",
		"%252F%252Fevil.com",

		// Unicode编码
		"http://evil\u002ecom",
		"https://evil\u002ecom",
		"http://evil\u0002ecom",
		"https://evil\u0002ecom",

		// HTML实体编码
		"http://evil&#46;com",
		"https://evil&#46;com",
		"http://evil&#x2e;com",
		"https://evil&#x2e;com",

		// 十六进制编码
		"http://evil%2ecom",
		"https://evil%2ecom",
		"http://evil%2Ecom",
		"https://evil%2Ecom",

		// Base64编码
		"aHR0cDovL2V2aWwuY29t",     // http://evil.com
		"aHR0cHM6Ly9ldmlsLmNvbQ==", // https://evil.com

		// 混合编码
		"http%3A//evil%2ecom",
		"https%3A//evil%2ecom",
		"//evil%2ecom",
		"///evil%2ecom",

		// 中文编码
		"http://恶意%2ecom",
		"https://钓鱼%2ecom",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *OpenRedirectDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 重定向相关参数
		"redirect", "redirect_to", "redirect_url", "redirectTo", "redirectURL",
		"return", "return_to", "return_url", "returnTo", "returnURL",
		"next", "next_page", "next_url", "nextPage", "nextURL",
		"continue", "continue_to", "continue_url", "continueTo", "continueURL",
		"goto", "go_to", "go_url", "goTo", "goURL",
		"jump", "jump_to", "jump_url", "jumpTo", "jumpURL",
		"forward", "forward_to", "forward_url", "forwardTo", "forwardURL",
		"back", "back_to", "back_url", "backTo", "backURL",

		// URL相关参数
		"url", "URL", "uri", "URI", "link", "href", "src",
		"target", "destination", "dest", "location", "loc",
		"site", "domain", "host", "server", "endpoint",
		"path", "route", "page", "view", "action",

		// 登录相关参数
		"login_redirect", "logout_redirect", "auth_redirect",
		"success_url", "failure_url", "error_url",
		"callback", "callback_url", "callbackURL",
		"success_redirect", "error_redirect",

		// 通用参数
		"r", "u", "l", "p", "t", "d", "s", "c", "n", "g",
		"ref", "referer", "referrer", "from", "source",
		"to", "target_url", "targetURL", "target_page",

		// 框架特定参数
		"_redirect", "_return", "_next", "_continue",
		"redirect_uri", "return_uri", "next_uri",
		"success_uri", "failure_uri", "error_uri",

		// 中文参数
		"重定向", "跳转", "返回", "继续", "前往", "目标",
		"链接", "地址", "页面", "路径", "位置",
	}
}

// initializePatterns 初始化检测模式
func (d *OpenRedirectDetector) initializePatterns() {
	// 重定向模式 - 检测重定向相关的响应内容
	redirectPatternStrings := []string{
		// HTTP重定向状态码
		`(?i)(30[1-8]|redirect)`,
		`(?i)(moved|found|see.*other|temporary|permanent)`,
		`(?i)(location.*:|refresh.*:)`,

		// Location头部
		`(?i)location\s*:\s*(https?://|//|/)`,
		`(?i)location\s*=\s*['"](https?://|//|/)`,
		`(?i)location\s*=\s*(https?://|//|/)`,

		// Refresh头部
		`(?i)refresh\s*:\s*\d+\s*;\s*url\s*=\s*(https?://|//|/)`,
		`(?i)refresh\s*=\s*['"]\d+\s*;\s*url\s*=\s*(https?://|//|/)`,
		`(?i)content\s*=\s*['"]\d+\s*;\s*url\s*=\s*(https?://|//|/)`,

		// Meta重定向
		`(?i)<meta.*http-equiv\s*=\s*['"]*refresh['"]*.*url\s*=\s*(https?://|//|/)`,
		`(?i)<meta.*refresh.*url\s*=\s*(https?://|//|/)`,

		// 其他重定向头部
		`(?i)(x-redirect|x-location|redirect-to)\s*:\s*(https?://|//|/)`,

		// 中文重定向
		`(?i)(重定向|跳转|转向|跳转到)`,
		`(?i)(页面.*跳转|自动.*跳转|即将.*跳转)`,
	}

	d.redirectPatterns = make([]*regexp.Regexp, 0, len(redirectPatternStrings))
	for _, pattern := range redirectPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.redirectPatterns = append(d.redirectPatterns, compiled)
		}
	}

	// JavaScript模式 - 检测JavaScript重定向相关的响应内容
	javascriptPatternStrings := []string{
		// JavaScript重定向
		`(?i)location\s*=\s*['"](https?://|//|/)`,
		`(?i)location\.href\s*=\s*['"](https?://|//|/)`,
		`(?i)window\.location\s*=\s*['"](https?://|//|/)`,
		`(?i)window\.location\.href\s*=\s*['"](https?://|//|/)`,
		`(?i)document\.location\s*=\s*['"](https?://|//|/)`,
		`(?i)document\.location\.href\s*=\s*['"](https?://|//|/)`,

		// JavaScript函数
		`(?i)(redirect|goto|jump|forward)\s*\(\s*['"](https?://|//|/)`,
		`(?i)window\.open\s*\(\s*['"](https?://|//|/)`,
		`(?i)location\.replace\s*\(\s*['"](https?://|//|/)`,
		`(?i)location\.assign\s*\(\s*['"](https?://|//|/)`,

		// JavaScript协议
		`(?i)javascript\s*:\s*(location|window\.location|document\.location)`,
		`(?i)javascript\s*:\s*(alert|confirm|prompt|eval)`,

		// Data URI
		`(?i)data\s*:\s*text/html`,
		`(?i)data\s*:\s*application/javascript`,

		// VBScript
		`(?i)vbscript\s*:\s*location`,

		// 中文JavaScript
		`(?i)(位置|地址|跳转|重定向)\s*=`,
		`(?i)(页面|窗口)\.location`,
	}

	d.javascriptPatterns = make([]*regexp.Regexp, 0, len(javascriptPatternStrings))
	for _, pattern := range javascriptPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.javascriptPatterns = append(d.javascriptPatterns, compiled)
		}
	}

	// 响应模式 - 检测成功重定向相关的响应内容
	responsePatternStrings := []string{
		// 重定向成功指示器
		`(?i)(redirect.*success|redirect.*complete|redirect.*ok)`,
		`(?i)(jump.*success|jump.*complete|jump.*ok)`,
		`(?i)(forward.*success|forward.*complete|forward.*ok)`,

		// 页面跳转指示器
		`(?i)(page.*redirect|page.*jump|page.*forward)`,
		`(?i)(auto.*redirect|auto.*jump|auto.*forward)`,
		`(?i)(will.*redirect|will.*jump|will.*forward)`,

		// 时间延迟指示器
		`(?i)(redirect.*in.*\d+|jump.*in.*\d+|forward.*in.*\d+)`,
		`(?i)(wait.*\d+.*redirect|wait.*\d+.*jump|wait.*\d+.*forward)`,

		// 目标URL指示器
		`(?i)(redirect.*to|jump.*to|forward.*to|go.*to)`,
		`(?i)(target.*url|destination.*url|target.*page)`,

		// 错误重定向指示器
		`(?i)(error.*redirect|error.*jump|error.*forward)`,
		`(?i)(invalid.*redirect|invalid.*jump|invalid.*forward)`,

		// 中文响应
		`(?i)(重定向.*成功|跳转.*成功|转向.*成功)`,
		`(?i)(页面.*跳转|自动.*跳转|即将.*跳转)`,
		`(?i)(重定向.*到|跳转.*到|转向.*到)`,
		`(?i)(目标.*页面|目标.*地址|目标.*链接)`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
