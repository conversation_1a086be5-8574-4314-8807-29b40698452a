package detectors

import (
	"fmt"
	"net/http"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// GraphQLInjectionDetector GraphQL注入检测器
// 专门检测GraphQL API中的注入漏洞，包括查询注入、变异注入、内省攻击等
type GraphQLInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	queryPayloads      []GraphQLPayload // GraphQL查询载荷
	mutationPayloads   []GraphQLPayload // GraphQL变异载荷
	introspectionTests []string         // 内省查询测试
	errorPatterns      []*regexp.Regexp // 错误模式匹配
	httpClient         *http.Client
}

// GraphQLPayload GraphQL载荷结构
type GraphQLPayload struct {
	Query     string                 `json:"query"`
	Variables map[string]interface{} `json:"variables,omitempty"`
	Type      string                 // 载荷类型：injection, introspection, mutation, etc.
	Purpose   string                 // 载荷目的描述
}

// NewGraphQLInjectionDetector 创建GraphQL注入检测器
func NewGraphQLInjectionDetector() *GraphQLInjectionDetector {
	detector := &GraphQLInjectionDetector{
		id:          "graphql-injection-detector",
		name:        "GraphQL注入检测器",
		category:    "injection",
		severity:    "high",
		cve:         []string{},                    // GraphQL注入通常没有特定CVE
		cwe:         []string{"CWE-89", "CWE-943"}, // SQL注入和NoSQL注入相关
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测GraphQL API中的注入漏洞，包括查询注入、变异注入、内省攻击等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         30 * time.Second,
			MaxRetries:      3,
			Concurrency:     1,
			RateLimit:       10,
			FollowRedirects: true,
			VerifySSL:       false,
			MaxResponseSize: 1024 * 1024, // 1MB
		},
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 初始化载荷和模式
	detector.initializePayloads()
	detector.initializeErrorPatterns()

	return detector
}

// initializePayloads 初始化GraphQL载荷
func (d *GraphQLInjectionDetector) initializePayloads() {
	// GraphQL查询注入载荷
	d.queryPayloads = []GraphQLPayload{
		// 基础注入测试
		{
			Query:   `query { __typename }`,
			Type:    "basic",
			Purpose: "基础GraphQL查询测试",
		},
		{
			Query:   `query { user(id: "1' OR '1'='1") { id name } }`,
			Type:    "injection",
			Purpose: "SQL注入风格的GraphQL查询",
		},
		{
			Query:   `query { user(id: "1\"; DROP TABLE users; --") { id name } }`,
			Type:    "injection",
			Purpose: "SQL注入破坏性查询",
		},
		// NoSQL注入风格
		{
			Query:   `query { user(filter: {$ne: null}) { id name } }`,
			Type:    "nosql_injection",
			Purpose: "NoSQL注入风格查询",
		},
		// 深度查询攻击（GraphQL Bomb）
		{
			Query:   `query { user { posts { comments { replies { replies { replies { id } } } } } } }`,
			Type:    "depth_attack",
			Purpose: "深度查询攻击测试",
		},
		// 批量查询攻击
		{
			Query:   `query { a1: user(id: "1") { id } a2: user(id: "2") { id } a3: user(id: "3") { id } }`,
			Type:    "batch_attack",
			Purpose: "批量查询攻击测试",
		},
	}

	// GraphQL变异载荷
	d.mutationPayloads = []GraphQLPayload{
		{
			Query:   `mutation { createUser(input: {name: "test", email: "<EMAIL>"}) { id } }`,
			Type:    "mutation_basic",
			Purpose: "基础变异操作测试",
		},
		{
			Query:   `mutation { deleteUser(id: "1' OR '1'='1") { success } }`,
			Type:    "mutation_injection",
			Purpose: "变异操作中的注入测试",
		},
	}

	// GraphQL内省查询
	d.introspectionTests = []string{
		`query { __schema { types { name } } }`,
		`query { __type(name: "User") { fields { name type { name } } } }`,
		`query IntrospectionQuery { __schema { queryType { name } mutationType { name } subscriptionType { name } types { ...FullType } directives { name description locations args { ...InputValue } } } } fragment FullType on __Type { kind name description fields(includeDeprecated: true) { name description args { ...InputValue } type { ...TypeRef } isDeprecated deprecationReason } inputFields { ...InputValue } interfaces { ...TypeRef } enumValues(includeDeprecated: true) { name description isDeprecated deprecationReason } possibleTypes { ...TypeRef } } fragment InputValue on __InputValue { name description type { ...TypeRef } defaultValue } fragment TypeRef on __Type { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name ofType { kind name } } } } } } } }`,
	}
}

// initializeErrorPatterns 初始化错误模式
func (d *GraphQLInjectionDetector) initializeErrorPatterns() {
	patterns := []string{
		// GraphQL特定错误
		`(?i)graphql.*error`,
		`(?i)syntax.*error.*graphql`,
		`(?i)cannot.*query.*field`,
		`(?i)unknown.*directive`,
		`(?i)validation.*error`,

		// 数据库错误（可能通过GraphQL暴露）
		`(?i)sql.*syntax.*error`,
		`(?i)mysql.*error`,
		`(?i)postgresql.*error`,
		`(?i)mongodb.*error`,
		`(?i)redis.*error`,

		// 应用程序错误
		`(?i)internal.*server.*error`,
		`(?i)stack.*trace`,
		`(?i)exception.*occurred`,
		`(?i)null.*pointer.*exception`,
	}

	d.errorPatterns = make([]*regexp.Regexp, len(patterns))
	for i, pattern := range patterns {
		d.errorPatterns[i] = regexp.MustCompile(pattern)
	}
}

// GetID 获取检测器ID
func (d *GraphQLInjectionDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *GraphQLInjectionDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *GraphQLInjectionDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *GraphQLInjectionDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *GraphQLInjectionDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *GraphQLInjectionDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *GraphQLInjectionDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *GraphQLInjectionDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *GraphQLInjectionDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *GraphQLInjectionDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *GraphQLInjectionDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// GetTargetTypes 获取支持的目标类型
func (d *GraphQLInjectionDetector) GetTargetTypes() []string {
	return []string{"http", "https"}
}

// GetRequiredPorts 获取需要的端口
func (d *GraphQLInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 3000, 4000, 8000, 8080, 8443}
}

// GetRequiredServices 获取需要的服务
func (d *GraphQLInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "graphql"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *GraphQLInjectionDetector) GetRequiredHeaders() []string {
	return []string{}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *GraphQLInjectionDetector) GetRequiredTechnologies() []string {
	return []string{"graphql", "apollo", "relay"}
}

// GetDependencies 获取依赖的其他检测器
func (d *GraphQLInjectionDetector) GetDependencies() []string {
	return []string{}
}

// GetConfiguration 获取检测器配置
func (d *GraphQLInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置检测器配置
func (d *GraphQLInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// IsEnabled 检查是否启用
func (d *GraphQLInjectionDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *GraphQLInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// Validate 验证检测器
func (d *GraphQLInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if d.config == nil {
		return fmt.Errorf("检测器配置不能为空")
	}
	return nil
}

// Initialize 初始化检测器
func (d *GraphQLInjectionDetector) Initialize() error {
	// 验证配置
	if err := d.Validate(); err != nil {
		return err
	}

	// 更新HTTP客户端配置
	if d.config.Timeout > 0 {
		d.httpClient.Timeout = d.config.Timeout
	}

	return nil
}

// Cleanup 清理资源
func (d *GraphQLInjectionDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}
