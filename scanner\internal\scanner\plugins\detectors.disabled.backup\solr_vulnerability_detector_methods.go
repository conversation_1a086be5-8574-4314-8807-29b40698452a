package detectors

import (
	"context"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// detectSolrService 检测Solr服务
func (d *SolrVulnerabilityDetector) detectSolrService(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测Solr服务...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 测试Solr路径
	for _, path := range d.solrPaths {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.buildSolrURL(target.URL, path)

		// 发送请求
		resp, err := d.sendSolrRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查Solr服务响应
		confidence := d.checkSolrResponse(resp, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "solr_service",
				Description: fmt.Sprintf("Solr服务检测 (置信度: %.2f)", confidence),
				Content:     d.extractSolrEvidence(resp, path),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJMXExposure 检测JMX端口暴露
func (d *SolrVulnerabilityDetector) detectJMXExposure(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测JMX端口暴露...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 解析目标URL获取主机
	u, err := url.Parse(target.URL)
	if err != nil {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	host := u.Hostname()
	if host == "" {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 测试JMX端口
	for _, port := range d.jmxPorts {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 检测端口开放
		if d.isPortOpen(host, port) {
			confidence := 0.6
			jmxURL := fmt.Sprintf("service:jmx:rmi:///jndi/rmi://%s:%d/jmxrmi", host, port)

			// 尝试JMX连接测试
			if d.testJMXConnection(host, port) {
				confidence = 0.8
			}

			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = jmxURL
				vulnerableRequest = fmt.Sprintf("JMX连接测试: %s:%d", host, port)
				vulnerableResponse = fmt.Sprintf("JMX端口 %d 开放", port)
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "jmx_exposure",
				Description: fmt.Sprintf("JMX端口暴露检测 (端口: %d, 置信度: %.2f)", port, confidence),
				Content:     fmt.Sprintf("JMX服务URL: %s", jmxURL),
				Location:    fmt.Sprintf("%s:%d", host, port),
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCVE201912409 检测CVE-2019-12409漏洞
func (d *SolrVulnerabilityDetector) detectCVE201912409(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	logger.Debugf("检测CVE-2019-12409漏洞...")

	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// CVE-2019-12409特定测试
	cveTests := []struct {
		path        string
		payload     string
		description string
	}{
		{
			path:        "/solr/admin/cores",
			payload:     "action=CREATE&name=test&instanceDir=../../../&dataDir=data&config=solrconfig.xml&schema=schema.xml",
			description: "Solr Core创建测试",
		},
		{
			path:        "/solr/admin/info/system",
			payload:     "wt=json&indent=true",
			description: "Solr系统信息访问",
		},
		{
			path:        "/solr/admin/collections",
			payload:     "action=LIST&wt=json",
			description: "Solr集合列表访问",
		},
	}

	for _, test := range cveTests {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造测试URL
		testURL := d.buildSolrURL(target.URL, test.path)
		if test.payload != "" {
			testURL += "?" + test.payload
		}

		// 发送请求
		resp, err := d.sendSolrRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查CVE-2019-12409特征
		confidence := d.checkCVE201912409Response(resp, test.path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = test.payload
			vulnerableRequest = testURL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "cve_2019_12409",
				Description: fmt.Sprintf("%s (置信度: %.2f)", test.description, confidence),
				Content:     d.extractCVEEvidence(resp, test.path),
				Location:    testURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// buildSolrURL 构造Solr测试URL
func (d *SolrVulnerabilityDetector) buildSolrURL(baseURL, path string) string {
	u, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	// 确保路径以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	u.Path = path
	return u.String()
}

// sendSolrRequest 发送Solr请求
func (d *SolrVulnerabilityDetector) sendSolrRequest(ctx context.Context, testURL string) (string, error) {
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	return string(body), nil
}

// checkSolrResponse 检查Solr服务响应
func (d *SolrVulnerabilityDetector) checkSolrResponse(response, path string) float64 {
	if response == "" {
		return 0.0
	}

	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查Solr指示器
	for _, indicator := range d.solrIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			confidence += 0.2
			logger.Debugf("发现Solr指示器: %s", indicator)
		}
	}

	// 检查Solr响应模式
	for _, pattern := range d.solrPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			logger.Debugf("匹配Solr响应模式: %s", pattern.String())
		}
	}

	// 检查特定路径的响应特征
	if strings.Contains(path, "admin") && strings.Contains(responseLower, "solr") {
		confidence += 0.2
	}

	if strings.Contains(path, "system") && (strings.Contains(responseLower, "lucene") || strings.Contains(responseLower, "java")) {
		confidence += 0.2
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkCVE201912409Response 检查CVE-2019-12409响应特征
func (d *SolrVulnerabilityDetector) checkCVE201912409Response(response, path string) float64 {
	if response == "" {
		return 0.0
	}

	confidence := 0.0
	responseLower := strings.ToLower(response)

	// 检查管理界面访问
	if strings.Contains(path, "admin") {
		if strings.Contains(responseLower, "solr") && strings.Contains(responseLower, "admin") {
			confidence += 0.4
		}
	}

	// 检查Core操作能力
	if strings.Contains(path, "cores") {
		if strings.Contains(responseLower, "status") || strings.Contains(responseLower, "instancedir") {
			confidence += 0.3
		}
	}

	// 检查系统信息泄露
	if strings.Contains(path, "system") {
		if strings.Contains(responseLower, "java.version") || strings.Contains(responseLower, "solr.home") {
			confidence += 0.3
		}
	}

	// 检查JMX相关信息
	for _, indicator := range d.jmxIndicators {
		if strings.Contains(responseLower, strings.ToLower(indicator)) {
			confidence += 0.2
			break
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// isPortOpen 检查端口是否开放
func (d *SolrVulnerabilityDetector) isPortOpen(host string, port int) bool {
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// testJMXConnection 测试JMX连接
func (d *SolrVulnerabilityDetector) testJMXConnection(host string, port int) bool {
	// 简化的JMX连接测试
	// 实际实现中可以使用JMX客户端库进行更详细的测试
	timeout := 5 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()

	// 发送简单的探测数据
	_, err = conn.Write([]byte("JRMI\x00\x02K"))
	if err != nil {
		return false
	}

	// 读取响应
	buffer := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := conn.Read(buffer)
	if err != nil {
		return false
	}

	response := string(buffer[:n])
	return strings.Contains(response, "java.rmi") || len(response) > 0
}

// extractSolrEvidence 提取Solr证据
func (d *SolrVulnerabilityDetector) extractSolrEvidence(response, path string) string {
	evidence := fmt.Sprintf("路径: %s\n", path)

	// 提取关键信息
	lines := strings.Split(response, "\n")
	var evidenceLines []string

	for _, line := range lines {
		lineLower := strings.ToLower(strings.TrimSpace(line))
		if len(lineLower) > 0 {
			// 检查是否包含Solr特征
			for _, indicator := range d.solrIndicators {
				if strings.Contains(lineLower, strings.ToLower(indicator)) {
					evidenceLines = append(evidenceLines, strings.TrimSpace(line))
					break
				}
			}
			if len(evidenceLines) >= 5 {
				break
			}
		}
	}

	if len(evidenceLines) > 0 {
		evidence += "关键证据:\n" + strings.Join(evidenceLines, "\n")
	} else {
		// 如果没有找到特定证据，提取前几行
		if len(lines) > 3 {
			evidence += "响应内容:\n" + strings.Join(lines[:3], "\n")
		} else {
			evidence += "响应内容:\n" + response
		}
	}

	return evidence
}

// extractCVEEvidence 提取CVE证据
func (d *SolrVulnerabilityDetector) extractCVEEvidence(response, path string) string {
	evidence := fmt.Sprintf("CVE-2019-12409检测路径: %s\n", path)

	// 提取CVE相关证据
	if strings.Contains(strings.ToLower(response), "solr") {
		evidence += "发现Solr服务特征\n"
	}

	if strings.Contains(strings.ToLower(response), "admin") {
		evidence += "管理界面可访问\n"
	}

	if strings.Contains(strings.ToLower(response), "jmx") || strings.Contains(strings.ToLower(response), "mbean") {
		evidence += "发现JMX相关信息\n"
	}

	// 截取响应的前200个字符作为证据
	if len(response) > 200 {
		evidence += "响应摘要:\n" + response[:200] + "..."
	} else {
		evidence += "响应内容:\n" + response
	}

	return evidence
}

// verifySolrService 验证Solr服务
func (d *SolrVulnerabilityDetector) verifySolrService(ctx context.Context, target *plugins.ScanTarget) bool {
	// 验证核心Solr路径
	verificationPaths := []string{
		"/solr/admin/info/system",
		"/solr/admin/cores",
		"/solr/",
	}

	for _, path := range verificationPaths {
		testURL := d.buildSolrURL(target.URL, path)
		resp, err := d.sendSolrRequest(ctx, testURL)
		if err != nil {
			continue
		}

		if d.checkSolrResponse(resp, path) > 0.5 {
			return true
		}
	}

	return false
}

// verifyJMXExposure 验证JMX暴露
func (d *SolrVulnerabilityDetector) verifyJMXExposure(ctx context.Context, target *plugins.ScanTarget) bool {
	u, err := url.Parse(target.URL)
	if err != nil {
		return false
	}

	host := u.Hostname()
	if host == "" {
		return false
	}

	// 验证关键JMX端口
	keyPorts := []int{8983, 9999, 1099}
	for _, port := range keyPorts {
		if d.isPortOpen(host, port) && d.testJMXConnection(host, port) {
			return true
		}
	}

	return false
}

// verifyExploitability 验证漏洞可利用性
func (d *SolrVulnerabilityDetector) verifyExploitability(ctx context.Context, target *plugins.ScanTarget) bool {
	// 安全的可利用性测试
	testPaths := []string{
		"/solr/admin/cores?action=STATUS&wt=json",
		"/solr/admin/info/system?wt=json",
	}

	for _, path := range testPaths {
		testURL := d.buildSolrURL(target.URL, path)
		resp, err := d.sendSolrRequest(ctx, testURL)
		if err != nil {
			continue
		}

		// 检查是否能够访问管理功能
		responseLower := strings.ToLower(resp)
		if strings.Contains(responseLower, "status") &&
			(strings.Contains(responseLower, "solr") || strings.Contains(responseLower, "core")) {
			return true
		}
	}

	return false
}
