package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// DataValidationDetector 数据验证检测器
// 支持输入验证绕过、数据类型混淆、边界值测试、格式验证绕过等多种数据验证漏洞检测
type DataValidationDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	boundaryValuePayloads []string         // 边界值测试载荷
	typeConfusionPayloads []string         // 类型混淆载荷
	formatBypassPayloads  []string         // 格式验证绕过载荷
	lengthBypassPayloads  []string         // 长度验证绕过载荷
	testParameters        []string         // 测试参数
	validationPatterns    []*regexp.Regexp // 数据验证模式
	errorPatterns         []*regexp.Regexp // 错误响应模式
	successPatterns       []*regexp.Regexp // 成功响应模式
	bypassPatterns        []*regexp.Regexp // 绕过检测模式
	httpClient            *http.Client
}

// NewDataValidationDetector 创建数据验证检测器
func NewDataValidationDetector() *DataValidationDetector {
	detector := &DataValidationDetector{
		id:          "data-validation-comprehensive",
		name:        "数据验证漏洞综合检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-19781", "CVE-2018-13379"},
		cwe:         []string{"CWE-20", "CWE-79", "CWE-89", "CWE-94", "CWE-190", "CWE-200"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测数据验证漏洞，包括输入验证绕过、数据类型混淆、边界值测试、格式验证绕过等多种数据验证安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // 数据验证检测需要适中时间
		MaxRetries:      2,                // 数据验证检测可以重试
		Concurrency:     6,                // 中等并发数
		RateLimit:       6,                // 中等速率限制
		FollowRedirects: true,             // 跟随重定向检查数据验证
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，数据验证响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeBoundaryValuePayloads()
	detector.initializeTypeConfusionPayloads()
	detector.initializeFormatBypassPayloads()
	detector.initializeLengthBypassPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *DataValidationDetector) GetID() string            { return d.id }
func (d *DataValidationDetector) GetName() string          { return d.name }
func (d *DataValidationDetector) GetCategory() string      { return d.category }
func (d *DataValidationDetector) GetSeverity() string      { return d.severity }
func (d *DataValidationDetector) GetCVE() []string         { return d.cve }
func (d *DataValidationDetector) GetCWE() []string         { return d.cwe }
func (d *DataValidationDetector) GetVersion() string       { return d.version }
func (d *DataValidationDetector) GetAuthor() string        { return d.author }
func (d *DataValidationDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *DataValidationDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *DataValidationDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *DataValidationDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *DataValidationDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *DataValidationDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *DataValidationDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *DataValidationDetector) GetDependencies() []string         { return []string{} }
func (d *DataValidationDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *DataValidationDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *DataValidationDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *DataValidationDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *DataValidationDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 6
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *DataValidationDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *DataValidationDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.boundaryValuePayloads) == 0 {
		return fmt.Errorf("边界值载荷不能为空")
	}
	if len(d.typeConfusionPayloads) == 0 {
		return fmt.Errorf("类型混淆载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *DataValidationDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 数据验证检测适用于有输入功能的Web应用
	// 检查是否有数据验证相关的特征
	if d.hasDataValidationFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于数据验证相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	validationKeywords := []string{
		"form", "input", "submit", "register", "login", "search",
		"contact", "feedback", "comment", "upload", "create", "edit",
		"api", "service", "endpoint", "data", "validate", "check",
		"表单", "输入", "提交", "注册", "登录", "搜索",
		"联系", "反馈", "评论", "上传", "创建", "编辑",
		"接口", "服务", "数据", "验证", "检查", "校验",
	}

	for _, keyword := range validationKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 数据验证是通用Web漏洞，默认适用于所有Web目标
}

// hasDataValidationFeatures 检查是否有数据验证功能
func (d *DataValidationDetector) hasDataValidationFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有数据验证相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "content-type") ||
			strings.Contains(keyLower, "accept") ||
			strings.Contains(valueLower, "form") ||
			strings.Contains(valueLower, "json") ||
			strings.Contains(valueLower, "xml") ||
			strings.Contains(valueLower, "multipart") {
			return true
		}
	}

	// 检查技术栈中是否有数据验证相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		validationTechnologies := []string{
			"spring", "django", "rails", "express", "laravel", "symfony",
			"react", "vue", "angular", "jquery", "bootstrap", "validation",
			"joi", "yup", "ajv", "validator", "sanitizer", "filter",
			"验证", "校验", "过滤", "净化", "表单", "输入",
		}

		for _, validationTech := range validationTechnologies {
			if strings.Contains(techNameLower, validationTech) {
				return true
			}
		}
	}

	// 检查链接中是否有数据验证相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "form") ||
			strings.Contains(linkURLLower, "input") ||
			strings.Contains(linkURLLower, "submit") ||
			strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkTextLower, "form") ||
			strings.Contains(linkTextLower, "input") ||
			strings.Contains(linkTextLower, "submit") ||
			strings.Contains(linkTextLower, "register") ||
			strings.Contains(linkTextLower, "login") ||
			strings.Contains(linkTextLower, "表单") ||
			strings.Contains(linkTextLower, "输入") ||
			strings.Contains(linkTextLower, "提交") ||
			strings.Contains(linkTextLower, "注册") ||
			strings.Contains(linkTextLower, "登录") {
			return true
		}
	}

	// 检查表单中是否有数据验证相关字段
	for _, form := range target.Forms {
		for fieldName, fieldType := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)
			fieldTypeLower := strings.ToLower(fieldType)

			validationFields := []string{
				"email", "phone", "age", "date", "time", "number", "url",
				"password", "confirm", "verify", "validate", "check",
				"name", "address", "zip", "postal", "credit", "card",
				"邮箱", "电话", "年龄", "日期", "时间", "数字", "网址",
				"密码", "确认", "验证", "校验", "检查", "姓名", "地址",
			}

			for _, validationField := range validationFields {
				if strings.Contains(fieldNameLower, validationField) ||
					strings.Contains(fieldTypeLower, validationField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *DataValidationDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种数据验证检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 边界值测试
	boundaryEvidence, boundaryConfidence, boundaryPayload, boundaryRequest, boundaryResponse := d.detectBoundaryValueVulnerabilities(ctx, target)
	if boundaryConfidence > maxConfidence {
		maxConfidence = boundaryConfidence
		vulnerablePayload = boundaryPayload
		vulnerableRequest = boundaryRequest
		vulnerableResponse = boundaryResponse
	}
	evidence = append(evidence, boundaryEvidence...)

	// 2. 类型混淆检测
	typeEvidence, typeConfidence, typePayload, typeRequest, typeResponse := d.detectTypeConfusionVulnerabilities(ctx, target)
	if typeConfidence > maxConfidence {
		maxConfidence = typeConfidence
		vulnerablePayload = typePayload
		vulnerableRequest = typeRequest
		vulnerableResponse = typeResponse
	}
	evidence = append(evidence, typeEvidence...)

	// 3. 格式验证绕过检测
	formatEvidence, formatConfidence, formatPayload, formatRequest, formatResponse := d.detectFormatBypassVulnerabilities(ctx, target)
	if formatConfidence > maxConfidence {
		maxConfidence = formatConfidence
		vulnerablePayload = formatPayload
		vulnerableRequest = formatRequest
		vulnerableResponse = formatResponse
	}
	evidence = append(evidence, formatEvidence...)

	// 4. 长度验证绕过检测
	lengthEvidence, lengthConfidence, lengthPayload, lengthRequest, lengthResponse := d.detectLengthBypassVulnerabilities(ctx, target)
	if lengthConfidence > maxConfidence {
		maxConfidence = lengthConfidence
		vulnerablePayload = lengthPayload
		vulnerableRequest = lengthRequest
		vulnerableResponse = lengthResponse
	}
	evidence = append(evidence, lengthEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "数据验证漏洞",
		Description:       "检测到数据验证漏洞，应用程序未能正确验证用户输入数据，可能导致安全问题",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强输入验证机制、实施严格的数据类型检查、添加边界值验证、完善格式验证逻辑",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Input_Validation", "https://cwe.mitre.org/data/definitions/20.html", "https://cwe.mitre.org/data/definitions/79.html"},
		Tags:              []string{"data", "validation", "input", "boundary", "type", "format"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *DataValidationDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"boundary-value-verification",
		"type-confusion-verification",
		"format-bypass-verification",
		"length-bypass-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyDataValidationMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了数据验证漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "data-validation-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用数据验证验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *DataValidationDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("data_validation_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *DataValidationDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (数据验证通常是中等风险漏洞)
	baseScore := 6.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyDataValidationMethod 验证数据验证方法
func (d *DataValidationDetector) verifyDataValidationMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "boundary-value-verification":
		return d.verifyBoundaryValue(ctx, target)
	case "type-confusion-verification":
		return d.verifyTypeConfusion(ctx, target)
	case "format-bypass-verification":
		return d.verifyFormatBypass(ctx, target)
	case "length-bypass-verification":
		return d.verifyLengthBypass(ctx, target)
	default:
		return 0.0
	}
}

// verifyBoundaryValue 验证边界值
func (d *DataValidationDetector) verifyBoundaryValue(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的边界值验证
	if d.hasDataValidationFeatures(target) {
		return 0.6 // 有数据验证特征的目标可能有边界值问题
	}
	return 0.2
}

// verifyTypeConfusion 验证类型混淆
func (d *DataValidationDetector) verifyTypeConfusion(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的类型混淆验证
	if d.hasDataValidationFeatures(target) {
		return 0.5 // 有数据验证特征的目标可能有类型混淆问题
	}
	return 0.2
}

// verifyFormatBypass 验证格式绕过
func (d *DataValidationDetector) verifyFormatBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的格式绕过验证
	if d.hasDataValidationFeatures(target) {
		return 0.5 // 有数据验证特征的目标可能有格式绕过问题
	}
	return 0.1
}

// verifyLengthBypass 验证长度绕过
func (d *DataValidationDetector) verifyLengthBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的长度绕过验证
	if d.hasDataValidationFeatures(target) {
		return 0.4 // 有数据验证特征的目标可能有长度绕过问题
	}
	return 0.1
}

// initializeBoundaryValuePayloads 初始化边界值测试载荷列表
func (d *DataValidationDetector) initializeBoundaryValuePayloads() {
	d.boundaryValuePayloads = []string{
		// 整数边界值
		"0", "-1", "1", "2147483647", "-2147483648", "4294967295", "-4294967296",
		"9223372036854775807", "-9223372036854775808", "18446744073709551615",
		"999999999999999999", "-999999999999999999", "1.7976931348623157e+308",

		// 浮点数边界值
		"0.0", "-0.0", "1.0", "-1.0", "3.14159", "-3.14159",
		"1.7976931348623157e+308", "2.2250738585072014e-308", "NaN", "Infinity", "-Infinity",
		"1e308", "1e-308", "1e400", "-1e400", "0.000000000000001", "-0.000000000000001",

		// 字符串长度边界值
		"", "a", strings.Repeat("A", 255), strings.Repeat("B", 256), strings.Repeat("C", 1023),
		strings.Repeat("D", 1024), strings.Repeat("E", 4095), strings.Repeat("F", 4096),
		strings.Repeat("G", 8191), strings.Repeat("H", 8192), strings.Repeat("I", 65535),
		strings.Repeat("J", 65536), strings.Repeat("K", 1048575), strings.Repeat("L", 1048576),

		// 特殊字符边界值
		"\x00", "\x01", "\x7F", "\x80", "\xFF", "\u0000", "\u0001", "\uFFFF",
		"\U00000000", "\U0010FFFF", "\r", "\n", "\r\n", "\t", "\b", "\f",

		// 数组/列表边界值
		"[]", "[0]", "[1,2,3]", strings.Repeat("[", 1000) + strings.Repeat("]", 1000),
		"[" + strings.Repeat("1,", 1000) + "1]", "[null]", "[undefined]", "[true,false]",

		// 对象边界值
		"{}", "{\"a\":1}", "{\"key\":\"value\"}", strings.Repeat("{\"a\":", 500) + "1" + strings.Repeat("}", 500),
		"{\"null\":null}", "{\"undefined\":undefined}", "{\"bool\":true}", "{\"array\":[]}",

		// 时间边界值
		"1970-01-01", "2038-01-19", "1900-01-01", "2100-12-31", "0000-00-00", "9999-12-31",
		"1970-01-01T00:00:00Z", "2038-01-19T03:14:07Z", "1900-01-01T00:00:00Z",
		"0", "2147483647", "4294967295", "-1", "253402300799", "946684800",

		// 布尔值边界值
		"true", "false", "True", "False", "TRUE", "FALSE", "1", "0", "yes", "no",
		"on", "off", "enabled", "disabled", "active", "inactive", "null", "undefined",

		// 中文边界值
		"", "中", strings.Repeat("中", 100), strings.Repeat("文", 1000), strings.Repeat("测", 10000),
		"测试", "边界值", "数据验证", "输入检查", "格式验证", "类型检查",
	}
}

// initializeTypeConfusionPayloads 初始化类型混淆载荷列表
func (d *DataValidationDetector) initializeTypeConfusionPayloads() {
	d.typeConfusionPayloads = []string{
		// 字符串与数字混淆
		"123", "'123'", "\"123\"", "123.0", "123.456", "0123", "0x123", "0b1010",
		"1e3", "1E3", "1.23e4", "1.23E-4", "+123", "-123", " 123 ", "123abc",

		// 布尔值混淆
		"true", "false", "True", "False", "TRUE", "FALSE", "1", "0", "yes", "no",
		"on", "off", "enabled", "disabled", "active", "inactive", "checked", "unchecked",

		// 空值混淆
		"null", "NULL", "Null", "nil", "NIL", "Nil", "none", "None", "NONE",
		"undefined", "UNDEFINED", "Undefined", "", "empty", "void", "blank",

		// 数组与字符串混淆
		"[]", "[1,2,3]", "['a','b','c']", "[\"x\",\"y\",\"z\"]", "1,2,3", "a,b,c",
		"[object Array]", "Array", "array", "list", "List", "LIST",

		// 对象与字符串混淆
		"{}", "{\"a\":1}", "{a:1}", "{'a':1}", "[object Object]", "Object", "object",
		"Map", "map", "HashMap", "Dictionary", "dict", "Hash", "hash",

		// 函数与字符串混淆
		"function", "Function", "FUNCTION", "function()", "() => {}", "lambda",
		"def", "method", "Method", "procedure", "Procedure", "callable",

		// 日期与字符串混淆
		"Date", "date", "DATE", "new Date()", "Date.now()", "timestamp", "time",
		"datetime", "DateTime", "DATETIME", "ISO8601", "RFC3339", "epoch",

		// 正则表达式混淆
		"/test/", "/test/i", "/test/g", "/test/ig", "RegExp", "regex", "regexp",
		"pattern", "Pattern", "PATTERN", "match", "Match", "MATCH",

		// 特殊对象混淆
		"window", "document", "console", "process", "global", "this", "self",
		"parent", "top", "frames", "location", "navigator", "history",

		// 类型转换混淆
		"parseInt", "parseFloat", "Number", "String", "Boolean", "Array", "Object",
		"toString", "valueOf", "toJSON", "JSON.parse", "JSON.stringify",

		// 中文类型混淆
		"真", "假", "空", "无", "数字", "字符串", "数组", "对象", "函数", "日期",
		"布尔", "整数", "浮点", "文本", "列表", "字典", "映射", "集合",
	}
}

// initializeFormatBypassPayloads 初始化格式验证绕过载荷列表
func (d *DataValidationDetector) initializeFormatBypassPayloads() {
	d.formatBypassPayloads = []string{
		// 邮箱格式绕过
		"test@", "@example.com", "test@@example.com", "test@.com", "test@com",
		"test@example.", "<EMAIL>", "<EMAIL>", ".<EMAIL>",
		"test@ex ample.com", "test@example.c", "<EMAIL>", "<EMAIL>",
		"<EMAIL>", "test@exam_ple.com", "test@exam+ple.com", "test@exam=ple.com",

		// 电话号码格式绕过
		"123", "12345678901234567890", "abc-def-ghij", "123-abc-4567", "123-456-abcd",
		"+1-abc-456-7890", "1-800-FLOWERS", "(123) 456-7890x", "123.456.7890.1",
		"123 456 7890 ext", "+86 138 0013 8000 1", "400-800-8888-9999",

		// URL格式绕过
		"http://", "https://", "ftp://", "file://", "javascript:", "data:",
		"http:///example.com", "http://example", "http://example.", "http://.com",
		"http://ex ample.com", "http://example..com", "http://example.com:99999",
		"http://user:<EMAIL>", "http://[::1]:8080", "http://256.256.256.256",

		// IP地址格式绕过
		"256.256.256.256", "192.168.1", "***********.1", "************", "************",
		"192.168.1.-1", "192.168.1.256", "***********/24", "***********:8080",
		"::1:8080", "2001:db8::1:8080", "fe80::1%eth0", "::ffff:***********",

		// 日期格式绕过
		"2023-13-01", "2023-12-32", "2023-02-30", "2023-00-01", "2023-01-00",
		"23-12-01", "2023/12/01", "12/32/2023", "32/12/2023", "2023.12.01",
		"2023-12-01T25:00:00", "2023-12-01T12:60:00", "2023-12-01T12:30:60",

		// 信用卡格式绕过
		"1234", "12345678901234567890", "1234-5678-9012-3456", "1234 5678 9012 3456",
		"1234567890123456a", "abcd-efgh-ijkl-mnop", "0000-0000-0000-0000",
		"1111-1111-1111-1111", "9999-9999-9999-9999", "1234-5678-9012-345",

		// 身份证格式绕过
		"12345678901234567", "1234567890123456789", "12345678901234567a",
		"abcdefghijklmnopq", "000000000000000000", "111111111111111111",
		"123456789012345678", "12345619001231234", "123456200013112345",

		// 邮政编码格式绕过
		"1234", "123456", "12345a", "abcde", "00000", "99999", "12345-678",
		"12345-6789", "12345-67890", "A1A 1A1", "A1A1A1", "A1A-1A1",

		// 密码格式绕过
		"", "a", "12", "abc", "123", "password", "Password", "PASSWORD",
		"12345678", "abcdefgh", "ABCDEFGH", "1234abcd", "1234ABCD", "abcdABCD",
		"password123", "Password123", "PASSWORD123", "p@ssw0rd", "P@ssw0rd",

		// 中文格式绕过
		"测试@", "@测试.com", "测试@@测试.com", "测试@.com", "测试@com",
		"138", "1380013800013800", "中文-电话-号码", "138-中文-8000",
		"http://测试", "http://测试.", "http://测试..com", "http://测 试.com",
		"2023年13月01日", "2023年12月32日", "2023年02月30日", "2023年00月01日",
	}
}

// initializeLengthBypassPayloads 初始化长度验证绕过载荷列表
func (d *DataValidationDetector) initializeLengthBypassPayloads() {
	d.lengthBypassPayloads = []string{
		// 超短长度测试
		"", "a", "ab", "abc", "1", "12", "123", "中", "中文", "测试",

		// 标准长度边界测试
		strings.Repeat("A", 7), strings.Repeat("B", 8), strings.Repeat("C", 15), strings.Repeat("D", 16),
		strings.Repeat("E", 31), strings.Repeat("F", 32), strings.Repeat("G", 63), strings.Repeat("H", 64),
		strings.Repeat("I", 127), strings.Repeat("J", 128), strings.Repeat("K", 255), strings.Repeat("L", 256),

		// 中等长度测试
		strings.Repeat("M", 511), strings.Repeat("N", 512), strings.Repeat("O", 1023), strings.Repeat("P", 1024),
		strings.Repeat("Q", 2047), strings.Repeat("R", 2048), strings.Repeat("S", 4095), strings.Repeat("T", 4096),

		// 大长度测试
		strings.Repeat("U", 8191), strings.Repeat("V", 8192), strings.Repeat("W", 16383), strings.Repeat("X", 16384),
		strings.Repeat("Y", 32767), strings.Repeat("Z", 32768), strings.Repeat("0", 65535), strings.Repeat("1", 65536),

		// 超大长度测试
		strings.Repeat("2", 131071), strings.Repeat("3", 131072), strings.Repeat("4", 262143), strings.Repeat("5", 262144),
		strings.Repeat("6", 524287), strings.Repeat("7", 524288), strings.Repeat("8", 1048575), strings.Repeat("9", 1048576),

		// 特殊字符长度测试
		strings.Repeat("\x00", 100), strings.Repeat("\xFF", 100), strings.Repeat("\u0000", 100), strings.Repeat("\uFFFF", 100),
		strings.Repeat("\r\n", 100), strings.Repeat("\t", 100), strings.Repeat(" ", 100), strings.Repeat("\\", 100),

		// 混合字符长度测试
		strings.Repeat("Aa1!", 100), strings.Repeat("测试123", 100), strings.Repeat("αβγδε", 100), strings.Repeat("🔥💯✨", 100),
		strings.Repeat("A中1!", 100), strings.Repeat("Test测试", 100), strings.Repeat("αβ中文", 100), strings.Repeat("🔥中文", 100),

		// 数字字符串长度测试
		strings.Repeat("0", 10), strings.Repeat("1", 20), strings.Repeat("9", 50), strings.Repeat("123", 100),
		strings.Repeat("0123456789", 100), strings.Repeat("9876543210", 100), strings.Repeat("1234567890", 1000),

		// 特殊格式长度测试
		strings.Repeat("a@b.c", 100), strings.Repeat("http://a.b", 100), strings.Repeat("123-456-7890", 100),
		strings.Repeat("2023-12-01", 100), strings.Repeat("***********", 100), strings.Repeat("user:pass", 100),

		// 中文长度测试
		strings.Repeat("中", 10), strings.Repeat("文", 50), strings.Repeat("测", 100), strings.Repeat("试", 500),
		strings.Repeat("数据", 100), strings.Repeat("验证", 100), strings.Repeat("测试数据", 100), strings.Repeat("中文测试", 1000),
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *DataValidationDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 数据验证相关参数
		"data", "input", "value", "content", "text", "message", "comment", "description",
		"validate", "validation", "check", "verify", "filter", "sanitize", "clean", "format",
		"data_validation", "input_validation", "value_check", "content_filter", "text_sanitize",

		// 表单字段参数
		"name", "username", "email", "phone", "address", "city", "country", "zipcode",
		"firstname", "lastname", "fullname", "nickname", "displayname", "realname",
		"first_name", "last_name", "full_name", "nick_name", "display_name", "real_name",

		// 数字相关参数
		"number", "num", "count", "amount", "price", "cost", "total", "sum",
		"age", "year", "month", "day", "hour", "minute", "second", "time",
		"quantity", "qty", "size", "length", "width", "height", "weight", "volume",

		// 文本相关参数
		"title", "subject", "topic", "keyword", "tag", "category", "type", "status",
		"content", "body", "text", "message", "comment", "note", "description", "summary",
		"search", "query", "term", "filter", "sort", "order", "direction", "limit",

		// 用户相关参数
		"user", "userid", "uid", "account", "profile", "member", "customer", "client",
		"role", "permission", "access", "level", "group", "team", "department", "organization",
		"user_id", "user_name", "user_email", "user_phone", "user_type", "user_status",

		// 密码相关参数
		"password", "passwd", "pwd", "pass", "secret", "key", "token", "code",
		"confirm", "confirmation", "verify", "verification", "old_password", "new_password",
		"current_password", "confirm_password", "password_confirm", "password_verification",

		// 文件相关参数
		"file", "filename", "filepath", "upload", "attachment", "document", "image", "photo",
		"video", "audio", "media", "resource", "asset", "binary", "data", "content",
		"file_name", "file_path", "file_type", "file_size", "file_content", "file_data",

		// 日期时间参数
		"date", "datetime", "timestamp", "time", "created", "updated", "modified", "deleted",
		"start", "end", "begin", "finish", "from", "to", "since", "until",
		"created_at", "updated_at", "modified_at", "deleted_at", "start_date", "end_date",

		// 网络相关参数
		"url", "uri", "link", "href", "src", "action", "target", "redirect", "callback",
		"domain", "host", "server", "endpoint", "api", "service", "method", "protocol",
		"ip", "port", "address", "location", "path", "route", "page", "site",

		// 配置相关参数
		"config", "setting", "option", "preference", "parameter", "attribute", "property", "field",
		"key", "value", "pair", "item", "element", "object", "array", "list",
		"min", "max", "range", "limit", "threshold", "boundary", "default", "initial",

		// 业务相关参数
		"order", "product", "item", "goods", "service", "payment", "transaction", "invoice",
		"customer", "supplier", "vendor", "partner", "contact", "company", "organization",
		"id", "code", "number", "reference", "identifier", "uuid", "guid", "hash",

		// 中文参数
		"数据", "输入", "值", "内容", "文本", "消息", "评论", "描述",
		"验证", "校验", "检查", "确认", "过滤", "净化", "清理", "格式",
		"姓名", "用户名", "邮箱", "电话", "地址", "城市", "国家", "邮编",
		"密码", "确认密码", "旧密码", "新密码", "验证码", "令牌", "密钥",
		"文件", "文件名", "文件路径", "上传", "附件", "文档", "图片", "照片",
		"日期", "时间", "时间戳", "创建时间", "更新时间", "修改时间", "删除时间",
		"网址", "链接", "域名", "主机", "服务器", "接口", "服务", "方法",
		"配置", "设置", "选项", "参数", "属性", "字段", "键", "值",
		"订单", "产品", "商品", "服务", "支付", "交易", "发票", "客户",
	}
}

// initializePatterns 初始化检测模式
func (d *DataValidationDetector) initializePatterns() {
	// 数据验证检测模式 - 检测数据验证相关的响应内容
	validationPatternStrings := []string{
		// 通用数据验证模式
		`(?i)validation\s+(error|failed|failure|invalid|required)`,
		`(?i)input\s+(validation|error|invalid|required|missing)`,
		`(?i)data\s+(validation|error|invalid|required|missing)`,
		`(?i)field\s+(validation|error|invalid|required|missing)`,
		`(?i)parameter\s+(validation|error|invalid|required|missing)`,
		`(?i)value\s+(validation|error|invalid|required|missing)`,

		// 类型验证模式
		`(?i)type\s+(error|mismatch|invalid|required|expected)`,
		`(?i)format\s+(error|invalid|required|expected|mismatch)`,
		`(?i)datatype\s+(error|invalid|required|expected|mismatch)`,
		`(?i)typeof\s+(error|invalid|required|expected|mismatch)`,
		`(?i)instanceof\s+(error|invalid|required|expected)`,
		`(?i)cast\s+(error|invalid|failed|exception)`,

		// 长度验证模式
		`(?i)length\s+(error|invalid|required|exceeded|minimum|maximum)`,
		`(?i)size\s+(error|invalid|required|exceeded|minimum|maximum)`,
		`(?i)limit\s+(error|invalid|required|exceeded|reached)`,
		`(?i)boundary\s+(error|invalid|exceeded|violation)`,
		`(?i)range\s+(error|invalid|exceeded|violation|out)`,
		`(?i)overflow\s+(error|exception|detected)`,

		// 格式验证模式
		`(?i)format\s+(error|invalid|mismatch|violation)`,
		`(?i)pattern\s+(error|invalid|mismatch|violation)`,
		`(?i)regex\s+(error|invalid|mismatch|failed)`,
		`(?i)regexp\s+(error|invalid|mismatch|failed)`,
		`(?i)match\s+(error|invalid|failed|not)`,
		`(?i)syntax\s+(error|invalid|violation)`,

		// 边界值验证模式
		`(?i)boundary\s+(error|violation|exceeded|invalid)`,
		`(?i)limit\s+(error|violation|exceeded|reached)`,
		`(?i)threshold\s+(error|violation|exceeded|reached)`,
		`(?i)minimum\s+(error|violation|not\s+met|required)`,
		`(?i)maximum\s+(error|violation|exceeded|reached)`,
		`(?i)out\s+of\s+(range|bounds|limit)`,

		// 中文数据验证模式
		`(?i)(数据|输入|字段|参数|值).*验证.*失败`,
		`(?i)(数据|输入|字段|参数|值).*无效`,
		`(?i)(数据|输入|字段|参数|值).*错误`,
		`(?i)(类型|格式|长度|范围).*错误`,
		`(?i)(验证|校验|检查).*失败`,
		`(?i)(超出|超过).*范围`,
	}

	d.validationPatterns = make([]*regexp.Regexp, 0, len(validationPatternStrings))
	for _, pattern := range validationPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.validationPatterns = append(d.validationPatterns, compiled)
		}
	}

	// 错误响应检测模式 - 检测错误响应相关的内容
	errorPatternStrings := []string{
		// 通用错误模式
		`(?i)error\s*[:=]\s*(true|1|yes|on)`,
		`(?i)status\s*[:=]\s*(error|fail|failed|failure)`,
		`(?i)success\s*[:=]\s*(false|0|no|off)`,
		`(?i)result\s*[:=]\s*(error|fail|failed|failure)`,
		`(?i)code\s*[:=]\s*[45]\d{2}`,
		`(?i)message\s*[:=].*error`,

		// HTTP错误模式
		`(?i)400\s+(bad\s+request|error)`,
		`(?i)422\s+(unprocessable\s+entity|error)`,
		`(?i)500\s+(internal\s+server\s+error)`,
		`(?i)503\s+(service\s+unavailable)`,
		`(?i)http\s+error\s+[45]\d{2}`,
		`(?i)status\s+code\s*[:=]\s*[45]\d{2}`,

		// 验证错误模式
		`(?i)validation\s+(error|failed|failure)`,
		`(?i)invalid\s+(input|data|parameter|value)`,
		`(?i)required\s+(field|parameter|value)`,
		`(?i)missing\s+(field|parameter|value)`,
		`(?i)empty\s+(field|parameter|value)`,
		`(?i)null\s+(field|parameter|value)`,

		// 异常模式
		`(?i)(exception|error)\s*[:]\s*\w+Exception`,
		`(?i)stack\s+trace`,
		`(?i)at\s+\w+\.\w+\(.*:\d+\)`,
		`(?i)caused\s+by\s*[:]\s*\w+Exception`,
		`(?i)throw\s+new\s+\w+Exception`,
		`(?i)fatal\s+(error|exception)`,

		// 中文错误模式
		`(?i)(错误|失败|异常)`,
		`(?i)(无效|非法|不正确)`,
		`(?i)(必填|必需|缺少)`,
		`(?i)(为空|空值|未定义)`,
		`(?i)(超出|超过|溢出)`,
		`(?i)(格式|类型|长度).*错误`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 成功响应检测模式 - 检测成功响应相关的内容
	successPatternStrings := []string{
		// 通用成功模式
		`(?i)success\s*[:=]\s*(true|1|yes|on)`,
		`(?i)status\s*[:=]\s*(success|ok|okay|valid)`,
		`(?i)error\s*[:=]\s*(false|0|no|off)`,
		`(?i)result\s*[:=]\s*(success|ok|okay|valid)`,
		`(?i)code\s*[:=]\s*[23]\d{2}`,
		`(?i)message\s*[:=].*success`,

		// HTTP成功模式
		`(?i)200\s+(ok|success)`,
		`(?i)201\s+(created|success)`,
		`(?i)202\s+(accepted|success)`,
		`(?i)204\s+(no\s+content|success)`,
		`(?i)http\s+status\s+[23]\d{2}`,
		`(?i)status\s+code\s*[:=]\s*[23]\d{2}`,

		// 验证成功模式
		`(?i)validation\s+(success|passed|ok)`,
		`(?i)valid\s+(input|data|parameter|value)`,
		`(?i)accepted\s+(input|data|parameter|value)`,
		`(?i)processed\s+(input|data|parameter|value)`,
		`(?i)saved\s+(input|data|parameter|value)`,
		`(?i)updated\s+(input|data|parameter|value)`,

		// 处理成功模式
		`(?i)(created|updated|deleted|saved)\s+successfully`,
		`(?i)operation\s+(completed|successful)`,
		`(?i)request\s+(completed|successful|processed)`,
		`(?i)data\s+(saved|updated|processed)`,
		`(?i)form\s+(submitted|processed|saved)`,
		`(?i)transaction\s+(completed|successful)`,

		// 中文成功模式
		`(?i)(成功|完成|正确)`,
		`(?i)(有效|合法|正确)`,
		`(?i)(已保存|已更新|已处理)`,
		`(?i)(操作|请求|事务).*成功`,
		`(?i)(验证|校验|检查).*通过`,
		`(?i)(数据|表单).*提交.*成功`,
	}

	d.successPatterns = make([]*regexp.Regexp, 0, len(successPatternStrings))
	for _, pattern := range successPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.successPatterns = append(d.successPatterns, compiled)
		}
	}

	// 绕过检测模式 - 检测绕过相关的内容
	bypassPatternStrings := []string{
		// 通用绕过模式
		`(?i)bypass\s+(detected|found|successful)`,
		`(?i)validation\s+(bypassed|skipped|ignored)`,
		`(?i)filter\s+(bypassed|skipped|ignored)`,
		`(?i)check\s+(bypassed|skipped|ignored)`,
		`(?i)security\s+(bypassed|skipped|ignored)`,
		`(?i)protection\s+(bypassed|skipped|ignored)`,

		// 类型绕过模式
		`(?i)type\s+(confusion|mismatch|bypass)`,
		`(?i)cast\s+(bypass|error|exception)`,
		`(?i)conversion\s+(bypass|error|failed)`,
		`(?i)coercion\s+(bypass|unexpected)`,
		`(?i)implicit\s+(conversion|cast)`,
		`(?i)explicit\s+(conversion|cast)`,

		// 格式绕过模式
		`(?i)format\s+(bypass|violation|mismatch)`,
		`(?i)pattern\s+(bypass|violation|mismatch)`,
		`(?i)regex\s+(bypass|failed|error)`,
		`(?i)regexp\s+(bypass|failed|error)`,
		`(?i)syntax\s+(bypass|violation|error)`,
		`(?i)encoding\s+(bypass|error|mismatch)`,

		// 长度绕过模式
		`(?i)length\s+(bypass|violation|exceeded)`,
		`(?i)size\s+(bypass|violation|exceeded)`,
		`(?i)limit\s+(bypass|violation|exceeded)`,
		`(?i)boundary\s+(bypass|violation|exceeded)`,
		`(?i)overflow\s+(detected|occurred|bypass)`,
		`(?i)underflow\s+(detected|occurred|bypass)`,

		// 中文绕过模式
		`(?i)(绕过|跳过|忽略).*验证`,
		`(?i)(绕过|跳过|忽略).*检查`,
		`(?i)(绕过|跳过|忽略).*过滤`,
		`(?i)(类型|格式|长度).*绕过`,
		`(?i)(验证|校验|检查).*被绕过`,
		`(?i)(安全|保护).*被绕过`,
	}

	d.bypassPatterns = make([]*regexp.Regexp, 0, len(bypassPatternStrings))
	for _, pattern := range bypassPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.bypassPatterns = append(d.bypassPatterns, compiled)
		}
	}
}
