package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestSSRFDetectorBasicFunctionality 测试SSRF检测器基础功能
func TestSSRFDetectorBasicFunctionality(t *testing.T) {
	detector := NewSSRFDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "ssrf-comprehensive", detector.GetID())
	assert.Equal(t, "服务器端请求伪造(SSRF)综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-918")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestSSRFDetectorApplicability 测试SSRF检测器适用性
func TestSSRFDetectorApplicability(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试有参数的HTTP目标
	httpTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/test?url=http://google.com",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(httpTarget))

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/submit",
				Method: "POST",
				Fields: map[string]string{
					"url": "text",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有可能的URL参数的链接
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{
				URL:  "http://example.com/redirect?url=http://google.com",
				Text: "Redirect",
			},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试无参数的简单URL
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestSSRFDetectorConfiguration 测试SSRF检测器配置
func TestSSRFDetectorConfiguration(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.False(t, config.FollowRedirects) // SSRF检测不跟随重定向

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       10,
		FollowRedirects: false,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 30*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestSSRFDetectorPayloads 测试SSRF检测器载荷
func TestSSRFDetectorPayloads(t *testing.T) {
	detector := NewSSRFDetector()

	// 检查内网载荷
	assert.NotEmpty(t, detector.internalPayloads)
	assert.Greater(t, len(detector.internalPayloads), 20)

	// 检查协议载荷
	assert.NotEmpty(t, detector.protocolPayloads)
	assert.Greater(t, len(detector.protocolPayloads), 15)

	// 检查云服务载荷
	assert.NotEmpty(t, detector.cloudPayloads)
	assert.Greater(t, len(detector.cloudPayloads), 10)

	// 检查绕过载荷
	assert.NotEmpty(t, detector.bypassPayloads)
	assert.Greater(t, len(detector.bypassPayloads), 5)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 15)

	// 检查服务模式
	assert.NotEmpty(t, detector.servicePatterns)
	assert.Greater(t, len(detector.servicePatterns), 5)
}

// TestSSRFDetectorResponseCheck 测试SSRF响应检查
func TestSSRFDetectorResponseCheck(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试SSH服务响应
	sshResponse := "SSH-2.0-OpenSSH_7.4"
	sshPayload := "http://127.0.0.1:22"
	confidence := detector.checkSSRFResponse(sshResponse, sshPayload)
	assert.Greater(t, confidence, 0.5)

	// 测试HTTP服务响应
	httpResponse := "HTTP/1.1 200 OK\nServer: nginx/1.18.0"
	httpPayload := "http://127.0.0.1:80"
	confidence2 := detector.checkSSRFResponse(httpResponse, httpPayload)
	assert.Greater(t, confidence2, 0.5)

	// 测试网络错误响应
	errorResponse := "connection refused"
	errorPayload := "http://127.0.0.1:3306"
	confidence3 := detector.checkSSRFResponse(errorResponse, errorPayload)
	assert.Greater(t, confidence3, 0.4)

	// 测试无SSRF特征的响应
	normalResponse := "Hello World"
	normalPayload := "http://example.com"
	confidence4 := detector.checkSSRFResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence4)
}

// TestSSRFDetectorCloudMetadataCheck 测试云服务元数据检查
func TestSSRFDetectorCloudMetadataCheck(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试AWS元数据响应
	awsResponse := `{
		"instance-id": "i-1234567890abcdef0",
		"ami-id": "ami-0abcdef1234567890",
		"security-groups": ["sg-1234567890abcdef0"]
	}`
	awsPayload := "http://169.254.169.254/latest/meta-data/"
	confidence := detector.checkCloudMetadataResponse(awsResponse, awsPayload)
	assert.Greater(t, confidence, 0.8)

	// 测试Google Cloud元数据响应
	gcpResponse := `{
		"computeMetadata": {
			"v1": {
				"instance": {
					"zone": "projects/123456789/zones/us-central1-a"
				}
			}
		}
	}`
	gcpPayload := "http://metadata.google.internal/computeMetadata/v1/"
	confidence2 := detector.checkCloudMetadataResponse(gcpResponse, gcpPayload)
	assert.Greater(t, confidence2, 0.6)

	// 测试非云服务响应
	normalResponse := "Hello World"
	normalPayload := "http://example.com"
	confidence3 := detector.checkCloudMetadataResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence3)
}

// TestSSRFDetectorProtocolBypassCheck 测试协议绕过检查
func TestSSRFDetectorProtocolBypassCheck(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试文件协议响应
	fileResponse := `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin`
	filePayload := "file:///etc/passwd"
	confidence := detector.checkProtocolBypassResponse(fileResponse, filePayload)
	assert.Greater(t, confidence, 0.4)

	// 测试Gopher协议响应
	gopherResponse := `# Server
redis_version:6.2.6
redis_mode:standalone`
	gopherPayload := "gopher://127.0.0.1:6379/_INFO"
	confidence2 := detector.checkProtocolBypassResponse(gopherResponse, gopherPayload)
	assert.Greater(t, confidence2, 0.5)

	// 测试FTP协议响应
	ftpResponse := "220 Welcome to FTP server"
	ftpPayload := "ftp://127.0.0.1/"
	confidence3 := detector.checkProtocolBypassResponse(ftpResponse, ftpPayload)
	assert.Greater(t, confidence3, 0.4)

	// 测试非协议绕过响应
	normalResponse := "Hello World"
	normalPayload := "http://example.com"
	confidence4 := detector.checkProtocolBypassResponse(normalResponse, normalPayload)
	assert.Equal(t, 0.0, confidence4)
}

// TestSSRFDetectorPayloadInjection 测试载荷注入
func TestSSRFDetectorPayloadInjection(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试URL参数注入
	targetURL := "http://example.com/test?url=http://google.com"
	payload := "http://127.0.0.1:80"
	injectedURL := detector.injectPayload(targetURL, payload)
	assert.Contains(t, injectedURL, "127.0.0.1")

	// 测试redirect参数注入
	targetURL2 := "http://example.com/redirect?redirect=http://google.com&other=value"
	injectedURL2 := detector.injectPayload(targetURL2, payload)
	assert.Contains(t, injectedURL2, "127.0.0.1")

	// 测试无参数URL注入
	targetURL3 := "http://example.com/test"
	injectedURL3 := detector.injectPayload(targetURL3, payload)
	assert.Contains(t, injectedURL3, "url=")
	assert.Contains(t, injectedURL3, "127.0.0.1")
}

// TestSSRFDetectorRiskScore 测试风险评分计算
func TestSSRFDetectorRiskScore(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestSSRFDetectorLifecycle 测试检测器生命周期
func TestSSRFDetectorLifecycle(t *testing.T) {
	detector := NewSSRFDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkSSRFDetectorResponseCheck 基准测试响应检查性能
func BenchmarkSSRFDetectorResponseCheck(b *testing.B) {
	detector := NewSSRFDetector()
	response := "SSH-2.0-OpenSSH_7.4\nHTTP/1.1 200 OK\nServer: nginx/1.18.0"
	payload := "http://127.0.0.1:22"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkSSRFResponse(response, payload)
	}
}

// BenchmarkSSRFDetectorPayloadInjection 基准测试载荷注入性能
func BenchmarkSSRFDetectorPayloadInjection(b *testing.B) {
	detector := NewSSRFDetector()
	targetURL := "http://example.com/test?url=http://google.com&redirect=http://yahoo.com"
	payload := "http://127.0.0.1:80"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.injectPayload(targetURL, payload)
	}
}
