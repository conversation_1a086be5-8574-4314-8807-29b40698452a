package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 查询扫描任务数据
	query := `
		SELECT id, name, type, status, 
		       CASE 
		           WHEN info_gathering_data IS NULL OR info_gathering_data = '' THEN '无数据'
		           ELSE SUBSTR(info_gathering_data, 1, 100) || '...'
		       END as info_data_preview
		FROM scan_tasks 
		ORDER BY id DESC 
		LIMIT 10
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Fatal("查询失败:", err)
	}
	defer rows.Close()

	fmt.Println("=== 扫描任务信息收集数据状态 ===")
	fmt.Printf("%-4s %-20s %-15s %-10s %-50s\n", "ID", "任务名称", "类型", "状态", "信息收集数据")
	fmt.Println(strings.Repeat("-", 120))

	for rows.Next() {
		var id int
		var name, taskType, status, infoData string

		err := rows.Scan(&id, &name, &taskType, &status, &infoData)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}

		fmt.Printf("%-4d %-20s %-15s %-10s %-50s\n",
			id,
			truncateString(name, 20),
			taskType,
			status,
			truncateString(infoData, 50))
	}

	// 检查具体某个任务的完整信息收集数据
	fmt.Println("\n=== 检查最新任务的完整信息收集数据 ===")
	var latestID int
	var latestInfoData sql.NullString

	err = db.QueryRow("SELECT id, info_gathering_data FROM scan_tasks ORDER BY id DESC LIMIT 1").Scan(&latestID, &latestInfoData)
	if err != nil {
		log.Printf("查询最新任务失败: %v", err)
	} else {
		fmt.Printf("最新任务ID: %d\n", latestID)
		if latestInfoData.Valid && latestInfoData.String != "" {
			fmt.Printf("信息收集数据长度: %d 字符\n", len(latestInfoData.String))
			fmt.Printf("数据内容预览:\n%s\n", latestInfoData.String[:min(500, len(latestInfoData.String))])
		} else {
			fmt.Println("该任务没有信息收集数据")
		}
	}
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
