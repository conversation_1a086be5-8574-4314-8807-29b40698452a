package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectSensitiveFiles 检测敏感文件
func (d *InformationLeakageDetector) detectSensitiveFiles(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试敏感文件
	for i, file := range d.sensitiveFiles {
		if i >= 20 { // 限制文件数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送文件请求
		resp, err := d.sendFileRequest(ctx, target.URL, file)
		if err != nil {
			continue
		}

		// 检查文件响应
		confidence := d.checkFileResponse(resp, file)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = file
			vulnerableRequest = target.URL + file
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "sensitive-file",
				Description: fmt.Sprintf("发现敏感文件: %s (置信度: %.2f)", file, confidence),
				Content:     d.extractFileEvidence(resp, file),
				Location:    target.URL + file,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectSensitiveData 检测敏感数据泄露
func (d *InformationLeakageDetector) detectSensitiveData(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 发送基础请求获取页面内容
	resp, err := d.sendPageRequest(ctx, target.URL)
	if err != nil {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 检查敏感数据模式
	for patternName, pattern := range d.sensitivePatterns {
		matches := pattern.FindAllString(resp, -1)
		if len(matches) > 0 {
			confidence := d.calculateSensitiveDataConfidence(patternName, matches)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = patternName
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			if confidence > 0.5 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "sensitive-data",
					Description: fmt.Sprintf("发现敏感数据: %s (置信度: %.2f)", patternName, confidence),
					Content:     d.extractSensitiveDataEvidence(matches, patternName),
					Location:    target.URL,
					Timestamp:   time.Now(),
				})
			}
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDebugInformation 检测调试信息
func (d *InformationLeakageDetector) detectDebugInformation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试调试端点
	for i, endpoint := range d.debugEndpoints {
		if i >= 10 { // 限制端点数量
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送调试端点请求
		debugURL := d.buildDebugURL(target.URL, endpoint)
		resp, err := d.sendPageRequest(ctx, debugURL)
		if err != nil {
			continue
		}

		// 检查调试信息
		confidence := d.checkDebugResponse(resp, endpoint)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = endpoint
			vulnerableRequest = debugURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "debug-information",
				Description: fmt.Sprintf("发现调试信息: %s (置信度: %.2f)", endpoint, confidence),
				Content:     d.extractDebugEvidence(resp, endpoint),
				Location:    debugURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectVersionInformation 检测版本信息泄露
func (d *InformationLeakageDetector) detectVersionInformation(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 发送HEAD请求获取响应头
	resp, err := d.sendHeadRequest(ctx, target.URL)
	if err != nil {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 检查版本信息模式
	for _, pattern := range d.versionPatterns {
		matches := pattern.FindAllString(resp, -1)
		if len(matches) > 0 {
			confidence := 0.7 // 版本信息泄露的基础置信度
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = "version-information"
				vulnerableRequest = target.URL
				vulnerableResponse = resp
			}

			evidence = append(evidence, plugins.Evidence{
				Type:        "version-information",
				Description: fmt.Sprintf("发现版本信息泄露 (置信度: %.2f)", confidence),
				Content:     d.extractVersionEvidence(matches),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendFileRequest 发送文件请求
func (d *InformationLeakageDetector) sendFileRequest(ctx context.Context, baseURL, filePath string) (string, error) {
	// 构造文件URL
	fileURL := d.buildFileURL(baseURL, filePath)

	req, err := http.NewRequestWithContext(ctx, "GET", fileURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendPageRequest 发送页面请求
func (d *InformationLeakageDetector) sendPageRequest(ctx context.Context, pageURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", pageURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendHeadRequest 发送HEAD请求
func (d *InformationLeakageDetector) sendHeadRequest(ctx context.Context, pageURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "HEAD", pageURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 只包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}

	return responseInfo, nil
}

// buildFileURL 构造文件URL
func (d *InformationLeakageDetector) buildFileURL(baseURL, filePath string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + filePath
	}

	// 确保文件路径以/开头
	if !strings.HasPrefix(filePath, "/") {
		filePath = "/" + filePath
	}

	parsedURL.Path = filePath
	parsedURL.RawQuery = ""
	return parsedURL.String()
}

// buildDebugURL 构造调试URL
func (d *InformationLeakageDetector) buildDebugURL(baseURL, endpoint string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + endpoint
	}

	// 确保端点以/开头
	if !strings.HasPrefix(endpoint, "/") {
		endpoint = "/" + endpoint
	}

	parsedURL.Path = endpoint
	parsedURL.RawQuery = ""
	return parsedURL.String()
}

// checkFileResponse 检查文件响应
func (d *InformationLeakageDetector) checkFileResponse(response, filePath string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.5
	} else if strings.Contains(response, "status: 403") {
		confidence += 0.2 // 文件存在但被保护
	} else {
		return 0.0 // 文件不存在
	}

	// 检查文件特定的内容
	fileLower := strings.ToLower(filePath)

	// 配置文件检查
	if strings.Contains(fileLower, ".env") {
		if strings.Contains(response, "app_key") || strings.Contains(response, "db_password") ||
			strings.Contains(response, "api_key") || strings.Contains(response, "secret") {
			confidence += 0.4
		}
	}

	if strings.Contains(fileLower, "config") {
		if strings.Contains(response, "database") || strings.Contains(response, "password") ||
			strings.Contains(response, "mysql") || strings.Contains(response, "<?php") {
			confidence += 0.4
		}
	}

	if strings.Contains(fileLower, "robots.txt") {
		if strings.Contains(response, "disallow") || strings.Contains(response, "user-agent") {
			confidence += 0.3
		}
	}

	if strings.Contains(fileLower, "phpinfo") {
		if strings.Contains(response, "php version") || strings.Contains(response, "configuration") {
			confidence += 0.4
		}
	}

	// 版本控制文件检查
	if strings.Contains(fileLower, ".git") {
		if strings.Contains(response, "ref:") || strings.Contains(response, "repository") {
			confidence += 0.4
		}
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkDebugResponse 检查调试响应
func (d *InformationLeakageDetector) checkDebugResponse(response, endpoint string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.4
	} else {
		return 0.0
	}

	// 检查调试信息模式
	for _, pattern := range d.debugPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查错误信息模式
	for _, pattern := range d.errorPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 检查端点特定的内容
	endpointLower := strings.ToLower(endpoint)
	if strings.Contains(endpointLower, "debug") {
		if strings.Contains(response, "debug") || strings.Contains(response, "trace") {
			confidence += 0.3
		}
	}

	return confidence
}

// calculateSensitiveDataConfidence 计算敏感数据置信度
func (d *InformationLeakageDetector) calculateSensitiveDataConfidence(patternName string, matches []string) float64 {
	confidence := 0.0

	// 基础置信度根据模式类型
	switch patternName {
	case "API密钥", "JWT令牌", "AWS密钥", "GitHub令牌":
		confidence = 0.9 // API密钥泄露是高风险
	case "私钥", "证书", "SSH密钥":
		confidence = 0.9 // 密钥泄露是高风险
	case "信用卡号", "社会保障号", "身份证号":
		confidence = 0.8 // 个人信息泄露是高风险
	case "密码", "数据库连接":
		confidence = 0.8 // 凭据泄露是高风险
	case "内部IP", "文件路径":
		confidence = 0.6 // 内部信息泄露是中等风险
	case "SQL错误", "PHP错误", "Java错误", "Python错误":
		confidence = 0.7 // 错误信息泄露是中等风险
	case "调试信息", "堆栈跟踪", "变量转储":
		confidence = 0.6 // 调试信息泄露是中等风险
	default:
		confidence = 0.5 // 其他信息泄露是低风险
	}

	// 根据匹配数量调整置信度
	matchCount := len(matches)
	if matchCount > 5 {
		confidence += 0.1
	} else if matchCount > 10 {
		confidence += 0.2
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkInformationLeakage 检查信息泄露
func (d *InformationLeakageDetector) checkInformationLeakage(response, file string) float64 {
	return d.checkFileResponse(response, file)
}

// extractFileEvidence 提取文件证据
func (d *InformationLeakageDetector) extractFileEvidence(response, filePath string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 查找文件内容的前几行
	contentStart := false
	var contentLines []string
	for _, line := range lines {
		if contentStart {
			contentLines = append(contentLines, line)
			if len(contentLines) >= 5 { // 只取前5行
				break
			}
		}
		if strings.TrimSpace(line) == "" && !contentStart {
			contentStart = true // 空行后开始是内容
		}
	}

	if len(contentLines) > 0 {
		return strings.Join(contentLines, "\n")
	}

	// 如果没有找到内容，返回前200个字符
	if len(response) > 200 {
		return response[:200] + "..."
	}
	return response
}

// extractSensitiveDataEvidence 提取敏感数据证据
func (d *InformationLeakageDetector) extractSensitiveDataEvidence(matches []string, patternName string) string {
	if len(matches) == 0 {
		return ""
	}

	// 对于敏感数据，只显示部分内容以保护隐私
	var maskedMatches []string
	for _, match := range matches {
		if len(match) > 10 {
			// 显示前3个和后3个字符，中间用*代替
			masked := match[:3] + strings.Repeat("*", len(match)-6) + match[len(match)-3:]
			maskedMatches = append(maskedMatches, masked)
		} else {
			// 短字符串用*代替大部分内容
			masked := match[:1] + strings.Repeat("*", len(match)-2) + match[len(match)-1:]
			maskedMatches = append(maskedMatches, masked)
		}

		// 限制显示的匹配数量
		if len(maskedMatches) >= 3 {
			break
		}
	}

	result := fmt.Sprintf("发现 %d 个 %s 匹配:\n", len(matches), patternName)
	for _, masked := range maskedMatches {
		result += fmt.Sprintf("- %s\n", masked)
	}

	if len(matches) > len(maskedMatches) {
		result += fmt.Sprintf("... 还有 %d 个匹配\n", len(matches)-len(maskedMatches))
	}

	return result
}

// extractDebugEvidence 提取调试证据
func (d *InformationLeakageDetector) extractDebugEvidence(response, endpoint string) string {
	lines := strings.Split(response, "\n")

	// 查找包含调试信息的行
	var debugLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)
		if strings.Contains(lineLower, "debug") ||
			strings.Contains(lineLower, "trace") ||
			strings.Contains(lineLower, "dump") ||
			strings.Contains(lineLower, "error") {
			debugLines = append(debugLines, line)
			if len(debugLines) >= 5 { // 只取前5行
				break
			}
		}
	}

	if len(debugLines) > 0 {
		return strings.Join(debugLines, "\n")
	}

	// 如果没有找到特定的调试信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}

// extractVersionEvidence 提取版本证据
func (d *InformationLeakageDetector) extractVersionEvidence(matches []string) string {
	if len(matches) == 0 {
		return ""
	}

	result := fmt.Sprintf("发现 %d 个版本信息:\n", len(matches))
	for i, match := range matches {
		result += fmt.Sprintf("- %s\n", match)
		if i >= 4 { // 限制显示数量
			break
		}
	}

	if len(matches) > 5 {
		result += fmt.Sprintf("... 还有 %d 个版本信息\n", len(matches)-5)
	}

	return result
}
