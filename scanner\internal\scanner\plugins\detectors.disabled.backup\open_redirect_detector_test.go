package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestOpenRedirectDetectorBasicFunctionality 测试开放重定向检测器基础功能
func TestOpenRedirectDetectorBasicFunctionality(t *testing.T) {
	detector := NewOpenRedirectDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "open-redirect-comprehensive", detector.GetID())
	assert.Equal(t, "开放重定向漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-601")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestOpenRedirectDetectorApplicability 测试开放重定向检测器适用性
func TestOpenRedirectDetectorApplicability(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 测试有重定向功能的目标
	redirectTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Location": "http://example.com/dashboard",
		},
	}
	assert.True(t, detector.IsApplicable(redirectTarget))

	// 测试有重定向链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/auth",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/redirect", Text: "Redirect Page"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Action: "/search", Method: "POST", Fields: map[string]string{"redirect": "text"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login?return=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试重定向相关URL
	redirectURLTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/redirect",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(redirectURLTarget))

	// 测试普通Web目标（开放重定向是通用漏洞）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestOpenRedirectDetectorConfiguration 测试开放重定向检测器配置
func TestOpenRedirectDetectorConfiguration(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 10*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 4, config.Concurrency)
	assert.False(t, config.FollowRedirects) // 不跟随重定向
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       6,
		FollowRedirects: false,
		VerifySSL:       false,
		MaxResponseSize: 1 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestOpenRedirectDetectorParameterPayloads 测试参数重定向载荷
func TestOpenRedirectDetectorParameterPayloads(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查参数重定向载荷列表
	assert.NotEmpty(t, detector.parameterPayloads)
	assert.Greater(t, len(detector.parameterPayloads), 50)

	// 检查基础HTTP/HTTPS重定向
	assert.Contains(t, detector.parameterPayloads, "http://evil.com")
	assert.Contains(t, detector.parameterPayloads, "https://evil.com")
	assert.Contains(t, detector.parameterPayloads, "http://evil.com/malicious")

	// 检查协议相对重定向
	assert.Contains(t, detector.parameterPayloads, "//evil.com")
	assert.Contains(t, detector.parameterPayloads, "///evil.com")
	assert.Contains(t, detector.parameterPayloads, "////evil.com")

	// 检查域名混淆
	assert.Contains(t, detector.parameterPayloads, "http://<EMAIL>")
	assert.Contains(t, detector.parameterPayloads, "http://legitimate.com.evil.com")
	assert.Contains(t, detector.parameterPayloads, "http://evil.com#legitimate.com")

	// 检查子域名攻击
	assert.Contains(t, detector.parameterPayloads, "http://sub.evil.com")
	assert.Contains(t, detector.parameterPayloads, "http://www.evil.com")

	// 检查IP地址重定向
	assert.Contains(t, detector.parameterPayloads, "http://*************")
	assert.Contains(t, detector.parameterPayloads, "http://127.0.0.1")
	assert.Contains(t, detector.parameterPayloads, "http://********")

	// 检查端口变化
	assert.Contains(t, detector.parameterPayloads, "http://evil.com:8080")
	assert.Contains(t, detector.parameterPayloads, "https://evil.com:8443")

	// 检查路径遍历
	assert.Contains(t, detector.parameterPayloads, "../../../evil.com")
	assert.Contains(t, detector.parameterPayloads, "/evil.com")

	// 检查编码绕过
	assert.Contains(t, detector.parameterPayloads, "http%3A%2F%2Fevil.com")
	assert.Contains(t, detector.parameterPayloads, "http://evil%2ecom")

	// 检查中文域名
	assert.Contains(t, detector.parameterPayloads, "http://恶意.com")
	assert.Contains(t, detector.parameterPayloads, "http://钓鱼.com")
}

// TestOpenRedirectDetectorHeaderPayloads 测试头部重定向载荷
func TestOpenRedirectDetectorHeaderPayloads(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查头部重定向载荷列表
	assert.NotEmpty(t, detector.headerPayloads)
	assert.Greater(t, len(detector.headerPayloads), 15)

	// 检查Location头部重定向
	assert.Contains(t, detector.headerPayloads, "Location: http://evil.com")
	assert.Contains(t, detector.headerPayloads, "Location: https://evil.com")
	assert.Contains(t, detector.headerPayloads, "Location: //evil.com")

	// 检查Refresh头部重定向
	assert.Contains(t, detector.headerPayloads, "Refresh: 0; url=http://evil.com")
	assert.Contains(t, detector.headerPayloads, "Refresh: 0; url=https://evil.com")
	assert.Contains(t, detector.headerPayloads, "Refresh: 1; url=http://evil.com")

	// 检查其他重定向头部
	assert.Contains(t, detector.headerPayloads, "X-Redirect: http://evil.com")
	assert.Contains(t, detector.headerPayloads, "X-Location: http://evil.com")
	assert.Contains(t, detector.headerPayloads, "Redirect-To: http://evil.com")

	// 检查中文头部
	assert.Contains(t, detector.headerPayloads, "Location: http://恶意.com")
	assert.Contains(t, detector.headerPayloads, "Refresh: 0; url=http://钓鱼.com")
}

// TestOpenRedirectDetectorJavascriptPayloads 测试JavaScript重定向载荷
func TestOpenRedirectDetectorJavascriptPayloads(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查JavaScript重定向载荷列表
	assert.NotEmpty(t, detector.javascriptPayloads)
	assert.Greater(t, len(detector.javascriptPayloads), 20)

	// 检查JavaScript协议
	assert.Contains(t, detector.javascriptPayloads, "javascript:location.href='http://evil.com'")
	assert.Contains(t, detector.javascriptPayloads, "javascript:location='https://evil.com'")
	assert.Contains(t, detector.javascriptPayloads, "javascript:window.location='http://evil.com'")

	// 检查JavaScript函数调用
	assert.Contains(t, detector.javascriptPayloads, "javascript:redirect('http://evil.com')")
	assert.Contains(t, detector.javascriptPayloads, "javascript:goto('https://evil.com')")

	// 检查JavaScript表达式
	assert.Contains(t, detector.javascriptPayloads, "javascript:alert('XSS')")
	assert.Contains(t, detector.javascriptPayloads, "javascript:confirm('XSS')")

	// 检查Data URI
	assert.Contains(t, detector.javascriptPayloads, "data:text/html,<script>location='http://evil.com'</script>")
	assert.Contains(t, detector.javascriptPayloads, "data:text/html,<meta http-equiv=\"refresh\" content=\"0;url=http://evil.com\">")

	// 检查VBScript (IE)
	assert.Contains(t, detector.javascriptPayloads, "vbscript:location.href=\"http://evil.com\"")
	assert.Contains(t, detector.javascriptPayloads, "vbscript:window.location=\"https://evil.com\"")

	// 检查中文JavaScript
	assert.Contains(t, detector.javascriptPayloads, "javascript:location.href='http://恶意.com'")
	assert.Contains(t, detector.javascriptPayloads, "javascript:alert('恶意代码')")
}

// TestOpenRedirectDetectorProtocolPayloads 测试协议重定向载荷
func TestOpenRedirectDetectorProtocolPayloads(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查协议重定向载荷列表
	assert.NotEmpty(t, detector.protocolPayloads)
	assert.Greater(t, len(detector.protocolPayloads), 20)

	// 检查文件协议
	assert.Contains(t, detector.protocolPayloads, "file:///etc/passwd")
	assert.Contains(t, detector.protocolPayloads, "file:///c:/windows/system32/drivers/etc/hosts")
	assert.Contains(t, detector.protocolPayloads, "file://evil.com/malicious")

	// 检查FTP协议
	assert.Contains(t, detector.protocolPayloads, "ftp://evil.com")
	assert.Contains(t, detector.protocolPayloads, "ftp://user:<EMAIL>")
	assert.Contains(t, detector.protocolPayloads, "ftps://evil.com")

	// 检查邮件协议
	assert.Contains(t, detector.protocolPayloads, "mailto:<EMAIL>")
	assert.Contains(t, detector.protocolPayloads, "mailto:<EMAIL>?subject=Phishing")

	// 检查其他协议
	assert.Contains(t, detector.protocolPayloads, "tel:+1234567890")
	assert.Contains(t, detector.protocolPayloads, "sms:+1234567890")
	assert.Contains(t, detector.protocolPayloads, "skype:evil.user")

	// 检查自定义协议
	assert.Contains(t, detector.protocolPayloads, "custom://evil.com")
	assert.Contains(t, detector.protocolPayloads, "app://evil.com")

	// 检查中文协议
	assert.Contains(t, detector.protocolPayloads, "mailto:管理员@恶意.com")
	assert.Contains(t, detector.protocolPayloads, "file:///恶意文件")
}

// TestOpenRedirectDetectorEncodingPayloads 测试编码重定向载荷
func TestOpenRedirectDetectorEncodingPayloads(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查编码重定向载荷列表
	assert.NotEmpty(t, detector.encodingPayloads)
	assert.Greater(t, len(detector.encodingPayloads), 25)

	// 检查URL编码
	assert.Contains(t, detector.encodingPayloads, "http%3A%2F%2Fevil.com")
	assert.Contains(t, detector.encodingPayloads, "https%3A%2F%2Fevil.com")
	assert.Contains(t, detector.encodingPayloads, "%2F%2Fevil.com")

	// 检查双重编码
	assert.Contains(t, detector.encodingPayloads, "http%253A%252F%252Fevil.com")
	assert.Contains(t, detector.encodingPayloads, "%252F%252Fevil.com")

	// 检查Unicode编码
	assert.Contains(t, detector.encodingPayloads, "http://evil\u002ecom")
	assert.Contains(t, detector.encodingPayloads, "https://evil\u002ecom")

	// 检查HTML实体编码
	assert.Contains(t, detector.encodingPayloads, "http://evil&#46;com")
	assert.Contains(t, detector.encodingPayloads, "http://evil&#x2e;com")

	// 检查十六进制编码
	assert.Contains(t, detector.encodingPayloads, "http://evil%2ecom")
	assert.Contains(t, detector.encodingPayloads, "http://evil%2Ecom")

	// 检查Base64编码
	assert.Contains(t, detector.encodingPayloads, "aHR0cDovL2V2aWwuY29t")    // http://evil.com
	assert.Contains(t, detector.encodingPayloads, "aHR0cHM6Ly9ldmlsLmNvbQ==") // https://evil.com

	// 检查混合编码
	assert.Contains(t, detector.encodingPayloads, "http%3A//evil%2ecom")
	assert.Contains(t, detector.encodingPayloads, "//evil%2ecom")

	// 检查中文编码
	assert.Contains(t, detector.encodingPayloads, "http://恶意%2ecom")
	assert.Contains(t, detector.encodingPayloads, "https://钓鱼%2ecom")
}

// TestOpenRedirectDetectorTestParameters 测试参数
func TestOpenRedirectDetectorTestParameters(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.Greater(t, len(detector.testParameters), 60)

	// 检查重定向相关参数
	assert.Contains(t, detector.testParameters, "redirect")
	assert.Contains(t, detector.testParameters, "redirect_to")
	assert.Contains(t, detector.testParameters, "redirect_url")
	assert.Contains(t, detector.testParameters, "return")
	assert.Contains(t, detector.testParameters, "next")
	assert.Contains(t, detector.testParameters, "continue")
	assert.Contains(t, detector.testParameters, "goto")
	assert.Contains(t, detector.testParameters, "jump")

	// 检查URL相关参数
	assert.Contains(t, detector.testParameters, "url")
	assert.Contains(t, detector.testParameters, "uri")
	assert.Contains(t, detector.testParameters, "link")
	assert.Contains(t, detector.testParameters, "href")
	assert.Contains(t, detector.testParameters, "target")
	assert.Contains(t, detector.testParameters, "destination")

	// 检查登录相关参数
	assert.Contains(t, detector.testParameters, "login_redirect")
	assert.Contains(t, detector.testParameters, "logout_redirect")
	assert.Contains(t, detector.testParameters, "success_url")
	assert.Contains(t, detector.testParameters, "callback")

	// 检查通用参数
	assert.Contains(t, detector.testParameters, "r")
	assert.Contains(t, detector.testParameters, "u")
	assert.Contains(t, detector.testParameters, "l")
	assert.Contains(t, detector.testParameters, "ref")

	// 检查框架特定参数
	assert.Contains(t, detector.testParameters, "_redirect")
	assert.Contains(t, detector.testParameters, "redirect_uri")
	assert.Contains(t, detector.testParameters, "return_uri")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "重定向")
	assert.Contains(t, detector.testParameters, "跳转")
	assert.Contains(t, detector.testParameters, "返回")
	assert.Contains(t, detector.testParameters, "链接")
}

// TestOpenRedirectDetectorPatterns 测试模式
func TestOpenRedirectDetectorPatterns(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 检查重定向模式
	assert.NotEmpty(t, detector.redirectPatterns)
	assert.Greater(t, len(detector.redirectPatterns), 10)

	// 检查JavaScript模式
	assert.NotEmpty(t, detector.javascriptPatterns)
	assert.Greater(t, len(detector.javascriptPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 10)
}

// TestOpenRedirectDetectorRedirectFeatures 测试重定向功能检查
func TestOpenRedirectDetectorRedirectFeatures(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 测试有重定向头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Location": "http://example.com/redirect",
		},
	}
	assert.True(t, detector.hasRedirectFeatures(headerTarget))

	// 测试有重定向链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/redirect", Text: "Redirect"},
		},
	}
	assert.True(t, detector.hasRedirectFeatures(linkTarget))

	// 测试有重定向表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"redirect": "text", "return": "hidden"}},
		},
	}
	assert.True(t, detector.hasRedirectFeatures(formTarget))

	// 测试无重定向功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers: map[string]string{},
		Links:   []plugins.LinkInfo{},
		Forms:   []plugins.FormInfo{},
	}
	assert.False(t, detector.hasRedirectFeatures(simpleTarget))
}

// TestOpenRedirectDetectorRiskScore 测试风险评分计算
func TestOpenRedirectDetectorRiskScore(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestOpenRedirectDetectorLifecycle 测试检测器生命周期
func TestOpenRedirectDetectorLifecycle(t *testing.T) {
	detector := NewOpenRedirectDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
