package detectors

import (
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestInputValidationDetectorBasicFunctionality 测试输入验证检测器基础功能
func TestInputValidationDetectorBasicFunctionality(t *testing.T) {
	detector := NewInputValidationDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "input-validation-comprehensive", detector.GetID())
	assert.Equal(t, "输入验证漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-20")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestInputValidationDetectorApplicability 测试输入验证检测器适用性
func TestInputValidationDetectorApplicability(t *testing.T) {
	detector := NewInputValidationDetector()

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/form",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/submit",
				Method: "POST",
				Fields: map[string]string{
					"name":  "text",
					"email": "email",
					"age":   "number",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有查询参数的目标
	queryTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(queryTarget))

	// 测试有JSON内容类型的目标
	jsonTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}
	assert.True(t, detector.IsApplicable(jsonTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/users",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试搜索相关URL
	searchTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(searchTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通Web目标（无输入功能）
	simpleTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(simpleTarget))
}

// TestInputValidationDetectorConfiguration 测试输入验证检测器配置
func TestInputValidationDetectorConfiguration(t *testing.T) {
	detector := NewInputValidationDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 10*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 4, config.Concurrency)
	assert.True(t, config.FollowRedirects)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       6,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 512 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestInputValidationDetectorInputTypes 测试输入验证检测器输入类型
func TestInputValidationDetectorInputTypes(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查输入类型列表
	assert.NotEmpty(t, detector.inputTypes)
	assert.Greater(t, len(detector.inputTypes), 25)

	// 检查基础输入类型
	assert.Contains(t, detector.inputTypes, "text")
	assert.Contains(t, detector.inputTypes, "password")
	assert.Contains(t, detector.inputTypes, "email")
	assert.Contains(t, detector.inputTypes, "number")
	assert.Contains(t, detector.inputTypes, "url")
}

// TestInputValidationDetectorValidationPatterns 测试输入验证检测器验证模式
func TestInputValidationDetectorValidationPatterns(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查验证模式列表
	assert.NotEmpty(t, detector.validationPatterns)
	assert.Greater(t, len(detector.validationPatterns), 15)

	// 检查邮箱验证模式
	emailPatterns := 0
	for _, pattern := range detector.validationPatterns {
		if strings.Contains(pattern, "@") {
			emailPatterns++
		}
	}
	assert.Greater(t, emailPatterns, 0)
}

// TestInputValidationDetectorBypassPayloads 测试输入验证检测器绕过载荷
func TestInputValidationDetectorBypassPayloads(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查绕过载荷列表
	assert.NotEmpty(t, detector.bypassPayloads)
	assert.Greater(t, len(detector.bypassPayloads), 20)

	// 检查XSS绕过载荷
	assert.Contains(t, detector.bypassPayloads, "<script>alert('xss')</script>")
	assert.Contains(t, detector.bypassPayloads, "<img src=x onerror=alert('xss')>")

	// 检查SQL注入绕过载荷
	assert.Contains(t, detector.bypassPayloads, "' OR '1'='1")
	assert.Contains(t, detector.bypassPayloads, "' UNION SELECT NULL--")
}

// TestInputValidationDetectorLengthTests 测试输入验证检测器长度测试
func TestInputValidationDetectorLengthTests(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查长度测试大小列表
	assert.NotEmpty(t, detector.lengthTestSizes)
	assert.Greater(t, len(detector.lengthTestSizes), 15)

	// 检查特定长度
	assert.Contains(t, detector.lengthTestSizes, 100)
	assert.Contains(t, detector.lengthTestSizes, 1000)
	assert.Contains(t, detector.lengthTestSizes, 10000)
	assert.Contains(t, detector.lengthTestSizes, 65536)
}

// TestInputValidationDetectorSpecialCharacters 测试输入验证检测器特殊字符
func TestInputValidationDetectorSpecialCharacters(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查特殊字符列表
	assert.NotEmpty(t, detector.specialCharacters)
	assert.Greater(t, len(detector.specialCharacters), 30)

	// 检查HTML特殊字符
	assert.Contains(t, detector.specialCharacters, "<")
	assert.Contains(t, detector.specialCharacters, ">")
	assert.Contains(t, detector.specialCharacters, "\"")
	assert.Contains(t, detector.specialCharacters, "'")
	assert.Contains(t, detector.specialCharacters, "&")

	// 检查SQL特殊字符
	assert.Contains(t, detector.specialCharacters, ";")
	assert.Contains(t, detector.specialCharacters, "--")
}

// TestInputValidationDetectorDataTypes 测试输入验证检测器数据类型
func TestInputValidationDetectorDataTypes(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查数据类型列表
	assert.NotEmpty(t, detector.dataTypes)
	assert.Greater(t, len(detector.dataTypes), 20)

	// 检查基础数据类型
	assert.Contains(t, detector.dataTypes, "string")
	assert.Contains(t, detector.dataTypes, "integer")
	assert.Contains(t, detector.dataTypes, "float")
	assert.Contains(t, detector.dataTypes, "boolean")
	assert.Contains(t, detector.dataTypes, "date")

	// 检查复合数据类型
	assert.Contains(t, detector.dataTypes, "array")
	assert.Contains(t, detector.dataTypes, "object")
	assert.Contains(t, detector.dataTypes, "json")
}

// TestInputValidationDetectorPatterns 测试输入验证检测器模式
func TestInputValidationDetectorPatterns(t *testing.T) {
	detector := NewInputValidationDetector()

	// 检查格式模式
	assert.NotEmpty(t, detector.formatPatterns)
	assert.Greater(t, len(detector.formatPatterns), 10)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 8)

	// 检查绕过模式
	assert.NotEmpty(t, detector.bypassPatterns)
	assert.Greater(t, len(detector.bypassPatterns), 10)
}

// TestInputValidationDetectorInputFeatures 测试输入功能检查
func TestInputValidationDetectorInputFeatures(t *testing.T) {
	detector := NewInputValidationDetector()

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{
				Fields: map[string]string{
					"name": "text",
				},
			},
		},
	}
	assert.True(t, detector.hasInputFeatures(formTarget))

	// 测试有查询参数的目标
	queryTarget := &plugins.ScanTarget{
		URL: "http://example.com/search?q=test",
	}
	assert.True(t, detector.hasInputFeatures(queryTarget))

	// 测试有POST表单的目标
	postTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{
				Method: "POST",
				Fields: map[string]string{
					"data": "text",
				},
			},
		},
	}
	assert.True(t, detector.hasInputFeatures(postTarget))

	// 测试有JSON内容类型的目标
	jsonTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	}
	assert.True(t, detector.hasInputFeatures(jsonTarget))

	// 测试无输入功能的目标
	simpleTarget := &plugins.ScanTarget{
		Forms:   []plugins.FormInfo{},
		Headers: map[string]string{},
		URL:     "http://example.com/about",
	}
	assert.False(t, detector.hasInputFeatures(simpleTarget))
}

// TestInputValidationDetectorRiskScore 测试风险评分计算
func TestInputValidationDetectorRiskScore(t *testing.T) {
	detector := NewInputValidationDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 4.5)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestInputValidationDetectorLifecycle 测试检测器生命周期
func TestInputValidationDetectorLifecycle(t *testing.T) {
	detector := NewInputValidationDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
