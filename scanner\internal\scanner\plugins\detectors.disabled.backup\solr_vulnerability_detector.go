package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
	"scanner/pkg/logger"
)

// SolrVulnerabilityDetector Apache Solr漏洞检测器
// 专门检测Apache Solr相关的安全漏洞，包括CVE-2019-12409等
type SolrVulnerabilityDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	solrPorts          []int            // Solr常用端口
	solrPaths          []string         // Solr路径
	jmxPorts           []int            // JMX端口
	solrIndicators     []string         // Solr服务指示器
	jmxIndicators      []string         // JMX服务指示器
	vulnerabilityTests []string         // 漏洞测试载荷
	solrPatterns       []*regexp.Regexp // Solr响应模式
	jmxPatterns        []*regexp.Regexp // JMX响应模式
	httpClient         *http.Client
}

// NewSolrVulnerabilityDetector 创建Solr漏洞检测器
func NewSolrVulnerabilityDetector() *SolrVulnerabilityDetector {
	detector := &SolrVulnerabilityDetector{
		id:          "solr-vulnerability-detector",
		name:        "Apache Solr漏洞检测器",
		category:    "remote-code-execution",
		severity:    "Critical",
		cve:         []string{"CVE-2019-12409", "CVE-2019-0193", "CVE-2017-12629"},
		cwe:         []string{"CWE-94", "CWE-502", "CWE-284"},
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测Apache Solr服务的安全漏洞，包括JMX配置错误、远程代码执行等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         30 * time.Second,
			MaxRetries:      3,
			Concurrency:     5,
			RateLimit:       10,
			FollowRedirects: true,
			VerifySSL:       false,
			MaxResponseSize: 1024 * 1024, // 1MB
			Priority:        8,           // 高优先级
		},
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				if len(via) >= 10 {
					return fmt.Errorf("stopped after 10 redirects")
				}
				return nil
			},
		},
	}

	// 初始化检测规则
	detector.initializeRules()

	return detector
}

// initializeRules 初始化检测规则
func (d *SolrVulnerabilityDetector) initializeRules() {
	// Solr常用端口
	d.solrPorts = []int{8983, 8984, 7574, 9983, 8080, 8443, 9000}

	// JMX常用端口
	d.jmxPorts = []int{8983, 9999, 1099, 1098, 8999, 9998}

	// Solr路径
	d.solrPaths = []string{
		"/solr/",
		"/solr/admin/",
		"/solr/admin/info/system",
		"/solr/admin/cores",
		"/solr/admin/collections",
		"/solr/admin/info/properties",
		"/solr/admin/info/threads",
		"/solr/admin/info/logging",
		"/solr/admin/metrics",
		"/solr/admin/zookeeper",
		"/solr/#/",
		"/solr/select",
		"/solr/update",
		"/solr/schema",
		"/solr/config",
	}

	// Solr服务指示器
	d.solrIndicators = []string{
		"Apache Solr", "solr-impl", "solr-spec-version", "lucene-impl-version",
		"solr-admin", "SolrCore", "solr.xml", "solrconfig.xml", "schema.xml",
		"Solr Admin", "Solr Dashboard", "SolrCloud", "ZooKeeper",
		"lucene", "Lucene", "solr.home", "solr.data.dir", "solr.install.dir",
		"org.apache.solr", "solr.war", "start.jar", "jetty", "Jetty",
	}

	// JMX服务指示器
	d.jmxIndicators = []string{
		"jmxrmi", "JMX", "javax.management", "MBeanServer", "ObjectName",
		"MLet", "MLetMBean", "javax.management.loading", "java.lang:type=Runtime",
		"solr:type=admin", "solr:type=core", "solr:type=system",
	}

	// 漏洞测试载荷
	d.vulnerabilityTests = []string{
		// CVE-2019-12409 JMX相关测试
		"service:jmx:rmi:///jndi/rmi://target:8983/jmxrmi",
		"service:jmx:rmi:///jndi/rmi://target:9999/jmxrmi",
		"javax.management.loading:type=MLet",

		// Solr Core操作测试
		"action=CREATE&name=test&instanceDir=../../../",
		"action=RELOAD&core=collection1",
		"action=STATUS",
		"action=UNLOAD&core=test",

		// Solr配置访问测试
		"wt=json&indent=true",
		"q=*:*&wt=json",
		"stream.url=file:///etc/passwd",
		"stream.file=/etc/passwd",

		// 代码执行测试载荷
		"qt=/update&stream.body=<delete><query>*:*</query></delete>",
		"qt=/select&q=*:*&wt=velocity&v.template=custom",
		"qt=/admin/luke?numTerms=0",
	}

	// 编译正则表达式模式
	d.compilePatterns()
}

// compilePatterns 编译正则表达式模式
func (d *SolrVulnerabilityDetector) compilePatterns() {
	// Solr响应模式
	solrPatterns := []string{
		`(?i)apache\s+solr`,
		`(?i)solr-impl.*version`,
		`(?i)lucene-impl.*version`,
		`(?i)solr-spec-version`,
		`(?i)solr\.home`,
		`(?i)solr\.data\.dir`,
		`(?i)solrconfig\.xml`,
		`(?i)schema\.xml`,
		`(?i)org\.apache\.solr`,
		`(?i)SolrCore`,
		`(?i)SolrCloud`,
		`(?i)ZooKeeper.*ensemble`,
		`(?i)solr.*admin.*dashboard`,
		`(?i)collection.*status`,
		`(?i)core.*status`,
	}

	for _, pattern := range solrPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.solrPatterns = append(d.solrPatterns, compiled)
		}
	}

	// JMX响应模式
	jmxPatterns := []string{
		`(?i)javax\.management`,
		`(?i)MBeanServer`,
		`(?i)ObjectName`,
		`(?i)jmxrmi`,
		`(?i)MLet.*MBean`,
		`(?i)java\.lang:type=Runtime`,
		`(?i)solr:type=admin`,
		`(?i)solr:type=core`,
		`(?i)solr:type=system`,
		`(?i)javax\.management\.loading`,
		`(?i)java\.rmi\.server`,
		`(?i)sun\.management`,
	}

	for _, pattern := range jmxPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.jmxPatterns = append(d.jmxPatterns, compiled)
		}
	}
}

// GetID 获取检测器ID
func (d *SolrVulnerabilityDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *SolrVulnerabilityDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *SolrVulnerabilityDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *SolrVulnerabilityDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *SolrVulnerabilityDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *SolrVulnerabilityDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *SolrVulnerabilityDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *SolrVulnerabilityDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *SolrVulnerabilityDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *SolrVulnerabilityDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *SolrVulnerabilityDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// IsEnabled 检查是否启用
func (d *SolrVulnerabilityDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *SolrVulnerabilityDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// GetConfig 获取配置
func (d *SolrVulnerabilityDetector) GetConfig() *plugins.DetectorConfig {
	return d.config
}

// SetConfig 设置配置
func (d *SolrVulnerabilityDetector) SetConfig(config *plugins.DetectorConfig) {
	d.config = config
	d.updatedAt = time.Now()
}

// IsApplicable 检查是否适用于目标
func (d *SolrVulnerabilityDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Type != "web" && target.Type != "service" {
		return false
	}

	// 检查URL协议
	if target.URL != "" {
		if !strings.HasPrefix(target.URL, "http://") && !strings.HasPrefix(target.URL, "https://") {
			return false
		}
	}

	return true
}

// generateVulnID 生成漏洞ID
func (d *SolrVulnerabilityDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("%s-%s-%d", d.id, target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *SolrVulnerabilityDetector) calculateRiskScore(confidence float64) float64 {
	baseScore := 9.0 // 高风险基础分

	// 根据置信度调整分数
	riskScore := baseScore * confidence

	// 确保分数在合理范围内
	if riskScore > 10.0 {
		riskScore = 10.0
	} else if riskScore < 0.0 {
		riskScore = 0.0
	}

	return riskScore
}

// Detect 执行Solr漏洞检测
func (d *SolrVulnerabilityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	logger.Infof("开始Apache Solr漏洞检测: %s", target.URL)

	// 执行多种Solr漏洞检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. Solr服务检测
	solrEvidence, solrConfidence, solrPayload, solrRequest, solrResponse := d.detectSolrService(ctx, target)
	if solrConfidence > maxConfidence {
		maxConfidence = solrConfidence
		vulnerablePayload = solrPayload
		vulnerableRequest = solrRequest
		vulnerableResponse = solrResponse
	}
	evidence = append(evidence, solrEvidence...)

	// 2. JMX端口检测
	jmxEvidence, jmxConfidence, jmxPayload, jmxRequest, jmxResponse := d.detectJMXExposure(ctx, target)
	if jmxConfidence > maxConfidence {
		maxConfidence = jmxConfidence
		vulnerablePayload = jmxPayload
		vulnerableRequest = jmxRequest
		vulnerableResponse = jmxResponse
	}
	evidence = append(evidence, jmxEvidence...)

	// 3. CVE-2019-12409检测
	cveEvidence, cveConfidence, cvePayload, cveRequest, cveResponse := d.detectCVE201912409(ctx, target)
	if cveConfidence > maxConfidence {
		maxConfidence = cveConfidence
		vulnerablePayload = cvePayload
		vulnerableRequest = cveRequest
		vulnerableResponse = cveResponse
	}
	evidence = append(evidence, cveEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "Apache Solr安全漏洞",
		Description:       "检测到Apache Solr服务存在安全漏洞，可能导致远程代码执行",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "升级Solr版本，禁用JMX远程访问，配置访问控制",
		References:        []string{"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2019-12409", "https://issues.apache.org/jira/browse/SOLR-13669"},
		Tags:              []string{"solr", "jmx", "rce", "java", "apache", "cve-2019-12409"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *SolrVulnerabilityDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	logger.Infof("开始验证Apache Solr漏洞: %s", target.URL)

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	// 验证Solr服务
	if d.verifySolrService(ctx, target) {
		verificationConfidence += 0.3
		evidence = append(evidence, plugins.Evidence{
			Type:        "service_verification",
			Description: "确认Solr服务运行",
			Content:     "Solr服务响应正常",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 验证JMX暴露
	if d.verifyJMXExposure(ctx, target) {
		verificationConfidence += 0.4
		evidence = append(evidence, plugins.Evidence{
			Type:        "jmx_verification",
			Description: "确认JMX端口暴露",
			Content:     "JMX服务可访问",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	// 验证漏洞利用能力
	if d.verifyExploitability(ctx, target) {
		verificationConfidence += 0.3
		evidence = append(evidence, plugins.Evidence{
			Type:        "exploit_verification",
			Description: "确认漏洞可利用",
			Content:     "漏洞利用测试成功",
			Location:    target.URL,
			Timestamp:   time.Now(),
		})
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "solr-vulnerability-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("Solr漏洞验证完成，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// GetTargetTypes 获取支持的目标类型
func (d *SolrVulnerabilityDetector) GetTargetTypes() []string {
	return []string{"http", "https"}
}

// GetRequiredPorts 获取需要的端口
func (d *SolrVulnerabilityDetector) GetRequiredPorts() []int {
	return []int{8983, 8080, 80, 443}
}

// GetRequiredServices 获取需要的服务
func (d *SolrVulnerabilityDetector) GetRequiredServices() []string {
	return []string{"solr", "http", "https"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *SolrVulnerabilityDetector) GetRequiredHeaders() []string {
	return []string{}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *SolrVulnerabilityDetector) GetRequiredTechnologies() []string {
	return []string{"solr", "java"}
}

// GetDependencies 获取依赖的其他检测器ID
func (d *SolrVulnerabilityDetector) GetDependencies() []string {
	return []string{}
}

// GetConfiguration 获取检测器配置
func (d *SolrVulnerabilityDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置检测器配置
func (d *SolrVulnerabilityDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// Validate 验证检测器有效性
func (d *SolrVulnerabilityDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if d.config == nil {
		return fmt.Errorf("检测器配置不能为空")
	}
	return nil
}

// Initialize 初始化检测器
func (d *SolrVulnerabilityDetector) Initialize() error {
	// 验证配置
	if err := d.Validate(); err != nil {
		return err
	}

	// 更新HTTP客户端配置
	if d.config.Timeout > 0 {
		d.httpClient.Timeout = d.config.Timeout
	}

	return nil
}

// Cleanup 清理资源
func (d *SolrVulnerabilityDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}
