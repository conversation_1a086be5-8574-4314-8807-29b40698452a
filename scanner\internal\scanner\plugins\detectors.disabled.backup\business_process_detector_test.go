package detectors

import (
	"testing"
	"time"

	"scanner/internal/scanner/plugins"
)

// TestBusinessProcessDetectorBasicFunctionality 测试业务流程检测器基本功能
func TestBusinessProcessDetectorBasicFunctionality(t *testing.T) {
	detector := NewBusinessProcessDetector()

	// 测试基本信息
	if detector.GetID() != "business-process-comprehensive" {
		t.<PERSON>("Expected ID 'business-process-comprehensive', got '%s'", detector.GetID())
	}

	if detector.GetName() != "业务流程安全检测器" {
		t.<PERSON><PERSON><PERSON>("Expected name '业务流程安全检测器', got '%s'", detector.GetName())
	}

	if detector.GetCategory() != "web" {
		t.<PERSON>rf("Expected category 'web', got '%s'", detector.GetCategory())
	}

	if detector.GetSeverity() != "high" {
		t.<PERSON><PERSON>rf("Expected severity 'high', got '%s'", detector.GetSeverity())
	}

	if !detector.IsEnabled() {
		t.<PERSON>rror("Expected detector to be enabled by default")
	}

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	expectedTypes := []string{"http", "https"}
	if len(targetTypes) != len(expectedTypes) {
		t.Errorf("Expected %d target types, got %d", len(expectedTypes), len(targetTypes))
	}

	// 测试端口
	ports := detector.GetRequiredPorts()
	if len(ports) == 0 {
		t.Error("Expected some required ports")
	}

	// 测试服务
	services := detector.GetRequiredServices()
	expectedServices := []string{"http", "https", "web", "api"}
	if len(services) != len(expectedServices) {
		t.Errorf("Expected %d services, got %d", len(expectedServices), len(services))
	}
}

// TestBusinessProcessDetectorApplicability 测试业务流程检测器适用性
func TestBusinessProcessDetectorApplicability(t *testing.T) {
	detector := NewBusinessProcessDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "HTTP URL目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
			},
			expected: true,
		},
		{
			name: "有工作流字段的表单目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Forms: []plugins.FormInfo{
					{
						Action: "/workflow",
						Method: "POST",
						Fields: map[string]string{"step": "text", "status": "hidden"},
					},
				},
			},
			expected: true,
		},
		{
			name: "有业务流程技术栈的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Technologies: []plugins.TechnologyInfo{
					{Name: "Activiti", Version: "7.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有工作流头部的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Headers: map[string]string{
					"X-Workflow-Step": "1",
				},
			},
			expected: true,
		},
		{
			name: "有业务流程链接的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/workflow", Text: "Workflow Management"},
				},
			},
			expected: true,
		},
		{
			name: "有工作流Cookie的目标",
			target: &plugins.ScanTarget{
				Type:     "url",
				URL:      "https://example.com",
				Protocol: "https",
				Port:     443,
				Cookies: []plugins.CookieInfo{
					{Name: "workflow_session", Value: "step_1"},
				},
			},
			expected: true,
		},
		{
			name: "非Web目标",
			target: &plugins.ScanTarget{
				Type:     "ip",
				Protocol: "tcp",
				Port:     22,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.IsApplicable(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestBusinessProcessDetectorConfiguration 测试业务流程检测器配置
func TestBusinessProcessDetectorConfiguration(t *testing.T) {
	detector := NewBusinessProcessDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	if config.Timeout != 25*time.Second {
		t.Errorf("Expected timeout 25s, got %v", config.Timeout)
	}

	if config.MaxRetries != 2 {
		t.Errorf("Expected max retries 2, got %d", config.MaxRetries)
	}

	if config.Concurrency != 4 {
		t.Errorf("Expected concurrency 4, got %d", config.Concurrency)
	}

	if config.RateLimit != 4 {
		t.Errorf("Expected rate limit 4, got %d", config.RateLimit)
	}

	if !config.FollowRedirects {
		t.Error("Expected FollowRedirects to be true")
	}

	if config.VerifySSL {
		t.Error("Expected VerifySSL to be false")
	}

	if config.MaxResponseSize != 3*1024*1024 {
		t.Errorf("Expected max response size 3MB, got %d", config.MaxResponseSize)
	}

	// 测试配置更新
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         35 * time.Second,
		MaxRetries:      3,
		Concurrency:     6,
		RateLimit:       6,
		FollowRedirects: false,
		VerifySSL:       true,
		MaxResponseSize: 5 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	if err != nil {
		t.Errorf("Failed to set configuration: %v", err)
	}

	updatedConfig := detector.GetConfiguration()
	if updatedConfig.Timeout != 35*time.Second {
		t.Errorf("Expected updated timeout 35s, got %v", updatedConfig.Timeout)
	}
}

// TestBusinessProcessDetectorWorkflowPaths 测试工作流路径
func TestBusinessProcessDetectorWorkflowPaths(t *testing.T) {
	detector := NewBusinessProcessDetector()

	if len(detector.workflowPaths) == 0 {
		t.Error("Expected some workflow paths")
	}

	// 检查是否包含关键的工作流路径
	expectedPaths := []string{
		"/workflow",
		"/process",
		"/step",
		"/approval",
		"/status",
		"/流程",
		"/工作流",
		"/步骤",
		"/审批",
		"/状态",
	}

	for _, expected := range expectedPaths {
		found := false
		for _, path := range detector.workflowPaths {
			if path == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find workflow path '%s'", expected)
		}
	}
}

// TestBusinessProcessDetectorProcessSteps 测试流程步骤
func TestBusinessProcessDetectorProcessSteps(t *testing.T) {
	detector := NewBusinessProcessDetector()

	if len(detector.processSteps) == 0 {
		t.Error("Expected some process steps")
	}

	// 检查是否包含关键的流程步骤
	expectedSteps := []string{
		"1", "2", "3",
		"step1", "step2", "step3",
		"start", "submit", "approve",
		"开始", "提交", "审批",
	}

	for _, expected := range expectedSteps {
		found := false
		for _, step := range detector.processSteps {
			if step == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected to find process step '%s'", expected)
		}
	}
}

// TestBusinessProcessDetectorWorkflowPayloads 测试工作流载荷
func TestBusinessProcessDetectorWorkflowPayloads(t *testing.T) {
	detector := NewBusinessProcessDetector()

	if len(detector.workflowPayloads) == 0 {
		t.Error("Expected some workflow payloads")
	}

	// 检查是否包含不同类别的载荷
	var hasStepBypass, hasStatusManipulation, hasApprovalBypass, hasWorkflowInjection bool
	for _, payload := range detector.workflowPayloads {
		switch payload.Category {
		case "step_bypass":
			hasStepBypass = true
		case "status_manipulation":
			hasStatusManipulation = true
		case "approval_bypass":
			hasApprovalBypass = true
		case "workflow_injection":
			hasWorkflowInjection = true
		}
	}

	if !hasStepBypass {
		t.Error("Expected to find step bypass payloads")
	}
	if !hasStatusManipulation {
		t.Error("Expected to find status manipulation payloads")
	}
	if !hasApprovalBypass {
		t.Error("Expected to find approval bypass payloads")
	}
	if !hasWorkflowInjection {
		t.Error("Expected to find workflow injection payloads")
	}
}

// TestBusinessProcessDetectorPatterns 测试检测模式
func TestBusinessProcessDetectorPatterns(t *testing.T) {
	detector := NewBusinessProcessDetector()

	// 测试流程模式
	if len(detector.processPatterns) == 0 {
		t.Error("Expected some process patterns")
	}

	// 测试绕过模式
	if len(detector.bypassPatterns) == 0 {
		t.Error("Expected some bypass patterns")
	}

	// 测试安全模式
	if len(detector.securityPatterns) == 0 {
		t.Error("Expected some security patterns")
	}
}

// TestBusinessProcessDetectorBusinessProcessFeatures 测试业务流程功能检查
func TestBusinessProcessDetectorBusinessProcessFeatures(t *testing.T) {
	detector := NewBusinessProcessDetector()

	testCases := []struct {
		name     string
		target   *plugins.ScanTarget
		expected bool
	}{
		{
			name: "有工作流头部的目标",
			target: &plugins.ScanTarget{
				Headers: map[string]string{
					"X-Workflow-Step": "1",
				},
			},
			expected: true,
		},
		{
			name: "有业务流程技术栈的目标",
			target: &plugins.ScanTarget{
				Technologies: []plugins.TechnologyInfo{
					{Name: "Activiti", Version: "7.0", Confidence: 0.9},
				},
			},
			expected: true,
		},
		{
			name: "有工作流链接的目标",
			target: &plugins.ScanTarget{
				Links: []plugins.LinkInfo{
					{URL: "https://example.com/workflow", Text: "Workflow"},
				},
			},
			expected: true,
		},
		{
			name: "有工作流Cookie的目标",
			target: &plugins.ScanTarget{
				Cookies: []plugins.CookieInfo{
					{Name: "workflow_session", Value: "step_1"},
				},
			},
			expected: true,
		},
		{
			name: "普通目标",
			target: &plugins.ScanTarget{
				URL: "https://example.com",
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.hasBusinessProcessFeatures(tc.target)
			if result != tc.expected {
				t.Errorf("Expected %v, got %v for target %s", tc.expected, result, tc.name)
			}
		})
	}
}

// TestBusinessProcessDetectorRiskScore 测试风险评分
func TestBusinessProcessDetectorRiskScore(t *testing.T) {
	detector := NewBusinessProcessDetector()

	testCases := []struct {
		confidence float64
		expected   float64
	}{
		{0.0, 0.0},
		{0.5, 3.75},
		{0.8, 6.0},
		{1.0, 7.5},
	}

	for _, tc := range testCases {
		score := detector.calculateRiskScore(tc.confidence)
		// 使用浮点数比较的容差
		if score < tc.expected-0.01 || score > tc.expected+0.01 {
			t.Errorf("Expected risk score %.2f for confidence %.1f, got %.2f", tc.expected, tc.confidence, score)
		}
	}
}

// TestBusinessProcessDetectorLifecycle 测试检测器生命周期
func TestBusinessProcessDetectorLifecycle(t *testing.T) {
	detector := NewBusinessProcessDetector()

	// 测试初始化
	err := detector.Initialize()
	if err != nil {
		t.Errorf("Failed to initialize detector: %v", err)
	}

	// 测试验证
	err = detector.Validate()
	if err != nil {
		t.Errorf("Detector validation failed: %v", err)
	}

	// 测试启用/禁用
	detector.SetEnabled(false)
	if detector.IsEnabled() {
		t.Error("Expected detector to be disabled")
	}

	detector.SetEnabled(true)
	if !detector.IsEnabled() {
		t.Error("Expected detector to be enabled")
	}

	// 测试清理
	err = detector.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup detector: %v", err)
	}
}
