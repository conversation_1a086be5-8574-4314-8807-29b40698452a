package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestSSTIDetectorBasicFunctionality 测试SSTI检测器基础功能
func TestSSTIDetectorBasicFunctionality(t *testing.T) {
	detector := NewSSTIDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "ssti-comprehensive", detector.GetID())
	assert.Equal(t, "SSTI服务端模板注入检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "critical", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-94")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestSSTIDetectorApplicability 测试SSTI检测器适用性
func TestSSTIDetectorApplicability(t *testing.T) {
	detector := NewSSTIDetector()

	// 测试有模板引擎的目标
	templateTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Server": "Flask/2.0.1",
		},
	}
	assert.True(t, detector.IsApplicable(templateTarget))

	// 测试有模板技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Jinja2", Version: "3.0", Confidence: 0.9},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Action: "/search", Method: "POST", Fields: map[string]string{"q": "text"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试普通Web目标（SSTI是通用漏洞）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestSSTIDetectorConfiguration 测试SSTI检测器配置
func TestSSTIDetectorConfiguration(t *testing.T) {
	detector := NewSSTIDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 20*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         25 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 25*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestSSTIDetectorTemplateEngines 测试SSTI检测器模板引擎
func TestSSTIDetectorTemplateEngines(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查模板引擎列表
	assert.NotEmpty(t, detector.templateEngines)
	assert.Greater(t, len(detector.templateEngines), 30)

	// 检查Python模板引擎
	assert.Contains(t, detector.templateEngines, "jinja2")
	assert.Contains(t, detector.templateEngines, "django")
	assert.Contains(t, detector.templateEngines, "mako")

	// 检查PHP模板引擎
	assert.Contains(t, detector.templateEngines, "twig")
	assert.Contains(t, detector.templateEngines, "smarty")
	assert.Contains(t, detector.templateEngines, "blade")

	// 检查Java模板引擎
	assert.Contains(t, detector.templateEngines, "freemarker")
	assert.Contains(t, detector.templateEngines, "velocity")
	assert.Contains(t, detector.templateEngines, "thymeleaf")

	// 检查JavaScript模板引擎
	assert.Contains(t, detector.templateEngines, "handlebars")
	assert.Contains(t, detector.templateEngines, "mustache")
	assert.Contains(t, detector.templateEngines, "ejs")

	// 检查Ruby模板引擎
	assert.Contains(t, detector.templateEngines, "erb")
	assert.Contains(t, detector.templateEngines, "haml")
	assert.Contains(t, detector.templateEngines, "liquid")
}

// TestSSTIDetectorMathPayloads 测试SSTI检测器数学载荷
func TestSSTIDetectorMathPayloads(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查数学载荷列表
	assert.NotEmpty(t, detector.mathPayloads)
	assert.Greater(t, len(detector.mathPayloads), 30)

	// 检查基础数学表达式
	assert.Contains(t, detector.mathPayloads, "{{7*7}}")
	assert.Contains(t, detector.mathPayloads, "${7*7}")
	assert.Contains(t, detector.mathPayloads, "<%=7*7%>")

	// 检查Jinja2载荷
	assert.Contains(t, detector.mathPayloads, "{{7*'7'}}")
	assert.Contains(t, detector.mathPayloads, "{{49-42}}")
	assert.Contains(t, detector.mathPayloads, "{{7**2}}")

	// 检查Twig载荷
	assert.Contains(t, detector.mathPayloads, "{{7~7}}")

	// 检查Smarty载荷
	assert.Contains(t, detector.mathPayloads, "{7*7}")
	assert.Contains(t, detector.mathPayloads, "{math equation=\"7*7\"}")

	// 检查Freemarker载荷
	assert.Contains(t, detector.mathPayloads, "${7+7}")
	assert.Contains(t, detector.mathPayloads, "<#assign x=7*7>${x}")

	// 检查Velocity载荷
	assert.Contains(t, detector.mathPayloads, "#set($x=7*7)$x")
}

// TestSSTIDetectorConfigPayloads 测试SSTI检测器配置载荷
func TestSSTIDetectorConfigPayloads(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查配置载荷列表
	assert.NotEmpty(t, detector.configPayloads)
	assert.Greater(t, len(detector.configPayloads), 40)

	// 检查Jinja2/Flask配置
	assert.Contains(t, detector.configPayloads, "{{config}}")
	assert.Contains(t, detector.configPayloads, "{{config.items()}}")
	assert.Contains(t, detector.configPayloads, "{{config['SECRET_KEY']}}")
	assert.Contains(t, detector.configPayloads, "{{request}}")
	assert.Contains(t, detector.configPayloads, "{{session}}")

	// 检查Django配置
	assert.Contains(t, detector.configPayloads, "{{settings}}")
	assert.Contains(t, detector.configPayloads, "{{settings.SECRET_KEY}}")

	// 检查Twig配置
	assert.Contains(t, detector.configPayloads, "{{_self}}")
	assert.Contains(t, detector.configPayloads, "{{_context}}")
	assert.Contains(t, detector.configPayloads, "{{dump()}}")

	// 检查Smarty配置
	assert.Contains(t, detector.configPayloads, "{$smarty}")
	assert.Contains(t, detector.configPayloads, "{$smarty.version}")

	// 检查Freemarker配置
	assert.Contains(t, detector.configPayloads, "${.data_model}")
	assert.Contains(t, detector.configPayloads, "${.globals}")
	assert.Contains(t, detector.configPayloads, "${.template_name}")
}

// TestSSTIDetectorCodePayloads 测试SSTI检测器代码载荷
func TestSSTIDetectorCodePayloads(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查代码载荷列表
	assert.NotEmpty(t, detector.codePayloads)
	assert.Greater(t, len(detector.codePayloads), 30)

	// 检查Jinja2代码执行
	assert.Contains(t, detector.codePayloads, "{{config.__class__.__init__.__globals__['os'].popen('id').read()}}")
	assert.Contains(t, detector.codePayloads, "{{lipsum.__globals__['os'].popen('id').read()}}")

	// 检查Smarty代码执行
	assert.Contains(t, detector.codePayloads, "{php}echo `id`;{/php}")
	assert.Contains(t, detector.codePayloads, "{php}system('id');{/php}")

	// 检查Freemarker代码执行
	assert.Contains(t, detector.codePayloads, "<#assign ex=\"freemarker.template.utility.Execute\"?new()>${ex(\"id\")}")

	// 检查ERB代码执行
	assert.Contains(t, detector.codePayloads, "<%=`id`%>")
	assert.Contains(t, detector.codePayloads, "<%=system('id')%>")

	// 检查JSP代码执行
	assert.Contains(t, detector.codePayloads, "<%Runtime.getRuntime().exec(\"id\");%>")

	// 检查Thymeleaf代码执行
	assert.Contains(t, detector.codePayloads, "[[${T(java.lang.Runtime).getRuntime().exec('id')}]]")
}

// TestSSTIDetectorErrorPayloads 测试SSTI检测器错误载荷
func TestSSTIDetectorErrorPayloads(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查错误载荷列表
	assert.NotEmpty(t, detector.errorPayloads)
	assert.Greater(t, len(detector.errorPayloads), 40)

	// 检查语法错误
	assert.Contains(t, detector.errorPayloads, "{{")
	assert.Contains(t, detector.errorPayloads, "}}")
	assert.Contains(t, detector.errorPayloads, "${")
	assert.Contains(t, detector.errorPayloads, "<%")

	// 检查Jinja2错误
	assert.Contains(t, detector.errorPayloads, "{{undefined_variable}}")
	assert.Contains(t, detector.errorPayloads, "{{''|nonexistent_filter}}")

	// 检查Twig错误
	assert.Contains(t, detector.errorPayloads, "{{dump(undefined)}}")

	// 检查Smarty错误
	assert.Contains(t, detector.errorPayloads, "{$undefined_variable}")
	assert.Contains(t, detector.errorPayloads, "{nonexistent_function}")

	// 检查Freemarker错误
	assert.Contains(t, detector.errorPayloads, "${undefined_variable}")
	assert.Contains(t, detector.errorPayloads, "<#assign x=undefined_variable>")

	// 检查Velocity错误
	assert.Contains(t, detector.errorPayloads, "$undefined_variable")
	assert.Contains(t, detector.errorPayloads, "#set($x=$undefined_variable)")
}

// TestSSTIDetectorBlindPayloads 测试SSTI检测器盲注载荷
func TestSSTIDetectorBlindPayloads(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查盲注载荷列表
	assert.NotEmpty(t, detector.blindPayloads)
	assert.Greater(t, len(detector.blindPayloads), 15)

	// 检查时间延迟
	assert.Contains(t, detector.blindPayloads, "{{''.__class__.__mro__[2].__subclasses__()[59].__init__.__globals__['time'].sleep(5)}}")
	assert.Contains(t, detector.blindPayloads, "${T(java.lang.Thread).sleep(5000)}")
	assert.Contains(t, detector.blindPayloads, "<%Thread.sleep(5000);%>")

	// 检查DNS查询
	assert.Contains(t, detector.blindPayloads, "${T(java.net.InetAddress).getByName('test.example.com')}")

	// 检查HTTP请求
	assert.Contains(t, detector.blindPayloads, "${T(java.net.URL).new('http://test.example.com').openConnection()}")

	// 检查文件操作
	assert.Contains(t, detector.blindPayloads, "${T(java.io.File).new('/tmp/ssti_test').createNewFile()}")
}

// TestSSTIDetectorPatterns 测试SSTI检测器模式
func TestSSTIDetectorPatterns(t *testing.T) {
	detector := NewSSTIDetector()

	// 检查模板模式
	assert.NotEmpty(t, detector.templatePatterns)
	assert.Greater(t, len(detector.templatePatterns), 20)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 15)
}

// TestSSTIDetectorTemplateFeatures 测试模板功能检查
func TestSSTIDetectorTemplateFeatures(t *testing.T) {
	detector := NewSSTIDetector()

	// 测试有模板头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Server": "Flask/2.0.1",
		},
	}
	assert.True(t, detector.hasTemplateFeatures(headerTarget))

	// 测试有模板技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Django", Version: "3.2", Confidence: 0.9},
		},
	}
	assert.True(t, detector.hasTemplateFeatures(techTarget))

	// 测试有模板链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/templates", Text: "Templates"},
		},
	}
	assert.True(t, detector.hasTemplateFeatures(linkTarget))

	// 测试无模板功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
	}
	assert.False(t, detector.hasTemplateFeatures(simpleTarget))
}

// TestSSTIDetectorRiskScore 测试风险评分计算
func TestSSTIDetectorRiskScore(t *testing.T) {
	detector := NewSSTIDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 8.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 5.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestSSTIDetectorLifecycle 测试检测器生命周期
func TestSSTIDetectorLifecycle(t *testing.T) {
	detector := NewSSTIDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
