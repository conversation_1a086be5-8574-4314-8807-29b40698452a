package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestCSRFDetectorBasicFunctionality 测试CSRF检测器基础功能
func TestCSRFDetectorBasicFunctionality(t *testing.T) {
	detector := NewCSRFDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "csrf-comprehensive", detector.GetID())
	assert.Equal(t, "跨站请求伪造(CSRF)综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "medium", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-352")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestCSRFDetectorApplicability 测试CSRF检测器适用性
func TestCSRFDetectorApplicability(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{
				Action: "/login",
				Method: "POST",
				Fields: map[string]string{
					"username": "text",
					"password": "password",
				},
			},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有状态改变操作链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{
				URL:  "http://example.com/delete/user/123",
				Text: "Delete User",
			},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试管理页面URL
	adminTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin/dashboard",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(adminTarget))

	// 测试API端点
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/users",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))

	// 测试普通静态页面
	staticTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/about.html",
		Protocol: "http",
		Port:     80,
	}
	assert.False(t, detector.IsApplicable(staticTarget))
}

// TestCSRFDetectorConfiguration 测试CSRF检测器配置
func TestCSRFDetectorConfiguration(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 10*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.True(t, config.FollowRedirects) // CSRF检测跟随重定向

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         15 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       15,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 15*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestCSRFDetectorPatterns 测试CSRF检测器模式
func TestCSRFDetectorPatterns(t *testing.T) {
	detector := NewCSRFDetector()

	// 检查CSRF令牌模式
	assert.NotEmpty(t, detector.csrfTokenPatterns)
	assert.Greater(t, len(detector.csrfTokenPatterns), 20)

	// 检查表单模式
	assert.NotEmpty(t, detector.formPatterns)
	assert.Greater(t, len(detector.formPatterns), 3)

	// 检查Cookie模式
	assert.NotEmpty(t, detector.cookiePatterns)
	assert.Greater(t, len(detector.cookiePatterns), 3)

	// 检查头部模式
	assert.NotEmpty(t, detector.headerPatterns)
	assert.Greater(t, len(detector.headerPatterns), 5)

	// 检查特定的CSRF令牌模式
	assert.Contains(t, detector.csrfTokenPatterns, "csrf_token")
	assert.Contains(t, detector.csrfTokenPatterns, "_token")
	assert.Contains(t, detector.csrfTokenPatterns, "authenticity_token")
	assert.Contains(t, detector.csrfTokenPatterns, "__requestverificationtoken")
}

// TestCSRFDetectorFormDetection 测试表单检测
func TestCSRFDetectorFormDetection(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试包含表单的响应
	formResponse := `
	<html>
	<body>
		<form action="/login" method="post">
			<input type="text" name="username">
			<input type="password" name="password">
			<input type="submit" value="Login">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.containsForms(formResponse))

	// 测试不包含表单的响应
	noFormResponse := `
	<html>
	<body>
		<h1>Welcome</h1>
		<p>This is a static page.</p>
	</body>
	</html>
	`
	assert.False(t, detector.containsForms(noFormResponse))
}

// TestCSRFDetectorTokenDetection 测试CSRF令牌检测
func TestCSRFDetectorTokenDetection(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试包含CSRF令牌的响应
	tokenResponse := `
	<html>
	<body>
		<form action="/login" method="post">
			<input type="hidden" name="csrf_token" value="abc123">
			<input type="text" name="username">
			<input type="password" name="password">
			<input type="submit" value="Login">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.checkCSRFTokenInResponse(tokenResponse))

	// 测试包含其他类型令牌的响应
	authTokenResponse := `
	<html>
	<body>
		<form action="/submit" method="post">
			<input type="hidden" name="authenticity_token" value="xyz789">
			<input type="text" name="data">
			<input type="submit" value="Submit">
		</form>
	</body>
	</html>
	`
	assert.True(t, detector.checkCSRFTokenInResponse(authTokenResponse))

	// 测试不包含CSRF令牌的响应
	noTokenResponse := `
	<html>
	<body>
		<form action="/login" method="post">
			<input type="text" name="username">
			<input type="password" name="password">
			<input type="submit" value="Login">
		</form>
	</body>
	</html>
	`
	assert.False(t, detector.checkCSRFTokenInResponse(noTokenResponse))
}

// TestCSRFDetectorFormCSRFProtection 测试表单CSRF保护检查
func TestCSRFDetectorFormCSRFProtection(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试有CSRF保护的表单
	protectedForm := plugins.FormInfo{
		Action: "/submit",
		Method: "POST",
		Fields: map[string]string{
			"csrf_token": "hidden",
			"username":   "text",
			"password":   "password",
		},
	}
	assert.True(t, detector.checkFormCSRFProtection(protectedForm))

	// 测试有其他类型令牌的表单
	authForm := plugins.FormInfo{
		Action: "/login",
		Method: "POST",
		Fields: map[string]string{
			"authenticity_token": "hidden",
			"email":              "email",
			"password":           "password",
		},
	}
	assert.True(t, detector.checkFormCSRFProtection(authForm))

	// 测试没有CSRF保护的表单
	unprotectedForm := plugins.FormInfo{
		Action: "/contact",
		Method: "POST",
		Fields: map[string]string{
			"name":    "text",
			"email":   "email",
			"message": "textarea",
		},
	}
	assert.False(t, detector.checkFormCSRFProtection(unprotectedForm))
}

// TestCSRFDetectorResponseComparison 测试响应比较
func TestCSRFDetectorResponseComparison(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试相似的响应
	resp1 := "This is a test response with some content."
	resp2 := "This is a test response with some content!"
	assert.True(t, detector.compareResponses(resp1, resp2))

	// 测试相同的响应
	resp3 := "Identical response"
	resp4 := "Identical response"
	assert.True(t, detector.compareResponses(resp3, resp4))

	// 测试差异较大的响应
	resp5 := "Short response"
	resp6 := "This is a much longer response with significantly more content that should be detected as different"
	assert.False(t, detector.compareResponses(resp5, resp6))
}

// TestCSRFDetectorRiskScore 测试风险评分计算
func TestCSRFDetectorRiskScore(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 5.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 3.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestCSRFDetectorLifecycle 测试检测器生命周期
func TestCSRFDetectorLifecycle(t *testing.T) {
	detector := NewCSRFDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}

// BenchmarkCSRFDetectorTokenCheck 基准测试令牌检查性能
func BenchmarkCSRFDetectorTokenCheck(b *testing.B) {
	detector := NewCSRFDetector()
	response := `
	<html>
	<body>
		<form action="/login" method="post">
			<input type="hidden" name="csrf_token" value="abc123">
			<input type="text" name="username">
			<input type="password" name="password">
			<input type="submit" value="Login">
		</form>
	</body>
	</html>
	`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.checkCSRFTokenInResponse(response)
	}
}

// BenchmarkCSRFDetectorFormCheck 基准测试表单检查性能
func BenchmarkCSRFDetectorFormCheck(b *testing.B) {
	detector := NewCSRFDetector()
	response := `
	<html>
	<body>
		<form action="/login" method="post">
			<input type="text" name="username">
			<input type="password" name="password">
			<input type="submit" value="Login">
		</form>
	</body>
	</html>
	`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		detector.containsForms(response)
	}
}
