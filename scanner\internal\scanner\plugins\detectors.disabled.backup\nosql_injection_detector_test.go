package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestNoSQLInjectionDetectorBasicFunctionality 测试NoSQL注入检测器基础功能
func TestNoSQLInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewNoSQLInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "nosql-injection-comprehensive", detector.GetID())
	assert.Equal(t, "NoSQL注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-943")
	assert.Contains(t, detector.GetCVE(), "CVE-2021-22911")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 8080)
	assert.Contains(t, ports, 8443)
	assert.Contains(t, ports, 27017) // MongoDB
	assert.Contains(t, ports, 5984)  // CouchDB
	assert.Contains(t, ports, 6379)  // Redis
	assert.Contains(t, ports, 9042)  // Cassandra
	assert.Contains(t, ports, 9200)  // Elasticsearch

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestNoSQLInjectionDetectorApplicability 测试NoSQL注入检测器适用性
func TestNoSQLInjectionDetectorApplicability(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 测试有NoSQL功能的目标
	nosqlTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Server":         "MongoDB/4.4.0",
			"X-Powered-By":   "Express",
		},
	}
	assert.True(t, detector.IsApplicable(nosqlTarget))

	// 测试有NoSQL技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "MongoDB", Version: "4.4.0", Confidence: 0.9},
			{Name: "Node.js", Version: "14.17.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有NoSQL链接的目标
	linkTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/home",
		Protocol: "http",
		Port:     80,
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api/data", Text: "Data API"},
		},
	}
	assert.True(t, detector.IsApplicable(linkTarget))

	// 测试有NoSQL表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"query": "text", "filter": "hidden"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search?q=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试API相关URL
	apiTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/api/mongo",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(apiTarget))

	// 测试普通Web目标（NoSQL注入是通用问题）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestNoSQLInjectionDetectorConfiguration 测试NoSQL注入检测器配置
func TestNoSQLInjectionDetectorConfiguration(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 4, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     5,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 5, updatedConfig.Concurrency)
}

// TestNoSQLInjectionDetectorMongoPayloads 测试MongoDB载荷
func TestNoSQLInjectionDetectorMongoPayloads(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查MongoDB载荷列表
	assert.NotEmpty(t, detector.mongoPayloads)
	assert.GreaterOrEqual(t, len(detector.mongoPayloads), 50)

	// 检查基础MongoDB操作符注入
	assert.Contains(t, detector.mongoPayloads, `{"$ne": null}`)
	assert.Contains(t, detector.mongoPayloads, `{"$ne": ""}`)
	assert.Contains(t, detector.mongoPayloads, `{"$gt": ""}`)
	assert.Contains(t, detector.mongoPayloads, `{"$exists": true}`)

	// 检查MongoDB正则表达式注入
	assert.Contains(t, detector.mongoPayloads, `{"$regex": ".*"}`)
	assert.Contains(t, detector.mongoPayloads, `{"$regex": "^.*"}`)

	// 检查MongoDB where条件注入
	assert.Contains(t, detector.mongoPayloads, `{"$where": "1==1"}`)
	assert.Contains(t, detector.mongoPayloads, `{"$where": "return true"}`)

	// 检查MongoDB逻辑操作符注入
	assert.Contains(t, detector.mongoPayloads, `{"$or": [{"username": "admin"}, {"username": "test"}]}`)
	assert.Contains(t, detector.mongoPayloads, `{"$and": [{"username": {"$ne": ""}}, {"password": {"$ne": ""}}]}`)

	// 检查MongoDB数组操作符注入
	assert.Contains(t, detector.mongoPayloads, `{"$in": ["admin", "test", "user"]}`)
	assert.Contains(t, detector.mongoPayloads, `{"$nin": ["invalid"]}`)

	// 检查操作符格式注入
	assert.Contains(t, detector.mongoPayloads, `[$ne]=null`)
	assert.Contains(t, detector.mongoPayloads, `[$gt]=`)

	// 检查JavaScript注入载荷
	assert.Contains(t, detector.mongoPayloads, `'; return true; var x='`)
	assert.Contains(t, detector.mongoPayloads, `'; return 1==1; var x='`)

	// 检查布尔盲注载荷
	assert.Contains(t, detector.mongoPayloads, `true`)
	assert.Contains(t, detector.mongoPayloads, `false`)
	assert.Contains(t, detector.mongoPayloads, `1==1`)

	// 检查中文MongoDB载荷
	assert.Contains(t, detector.mongoPayloads, `{"用户名": {"$ne": null}}`)
	assert.Contains(t, detector.mongoPayloads, `{"密码": {"$gt": ""}}`)
}

// TestNoSQLInjectionDetectorCouchDBPayloads 测试CouchDB载荷
func TestNoSQLInjectionDetectorCouchDBPayloads(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查CouchDB载荷列表
	assert.NotEmpty(t, detector.couchdbPayloads)
	assert.GreaterOrEqual(t, len(detector.couchdbPayloads), 15)

	// 检查CouchDB选择器注入
	assert.Contains(t, detector.couchdbPayloads, `{"selector": {"_id": {"$gt": null}}}`)
	assert.Contains(t, detector.couchdbPayloads, `{"selector": {"_id": {"$ne": null}}}`)

	// 检查CouchDB查询注入
	assert.Contains(t, detector.couchdbPayloads, `{"keys": ["_all_docs"]}`)
	assert.Contains(t, detector.couchdbPayloads, `{"include_docs": true}`)

	// 检查CouchDB视图注入
	assert.Contains(t, detector.couchdbPayloads, `{"map": "function(doc) { emit(doc._id, doc); }"}`)
	assert.Contains(t, detector.couchdbPayloads, `{"reduce": "_count"}`)

	// 检查CouchDB Mango查询注入
	assert.Contains(t, detector.couchdbPayloads, `{"selector": {"$and": [{"username": {"$gt": ""}}, {"password": {"$gt": ""}}]}}`)
	assert.Contains(t, detector.couchdbPayloads, `{"selector": {"$or": [{"username": "admin"}, {"username": "test"}]}}`)

	// 检查中文CouchDB载荷
	assert.Contains(t, detector.couchdbPayloads, `{"选择器": {"用户名": {"$ne": null}}}`)
	assert.Contains(t, detector.couchdbPayloads, `{"查询": {"密码": {"$gt": ""}}}`)
}

// TestNoSQLInjectionDetectorRedisPayloads 测试Redis载荷
func TestNoSQLInjectionDetectorRedisPayloads(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查Redis载荷列表
	assert.NotEmpty(t, detector.redisPayloads)
	assert.GreaterOrEqual(t, len(detector.redisPayloads), 20)

	// 检查Redis命令注入
	assert.Contains(t, detector.redisPayloads, `*\r\nFLUSHALL\r\n*`)
	assert.Contains(t, detector.redisPayloads, `*\r\nFLUSHDB\r\n*`)
	assert.Contains(t, detector.redisPayloads, `*\r\nKEYS *\r\n*`)

	// 检查Redis SET命令注入
	assert.Contains(t, detector.redisPayloads, `*\r\nSET test "injected"\r\n*`)
	assert.Contains(t, detector.redisPayloads, `*\r\nSETEX test 60 "injected"\r\n*`)

	// 检查Redis GET命令注入
	assert.Contains(t, detector.redisPayloads, `*\r\nGET *\r\n*`)
	assert.Contains(t, detector.redisPayloads, `*\r\nMGET *\r\n*`)

	// 检查Redis EVAL命令注入
	assert.Contains(t, detector.redisPayloads, `*\r\nEVAL "return 1" 0\r\n*`)
	assert.Contains(t, detector.redisPayloads, `*\r\nEVAL "redis.call('FLUSHALL')" 0\r\n*`)

	// 检查Redis管道注入
	assert.Contains(t, detector.redisPayloads, `\r\n+OK\r\n`)
	assert.Contains(t, detector.redisPayloads, `\r\n-ERR\r\n`)

	// 检查中文Redis载荷
	assert.Contains(t, detector.redisPayloads, `*\r\n设置 测试 "注入"\r\n*`)
	assert.Contains(t, detector.redisPayloads, `*\r\n获取 *\r\n*`)
}

// TestNoSQLInjectionDetectorCassandraPayloads 测试Cassandra载荷
func TestNoSQLInjectionDetectorCassandraPayloads(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查Cassandra载荷列表
	assert.NotEmpty(t, detector.cassandraPayloads)
	assert.GreaterOrEqual(t, len(detector.cassandraPayloads), 15)

	// 检查Cassandra CQL注入
	assert.Contains(t, detector.cassandraPayloads, `' OR 1=1 --`)
	assert.Contains(t, detector.cassandraPayloads, `' OR '1'='1`)
	assert.Contains(t, detector.cassandraPayloads, `' UNION SELECT * FROM system.local --`)

	// 检查Cassandra函数注入
	assert.Contains(t, detector.cassandraPayloads, `'; DROP KEYSPACE test; --`)
	assert.Contains(t, detector.cassandraPayloads, `'; CREATE KEYSPACE test; --`)

	// 检查Cassandra时间函数注入
	assert.Contains(t, detector.cassandraPayloads, `' OR now() = now() --`)
	assert.Contains(t, detector.cassandraPayloads, `' OR dateOf(now()) = dateOf(now()) --`)

	// 检查Cassandra集合注入
	assert.Contains(t, detector.cassandraPayloads, `' OR token(id) > 0 --`)
	assert.Contains(t, detector.cassandraPayloads, `' OR writetime(column) > 0 --`)

	// 检查中文Cassandra载荷
	assert.Contains(t, detector.cassandraPayloads, `' OR 用户名 = '管理员' --`)
	assert.Contains(t, detector.cassandraPayloads, `' OR 密码 = '密码' --`)
}

// TestNoSQLInjectionDetectorElasticPayloads 测试Elasticsearch载荷
func TestNoSQLInjectionDetectorElasticPayloads(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查Elasticsearch载荷列表
	assert.NotEmpty(t, detector.elasticPayloads)
	assert.GreaterOrEqual(t, len(detector.elasticPayloads), 10)

	// 检查Elasticsearch查询注入
	assert.Contains(t, detector.elasticPayloads, `{"query": {"match_all": {}}}`)
	assert.Contains(t, detector.elasticPayloads, `{"query": {"bool": {"must": [{"match_all": {}}]}}}`)
	assert.Contains(t, detector.elasticPayloads, `{"query": {"wildcard": {"field": "*"}}}`)

	// 检查Elasticsearch脚本注入
	assert.Contains(t, detector.elasticPayloads, `{"script": {"source": "Math.log(params.factor * doc['field'].value)", "params": {"factor": 2}}}`)
	assert.Contains(t, detector.elasticPayloads, `{"script": {"source": "1==1"}}`)

	// 检查Elasticsearch聚合注入
	assert.Contains(t, detector.elasticPayloads, `{"aggs": {"test": {"terms": {"field": "_index"}}}}`)
	assert.Contains(t, detector.elasticPayloads, `{"aggs": {"test": {"cardinality": {"field": "_id"}}}}`)

	// 检查中文Elasticsearch载荷
	assert.Contains(t, detector.elasticPayloads, `{"查询": {"匹配所有": {}}}`)
	assert.Contains(t, detector.elasticPayloads, `{"脚本": {"源码": "1==1"}}`)
}

// TestNoSQLInjectionDetectorTestParameters 测试参数列表
func TestNoSQLInjectionDetectorTestParameters(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.GreaterOrEqual(t, len(detector.testParameters), 100)

	// 检查数据库相关参数
	assert.Contains(t, detector.testParameters, "query")
	assert.Contains(t, detector.testParameters, "search")
	assert.Contains(t, detector.testParameters, "find")
	assert.Contains(t, detector.testParameters, "filter")
	assert.Contains(t, detector.testParameters, "data")
	assert.Contains(t, detector.testParameters, "document")

	// 检查用户相关参数
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "password")
	assert.Contains(t, detector.testParameters, "login")

	// 检查查询相关参数
	assert.Contains(t, detector.testParameters, "q")
	assert.Contains(t, detector.testParameters, "sort")
	assert.Contains(t, detector.testParameters, "limit")
	assert.Contains(t, detector.testParameters, "offset")

	// 检查条件相关参数
	assert.Contains(t, detector.testParameters, "condition")
	assert.Contains(t, detector.testParameters, "criteria")
	assert.Contains(t, detector.testParameters, "where")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "查询")
	assert.Contains(t, detector.testParameters, "搜索")
	assert.Contains(t, detector.testParameters, "数据")
	assert.Contains(t, detector.testParameters, "用户")
}

// TestNoSQLInjectionDetectorPatterns 测试模式
func TestNoSQLInjectionDetectorPatterns(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 检查MongoDB模式
	assert.NotEmpty(t, detector.mongoPatterns)
	assert.GreaterOrEqual(t, len(detector.mongoPatterns), 15)

	// 检查CouchDB模式
	assert.NotEmpty(t, detector.couchdbPatterns)
	assert.GreaterOrEqual(t, len(detector.couchdbPatterns), 15)

	// 检查Redis模式
	assert.NotEmpty(t, detector.redisPatterns)
	assert.GreaterOrEqual(t, len(detector.redisPatterns), 10)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.GreaterOrEqual(t, len(detector.errorPatterns), 15)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.GreaterOrEqual(t, len(detector.responsePatterns), 15)
}

// TestNoSQLInjectionDetectorNoSQLFeatures 测试NoSQL功能检查
func TestNoSQLInjectionDetectorNoSQLFeatures(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 测试有NoSQL头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Server":       "MongoDB/4.4.0",
			"X-Powered-By": "Express",
		},
	}
	assert.True(t, detector.hasNoSQLFeatures(headerTarget))

	// 测试有NoSQL技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "MongoDB", Version: "4.4.0", Confidence: 0.9},
			{Name: "CouchDB", Version: "3.1.0", Confidence: 0.8},
		},
	}
	assert.True(t, detector.hasNoSQLFeatures(techTarget))

	// 测试有NoSQL链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/api/data", Text: "Data API"},
		},
	}
	assert.True(t, detector.hasNoSQLFeatures(linkTarget))

	// 测试有NoSQL表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"query": "text", "search": "hidden"}},
		},
	}
	assert.True(t, detector.hasNoSQLFeatures(formTarget))

	// 测试无NoSQL功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasNoSQLFeatures(simpleTarget))
}

// TestNoSQLInjectionDetectorRiskScore 测试风险评分计算
func TestNoSQLInjectionDetectorRiskScore(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 2.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestNoSQLInjectionDetectorLifecycle 测试检测器生命周期
func TestNoSQLInjectionDetectorLifecycle(t *testing.T) {
	detector := NewNoSQLInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
