package detectors

import (
	"fmt"
	"net/http"
	"regexp"
	"time"

	"scanner/internal/scanner/plugins"
)

// OAuthVulnerabilityDetector OAuth漏洞检测器
// 专门检测OAuth 2.0和OpenID Connect相关的安全漏洞
type OAuthVulnerabilityDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	oauthEndpoints   []string         // OAuth端点
	oauthParameters  []string         // OAuth参数
	vulnerableParams []string         // 易受攻击的参数
	redirectPatterns []*regexp.Regexp // 重定向模式
	statePatterns    []*regexp.Regexp // State参数模式
	errorPatterns    []*regexp.Regexp // 错误模式
	httpClient       *http.Client
}

// OAuthFlow OAuth流程类型
type OAuthFlow struct {
	Name        string            `json:"name"`        // 流程名称
	Type        string            `json:"type"`        // 流程类型
	Description string            `json:"description"` // 描述
	Endpoint    string            `json:"endpoint"`    // 端点
	Parameters  map[string]string `json:"parameters"`  // 参数
}

// NewOAuthVulnerabilityDetector 创建OAuth漏洞检测器
func NewOAuthVulnerabilityDetector() *OAuthVulnerabilityDetector {
	detector := &OAuthVulnerabilityDetector{
		id:          "oauth-vulnerability-detector",
		name:        "OAuth漏洞检测器",
		category:    "authentication",
		severity:    "high",
		cve:         []string{"CVE-2019-11249", "CVE-2020-8911"}, // OAuth相关CVE
		cwe:         []string{"CWE-287", "CWE-352", "CWE-601"},   // 认证绕过、CSRF、开放重定向
		version:     "1.0.0",
		author:      "Security Scanner Team",
		description: "检测OAuth 2.0和OpenID Connect相关的安全漏洞，包括授权码劫持、CSRF、开放重定向等",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
		config: &plugins.DetectorConfig{
			Enabled:         true,
			Timeout:         15 * time.Second,
			MaxRetries:      3,
			Concurrency:     2,
			RateLimit:       5,
			FollowRedirects: false, // OAuth检测需要控制重定向
			VerifySSL:       false,
			MaxResponseSize: 256 * 1024, // 256KB
		},
		httpClient: &http.Client{
			Timeout: 15 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// 不自动跟随重定向，需要手动分析
				return http.ErrUseLastResponse
			},
		},
	}

	// 初始化检测数据
	detector.initializeEndpoints()
	detector.initializeParameters()
	detector.initializePatterns()

	return detector
}

// initializeEndpoints 初始化OAuth端点
func (d *OAuthVulnerabilityDetector) initializeEndpoints() {
	d.oauthEndpoints = []string{
		"/oauth/authorize",
		"/oauth/token",
		"/oauth2/authorize",
		"/oauth2/token",
		"/auth/oauth/authorize",
		"/auth/oauth/token",
		"/api/oauth/authorize",
		"/api/oauth/token",
		"/login/oauth/authorize",
		"/login/oauth/token",
		"/sso/oauth/authorize",
		"/sso/oauth/token",
		"/.well-known/openid_configuration",
		"/openid/authorize",
		"/openid/token",
		"/connect/authorize",
		"/connect/token",
		"/identity/connect/authorize",
		"/identity/connect/token",
	}
}

// initializeParameters 初始化OAuth参数
func (d *OAuthVulnerabilityDetector) initializeParameters() {
	d.oauthParameters = []string{
		"client_id",
		"client_secret",
		"redirect_uri",
		"response_type",
		"scope",
		"state",
		"code",
		"access_token",
		"refresh_token",
		"grant_type",
		"code_challenge",
		"code_challenge_method",
		"nonce",
		"response_mode",
	}

	d.vulnerableParams = []string{
		"redirect_uri",
		"state",
		"nonce",
		"code_challenge",
	}
}

// initializePatterns 初始化模式匹配
func (d *OAuthVulnerabilityDetector) initializePatterns() {
	// 重定向URI模式
	redirectPatterns := []string{
		`redirect_uri=([^&\s]+)`,
		`redirect_url=([^&\s]+)`,
		`return_url=([^&\s]+)`,
		`callback=([^&\s]+)`,
		`next=([^&\s]+)`,
	}

	d.redirectPatterns = make([]*regexp.Regexp, len(redirectPatterns))
	for i, pattern := range redirectPatterns {
		d.redirectPatterns[i] = regexp.MustCompile(pattern)
	}

	// State参数模式
	statePatterns := []string{
		`state=([^&\s]+)`,
		`csrf_token=([^&\s]+)`,
		`anti_csrf=([^&\s]+)`,
	}

	d.statePatterns = make([]*regexp.Regexp, len(statePatterns))
	for i, pattern := range statePatterns {
		d.statePatterns[i] = regexp.MustCompile(pattern)
	}

	// 错误模式
	errorPatterns := []string{
		`(?i)oauth.*error`,
		`(?i)invalid.*client`,
		`(?i)invalid.*grant`,
		`(?i)invalid.*request`,
		`(?i)unauthorized.*client`,
		`(?i)unsupported.*grant.*type`,
		`(?i)invalid.*scope`,
		`(?i)server.*error`,
		`(?i)temporarily.*unavailable`,
	}

	d.errorPatterns = make([]*regexp.Regexp, len(errorPatterns))
	for i, pattern := range errorPatterns {
		d.errorPatterns[i] = regexp.MustCompile(pattern)
	}
}

// GetID 获取检测器ID
func (d *OAuthVulnerabilityDetector) GetID() string {
	return d.id
}

// GetName 获取检测器名称
func (d *OAuthVulnerabilityDetector) GetName() string {
	return d.name
}

// GetCategory 获取检测器分类
func (d *OAuthVulnerabilityDetector) GetCategory() string {
	return d.category
}

// GetSeverity 获取严重程度
func (d *OAuthVulnerabilityDetector) GetSeverity() string {
	return d.severity
}

// GetCVE 获取相关CVE
func (d *OAuthVulnerabilityDetector) GetCVE() []string {
	return d.cve
}

// GetCWE 获取相关CWE
func (d *OAuthVulnerabilityDetector) GetCWE() []string {
	return d.cwe
}

// GetVersion 获取版本
func (d *OAuthVulnerabilityDetector) GetVersion() string {
	return d.version
}

// GetAuthor 获取作者
func (d *OAuthVulnerabilityDetector) GetAuthor() string {
	return d.author
}

// GetDescription 获取描述
func (d *OAuthVulnerabilityDetector) GetDescription() string {
	return d.description
}

// GetCreatedAt 获取创建时间
func (d *OAuthVulnerabilityDetector) GetCreatedAt() time.Time {
	return d.createdAt
}

// GetUpdatedAt 获取更新时间
func (d *OAuthVulnerabilityDetector) GetUpdatedAt() time.Time {
	return d.updatedAt
}

// GetTargetTypes 获取支持的目标类型
func (d *OAuthVulnerabilityDetector) GetTargetTypes() []string {
	return []string{"http", "https"}
}

// GetRequiredPorts 获取需要的端口
func (d *OAuthVulnerabilityDetector) GetRequiredPorts() []int {
	return []int{80, 443, 3000, 4000, 8000, 8080, 8443}
}

// GetRequiredServices 获取需要的服务
func (d *OAuthVulnerabilityDetector) GetRequiredServices() []string {
	return []string{"http", "https", "oauth", "openid"}
}

// GetRequiredHeaders 获取需要的HTTP头
func (d *OAuthVulnerabilityDetector) GetRequiredHeaders() []string {
	return []string{}
}

// GetRequiredTechnologies 获取需要的技术栈
func (d *OAuthVulnerabilityDetector) GetRequiredTechnologies() []string {
	return []string{"oauth", "oauth2", "openid", "oidc", "sso"}
}

// GetDependencies 获取依赖的其他检测器
func (d *OAuthVulnerabilityDetector) GetDependencies() []string {
	return []string{}
}

// GetConfiguration 获取检测器配置
func (d *OAuthVulnerabilityDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

// SetConfiguration 设置检测器配置
func (d *OAuthVulnerabilityDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

// IsEnabled 检查是否启用
func (d *OAuthVulnerabilityDetector) IsEnabled() bool {
	return d.enabled
}

// SetEnabled 设置启用状态
func (d *OAuthVulnerabilityDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

// Validate 验证检测器
func (d *OAuthVulnerabilityDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if d.config == nil {
		return fmt.Errorf("检测器配置不能为空")
	}
	return nil
}

// Initialize 初始化检测器
func (d *OAuthVulnerabilityDetector) Initialize() error {
	// 验证配置
	if err := d.Validate(); err != nil {
		return err
	}

	// 更新HTTP客户端配置
	if d.config.Timeout > 0 {
		d.httpClient.Timeout = d.config.Timeout
	}

	return nil
}

// Cleanup 清理资源
func (d *OAuthVulnerabilityDetector) Cleanup() error {
	// 关闭HTTP客户端连接
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}
