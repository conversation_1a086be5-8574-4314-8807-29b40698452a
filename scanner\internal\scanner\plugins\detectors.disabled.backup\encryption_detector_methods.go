package detectors

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectSSLTLSConfiguration 检测SSL/TLS配置
func (d *EncryptionDetector) detectSSLTLSConfiguration(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 只对HTTPS目标进行SSL/TLS检测
	if target.Protocol != "https" && target.Port != 443 && target.Port != 8443 {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 测试不同的TLS版本
	tlsVersions := map[string]uint16{
		"TLS 1.0": tls.VersionTLS10,
		"TLS 1.1": tls.VersionTLS11,
		"TLS 1.2": tls.VersionTLS12,
		"TLS 1.3": tls.VersionTLS13,
	}

	for versionName, versionCode := range tlsVersions {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 测试特定TLS版本
		tlsConfig := &tls.Config{
			InsecureSkipVerify: true,
			MinVersion:         versionCode,
			MaxVersion:         versionCode,
		}

		// 构造HTTPS URL
		httpsURL := d.buildHTTPSURL(target.URL)

		// 发送TLS请求
		resp, err := d.sendTLSRequest(ctx, httpsURL, tlsConfig)
		if err != nil {
			// TLS连接失败可能表示不支持该版本
			if strings.Contains(versionName, "1.0") || strings.Contains(versionName, "1.1") {
				// 不支持弱TLS版本是好事
				continue
			}
		} else {
			// 检查TLS配置响应
			confidence := d.checkTLSConfigurationResponse(resp, versionName)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("SSL/TLS配置: %s", versionName)
				vulnerableRequest = httpsURL
				vulnerableResponse = resp
			}

			if confidence > 0.6 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "ssl-tls-configuration",
					Description: fmt.Sprintf("发现SSL/TLS配置问题: %s (置信度: %.2f)", versionName, confidence),
					Content:     d.extractEncryptionEvidence(resp, "ssl-tls-configuration"),
					Location:    httpsURL,
					Timestamp:   time.Now(),
				})
			}
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 500)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectCertificateSecurity 检测证书安全
func (d *EncryptionDetector) detectCertificateSecurity(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 只对HTTPS目标进行证书检测
	if target.Protocol != "https" && target.Port != 443 && target.Port != 8443 {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 构造HTTPS URL
	httpsURL := d.buildHTTPSURL(target.URL)

	// 测试证书验证
	certTests := []struct {
		name      string
		tlsConfig *tls.Config
	}{
		{
			name: "证书验证",
			tlsConfig: &tls.Config{
				InsecureSkipVerify: false, // 验证证书
			},
		},
		{
			name: "自签名证书",
			tlsConfig: &tls.Config{
				InsecureSkipVerify: true, // 跳过验证以检测自签名
			},
		},
	}

	for _, test := range certTests {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送证书测试请求
		resp, err := d.sendTLSRequest(ctx, httpsURL, test.tlsConfig)

		// 检查证书安全响应
		confidence := d.checkCertificateSecurityResponse(resp, err, test.name)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("证书安全: %s", test.name)
			vulnerableRequest = httpsURL
			if resp != "" {
				vulnerableResponse = resp
			} else if err != nil {
				vulnerableResponse = fmt.Sprintf("证书错误: %v", err)
			}
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "certificate-security",
				Description: fmt.Sprintf("发现证书安全问题: %s (置信度: %.2f)", test.name, confidence),
				Content:     d.extractEncryptionEvidence(vulnerableResponse, "certificate-security"),
				Location:    httpsURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 600)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectJWTSecurity 检测JWT安全
func (d *EncryptionDetector) detectJWTSecurity(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试JWT相关的端点
	jwtEndpoints := []string{
		"/api/auth",
		"/auth/login",
		"/login",
		"/api/token",
		"/token",
		"/oauth/token",
		"/jwt",
	}

	for _, endpoint := range jwtEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造JWT测试URL
		jwtURL := d.buildJWTTestURL(target.URL, endpoint)

		// 测试JWT弱密钥
		for _, weakSecret := range d.jwtWeakSecrets {
			// 发送JWT测试请求
			resp, err := d.sendJWTRequest(ctx, jwtURL, weakSecret)
			if err != nil {
				continue
			}

			// 检查JWT安全响应
			confidence := d.checkJWTSecurityResponse(resp, weakSecret)
			if confidence > maxConfidence {
				maxConfidence = confidence
				vulnerablePayload = fmt.Sprintf("JWT安全: 弱密钥 %s", weakSecret)
				vulnerableRequest = jwtURL
				vulnerableResponse = resp
			}

			if confidence > 0.7 {
				evidence = append(evidence, plugins.Evidence{
					Type:        "jwt-security",
					Description: fmt.Sprintf("发现JWT安全问题: 弱密钥 %s (置信度: %.2f)", weakSecret, confidence),
					Content:     d.extractEncryptionEvidence(resp, "jwt-security"),
					Location:    jwtURL,
					Timestamp:   time.Now(),
				})
			}

			// 添加延迟避免触发防护
			time.Sleep(time.Millisecond * 300)
		}

		// 测试JWT算法混淆
		for _, algorithm := range d.jwtAlgorithms {
			if strings.ToLower(algorithm) == "none" {
				// 发送无算法JWT请求
				resp, err := d.sendJWTAlgorithmRequest(ctx, jwtURL, algorithm)
				if err != nil {
					continue
				}

				// 检查JWT算法响应
				confidence := d.checkJWTAlgorithmResponse(resp, algorithm)
				if confidence > maxConfidence {
					maxConfidence = confidence
					vulnerablePayload = fmt.Sprintf("JWT安全: 算法 %s", algorithm)
					vulnerableRequest = jwtURL
					vulnerableResponse = resp
				}

				if confidence > 0.8 {
					evidence = append(evidence, plugins.Evidence{
						Type:        "jwt-algorithm",
						Description: fmt.Sprintf("发现JWT算法问题: %s (置信度: %.2f)", algorithm, confidence),
						Content:     d.extractEncryptionEvidence(resp, "jwt-algorithm"),
						Location:    jwtURL,
						Timestamp:   time.Now(),
					})
				}

				// 添加延迟避免触发防护
				time.Sleep(time.Millisecond * 400)
			}
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectWeakCryptography 检测弱加密算法
func (d *EncryptionDetector) detectWeakCryptography(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试加密相关的端点
	cryptoEndpoints := []string{
		"/crypto",
		"/encryption",
		"/hash",
		"/api/crypto",
		"/api/hash",
		"/security",
	}

	for _, endpoint := range cryptoEndpoints {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造加密测试URL
		cryptoURL := d.buildCryptoTestURL(target.URL, endpoint)

		// 发送加密测试请求
		resp, err := d.sendCryptoRequest(ctx, cryptoURL)
		if err != nil {
			continue
		}

		// 检查弱加密算法响应
		confidence := d.checkWeakCryptographyResponse(resp)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("弱加密算法: %s", endpoint)
			vulnerableRequest = cryptoURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "weak-cryptography",
				Description: fmt.Sprintf("发现弱加密算法: %s (置信度: %.2f)", endpoint, confidence),
				Content:     d.extractEncryptionEvidence(resp, "weak-cryptography"),
				Location:    cryptoURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 700)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendTLSRequest 发送TLS请求
func (d *EncryptionDetector) sendTLSRequest(ctx context.Context, targetURL string, tlsConfig *tls.Config) (string, error) {
	// 创建带有特定TLS配置的HTTP客户端
	client := &http.Client{
		Timeout: d.config.Timeout,
		Transport: &http.Transport{
			TLSClientConfig: tlsConfig,
		},
	}

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和TLS信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	if resp.TLS != nil {
		responseInfo += fmt.Sprintf("TLS Version: %x\n", resp.TLS.Version)
		responseInfo += fmt.Sprintf("Cipher Suite: %x\n", resp.TLS.CipherSuite)
		responseInfo += fmt.Sprintf("Server Name: %s\n", resp.TLS.ServerName)
	}
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendJWTRequest 发送JWT请求
func (d *EncryptionDetector) sendJWTRequest(ctx context.Context, targetURL, weakSecret string) (string, error) {
	// 构造JWT测试数据
	jwtData := fmt.Sprintf(`{"username":"admin","password":"%s","secret":"%s"}`, weakSecret, weakSecret)

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(jwtData))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendJWTAlgorithmRequest 发送JWT算法请求
func (d *EncryptionDetector) sendJWTAlgorithmRequest(ctx context.Context, targetURL, algorithm string) (string, error) {
	// 构造JWT算法测试数据
	jwtData := fmt.Sprintf(`{"alg":"%s","username":"admin","test":"algorithm"}`, algorithm)

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(jwtData))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer eyJhbGciOiIlcyIsInR5cCI6IkpXVCJ9.%s", algorithm))
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendCryptoRequest 发送加密请求
func (d *EncryptionDetector) sendCryptoRequest(ctx context.Context, targetURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// buildHTTPSURL 构造HTTPS URL
func (d *EncryptionDetector) buildHTTPSURL(baseURL string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL
	}

	// 确保使用HTTPS
	parsedURL.Scheme = "https"
	if parsedURL.Port() == "80" || parsedURL.Port() == "" {
		parsedURL.Host = parsedURL.Hostname() + ":443"
	}

	return parsedURL.String()
}

// buildJWTTestURL 构造JWT测试URL
func (d *EncryptionDetector) buildJWTTestURL(baseURL, endpoint string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + endpoint
	}

	// 替换路径
	parsedURL.Path = endpoint

	return parsedURL.String()
}

// buildCryptoTestURL 构造加密测试URL
func (d *EncryptionDetector) buildCryptoTestURL(baseURL, endpoint string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + endpoint
	}

	// 替换路径
	parsedURL.Path = endpoint

	return parsedURL.String()
}

// checkTLSConfigurationResponse 检查TLS配置响应
func (d *EncryptionDetector) checkTLSConfigurationResponse(response, tlsVersion string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查弱TLS版本
	for _, weakVersion := range d.weakTLSVersions {
		if strings.Contains(response, strings.ToLower(weakVersion)) ||
			strings.Contains(tlsVersion, weakVersion) {
			confidence += 0.8 // 弱TLS版本是高风险
			break
		}
	}

	// 检查弱加密套件
	for _, weakCipher := range d.weakCipherSuites {
		if strings.Contains(response, strings.ToLower(weakCipher)) {
			confidence += 0.6
			break
		}
	}

	// 检查加密模式匹配
	for _, pattern := range d.encryptionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查TLS版本特定问题
	if strings.Contains(tlsVersion, "1.0") || strings.Contains(tlsVersion, "1.1") {
		confidence += 0.7 // TLS 1.0/1.1 是弱版本
	}

	return confidence
}

// checkCertificateSecurityResponse 检查证书安全响应
func (d *EncryptionDetector) checkCertificateSecurityResponse(response string, err error, testName string) float64 {
	confidence := 0.0

	// 检查证书错误
	if err != nil {
		errorStr := strings.ToLower(err.Error())

		// 证书验证失败
		if strings.Contains(errorStr, "certificate") {
			if strings.Contains(errorStr, "expired") {
				confidence += 0.9 // 过期证书
			} else if strings.Contains(errorStr, "invalid") {
				confidence += 0.8 // 无效证书
			} else if strings.Contains(errorStr, "self signed") {
				confidence += 0.7 // 自签名证书
			} else if strings.Contains(errorStr, "unknown authority") {
				confidence += 0.6 // 未知CA
			} else {
				confidence += 0.5 // 其他证书问题
			}
		}
	}

	if response != "" {
		response = strings.ToLower(response)

		// 检查证书模式匹配
		for _, pattern := range d.certificatePatterns {
			if pattern.MatchString(response) {
				confidence += 0.3
				break
			}
		}

		// 检查加密模式匹配
		for _, pattern := range d.encryptionPatterns {
			if pattern.MatchString(response) {
				confidence += 0.2
				break
			}
		}
	}

	return confidence
}

// checkJWTSecurityResponse 检查JWT安全响应
func (d *EncryptionDetector) checkJWTSecurityResponse(response, weakSecret string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查JWT成功指示器
	jwtSuccessIndicators := []string{
		"token", "jwt", "bearer", "access_token", "refresh_token",
		"authentication successful", "login successful", "authorized",
		"令牌", "认证成功", "登录成功", "授权成功",
	}

	for _, indicator := range jwtSuccessIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4
			break
		}
	}

	// 检查弱密钥在响应中的反映
	if strings.Contains(response, strings.ToLower(weakSecret)) {
		confidence += 0.3
	}

	// 检查加密模式匹配
	for _, pattern := range d.encryptionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据弱密钥类型调整置信度
	if weakSecret == "" || weakSecret == " " || weakSecret == "null" {
		if confidence > 0.3 {
			confidence += 0.4 // 空密钥更危险
		}
	} else if len(weakSecret) <= 3 {
		if confidence > 0.3 {
			confidence += 0.3 // 短密钥很危险
		}
	}

	return confidence
}

// checkJWTAlgorithmResponse 检查JWT算法响应
func (d *EncryptionDetector) checkJWTAlgorithmResponse(response, algorithm string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查算法接受指示器
	algorithmIndicators := []string{
		"algorithm accepted", "alg accepted", "none algorithm",
		"no signature", "unsigned token", "algorithm bypass",
		"算法接受", "无算法", "无签名", "算法绕过",
	}

	for _, indicator := range algorithmIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5
			break
		}
	}

	// 检查算法在响应中的反映
	if strings.Contains(response, strings.ToLower(algorithm)) {
		confidence += 0.3
	}

	// 检查加密模式匹配
	for _, pattern := range d.encryptionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	// 根据算法类型调整置信度
	if strings.ToLower(algorithm) == "none" {
		if confidence > 0.3 {
			confidence += 0.5 // "none"算法极其危险
		}
	}

	return confidence
}

// checkWeakCryptographyResponse 检查弱加密算法响应
func (d *EncryptionDetector) checkWeakCryptographyResponse(response string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查弱哈希算法
	for _, weakHash := range d.weakHashAlgorithms {
		if strings.Contains(response, strings.ToLower(weakHash)) {
			confidence += 0.4
			break
		}
	}

	// 检查弱密钥交换
	for _, weakKeyExchange := range d.weakKeyExchanges {
		if strings.Contains(response, strings.ToLower(weakKeyExchange)) {
			confidence += 0.3
			break
		}
	}

	// 检查加密模式匹配
	for _, pattern := range d.encryptionPatterns {
		if pattern.MatchString(response) {
			confidence += 0.3
			break
		}
	}

	// 检查密钥模式匹配
	for _, pattern := range d.keyPatterns {
		if pattern.MatchString(response) {
			confidence += 0.2
			break
		}
	}

	return confidence
}

// extractEncryptionEvidence 提取加密证据
func (d *EncryptionDetector) extractEncryptionEvidence(response, evidenceType string) string {
	lines := strings.Split(response, "\n")

	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}

	// 根据证据类型查找相关信息
	var encLines []string
	for _, line := range lines {
		lineLower := strings.ToLower(line)

		switch evidenceType {
		case "ssl-tls-configuration":
			if strings.Contains(lineLower, "tls") ||
				strings.Contains(lineLower, "ssl") ||
				strings.Contains(lineLower, "cipher") ||
				strings.Contains(lineLower, "version") ||
				strings.Contains(lineLower, "protocol") ||
				strings.Contains(lineLower, "handshake") ||
				strings.Contains(lineLower, "加密") ||
				strings.Contains(lineLower, "协议") ||
				strings.Contains(lineLower, "版本") {
				encLines = append(encLines, line)
			}
		case "certificate-security":
			if strings.Contains(lineLower, "certificate") ||
				strings.Contains(lineLower, "cert") ||
				strings.Contains(lineLower, "ca") ||
				strings.Contains(lineLower, "authority") ||
				strings.Contains(lineLower, "expired") ||
				strings.Contains(lineLower, "invalid") ||
				strings.Contains(lineLower, "self signed") ||
				strings.Contains(lineLower, "证书") ||
				strings.Contains(lineLower, "颁发") ||
				strings.Contains(lineLower, "过期") {
				encLines = append(encLines, line)
			}
		case "jwt-security":
			if strings.Contains(lineLower, "jwt") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "bearer") ||
				strings.Contains(lineLower, "secret") ||
				strings.Contains(lineLower, "algorithm") ||
				strings.Contains(lineLower, "alg") ||
				strings.Contains(lineLower, "signature") ||
				strings.Contains(lineLower, "令牌") ||
				strings.Contains(lineLower, "密钥") ||
				strings.Contains(lineLower, "算法") ||
				strings.Contains(lineLower, "签名") {
				encLines = append(encLines, line)
			}
		case "jwt-algorithm":
			if strings.Contains(lineLower, "algorithm") ||
				strings.Contains(lineLower, "alg") ||
				strings.Contains(lineLower, "none") ||
				strings.Contains(lineLower, "signature") ||
				strings.Contains(lineLower, "unsigned") ||
				strings.Contains(lineLower, "bypass") ||
				strings.Contains(lineLower, "算法") ||
				strings.Contains(lineLower, "无算法") ||
				strings.Contains(lineLower, "签名") ||
				strings.Contains(lineLower, "绕过") {
				encLines = append(encLines, line)
			}
		case "weak-cryptography":
			if strings.Contains(lineLower, "md5") ||
				strings.Contains(lineLower, "sha1") ||
				strings.Contains(lineLower, "des") ||
				strings.Contains(lineLower, "rc4") ||
				strings.Contains(lineLower, "weak") ||
				strings.Contains(lineLower, "insecure") ||
				strings.Contains(lineLower, "deprecated") ||
				strings.Contains(lineLower, "弱") ||
				strings.Contains(lineLower, "不安全") ||
				strings.Contains(lineLower, "过时") {
				encLines = append(encLines, line)
			}
		default:
			if strings.Contains(lineLower, "ssl") ||
				strings.Contains(lineLower, "tls") ||
				strings.Contains(lineLower, "certificate") ||
				strings.Contains(lineLower, "cert") ||
				strings.Contains(lineLower, "jwt") ||
				strings.Contains(lineLower, "token") ||
				strings.Contains(lineLower, "crypto") ||
				strings.Contains(lineLower, "encrypt") ||
				strings.Contains(lineLower, "cipher") ||
				strings.Contains(lineLower, "hash") ||
				strings.Contains(lineLower, "key") ||
				strings.Contains(lineLower, "加密") ||
				strings.Contains(lineLower, "证书") ||
				strings.Contains(lineLower, "令牌") ||
				strings.Contains(lineLower, "密钥") ||
				strings.Contains(lineLower, "算法") {
				encLines = append(encLines, line)
			}
		}

		if len(encLines) >= 5 { // 只取前5行
			break
		}
	}

	if len(encLines) > 0 {
		return strings.Join(encLines, "\n")
	}

	// 如果没有找到特定的加密信息，返回前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}
