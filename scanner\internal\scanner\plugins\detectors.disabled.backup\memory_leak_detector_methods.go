package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectSensitiveInfoLeak 检测敏感信息泄露
func (d *MemoryLeakDetector) detectSensitiveInfoLeak(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试敏感信息载荷
	for _, payload := range d.sensitiveInfoPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送敏感信息请求
		resp, err := d.sendMemoryLeakRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查敏感信息响应
		confidence := d.checkSensitiveInfoResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("敏感信息泄露: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "sensitive-info",
				Description: fmt.Sprintf("发现敏感信息泄露: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractMemoryEvidence(resp, "sensitive-info"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectMemoryOverflow 检测内存溢出
func (d *MemoryLeakDetector) detectMemoryOverflow(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试内存溢出载荷（只测试较小的载荷以避免真正的DoS）
	smallOverflowPayloads := []string{
		strings.Repeat("A", 1024),  // 1KB
		strings.Repeat("B", 4096),  // 4KB
		strings.Repeat("C", 8192),  // 8KB
		strings.Repeat("D", 16384), // 16KB
		strings.Repeat("E", 32768), // 32KB
		strings.Repeat("0", 10000),
		strings.Repeat("1", 10000),
		strings.Repeat("@", 10000),
		strings.Repeat("#", 10000),
		strings.Repeat("中", 5000),
	}

	for _, payload := range smallOverflowPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送内存溢出请求
		resp, err := d.sendMemoryLeakRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查内存溢出响应
		confidence := d.checkMemoryOverflowResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("内存溢出: %s", payload[:50]+"...")
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "memory-overflow",
				Description: fmt.Sprintf("发现内存溢出: %s... (置信度: %.2f)", payload[:50], confidence),
				Content:     d.extractMemoryEvidence(resp, "memory-overflow"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 300)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectStackTrace 检测堆栈跟踪
func (d *MemoryLeakDetector) detectStackTrace(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试堆栈跟踪载荷
	for _, payload := range d.stackTracePayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送堆栈跟踪请求
		resp, err := d.sendMemoryLeakRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查堆栈跟踪响应
		confidence := d.checkStackTraceResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("堆栈跟踪: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "stack-trace",
				Description: fmt.Sprintf("发现堆栈跟踪: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractMemoryEvidence(resp, "stack-trace"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDebugInfo 检测调试信息
func (d *MemoryLeakDetector) detectDebugInfo(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试调试信息载荷
	for _, payload := range d.debugInfoPayloads {
		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送调试信息请求
		resp, err := d.sendMemoryLeakRequest(ctx, target.URL, payload)
		if err != nil {
			continue
		}

		// 检查调试信息响应
		confidence := d.checkDebugInfoResponse(resp, payload)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("调试信息: %s", payload)
			vulnerableRequest = target.URL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "debug-info",
				Description: fmt.Sprintf("发现调试信息: %s (置信度: %.2f)", payload, confidence),
				Content:     d.extractMemoryEvidence(resp, "debug-info"),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Millisecond * 200)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// sendMemoryLeakRequest 发送内存泄露请求
func (d *MemoryLeakDetector) sendMemoryLeakRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 尝试GET参数注入
	getResp, err := d.sendMemoryLeakGETRequest(ctx, targetURL, payload)
	if err == nil && getResp != "" {
		return getResp, nil
	}

	// 尝试POST表单注入
	postResp, err := d.sendMemoryLeakPOSTRequest(ctx, targetURL, payload)
	if err == nil && postResp != "" {
		return postResp, nil
	}

	// 返回POST响应（即使有错误）
	if postResp != "" {
		return postResp, nil
	}

	return "", fmt.Errorf("所有请求方法都失败")
}

// sendMemoryLeakGETRequest 发送内存泄露GET请求
func (d *MemoryLeakDetector) sendMemoryLeakGETRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return "", err
	}

	// 添加测试参数
	query := parsedURL.Query()

	for _, param := range d.testParameters {
		query.Set(param, payload)
	}
	parsedURL.RawQuery = query.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", parsedURL.String(), nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendMemoryLeakPOSTRequest 发送内存泄露POST请求
func (d *MemoryLeakDetector) sendMemoryLeakPOSTRequest(ctx context.Context, targetURL, payload string) (string, error) {
	// 构建表单数据
	formData := url.Values{}
	for _, param := range d.testParameters {
		formData.Set(param, payload)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", targetURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkSensitiveInfoResponse 检查敏感信息响应
func (d *MemoryLeakDetector) checkSensitiveInfoResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.2 // 成功响应可能包含敏感信息
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.3 // 服务器错误可能泄露敏感信息
	}

	// 检查敏感信息模式匹配
	for _, pattern := range d.sensitivePatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查敏感信息特定指示器
	sensitiveIndicators := []string{
		"password", "passwd", "pwd", "secret", "key", "token", "api_key",
		"database", "db", "mysql", "postgresql", "oracle", "mongodb",
		"connection", "connect", "dsn", "jdbc", "username", "user",
		"config", "configuration", "settings", "properties", "env",
		"version", "build", "commit", "revision", "system", "os",
		"session", "sessionid", "cookie", "csrf", "xsrf", "auth",
		"debug", "trace", "log", "error", "exception", "stack",
		"file", "path", "directory", "home", "root", "admin",
		"ip", "localhost", "127.0.0.1", "internal", "private",
		"数据库", "密码", "密钥", "令牌", "配置", "版本", "系统",
		"会话", "调试", "错误", "异常", "文件", "路径", "内部",
	}

	for _, indicator := range sensitiveIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.3 // 敏感信息指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// checkMemoryOverflowResponse 检查内存溢出响应
func (d *MemoryLeakDetector) checkMemoryOverflowResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.4 // 服务器错误可能表示内存溢出
	} else if strings.Contains(response, "status: 503") {
		confidence += 0.5 // 服务不可用可能表示内存溢出
	} else if strings.Contains(response, "status: 504") {
		confidence += 0.3 // 网关超时可能表示内存溢出
	}

	// 检查内存溢出模式匹配
	for _, pattern := range d.memoryPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查内存溢出特定指示器
	memoryIndicators := []string{
		"out of memory", "memory limit", "memory exceeded", "heap space",
		"stack overflow", "buffer overflow", "memory allocation",
		"outofmemoryerror", "memoryerror", "stackoverflow",
		"heap overflow", "memory leak", "memory exhausted",
		"allocation failed", "cannot allocate", "insufficient memory",
		"memory usage", "memory consumption", "memory pressure",
		"gc overhead", "garbage collection", "heap dump",
		"内存溢出", "内存不足", "内存超限", "堆栈溢出", "缓冲区溢出",
		"内存分配", "内存泄露", "内存耗尽", "垃圾回收", "堆转储",
	}

	for _, indicator := range memoryIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.5 // 内存溢出指示器
			break
		}
	}

	// 检查载荷在响应中的反映（可能表示内存处理问题）
	payloadLower := strings.ToLower(payload)
	if len(payloadLower) > 1000 && strings.Contains(response, payloadLower[:100]) {
		confidence += 0.3
	}

	return confidence
}

// checkStackTraceResponse 检查堆栈跟踪响应
func (d *MemoryLeakDetector) checkStackTraceResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 500") {
		confidence += 0.3 // 服务器错误可能包含堆栈跟踪
	} else if strings.Contains(response, "status: 400") {
		confidence += 0.2 // 客户端错误可能包含堆栈跟踪
	}

	// 检查堆栈跟踪模式匹配
	for _, pattern := range d.stackPatterns {
		if pattern.MatchString(response) {
			confidence += 0.6
			break
		}
	}

	// 检查堆栈跟踪特定指示器
	stackIndicators := []string{
		"traceback", "stack trace", "stacktrace", "call stack",
		"at java.", "at org.", "at com.", "at javax.",
		"at line", "in file", "in function", "in method",
		"exception", "error", "fault", "failure",
		"nullpointerexception", "outofmemoryerror", "stackoverflow",
		"runtimeexception", "illegalargumentexception",
		"nameerror", "typeerror", "valueerror", "attributeerror",
		"keyerror", "indexerror", "importerror", "syntaxerror",
		"system.nullreferenceexception", "system.outofmemoryexception",
		"system.stackoverflowexception", "system.argumentexception",
		"fatal error", "parse error", "warning", "notice",
		"referenceerror", "syntaxerror", "rangeerror", "evalerror",
		"nomethoderror", "argumenterror", "runtimeerror",
		"空指针异常", "内存溢出", "堆栈溢出", "运行时错误",
		"参数错误", "类型错误", "值错误", "属性错误",
		"键错误", "索引错误", "导入错误", "语法错误",
	}

	for _, indicator := range stackIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 堆栈跟踪指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// checkDebugInfoResponse 检查调试信息响应
func (d *MemoryLeakDetector) checkDebugInfoResponse(response, payload string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3 // 成功响应可能包含调试信息
	} else if strings.Contains(response, "status: 500") {
		confidence += 0.2 // 服务器错误可能包含调试信息
	}

	// 检查调试信息模式匹配
	for _, pattern := range d.debugPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查调试信息特定指示器
	debugIndicators := []string{
		"debug", "trace", "verbose", "info", "log",
		"debug mode", "trace mode", "verbose mode", "info mode",
		"debug level", "trace level", "verbose level", "info level",
		"debug output", "trace output", "verbose output", "info output",
		"development", "dev", "test", "staging", "local",
		"debug=true", "trace=true", "verbose=true", "info=true",
		"debug=1", "trace=1", "verbose=1", "info=1",
		"debug=on", "trace=on", "verbose=on", "info=on",
		"x-debug", "x-trace", "x-verbose", "x-info",
		"debug_mode", "trace_mode", "verbose_mode", "info_mode",
		"调试", "跟踪", "详细", "信息", "日志",
		"调试模式", "跟踪模式", "详细模式", "信息模式",
		"开发模式", "测试模式", "本地模式", "暂存模式",
	}

	for _, indicator := range debugIndicators {
		if strings.Contains(response, indicator) {
			confidence += 0.4 // 调试信息指示器
			break
		}
	}

	// 检查载荷在响应中的反映
	payloadLower := strings.ToLower(payload)
	if payloadLower != "" && strings.Contains(response, payloadLower) {
		confidence += 0.2
	}

	return confidence
}

// extractMemoryEvidence 提取内存泄露证据
func (d *MemoryLeakDetector) extractMemoryEvidence(response, memoryType string) string {
	// 限制证据长度
	maxLength := 500
	if len(response) > maxLength {
		response = response[:maxLength] + "..."
	}

	// 根据内存泄露类型提取相关证据
	switch memoryType {
	case "sensitive-info":
		return d.extractSensitiveEvidence(response)
	case "memory-overflow":
		return d.extractOverflowEvidence(response)
	case "stack-trace":
		return d.extractStackEvidence(response)
	case "debug-info":
		return d.extractDebugEvidence(response)
	default:
		return response
	}
}

// extractSensitiveEvidence 提取敏感信息证据
func (d *MemoryLeakDetector) extractSensitiveEvidence(response string) string {
	evidence := "敏感信息泄露证据:\n"

	// 查找敏感信息特定内容
	sensitiveIndicators := []string{
		"password", "passwd", "secret", "key", "token",
		"database", "mysql", "postgresql", "connection",
		"config", "settings", "version", "build",
		"session", "cookie", "csrf", "auth",
	}

	for _, indicator := range sensitiveIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现敏感信息指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractOverflowEvidence 提取内存溢出证据
func (d *MemoryLeakDetector) extractOverflowEvidence(response string) string {
	evidence := "内存溢出证据:\n"

	// 查找内存溢出特定内容
	overflowIndicators := []string{
		"out of memory", "memory limit", "heap space",
		"stack overflow", "buffer overflow", "allocation failed",
		"memory exhausted", "gc overhead", "heap dump",
	}

	for _, indicator := range overflowIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现内存溢出指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractStackEvidence 提取堆栈跟踪证据
func (d *MemoryLeakDetector) extractStackEvidence(response string) string {
	evidence := "堆栈跟踪证据:\n"

	// 查找堆栈跟踪特定内容
	stackIndicators := []string{
		"traceback", "stack trace", "stacktrace",
		"exception", "error", "at java.", "at org.",
		"nullpointerexception", "runtimeexception",
		"nameerror", "typeerror", "fatal error",
	}

	for _, indicator := range stackIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现堆栈跟踪指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}

// extractDebugEvidence 提取调试信息证据
func (d *MemoryLeakDetector) extractDebugEvidence(response string) string {
	evidence := "调试信息证据:\n"

	// 查找调试信息特定内容
	debugIndicators := []string{
		"debug", "trace", "verbose", "info",
		"debug mode", "development", "test", "staging",
		"debug=true", "x-debug", "debug_mode",
	}

	for _, indicator := range debugIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			evidence += fmt.Sprintf("- 发现调试信息指示器: %s\n", indicator)
		}
	}

	evidence += "\n响应内容:\n" + response
	return evidence
}
