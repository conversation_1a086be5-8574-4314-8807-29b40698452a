package detectors

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"scanner/internal/scanner/plugins"
)

// TestLDAPInjectionDetectorBasicFunctionality 测试LDAP注入检测器基础功能
func TestLDAPInjectionDetectorBasicFunctionality(t *testing.T) {
	detector := NewLDAPInjectionDetector()
	require.NotNil(t, detector)

	// 测试基础信息
	assert.Equal(t, "ldap-injection-comprehensive", detector.GetID())
	assert.Equal(t, "LDAP注入漏洞综合检测器", detector.GetName())
	assert.Equal(t, "web", detector.GetCategory())
	assert.Equal(t, "high", detector.GetSeverity())
	assert.Contains(t, detector.GetCWE(), "CWE-90")
	assert.True(t, detector.IsEnabled())

	// 测试目标类型
	targetTypes := detector.GetTargetTypes()
	assert.Contains(t, targetTypes, "http")
	assert.Contains(t, targetTypes, "https")

	// 测试端口
	ports := detector.GetRequiredPorts()
	assert.Contains(t, ports, 80)
	assert.Contains(t, ports, 443)
	assert.Contains(t, ports, 389)
	assert.Contains(t, ports, 636)

	// 测试验证
	err := detector.Validate()
	assert.NoError(t, err)
}

// TestLDAPInjectionDetectorApplicability 测试LDAP注入检测器适用性
func TestLDAPInjectionDetectorApplicability(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 测试有LDAP功能的目标
	ldapTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login",
		Protocol: "http",
		Port:     80,
		Headers: map[string]string{
			"Server": "OpenLDAP/2.4.44",
		},
	}
	assert.True(t, detector.IsApplicable(ldapTarget))

	// 测试有LDAP技术的目标
	techTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/auth",
		Protocol: "http",
		Port:     80,
		Technologies: []plugins.TechnologyInfo{
			{Name: "Active Directory", Version: "2019", Confidence: 0.9},
		},
	}
	assert.True(t, detector.IsApplicable(techTarget))

	// 测试有表单的目标
	formTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/search",
		Protocol: "http",
		Port:     80,
		Forms: []plugins.FormInfo{
			{Action: "/search", Method: "POST", Fields: map[string]string{"username": "text"}},
		},
	}
	assert.True(t, detector.IsApplicable(formTarget))

	// 测试有参数的目标
	paramTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/login?user=test",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(paramTarget))

	// 测试认证相关URL
	authTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/admin/login",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(authTarget))

	// 测试普通Web目标（LDAP注入是通用漏洞）
	webTarget := &plugins.ScanTarget{
		Type:     "url",
		URL:      "http://example.com/app",
		Protocol: "http",
		Port:     80,
	}
	assert.True(t, detector.IsApplicable(webTarget))

	// 测试非Web目标
	tcpTarget := &plugins.ScanTarget{
		Type:     "ip",
		Protocol: "tcp",
		Port:     22,
	}
	assert.False(t, detector.IsApplicable(tcpTarget))
}

// TestLDAPInjectionDetectorConfiguration 测试LDAP注入检测器配置
func TestLDAPInjectionDetectorConfiguration(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 测试默认配置
	config := detector.GetConfiguration()
	assert.NotNil(t, config)
	assert.True(t, config.Enabled)
	assert.Equal(t, 15*time.Second, config.Timeout)
	assert.Equal(t, 2, config.MaxRetries)
	assert.Equal(t, 3, config.Concurrency)
	assert.True(t, config.FollowRedirects)
	assert.False(t, config.VerifySSL)

	// 测试设置配置
	newConfig := &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         20 * time.Second,
		MaxRetries:      3,
		Concurrency:     4,
		RateLimit:       5,
		FollowRedirects: true,
		VerifySSL:       false,
		MaxResponseSize: 2 * 1024 * 1024,
	}

	err := detector.SetConfiguration(newConfig)
	assert.NoError(t, err)

	updatedConfig := detector.GetConfiguration()
	assert.Equal(t, 20*time.Second, updatedConfig.Timeout)
	assert.Equal(t, 3, updatedConfig.MaxRetries)
	assert.Equal(t, 4, updatedConfig.Concurrency)
}

// TestLDAPInjectionDetectorAuthBypassPayloads 测试认证绕过载荷
func TestLDAPInjectionDetectorAuthBypassPayloads(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查认证绕过载荷列表
	assert.NotEmpty(t, detector.authBypassPayloads)
	assert.Greater(t, len(detector.authBypassPayloads), 40)

	// 检查基础认证绕过
	assert.Contains(t, detector.authBypassPayloads, "*")
	assert.Contains(t, detector.authBypassPayloads, "*)(&")
	assert.Contains(t, detector.authBypassPayloads, "*)(uid=*")

	// 检查管理员绕过
	assert.Contains(t, detector.authBypassPayloads, "admin)(&")
	assert.Contains(t, detector.authBypassPayloads, "administrator)(&")
	assert.Contains(t, detector.authBypassPayloads, "root)(&")

	// 检查密码绕过
	assert.Contains(t, detector.authBypassPayloads, "*)(|(password=*)")
	assert.Contains(t, detector.authBypassPayloads, "*)(userPassword=*")

	// 检查用户名绕过
	assert.Contains(t, detector.authBypassPayloads, "*)(|(uid=admin)")
	assert.Contains(t, detector.authBypassPayloads, "*)(|(cn=admin)")
	assert.Contains(t, detector.authBypassPayloads, "*)(|(sAMAccountName=admin)")

	// 检查组绕过
	assert.Contains(t, detector.authBypassPayloads, "*)(|(memberOf=*admin*)")
	assert.Contains(t, detector.authBypassPayloads, "*)(|(group=*admin*)")

	// 检查布尔逻辑绕过
	assert.Contains(t, detector.authBypassPayloads, "*)(|(objectClass=*)")
	assert.Contains(t, detector.authBypassPayloads, "*)(&(objectClass=user)")

	// 检查空字节注入
	assert.Contains(t, detector.authBypassPayloads, "*))%00")
	assert.Contains(t, detector.authBypassPayloads, "admin))%00")

	// 检查通配符绕过
	assert.Contains(t, detector.authBypassPayloads, "*)(uid=*)")
	assert.Contains(t, detector.authBypassPayloads, "*)(cn=*)")
	assert.Contains(t, detector.authBypassPayloads, "*)(mail=*)")

	// 检查中文认证绕过
	assert.Contains(t, detector.authBypassPayloads, "管理员)(&")
	assert.Contains(t, detector.authBypassPayloads, "用户)(&")
}

// TestLDAPInjectionDetectorInfoLeakagePayloads 测试信息泄露载荷
func TestLDAPInjectionDetectorInfoLeakagePayloads(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查信息泄露载荷列表
	assert.NotEmpty(t, detector.infoLeakagePayloads)
	assert.Greater(t, len(detector.infoLeakagePayloads), 45)

	// 检查用户信息泄露
	assert.Contains(t, detector.infoLeakagePayloads, "*)(uid=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(cn=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(mail=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(telephoneNumber=*")

	// 检查密码信息泄露
	assert.Contains(t, detector.infoLeakagePayloads, "*)(userPassword=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(password=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(unicodePwd=*")

	// 检查组织信息泄露
	assert.Contains(t, detector.infoLeakagePayloads, "*)(ou=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(department=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(title=*")

	// 检查系统信息泄露
	assert.Contains(t, detector.infoLeakagePayloads, "*)(objectClass=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(distinguishedName=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(memberOf=*")

	// 检查Active Directory特定
	assert.Contains(t, detector.infoLeakagePayloads, "*)(sAMAccountName=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(userPrincipalName=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(displayName=*")

	// 检查服务账户信息
	assert.Contains(t, detector.infoLeakagePayloads, "*)(servicePrincipalName=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(userAccountControl=*")

	// 检查敏感属性
	assert.Contains(t, detector.infoLeakagePayloads, "*)(homeDirectory=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(profilePath=*")

	// 检查中文信息泄露
	assert.Contains(t, detector.infoLeakagePayloads, "*)(用户名=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(姓名=*")
	assert.Contains(t, detector.infoLeakagePayloads, "*)(邮箱=*")
}

// TestLDAPInjectionDetectorBlindPayloads 测试盲注载荷
func TestLDAPInjectionDetectorBlindPayloads(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查盲注载荷列表
	assert.NotEmpty(t, detector.blindPayloads)
	assert.Greater(t, len(detector.blindPayloads), 40)

	// 检查布尔盲注 - True条件
	assert.Contains(t, detector.blindPayloads, "*)(objectClass=*")
	assert.Contains(t, detector.blindPayloads, "*)(objectCategory=*")
	assert.Contains(t, detector.blindPayloads, "*)(&(objectClass=user)")

	// 检查布尔盲注 - False条件
	assert.Contains(t, detector.blindPayloads, "*)(objectClass=nonexistent")
	assert.Contains(t, detector.blindPayloads, "*)(objectCategory=nonexistent")

	// 检查字符串比较盲注
	assert.Contains(t, detector.blindPayloads, "*)(uid>=a")
	assert.Contains(t, detector.blindPayloads, "*)(cn<=z")
	assert.Contains(t, detector.blindPayloads, "*)(mail>=a")

	// 检查数值比较盲注
	assert.Contains(t, detector.blindPayloads, "*)(uidNumber>=0")
	assert.Contains(t, detector.blindPayloads, "*)(gidNumber<=9999")
	assert.Contains(t, detector.blindPayloads, "*)(userAccountControl>=0")

	// 检查存在性检查
	assert.Contains(t, detector.blindPayloads, "*)(uid=*")
	assert.Contains(t, detector.blindPayloads, "*)(cn=*")
	assert.Contains(t, detector.blindPayloads, "*)(mail=*")

	// 检查长度检查
	assert.Contains(t, detector.blindPayloads, "*)(|(uid=a*)(uid=b*)")
	assert.Contains(t, detector.blindPayloads, "*)(|(cn=a*)(cn=b*)")

	// 检查模式匹配
	assert.Contains(t, detector.blindPayloads, "*)(uid=admin*")
	assert.Contains(t, detector.blindPayloads, "*)(cn=test*")
	assert.Contains(t, detector.blindPayloads, "*)(mail=*@*")

	// 检查中文盲注
	assert.Contains(t, detector.blindPayloads, "*)(用户名=*")
	assert.Contains(t, detector.blindPayloads, "*)(姓名=*")
}

// TestLDAPInjectionDetectorErrorPayloads 测试错误载荷
func TestLDAPInjectionDetectorErrorPayloads(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查错误载荷列表
	assert.NotEmpty(t, detector.errorPayloads)
	assert.Greater(t, len(detector.errorPayloads), 30)

	// 检查语法错误
	assert.Contains(t, detector.errorPayloads, "*)(invalidAttribute=*")
	assert.Contains(t, detector.errorPayloads, "*)(nonExistentAttr=*")

	// 检查无效操作符
	assert.Contains(t, detector.errorPayloads, "*)(uid~=*")
	assert.Contains(t, detector.errorPayloads, "*)(cn:=*")

	// 检查无效语法
	assert.Contains(t, detector.errorPayloads, "*)((uid=*")
	assert.Contains(t, detector.errorPayloads, "*)(uid=*))")

	// 检查无效过滤器
	assert.Contains(t, detector.errorPayloads, "*)(uid=")
	assert.Contains(t, detector.errorPayloads, "*)(=admin")

	// 检查特殊字符
	assert.Contains(t, detector.errorPayloads, "*)(uid=admin'")
	assert.Contains(t, detector.errorPayloads, "*)(cn=test\"")

	// 检查无效DN
	assert.Contains(t, detector.errorPayloads, "*)(distinguishedName=invalid")
	assert.Contains(t, detector.errorPayloads, "*)(dn=invalid")

	// 检查无效对象类
	assert.Contains(t, detector.errorPayloads, "*)(objectClass=invalidClass")
	assert.Contains(t, detector.errorPayloads, "*)(objectCategory=invalidCategory")

	// 检查中文错误
	assert.Contains(t, detector.errorPayloads, "*)(无效属性=*")
	assert.Contains(t, detector.errorPayloads, "*)(错误字段=*")
}

// TestLDAPInjectionDetectorTimeBasedPayloads 测试时间盲注载荷
func TestLDAPInjectionDetectorTimeBasedPayloads(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查时间盲注载荷列表
	assert.NotEmpty(t, detector.timeBasedPayloads)
	assert.Greater(t, len(detector.timeBasedPayloads), 15)

	// 检查复杂查询导致延迟
	assert.Contains(t, detector.timeBasedPayloads, "*)(|(uid=*)(cn=*)(mail=*)(sn=*)(givenName=*)")
	assert.Contains(t, detector.timeBasedPayloads, "*)(&(uid=*)(cn=*)(mail=*)(sn=*)(givenName=*))")

	// 检查大量OR条件
	assert.Contains(t, detector.timeBasedPayloads, "*)(|(uid=a)(uid=b)(uid=c)(uid=d)(uid=e)(uid=f)(uid=g)(uid=h)(uid=i)(uid=j)")

	// 检查嵌套查询
	assert.Contains(t, detector.timeBasedPayloads, "*)(|(uid=*)(|(cn=*)(|(mail=*)(|(sn=*)(givenName=*))))")

	// 检查通配符匹配
	assert.Contains(t, detector.timeBasedPayloads, "*)(uid=*a*b*c*d*e*f*g*h*i*j*")

	// 检查范围查询
	assert.Contains(t, detector.timeBasedPayloads, "*)(|(uid>=aaaa)(uid<=zzzz)")

	// 检查中文时间盲注
	assert.Contains(t, detector.timeBasedPayloads, "*)(|(用户名=*)(姓名=*)(邮箱=*)(电话=*)(部门=*)")
}

// TestLDAPInjectionDetectorTestParameters 测试参数
func TestLDAPInjectionDetectorTestParameters(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查测试参数列表
	assert.NotEmpty(t, detector.testParameters)
	assert.Greater(t, len(detector.testParameters), 50)

	// 检查用户相关参数
	assert.Contains(t, detector.testParameters, "username")
	assert.Contains(t, detector.testParameters, "user")
	assert.Contains(t, detector.testParameters, "uid")
	assert.Contains(t, detector.testParameters, "login")

	// 检查LDAP特定参数
	assert.Contains(t, detector.testParameters, "cn")
	assert.Contains(t, detector.testParameters, "dn")
	assert.Contains(t, detector.testParameters, "mail")
	assert.Contains(t, detector.testParameters, "sn")

	// 检查搜索相关参数
	assert.Contains(t, detector.testParameters, "search")
	assert.Contains(t, detector.testParameters, "query")
	assert.Contains(t, detector.testParameters, "filter")

	// 检查认证相关参数
	assert.Contains(t, detector.testParameters, "auth")
	assert.Contains(t, detector.testParameters, "password")
	assert.Contains(t, detector.testParameters, "credential")

	// 检查组织相关参数
	assert.Contains(t, detector.testParameters, "ou")
	assert.Contains(t, detector.testParameters, "department")
	assert.Contains(t, detector.testParameters, "group")

	// 检查Active Directory特定
	assert.Contains(t, detector.testParameters, "samaccountname")
	assert.Contains(t, detector.testParameters, "userprincipalname")
	assert.Contains(t, detector.testParameters, "objectclass")

	// 检查中文参数
	assert.Contains(t, detector.testParameters, "用户名")
	assert.Contains(t, detector.testParameters, "姓名")
	assert.Contains(t, detector.testParameters, "邮箱")
	assert.Contains(t, detector.testParameters, "搜索")
	assert.Contains(t, detector.testParameters, "认证")
}

// TestLDAPInjectionDetectorPatterns 测试模式
func TestLDAPInjectionDetectorPatterns(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 检查LDAP模式
	assert.NotEmpty(t, detector.ldapPatterns)
	assert.Greater(t, len(detector.ldapPatterns), 15)

	// 检查错误模式
	assert.NotEmpty(t, detector.errorPatterns)
	assert.Greater(t, len(detector.errorPatterns), 10)

	// 检查响应模式
	assert.NotEmpty(t, detector.responsePatterns)
	assert.Greater(t, len(detector.responsePatterns), 15)
}

// TestLDAPInjectionDetectorLDAPFeatures 测试LDAP功能检查
func TestLDAPInjectionDetectorLDAPFeatures(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 测试有LDAP头部的目标
	headerTarget := &plugins.ScanTarget{
		Headers: map[string]string{
			"Server": "OpenLDAP/2.4.44",
		},
	}
	assert.True(t, detector.hasLDAPFeatures(headerTarget))

	// 测试有LDAP技术的目标
	techTarget := &plugins.ScanTarget{
		Technologies: []plugins.TechnologyInfo{
			{Name: "Active Directory", Version: "2019", Confidence: 0.9},
		},
	}
	assert.True(t, detector.hasLDAPFeatures(techTarget))

	// 测试有LDAP链接的目标
	linkTarget := &plugins.ScanTarget{
		Links: []plugins.LinkInfo{
			{URL: "http://example.com/ldap", Text: "LDAP Directory"},
		},
	}
	assert.True(t, detector.hasLDAPFeatures(linkTarget))

	// 测试有LDAP表单的目标
	formTarget := &plugins.ScanTarget{
		Forms: []plugins.FormInfo{
			{Fields: map[string]string{"username": "text", "uid": "hidden"}},
		},
	}
	assert.True(t, detector.hasLDAPFeatures(formTarget))

	// 测试无LDAP功能的目标
	simpleTarget := &plugins.ScanTarget{
		Headers:      map[string]string{},
		Technologies: []plugins.TechnologyInfo{},
		Links:        []plugins.LinkInfo{},
		Forms:        []plugins.FormInfo{},
	}
	assert.False(t, detector.hasLDAPFeatures(simpleTarget))
}

// TestLDAPInjectionDetectorRiskScore 测试风险评分计算
func TestLDAPInjectionDetectorRiskScore(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 测试高置信度评分
	highConfidence := 0.9
	highScore := detector.calculateRiskScore(highConfidence)
	assert.Greater(t, highScore, 7.0)
	assert.LessOrEqual(t, highScore, 10.0)

	// 测试中等置信度评分
	mediumConfidence := 0.6
	mediumScore := detector.calculateRiskScore(mediumConfidence)
	assert.Greater(t, mediumScore, 4.0)
	assert.Less(t, mediumScore, highScore)

	// 测试低置信度评分
	lowConfidence := 0.3
	lowScore := detector.calculateRiskScore(lowConfidence)
	assert.Greater(t, lowScore, 0.0)
	assert.Less(t, lowScore, mediumScore)

	// 测试零置信度评分
	zeroConfidence := 0.0
	zeroScore := detector.calculateRiskScore(zeroConfidence)
	assert.Equal(t, 0.0, zeroScore)
}

// TestLDAPInjectionDetectorLifecycle 测试检测器生命周期
func TestLDAPInjectionDetectorLifecycle(t *testing.T) {
	detector := NewLDAPInjectionDetector()

	// 测试初始化
	err := detector.Initialize()
	assert.NoError(t, err)

	// 测试启用/禁用
	assert.True(t, detector.IsEnabled())
	detector.SetEnabled(false)
	assert.False(t, detector.IsEnabled())
	detector.SetEnabled(true)
	assert.True(t, detector.IsEnabled())

	// 测试清理
	err = detector.Cleanup()
	assert.NoError(t, err)
}
