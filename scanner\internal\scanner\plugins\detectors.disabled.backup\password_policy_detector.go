package detectors

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// PasswordPolicyDetector 密码策略检测器
// 支持密码复杂度检测、密码历史检测、密码过期检测、密码重置检测等多种密码策略安全检测
type PasswordPolicyDetector struct {
	// 基础信息
	id          string
	name        string
	category    string
	severity    string
	cve         []string
	cwe         []string
	version     string
	author      string
	description string
	createdAt   time.Time
	updatedAt   time.Time

	// 配置
	config  *plugins.DetectorConfig
	enabled bool

	// 检测逻辑相关
	passwordPolicyPaths  []string         // 密码策略路径
	passwordTestPayloads []string         // 密码测试载荷
	weakPasswordPatterns []string         // 弱密码模式
	policyViolationTests []PolicyTest     // 策略违规测试
	passwordResetPaths   []string         // 密码重置路径
	passwordChangePaths  []string         // 密码修改路径
	policyPatterns       []*regexp.Regexp // 策略模式
	weaknessPatterns     []*regexp.Regexp // 弱点模式
	securityPatterns     []*regexp.Regexp // 安全模式
	httpClient           *http.Client
}

// PolicyTest 策略测试结构
type PolicyTest struct {
	Name        string
	Password    string
	Description string
	Expected    string // should_reject, should_accept
	Category    string // length, complexity, history, expiry
}

// NewPasswordPolicyDetector 创建密码策略检测器
func NewPasswordPolicyDetector() *PasswordPolicyDetector {
	detector := &PasswordPolicyDetector{
		id:          "password-policy-comprehensive",
		name:        "密码策略安全检测器",
		category:    "web",
		severity:    "medium",
		cve:         []string{"CVE-2021-44228", "CVE-2020-1472", "CVE-2019-19781", "CVE-2018-13379"},
		cwe:         []string{"CWE-521", "CWE-522", "CWE-523", "CWE-620", "CWE-256", "CWE-257"},
		version:     "1.0.0",
		author:      "Security Team",
		description: "检测密码策略安全问题，包括密码复杂度、密码历史、密码过期、密码重置等多种密码策略安全问题",
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		enabled:     true,
	}

	// 初始化默认配置
	detector.config = &plugins.DetectorConfig{
		Enabled:         true,
		Timeout:         25 * time.Second, // 密码策略检测需要较长时间
		MaxRetries:      3,                // 密码策略检测可以多次重试
		Concurrency:     4,                // 较低并发数避免触发防护
		RateLimit:       4,                // 较低速率限制
		FollowRedirects: true,             // 跟随重定向检查密码策略
		VerifySSL:       false,
		MaxResponseSize: 3 * 1024 * 1024, // 3MB，密码策略响应可能较大
	}

	// 初始化HTTP客户端
	detector.httpClient = &http.Client{
		Timeout: detector.config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !detector.config.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 初始化检测数据
	detector.initializePasswordPolicyPaths()
	detector.initializePasswordTestPayloads()
	detector.initializeWeakPasswordPatterns()
	detector.initializePolicyViolationTests()
	detector.initializePasswordResetPaths()
	detector.initializePasswordChangePaths()
	detector.initializePatterns()

	return detector
}

// 实现 VulnerabilityDetector 接口

func (d *PasswordPolicyDetector) GetID() string            { return d.id }
func (d *PasswordPolicyDetector) GetName() string          { return d.name }
func (d *PasswordPolicyDetector) GetCategory() string      { return d.category }
func (d *PasswordPolicyDetector) GetSeverity() string      { return d.severity }
func (d *PasswordPolicyDetector) GetCVE() []string         { return d.cve }
func (d *PasswordPolicyDetector) GetCWE() []string         { return d.cwe }
func (d *PasswordPolicyDetector) GetVersion() string       { return d.version }
func (d *PasswordPolicyDetector) GetAuthor() string        { return d.author }
func (d *PasswordPolicyDetector) GetCreatedAt() time.Time  { return d.createdAt }
func (d *PasswordPolicyDetector) GetUpdatedAt() time.Time  { return d.updatedAt }
func (d *PasswordPolicyDetector) GetTargetTypes() []string { return []string{"http", "https"} }
func (d *PasswordPolicyDetector) GetRequiredPorts() []int {
	return []int{80, 443, 8080, 8443, 3000, 4200, 5000, 8000, 9000}
}
func (d *PasswordPolicyDetector) GetRequiredServices() []string {
	return []string{"http", "https", "web", "api"}
}
func (d *PasswordPolicyDetector) GetRequiredHeaders() []string      { return []string{} }
func (d *PasswordPolicyDetector) GetRequiredTechnologies() []string { return []string{} }
func (d *PasswordPolicyDetector) GetDependencies() []string         { return []string{} }
func (d *PasswordPolicyDetector) IsEnabled() bool                   { return d.enabled && d.config.Enabled }
func (d *PasswordPolicyDetector) SetEnabled(enabled bool) {
	d.enabled = enabled
	d.updatedAt = time.Now()
}

func (d *PasswordPolicyDetector) GetConfiguration() *plugins.DetectorConfig {
	return d.config
}

func (d *PasswordPolicyDetector) SetConfiguration(config *plugins.DetectorConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	d.config = config
	d.updatedAt = time.Now()
	return nil
}

func (d *PasswordPolicyDetector) Initialize() error {
	if d.config.Timeout <= 0 {
		d.config.Timeout = 25 * time.Second
	}
	if d.config.MaxRetries < 0 {
		d.config.MaxRetries = 3
	}
	if d.config.Concurrency <= 0 {
		d.config.Concurrency = 4
	}

	d.httpClient.Timeout = d.config.Timeout
	return nil
}

func (d *PasswordPolicyDetector) Cleanup() error {
	if d.httpClient != nil {
		d.httpClient.CloseIdleConnections()
	}
	return nil
}

func (d *PasswordPolicyDetector) Validate() error {
	if d.id == "" {
		return fmt.Errorf("检测器ID不能为空")
	}
	if d.name == "" {
		return fmt.Errorf("检测器名称不能为空")
	}
	if len(d.passwordPolicyPaths) == 0 {
		return fmt.Errorf("密码策略路径不能为空")
	}
	if len(d.passwordTestPayloads) == 0 {
		return fmt.Errorf("密码测试载荷不能为空")
	}
	if len(d.policyViolationTests) == 0 {
		return fmt.Errorf("策略违规测试不能为空")
	}
	return nil
}

// IsApplicable 检查是否适用于目标
func (d *PasswordPolicyDetector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查目标类型
	if target.Type != "url" && target.Protocol != "http" && target.Protocol != "https" {
		return false
	}

	// 密码策略检测适用于有认证功能的Web应用
	// 检查是否有密码策略相关的特征
	if d.hasPasswordPolicyFeatures(target) {
		return true
	}

	// 对于有表单或认证的Web应用，也适用
	if len(target.Forms) > 0 {
		for _, form := range target.Forms {
			for fieldName, fieldType := range form.Fields {
				fieldNameLower := strings.ToLower(fieldName)
				fieldTypeLower := strings.ToLower(fieldType)
				if strings.Contains(fieldNameLower, "password") ||
					strings.Contains(fieldNameLower, "passwd") ||
					strings.Contains(fieldNameLower, "pwd") ||
					strings.Contains(fieldTypeLower, "password") ||
					strings.Contains(fieldNameLower, "密码") {
					return true
				}
			}
		}
	}

	// 对于密码策略相关的URL，也适用
	targetLower := strings.ToLower(target.URL)
	passwordKeywords := []string{
		"password", "passwd", "pwd", "pass", "login", "auth", "signin", "register",
		"signup", "account", "user", "profile", "settings", "security", "policy",
		"reset", "change", "update", "modify", "forgot", "recover", "strength",
		"密码", "登录", "注册", "账户", "用户", "设置", "安全", "策略",
		"重置", "修改", "更新", "忘记", "恢复", "强度", "复杂度",
	}

	for _, keyword := range passwordKeywords {
		if strings.Contains(targetLower, keyword) {
			return true
		}
	}

	return true // 密码策略是通用Web安全问题，默认适用于所有Web目标
}

// hasPasswordPolicyFeatures 检查是否有密码策略功能
func (d *PasswordPolicyDetector) hasPasswordPolicyFeatures(target *plugins.ScanTarget) bool {
	// 检查头部中是否有密码策略相关信息
	for key, value := range target.Headers {
		keyLower := strings.ToLower(key)
		valueLower := strings.ToLower(value)

		if strings.Contains(keyLower, "password") ||
			strings.Contains(keyLower, "auth") ||
			strings.Contains(keyLower, "security") ||
			strings.Contains(valueLower, "password") ||
			strings.Contains(valueLower, "auth") ||
			strings.Contains(valueLower, "security") ||
			strings.Contains(valueLower, "policy") ||
			strings.Contains(valueLower, "strength") {
			return true
		}
	}

	// 检查技术栈中是否有密码策略相关技术
	for _, tech := range target.Technologies {
		techNameLower := strings.ToLower(tech.Name)

		passwordTechnologies := []string{
			"spring security", "oauth", "jwt", "passport", "auth0", "okta", "ldap",
			"active directory", "kerberos", "saml", "openid", "cas", "shiro",
			"security", "authentication", "authorization", "password", "policy",
			"安全", "认证", "授权", "密码", "策略", "验证", "登录",
		}

		for _, passwordTech := range passwordTechnologies {
			if strings.Contains(techNameLower, passwordTech) {
				return true
			}
		}
	}

	// 检查链接中是否有密码策略相关链接
	for _, link := range target.Links {
		linkURLLower := strings.ToLower(link.URL)
		linkTextLower := strings.ToLower(link.Text)

		if strings.Contains(linkURLLower, "password") ||
			strings.Contains(linkURLLower, "auth") ||
			strings.Contains(linkURLLower, "login") ||
			strings.Contains(linkURLLower, "security") ||
			strings.Contains(linkTextLower, "password") ||
			strings.Contains(linkTextLower, "auth") ||
			strings.Contains(linkTextLower, "login") ||
			strings.Contains(linkTextLower, "security") ||
			strings.Contains(linkTextLower, "policy") ||
			strings.Contains(linkTextLower, "密码") ||
			strings.Contains(linkTextLower, "安全") ||
			strings.Contains(linkTextLower, "策略") ||
			strings.Contains(linkTextLower, "登录") {
			return true
		}
	}

	// 检查Cookie中是否有密码策略相关信息
	for _, cookie := range target.Cookies {
		cookieNameLower := strings.ToLower(cookie.Name)
		cookieValueLower := strings.ToLower(cookie.Value)

		if strings.Contains(cookieNameLower, "password") ||
			strings.Contains(cookieNameLower, "auth") ||
			strings.Contains(cookieNameLower, "security") ||
			strings.Contains(cookieNameLower, "policy") ||
			strings.Contains(cookieValueLower, "password") ||
			strings.Contains(cookieValueLower, "auth") ||
			strings.Contains(cookieValueLower, "security") ||
			strings.Contains(cookieValueLower, "policy") {
			return true
		}
	}

	return false
}

// Detect 执行检测
func (d *PasswordPolicyDetector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	if !d.IsEnabled() {
		return nil, fmt.Errorf("检测器未启用")
	}

	if !d.IsApplicable(target) {
		return &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.id,
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
		}, nil
	}

	// 执行多种密码策略检测方法
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload string
	var vulnerableRequest string
	var vulnerableResponse string

	// 1. 密码复杂度检测
	complexityEvidence, complexityConfidence, complexityPayload, complexityRequest, complexityResponse := d.detectPasswordComplexity(ctx, target)
	if complexityConfidence > maxConfidence {
		maxConfidence = complexityConfidence
		vulnerablePayload = complexityPayload
		vulnerableRequest = complexityRequest
		vulnerableResponse = complexityResponse
	}
	evidence = append(evidence, complexityEvidence...)

	// 2. 密码历史检测
	historyEvidence, historyConfidence, historyPayload, historyRequest, historyResponse := d.detectPasswordHistory(ctx, target)
	if historyConfidence > maxConfidence {
		maxConfidence = historyConfidence
		vulnerablePayload = historyPayload
		vulnerableRequest = historyRequest
		vulnerableResponse = historyResponse
	}
	evidence = append(evidence, historyEvidence...)

	// 3. 密码过期检测
	expiryEvidence, expiryConfidence, expiryPayload, expiryRequest, expiryResponse := d.detectPasswordExpiry(ctx, target)
	if expiryConfidence > maxConfidence {
		maxConfidence = expiryConfidence
		vulnerablePayload = expiryPayload
		vulnerableRequest = expiryRequest
		vulnerableResponse = expiryResponse
	}
	evidence = append(evidence, expiryEvidence...)

	// 4. 密码重置检测
	resetEvidence, resetConfidence, resetPayload, resetRequest, resetResponse := d.detectPasswordReset(ctx, target)
	if resetConfidence > maxConfidence {
		maxConfidence = resetConfidence
		vulnerablePayload = resetPayload
		vulnerableRequest = resetRequest
		vulnerableResponse = resetResponse
	}
	evidence = append(evidence, resetEvidence...)

	// 判断是否存在漏洞
	isVulnerable := maxConfidence >= 0.6

	result := &plugins.DetectionResult{
		VulnerabilityID:   d.generateVulnID(target),
		DetectorID:        d.id,
		IsVulnerable:      isVulnerable,
		Confidence:        maxConfidence,
		Severity:          d.severity,
		Title:             "密码策略安全问题",
		Description:       "检测到密码策略安全问题，应用程序的密码策略配置存在安全缺陷，可能导致弱密码攻击",
		Evidence:          evidence,
		Payload:           vulnerablePayload,
		Request:           vulnerableRequest,
		Response:          vulnerableResponse,
		RiskScore:         d.calculateRiskScore(maxConfidence),
		Remediation:       "加强密码复杂度要求、实施密码历史检查、设置密码过期策略、完善密码重置机制",
		References:        []string{"https://owasp.org/www-community/vulnerabilities/Weak_password_policy", "https://cwe.mitre.org/data/definitions/521.html", "https://cwe.mitre.org/data/definitions/522.html"},
		Tags:              []string{"password", "policy", "complexity", "history", "expiry", "reset"},
		DetectedAt:        time.Now(),
		VerificationState: "pending",
	}

	return result, nil
}

// Verify 验证检测结果
func (d *PasswordPolicyDetector) Verify(ctx context.Context, target *plugins.ScanTarget, result *plugins.DetectionResult) (*plugins.VerificationResult, error) {
	if !result.IsVulnerable {
		return &plugins.VerificationResult{
			IsVerified: false,
			Confidence: 0.0,
			Method:     "no-verification-needed",
			Notes:      "未检测到漏洞，无需验证",
			VerifiedAt: time.Now(),
		}, nil
	}

	// 使用更保守的方法进行验证
	verificationMethods := []string{
		"password-complexity-verification",
		"password-history-verification",
		"password-expiry-verification",
		"password-reset-verification",
	}

	var evidence []plugins.Evidence
	verificationConfidence := 0.0

	for _, method := range verificationMethods {
		// 根据方法进行验证
		methodConfidence := d.verifyPasswordPolicyMethod(ctx, target, method)
		if methodConfidence > 0.4 {
			verificationConfidence += 0.2
			evidence = append(evidence, plugins.Evidence{
				Type:        "verification",
				Description: fmt.Sprintf("验证方法确认了密码策略问题: %s", method),
				Content:     fmt.Sprintf("验证方法: %s, 置信度: %.2f", method, methodConfidence),
				Location:    target.URL,
				Timestamp:   time.Now(),
			})
		}
	}

	isVerified := verificationConfidence >= 0.5

	return &plugins.VerificationResult{
		IsVerified: isVerified,
		Confidence: verificationConfidence,
		Method:     "password-policy-verification",
		Evidence:   evidence,
		Notes:      fmt.Sprintf("使用密码策略验证方法验证，置信度: %.2f", verificationConfidence),
		VerifiedAt: time.Now(),
	}, nil
}

// 辅助方法

// generateVulnID 生成漏洞ID
func (d *PasswordPolicyDetector) generateVulnID(target *plugins.ScanTarget) string {
	return fmt.Sprintf("password_policy_%s_%d", target.ID, time.Now().Unix())
}

// calculateRiskScore 计算风险评分
func (d *PasswordPolicyDetector) calculateRiskScore(confidence float64) float64 {
	// 基础风险评分 (密码策略通常是中等风险漏洞)
	baseScore := 6.0

	// 根据置信度调整评分
	adjustedScore := baseScore * confidence

	// 确保评分在合理范围内
	if adjustedScore > 10.0 {
		adjustedScore = 10.0
	}
	if adjustedScore < 0.0 {
		adjustedScore = 0.0
	}

	return adjustedScore
}

// verifyPasswordPolicyMethod 验证密码策略方法
func (d *PasswordPolicyDetector) verifyPasswordPolicyMethod(ctx context.Context, target *plugins.ScanTarget, method string) float64 {
	switch method {
	case "password-complexity-verification":
		return d.verifyPasswordComplexity(ctx, target)
	case "password-history-verification":
		return d.verifyPasswordHistory(ctx, target)
	case "password-expiry-verification":
		return d.verifyPasswordExpiry(ctx, target)
	case "password-reset-verification":
		return d.verifyPasswordReset(ctx, target)
	default:
		return 0.0
	}
}

// verifyPasswordComplexity 验证密码复杂度
func (d *PasswordPolicyDetector) verifyPasswordComplexity(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的密码复杂度验证
	if d.hasPasswordPolicyFeatures(target) {
		return 0.7 // 有密码策略特征的目标可能有复杂度问题
	}
	return 0.3
}

// verifyPasswordHistory 验证密码历史
func (d *PasswordPolicyDetector) verifyPasswordHistory(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的密码历史验证
	if d.hasPasswordPolicyFeatures(target) {
		return 0.6 // 有密码策略特征的目标可能有历史问题
	}
	return 0.2
}

// verifyPasswordExpiry 验证密码过期
func (d *PasswordPolicyDetector) verifyPasswordExpiry(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的密码过期验证
	if d.hasPasswordPolicyFeatures(target) {
		return 0.5 // 有密码策略特征的目标可能有过期问题
	}
	return 0.2
}

// verifyPasswordReset 验证密码重置
func (d *PasswordPolicyDetector) verifyPasswordReset(ctx context.Context, target *plugins.ScanTarget) float64 {
	// 简化的密码重置验证
	if d.hasPasswordPolicyFeatures(target) {
		return 0.6 // 有密码策略特征的目标可能有重置问题
	}
	return 0.3
}

// initializePasswordPolicyPaths 初始化密码策略路径列表
func (d *PasswordPolicyDetector) initializePasswordPolicyPaths() {
	d.passwordPolicyPaths = []string{
		// 密码策略相关路径
		"/password/policy", "/password-policy", "/pwd/policy", "/pwd-policy",
		"/security/password", "/security/policy", "/auth/password", "/auth/policy",
		"/account/password", "/account/policy", "/user/password", "/user/policy",
		"/profile/password", "/profile/security", "/settings/password", "/settings/security",
		"/admin/password", "/admin/policy", "/management/password", "/management/policy",

		// 密码修改路径
		"/password/change", "/password-change", "/pwd/change", "/pwd-change",
		"/change-password", "/changepassword", "/change_password", "/update-password",
		"/update_password", "/updatepassword", "/modify-password", "/modify_password",
		"/password/update", "/password-update", "/pwd/update", "/pwd-update",

		// 密码重置路径
		"/password/reset", "/password-reset", "/pwd/reset", "/pwd-reset",
		"/reset-password", "/resetpassword", "/reset_password", "/forgot-password",
		"/forgot_password", "/forgotpassword", "/password/forgot", "/pwd/forgot",
		"/password/recover", "/password-recover", "/recover-password", "/recover_password",

		// 密码强度检查路径
		"/password/strength", "/password-strength", "/pwd/strength", "/pwd-strength",
		"/password/check", "/password-check", "/pwd/check", "/pwd-check",
		"/password/validate", "/password-validate", "/pwd/validate", "/pwd-validate",
		"/password/verify", "/password-verify", "/pwd/verify", "/pwd-verify",

		// 认证相关路径
		"/auth/login", "/auth/signin", "/auth/register", "/auth/signup",
		"/login", "/signin", "/register", "/signup", "/authentication",
		"/account/login", "/account/signin", "/user/login", "/user/signin",
		"/member/login", "/member/signin", "/customer/login", "/customer/signin",

		// 安全设置路径
		"/security", "/security/settings", "/security/config", "/security/policy",
		"/settings/security", "/config/security", "/policy/security", "/admin/security",
		"/management/security", "/system/security", "/account/security", "/user/security",

		// API路径
		"/api/password", "/api/auth", "/api/security", "/api/policy",
		"/api/v1/password", "/api/v1/auth", "/api/v1/security", "/api/v1/policy",
		"/api/v2/password", "/api/v2/auth", "/api/v2/security", "/api/v2/policy",
		"/rest/password", "/rest/auth", "/rest/security", "/rest/policy",
		"/service/password", "/service/auth", "/service/security", "/service/policy",

		// 中文路径
		"/密码/策略", "/密码策略", "/密码/修改", "/密码修改", "/密码/重置", "/密码重置",
		"/安全/密码", "/安全密码", "/安全/策略", "/安全策略", "/用户/密码", "/用户密码",
		"/账户/密码", "/账户密码", "/设置/密码", "/设置密码", "/管理/密码", "/管理密码",
	}
}

// initializePasswordTestPayloads 初始化密码测试载荷列表
func (d *PasswordPolicyDetector) initializePasswordTestPayloads() {
	d.passwordTestPayloads = []string{
		// 弱密码测试
		"", "a", "12", "abc", "123", "1234", "12345", "123456", "1234567", "12345678",
		"password", "Password", "PASSWORD", "pass", "Pass", "PASS", "pwd", "Pwd", "PWD",
		"admin", "Admin", "ADMIN", "root", "Root", "ROOT", "user", "User", "USER",
		"test", "Test", "TEST", "guest", "Guest", "GUEST", "demo", "Demo", "DEMO",

		// 常见弱密码
		"password123", "Password123", "PASSWORD123", "admin123", "Admin123", "ADMIN123",
		"root123", "Root123", "ROOT123", "user123", "User123", "USER123",
		"test123", "Test123", "TEST123", "guest123", "Guest123", "GUEST123",
		"*********", "*********0", "qwerty", "QWERTY", "Qwerty", "qwertyuiop",
		"asdfgh", "ASDFGH", "Asdfgh", "zxcvbn", "ZXCVBN", "Zxcvbn", "qazwsx",

		// 键盘模式密码
		"123qwe", "123QWE", "qwe123", "QWE123", "abc123", "ABC123", "123abc", "123ABC",
		"asd123", "ASD123", "123asd", "123ASD", "zxc123", "ZXC123", "123zxc", "123ZXC",
		"qweasd", "QWEASD", "asdzxc", "ASDZXC", "zxcasd", "ZXCASD", "qwezxc", "QWEZXC",

		// 日期模式密码
		"20230101", "20231231", "19900101", "19991231", "20000101", "20241231",
		"010101", "123101", "311201", "010120", "311220", "123120", "010123", "311223",
		"2023", "2024", "1990", "1999", "2000", "1980", "1970", "2030", "2025",

		// 重复字符密码
		"aaa", "AAA", "111", "000", "aaaa", "AAAA", "1111", "0000", "aaaaa", "AAAAA",
		"11111", "00000", "aaaaaa", "AAAAAA", "111111", "000000", "aaaaaaa", "AAAAAAA",
		"1111111", "0000000", "aaaaaaaa", "AAAAAAAA", "11111111", "00000000",

		// 序列密码
		"abcd", "ABCD", "abcde", "ABCDE", "abcdef", "ABCDEF", "abcdefg", "ABCDEFG",
		"abcdefgh", "ABCDEFGH", "abcdefghi", "ABCDEFGHI", "abcdefghij", "ABCDEFGHIJ",
		"1234", "12345", "123456", "1234567", "12345678", "*********", "*********0",

		// 公司/组织相关密码
		"company", "Company", "COMPANY", "company123", "Company123", "COMPANY123",
		"organization", "Organization", "ORGANIZATION", "corp", "Corp", "CORP",
		"enterprise", "Enterprise", "ENTERPRISE", "business", "Business", "BUSINESS",

		// 中文弱密码
		"密码", "密码123", "用户", "用户123", "管理员", "管理员123", "测试", "测试123",
		"账户", "账户123", "系统", "系统123", "安全", "安全123", "登录", "登录123",
		"注册", "注册123", "验证", "验证123", "确认", "确认123", "修改", "修改123",
	}
}

// initializeWeakPasswordPatterns 初始化弱密码模式列表
func (d *PasswordPolicyDetector) initializeWeakPasswordPatterns() {
	d.weakPasswordPatterns = []string{
		// 长度模式
		"^.{1,3}$", // 1-3字符
		"^.{1,5}$", // 1-5字符
		"^.{1,7}$", // 1-7字符

		// 纯数字模式
		"^\\d+$",     // 纯数字
		"^\\d{1,8}$", // 1-8位数字

		// 纯字母模式
		"^[a-z]+$",    // 纯小写字母
		"^[A-Z]+$",    // 纯大写字母
		"^[a-zA-Z]+$", // 纯字母

		// 重复字符模式
		"^(.)\\1+$",    // 重复字符
		"^(.)\\1{2,}$", // 3个以上重复字符

		// 序列模式
		"(012|123|234|345|456|567|678|789|890)",
		"(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)",
		"(qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)",

		// 常见弱密码模式
		"(?i)(password|passwd|pwd|pass)",
		"(?i)(admin|root|user|test|guest|demo)",
		"(?i)(company|corp|enterprise|business|organization)",
		"(?i)(login|signin|auth|access|account)",
		"(?i)(welcome|default|secret|private|public)",
		"(?i)(master|super|system|service|manager)",
		"(?i)(temp|temporary|sample|example|dummy)",

		// 日期模式
		"\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])", // YYYYMMDD
		"(0[1-9]|[12]\\d|3[01])(0[1-9]|1[0-2])\\d{4}", // DDMMYYYY
		"(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{4}", // MMDDYYYY
		"\\d{4}", // 年份
		"\\d{6}", // YYMMDD或DDMMYY
		"\\d{8}", // YYYYMMDD或DDMMYYYY

		// 键盘模式
		"(?i)(qwerty|qwertyuiop|asdfgh|asdfghjkl|zxcvbn|zxcvbnm)",
		"(?i)(qweasd|asdzxc|zxcasd|qwezxc|qazwsx|wsxedc|edcrfv)",
		"(?i)(123qwe|qwe123|abc123|123abc|asd123|123asd)",

		// 中文弱密码模式
		"(?i)(密码|用户|管理员|测试|账户|系统|安全|登录)",
		"(?i)(注册|验证|确认|修改|重置|恢复|找回)",
		"(?i)(公司|企业|组织|机构|部门|团队|小组)",
	}
}

// initializePolicyViolationTests 初始化策略违规测试列表
func (d *PasswordPolicyDetector) initializePolicyViolationTests() {
	d.policyViolationTests = []PolicyTest{
		// 长度策略测试
		{Name: "空密码测试", Password: "", Description: "测试空密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "单字符密码测试", Password: "a", Description: "测试单字符密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "双字符密码测试", Password: "ab", Description: "测试双字符密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "三字符密码测试", Password: "abc", Description: "测试三字符密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "四字符密码测试", Password: "abcd", Description: "测试四字符密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "五字符密码测试", Password: "abcde", Description: "测试五字符密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "六字符密码测试", Password: "abcdef", Description: "测试六字符密码是否被接受", Expected: "should_reject", Category: "length"},
		{Name: "七字符密码测试", Password: "abcdefg", Description: "测试七字符密码是否被接受", Expected: "should_reject", Category: "length"},

		// 复杂度策略测试
		{Name: "纯数字密码测试", Password: "12345678", Description: "测试纯数字密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "纯小写字母密码测试", Password: "abcdefgh", Description: "测试纯小写字母密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "纯大写字母密码测试", Password: "ABCDEFGH", Description: "测试纯大写字母密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "数字+小写字母密码测试", Password: "abc12345", Description: "测试数字+小写字母密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "数字+大写字母密码测试", Password: "ABC12345", Description: "测试数字+大写字母密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "大小写字母密码测试", Password: "AbCdEfGh", Description: "测试大小写字母密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "强密码测试", Password: "Abc123!@#", Description: "测试强密码是否被接受", Expected: "should_accept", Category: "complexity"},

		// 常见弱密码测试
		{Name: "password测试", Password: "password", Description: "测试常见弱密码password是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "Password测试", Password: "Password", Description: "测试常见弱密码Password是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "password123测试", Password: "password123", Description: "测试常见弱密码password123是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "admin测试", Password: "admin", Description: "测试常见弱密码admin是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "admin123测试", Password: "admin123", Description: "测试常见弱密码admin123是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "123456测试", Password: "123456", Description: "测试常见弱密码123456是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "qwerty测试", Password: "qwerty", Description: "测试常见弱密码qwerty是否被接受", Expected: "should_reject", Category: "complexity"},

		// 重复字符测试
		{Name: "重复字符aaa测试", Password: "aaaaaaaa", Description: "测试重复字符密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "重复字符111测试", Password: "11111111", Description: "测试重复数字密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "重复字符AAA测试", Password: "AAAAAAAA", Description: "测试重复大写字母密码是否被接受", Expected: "should_reject", Category: "complexity"},

		// 序列字符测试
		{Name: "序列字符abc测试", Password: "abcdefgh", Description: "测试序列字符密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "序列数字123测试", Password: "12345678", Description: "测试序列数字密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "键盘序列qwe测试", Password: "qwertyui", Description: "测试键盘序列密码是否被接受", Expected: "should_reject", Category: "complexity"},

		// 历史密码测试
		{Name: "旧密码重用测试", Password: "oldpassword", Description: "测试旧密码重用是否被接受", Expected: "should_reject", Category: "history"},
		{Name: "之前密码重用测试", Password: "previouspassword", Description: "测试之前密码重用是否被接受", Expected: "should_reject", Category: "history"},
		{Name: "相同密码重用测试", Password: "samepassword", Description: "测试相同密码重用是否被接受", Expected: "should_reject", Category: "history"},
		{Name: "最近密码重用测试", Password: "recentpassword", Description: "测试最近密码重用是否被接受", Expected: "should_reject", Category: "history"},

		// 过期密码测试
		{Name: "过期密码测试", Password: "expiredpassword", Description: "测试过期密码是否被接受", Expected: "should_reject", Category: "expiry"},
		{Name: "过时密码测试", Password: "outdatedpassword", Description: "测试过时密码是否被接受", Expected: "should_reject", Category: "expiry"},
		{Name: "失效密码测试", Password: "invalidpassword", Description: "测试失效密码是否被接受", Expected: "should_reject", Category: "expiry"},

		// 中文密码测试
		{Name: "中文弱密码测试", Password: "密码", Description: "测试中文弱密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "中文密码123测试", Password: "密码123", Description: "测试中文密码123是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "中文用户测试", Password: "用户", Description: "测试中文用户密码是否被接受", Expected: "should_reject", Category: "complexity"},
		{Name: "中文管理员测试", Password: "管理员", Description: "测试中文管理员密码是否被接受", Expected: "should_reject", Category: "complexity"},
	}
}

// initializePasswordResetPaths 初始化密码重置路径列表
func (d *PasswordPolicyDetector) initializePasswordResetPaths() {
	d.passwordResetPaths = []string{
		// 密码重置路径
		"/password/reset", "/password-reset", "/pwd/reset", "/pwd-reset",
		"/reset-password", "/resetpassword", "/reset_password", "/forgot-password",
		"/forgot_password", "/forgotpassword", "/password/forgot", "/pwd/forgot",
		"/password/recover", "/password-recover", "/recover-password", "/recover_password",
		"/password/recovery", "/password-recovery", "/recovery-password", "/recovery_password",

		// 忘记密码路径
		"/forgot", "/forget", "/forgotten", "/forgotpwd", "/forgetpwd",
		"/forgot-pwd", "/forget-pwd", "/forgotten-pwd", "/forgot-pass", "/forget-pass",
		"/forgotten-pass", "/forgotpass", "/forgetpass", "/forgottenpass",

		// 密码恢复路径
		"/recover", "/recovery", "/restore", "/retrieve", "/reclaim",
		"/recover-pwd", "/recovery-pwd", "/restore-pwd", "/retrieve-pwd", "/reclaim-pwd",
		"/recover-pass", "/recovery-pass", "/restore-pass", "/retrieve-pass", "/reclaim-pass",

		// 密码找回路径
		"/find-password", "/find_password", "/findpassword", "/find-pwd", "/find_pwd",
		"/findpwd", "/find-pass", "/find_pass", "/findpass", "/get-password",
		"/get_password", "/getpassword", "/get-pwd", "/get_pwd", "/getpwd",

		// 中文密码重置路径
		"/密码重置", "/密码找回", "/密码恢复", "/忘记密码", "/找回密码", "/恢复密码",
		"/重置密码", "/密码修复", "/密码救援", "/密码帮助", "/密码支持", "/密码服务",
	}
}

// initializePasswordChangePaths 初始化密码修改路径列表
func (d *PasswordPolicyDetector) initializePasswordChangePaths() {
	d.passwordChangePaths = []string{
		// 密码修改路径
		"/password/change", "/password-change", "/pwd/change", "/pwd-change",
		"/change-password", "/changepassword", "/change_password", "/update-password",
		"/update_password", "/updatepassword", "/modify-password", "/modify_password",
		"/password/update", "/password-update", "/pwd/update", "/pwd-update",

		// 密码编辑路径
		"/password/edit", "/password-edit", "/pwd/edit", "/pwd-edit",
		"/edit-password", "/editpassword", "/edit_password", "/alter-password",
		"/alter_password", "/alterpassword", "/revise-password", "/revise_password",

		// 密码设置路径
		"/password/set", "/password-set", "/pwd/set", "/pwd-set",
		"/set-password", "/setpassword", "/set_password", "/new-password",
		"/new_password", "/newpassword", "/create-password", "/create_password",

		// 中文密码修改路径
		"/密码修改", "/密码更新", "/密码编辑", "/密码设置", "/修改密码", "/更新密码",
		"/编辑密码", "/设置密码", "/新密码", "/创建密码", "/建立密码", "/配置密码",
	}
}

// initializePatterns 初始化检测模式
func (d *PasswordPolicyDetector) initializePatterns() {
	// 密码策略检测模式 - 检测密码策略相关的响应内容
	policyPatternStrings := []string{
		// 通用密码策略模式
		`(?i)password\s+(policy|requirement|rule|strength|complexity)`,
		`(?i)policy\s+(password|pwd|pass|security|auth)`,
		`(?i)requirement\s+(password|pwd|pass|security|auth)`,
		`(?i)rule\s+(password|pwd|pass|security|auth)`,
		`(?i)strength\s+(password|pwd|pass|security|auth)`,
		`(?i)complexity\s+(password|pwd|pass|security|auth)`,

		// 密码验证模式
		`(?i)password\s+(validation|check|verify|test|audit)`,
		`(?i)validation\s+(password|pwd|pass|security|auth)`,
		`(?i)check\s+(password|pwd|pass|security|auth)`,
		`(?i)verify\s+(password|pwd|pass|security|auth)`,
		`(?i)test\s+(password|pwd|pass|security|auth)`,
		`(?i)audit\s+(password|pwd|pass|security|auth)`,

		// 密码历史模式
		`(?i)password\s+(history|previous|recent|used|reuse)`,
		`(?i)history\s+(password|pwd|pass|security|auth)`,
		`(?i)previous\s+(password|pwd|pass|security|auth)`,
		`(?i)recent\s+(password|pwd|pass|security|auth)`,
		`(?i)used\s+(password|pwd|pass|security|auth)`,
		`(?i)reuse\s+(password|pwd|pass|security|auth)`,

		// 密码过期模式
		`(?i)password\s+(expiry|expiration|expired|expire|age|aging)`,
		`(?i)expiry\s+(password|pwd|pass|security|auth)`,
		`(?i)expiration\s+(password|pwd|pass|security|auth)`,
		`(?i)expired\s+(password|pwd|pass|security|auth)`,
		`(?i)expire\s+(password|pwd|pass|security|auth)`,
		`(?i)age\s+(password|pwd|pass|security|auth)`,

		// 密码重置模式
		`(?i)password\s+(reset|forgot|recover|recovery|change|update)`,
		`(?i)reset\s+(password|pwd|pass|security|auth)`,
		`(?i)forgot\s+(password|pwd|pass|security|auth)`,
		`(?i)recover\s+(password|pwd|pass|security|auth)`,
		`(?i)recovery\s+(password|pwd|pass|security|auth)`,
		`(?i)change\s+(password|pwd|pass|security|auth)`,

		// 中文密码策略模式
		`(?i)(密码|口令).*策略`,
		`(?i)(密码|口令).*要求`,
		`(?i)(密码|口令).*规则`,
		`(?i)(密码|口令).*强度`,
		`(?i)(密码|口令).*复杂度`,
		`(?i)(密码|口令).*验证`,
		`(?i)(密码|口令).*检查`,
		`(?i)(密码|口令).*历史`,
		`(?i)(密码|口令).*过期`,
		`(?i)(密码|口令).*重置`,
	}

	d.policyPatterns = make([]*regexp.Regexp, 0, len(policyPatternStrings))
	for _, pattern := range policyPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.policyPatterns = append(d.policyPatterns, compiled)
		}
	}

	// 弱点检测模式 - 检测密码策略弱点相关的内容
	weaknessPatternStrings := []string{
		// 通用弱点模式
		`(?i)weak\s+(password|pwd|pass|security|auth)`,
		`(?i)simple\s+(password|pwd|pass|security|auth)`,
		`(?i)easy\s+(password|pwd|pass|security|auth)`,
		`(?i)common\s+(password|pwd|pass|security|auth)`,
		`(?i)default\s+(password|pwd|pass|security|auth)`,
		`(?i)insecure\s+(password|pwd|pass|security|auth)`,

		// 密码弱点模式
		`(?i)password\s+(weak|simple|easy|common|default|insecure)`,
		`(?i)password\s+(too\s+)?(short|long|simple|weak)`,
		`(?i)password\s+(not\s+)?(complex|strong|secure|safe)`,
		`(?i)password\s+(must\s+)?(contain|include|have|use)`,
		`(?i)password\s+(should\s+)?(contain|include|have|use)`,
		`(?i)password\s+(cannot\s+)?(contain|include|have|use)`,

		// 复杂度弱点模式
		`(?i)complexity\s+(low|poor|weak|insufficient|inadequate)`,
		`(?i)strength\s+(low|poor|weak|insufficient|inadequate)`,
		`(?i)security\s+(low|poor|weak|insufficient|inadequate)`,
		`(?i)quality\s+(low|poor|weak|insufficient|inadequate)`,
		`(?i)level\s+(low|poor|weak|insufficient|inadequate)`,
		`(?i)score\s+(low|poor|weak|insufficient|inadequate)`,

		// 历史弱点模式
		`(?i)history\s+(not\s+)?(checked|verified|enforced|enabled)`,
		`(?i)reuse\s+(allowed|permitted|enabled|possible)`,
		`(?i)previous\s+(password|pwd|pass)\s+(allowed|permitted|enabled)`,
		`(?i)same\s+(password|pwd|pass)\s+(allowed|permitted|enabled)`,
		`(?i)repeated\s+(password|pwd|pass)\s+(allowed|permitted|enabled)`,
		`(?i)duplicate\s+(password|pwd|pass)\s+(allowed|permitted|enabled)`,

		// 过期弱点模式
		`(?i)expiry\s+(not\s+)?(set|configured|enabled|enforced)`,
		`(?i)expiration\s+(not\s+)?(set|configured|enabled|enforced)`,
		`(?i)aging\s+(not\s+)?(set|configured|enabled|enforced)`,
		`(?i)lifetime\s+(not\s+)?(set|configured|enabled|enforced)`,
		`(?i)validity\s+(not\s+)?(set|configured|enabled|enforced)`,
		`(?i)duration\s+(not\s+)?(set|configured|enabled|enforced)`,

		// 中文弱点模式
		`(?i)(密码|口令).*(弱|简单|容易|常见|默认|不安全)`,
		`(?i)(复杂度|强度|安全性|质量|等级|分数).*(低|差|弱|不足|不够)`,
		`(?i)(历史|重用|之前|相同|重复|重复).*(未|不|没有).*(检查|验证|强制|启用)`,
		`(?i)(过期|失效|有效期|生命周期).*(未|不|没有).*(设置|配置|启用|强制)`,
	}

	d.weaknessPatterns = make([]*regexp.Regexp, 0, len(weaknessPatternStrings))
	for _, pattern := range weaknessPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.weaknessPatterns = append(d.weaknessPatterns, compiled)
		}
	}

	// 安全检测模式 - 检测密码策略安全相关的内容
	securityPatternStrings := []string{
		// 通用安全模式
		`(?i)strong\s+(password|pwd|pass|security|auth)`,
		`(?i)secure\s+(password|pwd|pass|security|auth)`,
		`(?i)safe\s+(password|pwd|pass|security|auth)`,
		`(?i)complex\s+(password|pwd|pass|security|auth)`,
		`(?i)robust\s+(password|pwd|pass|security|auth)`,
		`(?i)advanced\s+(password|pwd|pass|security|auth)`,

		// 密码安全模式
		`(?i)password\s+(strong|secure|safe|complex|robust|advanced)`,
		`(?i)password\s+(meets|satisfies|passes|complies)\s+(requirement|policy|rule)`,
		`(?i)password\s+(accepted|approved|validated|verified|confirmed)`,
		`(?i)password\s+(policy|requirement|rule)\s+(met|satisfied|passed|complied)`,
		`(?i)password\s+(strength|complexity|security)\s+(high|good|excellent|strong)`,
		`(?i)password\s+(quality|level|score)\s+(high|good|excellent|strong)`,

		// 复杂度安全模式
		`(?i)complexity\s+(high|good|excellent|strong|sufficient|adequate)`,
		`(?i)strength\s+(high|good|excellent|strong|sufficient|adequate)`,
		`(?i)security\s+(high|good|excellent|strong|sufficient|adequate)`,
		`(?i)quality\s+(high|good|excellent|strong|sufficient|adequate)`,
		`(?i)level\s+(high|good|excellent|strong|sufficient|adequate)`,
		`(?i)score\s+(high|good|excellent|strong|sufficient|adequate)`,

		// 历史安全模式
		`(?i)history\s+(checked|verified|enforced|enabled|configured)`,
		`(?i)reuse\s+(prevented|blocked|disabled|forbidden|prohibited)`,
		`(?i)previous\s+(password|pwd|pass)\s+(prevented|blocked|disabled|forbidden)`,
		`(?i)same\s+(password|pwd|pass)\s+(prevented|blocked|disabled|forbidden)`,
		`(?i)repeated\s+(password|pwd|pass)\s+(prevented|blocked|disabled|forbidden)`,
		`(?i)duplicate\s+(password|pwd|pass)\s+(prevented|blocked|disabled|forbidden)`,

		// 过期安全模式
		`(?i)expiry\s+(set|configured|enabled|enforced|active)`,
		`(?i)expiration\s+(set|configured|enabled|enforced|active)`,
		`(?i)aging\s+(set|configured|enabled|enforced|active)`,
		`(?i)lifetime\s+(set|configured|enabled|enforced|active)`,
		`(?i)validity\s+(set|configured|enabled|enforced|active)`,
		`(?i)duration\s+(set|configured|enabled|enforced|active)`,

		// 中文安全模式
		`(?i)(密码|口令).*(强|安全|复杂|健壮|高级|先进)`,
		`(?i)(复杂度|强度|安全性|质量|等级|分数).*(高|好|优秀|强|足够|充分)`,
		`(?i)(历史|重用|之前|相同|重复|重复).*(检查|验证|强制|启用|配置)`,
		`(?i)(过期|失效|有效期|生命周期).*(设置|配置|启用|强制|激活)`,
	}

	d.securityPatterns = make([]*regexp.Regexp, 0, len(securityPatternStrings))
	for _, pattern := range securityPatternStrings {
		if compiled, err := regexp.Compile(pattern); err == nil {
			d.securityPatterns = append(d.securityPatterns, compiled)
		}
	}
}
