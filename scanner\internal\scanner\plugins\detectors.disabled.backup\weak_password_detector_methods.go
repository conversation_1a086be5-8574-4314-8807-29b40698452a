package detectors

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// detectLoginPages 检测登录页面
func (d *WeakPasswordDetector) detectLoginPages(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 测试登录页面路径
	for i, path := range d.loginPaths {
		if i >= 15 { // 限制路径数量避免过多请求
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 构造登录页面URL
		loginURL := d.buildLoginURL(target.URL, path)
		
		// 发送登录页面请求
		resp, err := d.sendLoginPageRequest(ctx, loginURL)
		if err != nil {
			continue
		}

		// 检查登录页面响应
		confidence := d.checkLoginPageResponse(resp, path)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = path
			vulnerableRequest = loginURL
			vulnerableResponse = resp
		}

		if confidence > 0.5 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "login-page",
				Description: fmt.Sprintf("发现登录页面 (置信度: %.2f)", confidence),
				Content:     d.extractLoginEvidence(resp, path),
				Location:    loginURL,
				Timestamp:   time.Now(),
			})
		}
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectDefaultCredentials 检测默认凭据
func (d *WeakPasswordDetector) detectDefaultCredentials(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 首先找到登录页面
	loginURL := d.findLoginPage(ctx, target.URL)
	if loginURL == "" {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 测试默认凭据
	for i, cred := range d.defaultCreds {
		if i >= 10 { // 限制凭据数量，避免账户锁定
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送认证请求
		resp, err := d.sendAuthRequest(ctx, loginURL, cred.Username, cred.Password)
		if err != nil {
			continue
		}

		// 检查认证响应
		confidence := d.checkAuthResponse(resp, cred)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("%s:%s", cred.Username, cred.Password)
			vulnerableRequest = loginURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "default-credentials",
				Description: fmt.Sprintf("默认凭据可能有效: %s:%s (置信度: %.2f)", cred.Username, cred.Password, confidence),
				Content:     d.extractAuthEvidence(resp, cred),
				Location:    loginURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Second * 2)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// detectWeakPasswords 检测弱密码
func (d *WeakPasswordDetector) detectWeakPasswords(ctx context.Context, target *plugins.ScanTarget) ([]plugins.Evidence, float64, string, string, string) {
	var evidence []plugins.Evidence
	var maxConfidence float64
	var vulnerablePayload, vulnerableRequest, vulnerableResponse string

	// 首先找到登录页面
	loginURL := d.findLoginPage(ctx, target.URL)
	if loginURL == "" {
		return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
	}

	// 测试常见凭据组合
	for i, cred := range d.commonCreds {
		if i >= 8 { // 限制凭据数量，避免账户锁定
			break
		}

		select {
		case <-ctx.Done():
			return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
		default:
		}

		// 发送认证请求
		resp, err := d.sendAuthRequest(ctx, loginURL, cred.Username, cred.Password)
		if err != nil {
			continue
		}

		// 检查认证响应
		confidence := d.checkAuthResponse(resp, cred)
		if confidence > maxConfidence {
			maxConfidence = confidence
			vulnerablePayload = fmt.Sprintf("%s:%s", cred.Username, cred.Password)
			vulnerableRequest = loginURL
			vulnerableResponse = resp
		}

		if confidence > 0.6 {
			evidence = append(evidence, plugins.Evidence{
				Type:        "weak-password",
				Description: fmt.Sprintf("弱密码可能有效: %s:%s (置信度: %.2f)", cred.Username, cred.Password, confidence),
				Content:     d.extractAuthEvidence(resp, cred),
				Location:    loginURL,
				Timestamp:   time.Now(),
			})
		}

		// 添加延迟避免触发防护
		time.Sleep(time.Second * 3)
	}

	return evidence, maxConfidence, vulnerablePayload, vulnerableRequest, vulnerableResponse
}

// buildLoginURL 构造登录URL
func (d *WeakPasswordDetector) buildLoginURL(baseURL, path string) string {
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return baseURL + path
	}

	parsedURL.Path = path
	parsedURL.RawQuery = ""
	return parsedURL.String()
}

// findLoginPage 查找登录页面
func (d *WeakPasswordDetector) findLoginPage(ctx context.Context, baseURL string) string {
	// 首先检查常见的登录路径
	commonPaths := []string{"/login", "/admin", "/signin", "/auth"}
	
	for _, path := range commonPaths {
		loginURL := d.buildLoginURL(baseURL, path)
		resp, err := d.sendLoginPageRequest(ctx, loginURL)
		if err != nil {
			continue
		}

		if d.checkLoginPageResponse(resp, path) > 0.5 {
			return loginURL
		}
	}

	// 如果没有找到，返回基础URL（可能有内嵌登录表单）
	return baseURL
}

// sendLoginPageRequest 发送登录页面请求
func (d *WeakPasswordDetector) sendLoginPageRequest(ctx context.Context, loginURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", loginURL, nil)
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// sendAuthRequest 发送认证请求
func (d *WeakPasswordDetector) sendAuthRequest(ctx context.Context, loginURL, username, password string) (string, error) {
	// 构造POST数据
	data := url.Values{}
	data.Set("username", username)
	data.Set("password", password)
	// 添加常见的字段名变体
	data.Set("user", username)
	data.Set("passwd", password)
	data.Set("pwd", password)
	data.Set("email", username)

	req, err := http.NewRequestWithContext(ctx, "POST", loginURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 限制响应大小
	limitedReader := io.LimitReader(resp.Body, d.config.MaxResponseSize)
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return "", err
	}

	// 包含状态码和响应头信息
	responseInfo := fmt.Sprintf("Status: %d %s\n", resp.StatusCode, resp.Status)
	for key, values := range resp.Header {
		for _, value := range values {
			responseInfo += fmt.Sprintf("%s: %s\n", key, value)
		}
	}
	responseInfo += "\n" + string(body)

	return responseInfo, nil
}

// checkLoginPageResponse 检查登录页面响应
func (d *WeakPasswordDetector) checkLoginPageResponse(response, path string) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查HTTP状态码
	if strings.Contains(response, "status: 200") {
		confidence += 0.3
	}

	// 检查登录页面指示器
	for _, indicator := range d.loginIndicators {
		if strings.Contains(response, strings.ToLower(indicator)) {
			confidence += 0.2
			break
		}
	}

	// 检查认证模式
	for _, pattern := range d.authPatterns {
		if pattern.MatchString(response) {
			confidence += 0.4
			break
		}
	}

	// 检查路径特定的指示器
	if strings.Contains(path, "login") && strings.Contains(response, "password") {
		confidence += 0.3
	}
	if strings.Contains(path, "admin") && strings.Contains(response, "username") {
		confidence += 0.3
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// checkAuthResponse 检查认证响应
func (d *WeakPasswordDetector) checkAuthResponse(response string, cred Credential) float64 {
	confidence := 0.0
	response = strings.ToLower(response)

	// 检查成功模式
	for _, pattern := range d.successPatterns {
		if pattern.MatchString(response) {
			confidence += 0.5
			break
		}
	}

	// 检查重定向（可能表示成功登录）
	if strings.Contains(response, "status: 302") || strings.Contains(response, "status: 301") {
		if strings.Contains(response, "location:") {
			confidence += 0.4
		}
	}

	// 检查Cookie设置（可能表示会话建立）
	if strings.Contains(response, "set-cookie:") {
		if strings.Contains(response, "session") || strings.Contains(response, "auth") {
			confidence += 0.3
		}
	}

	// 检查错误指示器（如果没有错误，可能成功）
	errorIndicators := []string{
		"invalid", "incorrect", "wrong", "failed", "error",
		"denied", "unauthorized", "forbidden", "bad",
		"无效", "错误", "失败", "拒绝", "禁止",
	}
	
	hasError := false
	for _, indicator := range errorIndicators {
		if strings.Contains(response, indicator) {
			hasError = true
			break
		}
	}
	
	if !hasError && strings.Contains(response, "status: 200") {
		confidence += 0.2
	}

	// 确保置信度在合理范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractLoginEvidence 提取登录证据
func (d *WeakPasswordDetector) extractLoginEvidence(response, path string) string {
	lines := strings.Split(response, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 5 {
			lineLower := strings.ToLower(line)
			
			// 查找包含登录表单的行
			for _, indicator := range d.loginIndicators {
				if strings.Contains(lineLower, strings.ToLower(indicator)) {
					// 返回相关行及其上下文
					start := i
					if start > 0 {
						start--
					}
					end := i + 2
					if end >= len(lines) {
						end = len(lines)
					}
					return strings.Join(lines[start:end], "\n")
				}
			}
		}
	}

	// 如果没有找到特定证据，返回响应的前300个字符
	if len(response) > 300 {
		return response[:300] + "..."
	}
	return response
}

// extractAuthEvidence 提取认证证据
func (d *WeakPasswordDetector) extractAuthEvidence(response string, cred Credential) string {
	lines := strings.Split(response, "\n")
	
	// 查找状态码行
	for _, line := range lines {
		if strings.Contains(line, "Status:") {
			return line
		}
	}
	
	// 查找重定向行
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "location:") {
			return line
		}
	}
	
	// 如果没有找到特定证据，返回前200个字符
	if len(response) > 200 {
		return response[:200] + "..."
	}
	return response
}
